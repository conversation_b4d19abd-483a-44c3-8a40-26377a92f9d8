from apps.base.schemas import PaginationModel
from typing import Optional
from pydantic import BaseModel, Field

class UpdateVerificationStatusRequest(BaseModel):
    student_subject_grade_id: str
    verification_status: int


class GetVerificationBase(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    verification_status: Optional[int] = Field(-1, description="核验状态：0 未核验，1 核验通过，2 核验不通过")


class GetVerificationStataisitcs(BaseModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    exam_session: Optional[str] = Field(None, description="场次id")
    task_type: Optional[int] = Field(None, description="核验状态id")

class GetVerificationResult(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    exam_session: Optional[str] = Field(None, description="场次id")
    task_type: Optional[int] = Field(None, description="核验状态id")


class GetVerificationCount(BaseModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    exam_session: Optional[str] = Field(None, description="场次id")
    task_type: Optional[int] = Field(None, description="核验状态id")


class GetVerificationResultsByCategory(BaseModel):
    subject_id: str = Field(..., description="科目id")



class VerificationResultsTableData(BaseModel):
    data:list = Field(None, description="表数据")
    business_class: str = Field(None, description="业务分类")