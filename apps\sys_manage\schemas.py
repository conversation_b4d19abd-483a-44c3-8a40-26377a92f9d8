from typing import Optional, Literal
from pydantic import BaseModel, Field


class UpdateModuleReq(BaseModel):
    flag: Literal[1, 2] = Field(..., title="层级")
    module_id: Optional[str] = Field(..., title="模块id")
    rank: Optional[int] = Field(..., title="排序序号")
    module_name: Optional[str] = Field(..., title="模块名称")
    web_path: Optional[str] = Field(..., title="前端路由路径")
    icon: Optional[str] = Field(None, title="前端模块图标")
    show_link: bool = Field(True, title="是否显示在导航栏")


class CreateModuleReq(BaseModel):
    flag: Literal[1, 2] = Field(..., title="层级")
    module_id: Optional[str] = Field(None, title="模块id",
                                     description="如果创建的是父模块，该字段为 None，如果创建的是子模块，该字段为上一级模块的id")
    rank: int = Field(..., title="排序序号")
    module_name: str = Field(..., title="模块名称")
    web_path: str = Field(..., title="前端路由路径")
    icon: Optional[str] = Field(None, title="前端模块图标")
    show_link: bool = Field(True, title="是否显示在导航栏")


class DeleteModuleReq(BaseModel):
    flag: Literal[1, 2] = Field(..., title="层级")
    module_id: Optional[str] = Field(..., title="模块id")


class CreateFuncPointReq(BaseModel):
    parent_module_flag: Optional[int] = Field(..., title="上一级模块唯一标识")
    func_point_name: Optional[str] = Field(..., title="功能点名称")
    func_point_rank: Optional[int] = Field(..., title="排序序号")
    func_point_value: Optional[str] = Field(..., title="功能值，对应前端代码的值")


class UpdateFuncPointReq(BaseModel):
    func_point_id: Optional[str] = Field(..., title="功能点id")
    func_point_rank: Optional[int] = Field(..., title="排序序号")
    func_point_name: Optional[str] = Field(..., title="模块名称")
    func_point_value: Optional[str] = Field(..., title="前端路由路径")


class DeleteFuncPointReq(BaseModel):
    func_point_id: Optional[str] = Field(..., title="功能点id")
