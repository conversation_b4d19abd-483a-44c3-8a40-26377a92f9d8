import os
import traceback
import json
import math
from typing import Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy import select, and_, func, case
from settings import logger
from pydantic import BaseModel
from sqlalchemy.orm import Session
from apps.models.models import ExamQuestion, ExamPaper
from apps.human_task_manage.models import HumanReadTask,HumanPersonDistriAnswer
from sqlalchemy import distinct
from apps.human_mark_group.models import HumanMarkGroup,HumanGroupMember
from apps.users.services import get_current_user
from apps.human_statistics.models import HumanStatisticsSmallGroup
from apps.human_statistics.schemas import (
    PCalculateAverageScoreReq,
    PCalculateScoreDistributionReq,
    PCalculateScoreProgressReq,
    GetReviewStatisticsReq,
    GetSubjectScoreReq,
    TGetReviewStatisticsReq
)
from apps.models.models import Subject, ManualMark,Project
from apps.grade_manage.models import HumanStudentSubjectGrade
from apps.human_mark_exception.models import HumanAnswerException
from utils.utils import round_half_up
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from settings import configs
from apps.models.models import UserInfo, ManualReadTask, QuesUsed,UserRole

# 创建APIRouter
p_score_analysis_router = APIRouter()

@p_score_analysis_router.post(path="/calculate_average_score_per_question", response_model=BaseResponse,
                            summary="资格组长页面：各题得分率")
async def calculate_average_score_per_question_api(
        query: PCalculateAverageScoreReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 资格组长页面：各题得分率")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件
    filters = [
        HumanStatisticsSmallGroup.statistics_type == 1,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

    # 查询所有符合条件的平均分记录 (statistics_type == 1)
    score_data = new_session.query(
        HumanStatisticsSmallGroup.ques_group_id,
        HumanStatisticsSmallGroup.date,
        HumanStatisticsSmallGroup.statistics_result_1
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        score_data = score_data.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        score_data = score_data.filter(HumanStatisticsSmallGroup.date <= end_date)

    score_results = score_data.all()

    # 按题组分组
    question_avg_scores = {}
    for result in score_results:
        ques_group_id = result.ques_group_id
        if ques_group_id not in question_avg_scores:
            question_avg_scores[ques_group_id] = []
        question_avg_scores[ques_group_id].append(result)

    # 计算每个题组的加权平均分并获取题组名称和总分
    final_results = []
    for ques_group_id, daily_results in question_avg_scores.items():
        # 获取该题组的工作量数据
        workload_filters = [
            HumanStatisticsSmallGroup.statistics_type == 4,
            HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
            HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
        ]
        if query.subject_id is not None:
            workload_filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
        if query.task_id:
            workload_filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
        if query.project_id:
            workload_filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
        if query.ques_group_id:
            workload_filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
            
        # 添加日期范围条件
        if start_date:
            workload_filters.append(HumanStatisticsSmallGroup.date >= start_date)
        if end_date:
            workload_filters.append(HumanStatisticsSmallGroup.date <= end_date)
            
        workload_data = new_session.query(
            HumanStatisticsSmallGroup.date,
            HumanStatisticsSmallGroup.statistics_result_1
        ).filter(*workload_filters)
        
        workload_results = workload_data.all()
        
        # 将工作量结果按日期组织成字典
        workload_dict = {}
        for result in workload_results:
            workload_dict[result.date] = int(result.statistics_result_1) if result.statistics_result_1 else 0.0

        # 计算加权平均分：每一天的平均分 * 该天工作量求和，然后除以总工作量
        total_weighted_score = 0.0
        total_workload = 0

        for result in daily_results:
            daily_avg_score = float(result.statistics_result_1) if result.statistics_result_1 else 0.0
            # 从工作量字典中获取对应日期的工作量
            daily_workload = workload_dict.get(result.date, 0.0)

            total_weighted_score += daily_avg_score * daily_workload
            total_workload += daily_workload

        # 避免除以0，并保留两位小数
        avg_score = round_half_up(total_weighted_score / total_workload, 2) if total_workload > 0 else 0.0

        # 获取题组名称和试题总分
        group_info = new_session.query(
            HumanMarkGroup.group_name,
            ExamQuestion.ques_score
        ).outerjoin(
            ExamQuestion,
            HumanMarkGroup.ques_code == ExamQuestion.ques_id
        ).filter(
            HumanMarkGroup.group_id == ques_group_id
        ).first()

        if group_info:
            group_name = group_info.group_name or "未知题组"
            total_score = float(group_info.ques_score) if group_info.ques_score else 0.0

            # 计算得分率
            score_rate = round_half_up(avg_score / total_score * 100, 2) if total_score > 0 else 0.0

            final_results.append({
                "ques_group_id": ques_group_id,
                "group_name": group_name,
                "score_rate": score_rate
            })

    # 构造返回数据，按题组名称排序
    sorted_results = sorted(final_results, key=lambda x: x['group_name'])

    # 构建图表数据格式
    chart_data = {
        "legend": ["得分率"],
        "x_data": [item['group_name'] for item in sorted_results],
        "y_data": [item['score_rate'] for item in sorted_results]
    }

    logger.info(f"资格组长页面：各题得分率，结果数量: {len(sorted_results)}")
    return BaseResponse(data=chart_data, msg="资格组长页面：各题得分率")


@p_score_analysis_router.post(path="/calculate_score_distribution", response_model=BaseResponse,summary="资格组长页面：计算科目成绩分布")
async def calculate_score_distribution_api(
        query: PCalculateScoreDistributionReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 资格组长页面：计算科目成绩分布")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 构建查询条件
    filters = []

    if query.project_id:
        filters.append(HumanStudentSubjectGrade.project_id == query.project_id)
    if query.subject_id:
        filters.append(HumanStudentSubjectGrade.subject_id == query.subject_id)

    # 添加日期范围条件
    if start_date:
        filters.append(HumanStudentSubjectGrade.date >= start_date)
    if end_date:
        filters.append(HumanStudentSubjectGrade.date <= end_date)

    score_data = new_session.query(HumanStudentSubjectGrade.score, Subject.subject_total_score).join(
        Subject, HumanStudentSubjectGrade.subject_id == Subject.subject_id
    ).filter(*filters)
    results = score_data.all()

    # 获取试卷总分
    total_score = 0
    if results:
        total_score = int(results[0].subject_total_score)

    score_count = {}
    for result in results:
        # 检查 score 是否为 None
        if result.score is not None:
            grade = float(result.score)
            rounded_grade = math.ceil(grade)
            if rounded_grade in score_count:
                score_count[rounded_grade] += 1
            else:
                score_count[rounded_grade] = 1

    x_data = []  # 分数
    y_data = []  # 对应人数
    for score in range(0,total_score + 1):
        x_data.append(str(score))
        count = score_count.get(score, 0)
        y_data.append(count)

    chart_data = {
        "legend": ["分数分布"],
        "x_data": x_data,
        "y_data": y_data
    }

    logger.info(f"资格组长页面：计算科目成绩分布，结果数量: {len(results)}")
    return BaseResponse(data=chart_data, msg="资格组长页面：计算科目成绩分布")

@p_score_analysis_router.post(path="/calculate_score_progress", response_model=BaseResponse, summary="资格组长页面：计算评分进度")
async def calculate_score_progress_api(
        query: PCalculateScoreProgressReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 资格组长页面：计算评分进度")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 验证参数
    if not query.project_id and not query.subject_id:
        return BaseResponse(code=400, msg="project_id 和 subject_id 至少需要提供一个")

    # 构建查询条件
    filters = [
        HumanStatisticsSmallGroup.statistics_type == 4,  # 已阅量
    ]
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.subject_id:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.round_count is not None:
        filters.append(HumanStatisticsSmallGroup.round_count == query.round_count)
    if query.task_type is not None:
        filters.append(HumanStatisticsSmallGroup.task_type == query.task_type)

    # 添加日期范围条件
    if start_date:
        filters.append(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        filters.append(HumanStatisticsSmallGroup.date <= end_date)

    # 查询已阅量
    reviewed_count = new_session.query(
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label("total_reviewed")
    ).filter(*filters).scalar()

    reviewed_count = float(reviewed_count) if reviewed_count else 0.0

    # 获取阅卷总量
    # 1. 通过 HumanReadTask 获取所有相关的 ques_code
    # 构建阅卷任务查询条件
    task_filters = []
    if query.project_id:
        task_filters.append(HumanReadTask.project_id == query.project_id)
    if query.subject_id:
        task_filters.append(HumanReadTask.subject_id == query.subject_id)
    if query.task_type is not None:
        task_filters.append(HumanReadTask.task_type == query.task_type)

    # 获取所有相关的 ques_code
    ques_codes = new_session.query(distinct(HumanReadTask.ques_code)).filter(*task_filters).all()
    ques_codes = [qc[0] for qc in ques_codes if qc[0]]  # 提取元组中的字符串值

    # 2. 通过 t_ques_used 获取每个 ques_code 的 used_count 并去重求和
    total_count = 0
    if ques_codes:
        used_counts = new_session.query(func.sum(QuesUsed.used_count).label("total_used")).filter(
            QuesUsed.ques_code.in_(ques_codes)
        ).scalar()
        total_count = float(used_counts) if used_counts else 0.0

    # 计算评分进度
    progress = 0.0
    if total_count > 0:
        progress = round_half_up(reviewed_count / total_count * 100, 2)

    # 构造返回数据
    result_data = {
        "reviewed_count": reviewed_count,
        "total_count": total_count,
        "progress": progress
    }

    logger.info(f"计算评分进度完成，结果: {result_data}")
    return BaseResponse(data=result_data, msg="资格组长页面：计算评分进度成功")

@p_score_analysis_router.post(path="/get_review_statistics2", response_model=BaseResponse, summary="资格组长页面：获取评卷统计信息")
async def get_review_statistics_api1(
        query: GetReviewStatisticsReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']}资格组长页面： 获取评卷统计信息")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # # 1. 获取所有相关的 subject_id 和 ques_group_id
    # # 通过 project_id 筛选 HumanStatisticsSmallGroup 中的所有 subject_id 和 ques_group_id
    # group_query = new_session.query(
    #     HumanStatisticsSmallGroup.project_id,
    #     HumanStatisticsSmallGroup.subject_id,
    #     HumanStatisticsSmallGroup.ques_group_id
    # ).filter(
    #     HumanStatisticsSmallGroup.project_id == query.project_id
    # ).distinct(
    #
    #     HumanStatisticsSmallGroup.subject_id,
    #     HumanStatisticsSmallGroup.ques_group_id
    # )
    #
    # # 添加日期范围条件
    # if start_date:
    #     group_query = group_query.filter(HumanStatisticsSmallGroup.date >= start_date)
    # if end_date:
    #     group_query = group_query.filter(HumanStatisticsSmallGroup.date <= end_date)
    # if query.task_type is not None:
    #     group_query = group_query.filter(HumanStatisticsSmallGroup.task_type == query.task_type)
    # if query.round_count is not None:
    #     group_query = group_query.filter(HumanStatisticsSmallGroup.round_count == query.round_count)
    #
    # groups_data = group_query.all()
    #
    # if not groups_data:
    #     return BaseResponse(data=[], msg="未找到相关数据")
    #
    # # 获取所有 project_id 对应的 project_name
    # project_ids = list(set([g.project_id for g in groups_data if g.project_id]))
    # project_names = {}
    # if project_ids:
    #     projects = new_session.query(Project.project_id, Project.project_name).filter(
    #         Project.project_id.in_(project_ids)
    #     ).all()
    #     project_names = {p.project_id: p.project_name for p in projects}
    #
    # # 2. 获取所有 subject_id 对应的 subject_name
    # subject_ids = list(set([g.subject_id for g in groups_data if g.subject_id]))
    # subject_names = {}
    # if subject_ids:
    #     subjects = new_session.query(Subject.subject_id, Subject.subject_name).filter(
    #         Subject.subject_id.in_(subject_ids)
    #     ).all()
    #     subject_names = {s.subject_id: s.subject_name for s in subjects}

    # 3. 构建一次查询，获取所有需要的统计信息
    main_query = select(
        HumanStatisticsSmallGroup.project_id,
        Project.project_name,
        HumanStatisticsSmallGroup.subject_id,
        Subject.subject_name,
        HumanStatisticsSmallGroup.ques_group_id,
        HumanMarkGroup.group_name,
        ExamQuestion.ques_score,
        # 已阅量统计 - 按题组分组
        func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('reviewed_count'),
        # 工作时间统计 - 按题组分组
        func.sum(case((HumanStatisticsSmallGroup.statistics_type == 10, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('work_time'),
        # 平均速度（份/时）
        (func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsSmallGroup.statistics_type == 10, HumanStatisticsSmallGroup.statistics_result_1), else_=0)), 0) * 3600).label('average_speed'),
        # 问题卷数量 - 按题组分组
        func.count(case((HumanAnswerException.exception_type != 0, HumanAnswerException.answer_exception_id), else_=0)).label('exception_count'),

        # 题组对应的总题量 - 按题组分组
        func.sum(QuesUsed.used_count).label('group_total_count')
    ).join(Project, Project.project_id == HumanStatisticsSmallGroup.project_id) \
        .join(Subject, Subject.subject_id == HumanStatisticsSmallGroup.subject_id) \
        .join(HumanMarkGroup, and_(HumanMarkGroup.group_id == HumanStatisticsSmallGroup.ques_group_id, HumanMarkGroup.group_level==2 )) \
        .join(ExamQuestion, HumanMarkGroup.ques_code == ExamQuestion.ques_code) \
        .join(HumanReadTask, HumanReadTask.ques_code == ExamQuestion.ques_code) \
        .outerjoin(HumanAnswerException, HumanReadTask.task_id == HumanAnswerException.task_id) \
        .join(QuesUsed, QuesUsed.ques_code == ExamQuestion.ques_code) \

    # 添加日期范围条件
    if start_date:
        main_query = main_query.filter(HumanStatisticsSmallGroup.date >= start_date)
        main_query = main_query.filter(HumanAnswerException.date >= start_date)
    if end_date:
        main_query = main_query.filter(HumanStatisticsSmallGroup.date <= end_date)
        main_query = main_query.filter(HumanAnswerException.date <= end_date)
    if query.task_type is not None:
        main_query = main_query.filter(HumanStatisticsSmallGroup.task_type == query.task_type)
    if query.round_count is not None:
        main_query = main_query.filter(HumanStatisticsSmallGroup.round_count == query.round_count)

    # 分组 - 按题组分组
    main_query = main_query.group_by(
        HumanStatisticsSmallGroup.project_id,
        Project.project_name,
        HumanStatisticsSmallGroup.subject_id,
        Subject.subject_name,
        HumanStatisticsSmallGroup.ques_group_id,
        HumanMarkGroup.group_name,
        ExamQuestion.ques_score
    )

    # 执行查询
    results = new_session.execute(main_query).all()

    # 4. 构造返回数据
    final_results = []
    for row in results:
        subject_id = row.subject_id
        subject_name = row.subject_name
        ques_group_id = row.ques_group_id
        group_name = row.group_name or "未知题组"
        total_score = float(row.ques_score) if row.ques_score else 0.0
        # 已阅量
        reviewed_count = int(row.reviewed_count) if row.reviewed_count else 0
        # 题组总题量
        group_total_count = int(row.group_total_count) if row.group_total_count else 0
        # 未阅量
        unreviewed_count = group_total_count - reviewed_count
        average_speed = int(row.average_speed) if row.average_speed else 0
        remaining_time = round_half_up(unreviewed_count / average_speed, 2) if row.average_speed else 0
        # # 参与专家人数
        # expert_count = int(row.expert_count) if row.expert_count else 0
        # 平均分



        # average_score = round_half_up(float(row.average_score) if row.average_score else 0.0, 2)
        # 问题卷数量
        exception_count = int(row.exception_count) if row.exception_count else 0
        # 满分率和零分率（通过HumanPersonDistriAnswer表获取）
        full_score_rate = 0.0
        zero_score_rate = 0.0
        if group_total_count > 0 and total_score > 0:
            # 获取该题组对应的所有试题编号
            ques_codes_for_group = new_session.query(HumanMarkGroup.ques_code).filter(
                HumanMarkGroup.group_id == ques_group_id
            ).all()

            if ques_codes_for_group:
                ques_code_list = [q.ques_code for q in ques_codes_for_group]
                try:
                    score_query = select(
                        HumanPersonDistriAnswer.stu_secret_num,
                        func.sum(HumanPersonDistriAnswer.mark_score).label('mark_total_score')
                    ).filter(
                        HumanPersonDistriAnswer.ques_code.in_(ques_code_list)
                    ).group_by(HumanPersonDistriAnswer.stu_secret_num)

                    score_results = new_session.execute(score_query).all()

                    # 统计满分和零分的数量
                    full_score_count = 0
                    zero_score_count = 0

                    for result in score_results:
                        score = float(result.mark_total_score) if result.mark_total_score else 0.0
                        if score == total_score:
                            full_score_count = result.count
                        elif score == 0:
                            zero_score_count = result.count

                    if group_total_count > 0:
                        full_score_rate = round_half_up(full_score_count / group_total_count * 100, 2)
                        zero_score_rate = round_half_up(zero_score_count / group_total_count * 100, 2)
                except Exception as e:
                    logger.error(f"通过HumanPersonDistriAnswer获取满分和零分数据失败：{e}")
                    # 出现异常时，设置默认值
                    full_score_rate = 0.0
                    zero_score_rate = 0.0

        # 构造返回数据
        result_item = {
            "subject_id": subject_id,
            "subject_name": subject_name,
            "ques_group_id": ques_group_id,
            "group_name": group_name,
            "total_count": group_total_count,
            "reviewed_count": reviewed_count,
            "unreviewed_count": unreviewed_count,
            "average_speed": average_speed,
            "remaining_time": remaining_time,
            # "expert_count": expert_count,
            # "average_score": average_score,
            "exception_count": exception_count,
            "full_score_rate": full_score_rate,
            "zero_score_rate": zero_score_rate
        }
        final_results.append(result_item)

    # 按 subject_name 和 group_name 排序
    sorted_results = sorted(final_results, key=lambda x: (x['subject_name'], x['group_name']))

    # 计算总数
    subject_count = len(set([item['subject_id'] for item in sorted_results]))
    # expert_total = sum([item['expert_count'] for item in sorted_results])
    exception_total = sum([item['exception_count'] for item in sorted_results])
    total_count_total = sum([item['total_count'] for item in sorted_results])

    total = {
        "subject_count": subject_count,
        # "expert_total": expert_total,
        "exception_total": exception_total,
        "total_count_total": total_count_total
    }

    # 构造返回数据
    response_data = {
        "group_data": sorted_results,
        "total": total
    }

    logger.info(f"资格组长页面：获取评卷统计信息完成，共 {len(sorted_results)} 个题组")
    return BaseResponse(data=response_data, msg="资格组长页面：获取评卷统计信息成功")


@p_score_analysis_router.post(path="/get_review_statistics1", response_model=BaseResponse, summary="资格组长页面：获取评卷统计信息")
async def get_review_statistics_api(
        query: GetReviewStatisticsReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']}资格组长页面： 获取评卷统计信息")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
            logger.info(f"日期范围: {start_date} 至 {end_date}")
        except ValueError:
            logger.error("日期范围格式错误")

    # 1. 获取所有相关的 subject_id 和 ques_group_id
    # 通过 project_id 筛选 HumanStatisticsSmallGroup 中的所有 subject_id 和 ques_group_id
    group_query = new_session.query(
        HumanStatisticsSmallGroup.subject_id,
        HumanStatisticsSmallGroup.ques_group_id
    ).filter(
        HumanStatisticsSmallGroup.project_id == query.project_id,
        HumanStatisticsSmallGroup.subject_id == query.subject_id
    ).distinct(
        HumanStatisticsSmallGroup.subject_id,
        HumanStatisticsSmallGroup.ques_group_id
    )

    # 添加日期范围条件
    if start_date:
        group_query = group_query.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        group_query = group_query.filter(HumanStatisticsSmallGroup.date <= end_date)
    if query.task_type is not None:
        group_query = group_query.filter(HumanStatisticsSmallGroup.task_type == query.task_type)
    if query.round_count is not None:
        group_query = group_query.filter(HumanStatisticsSmallGroup.round_count == query.round_count)
    if query.subject_id:
        group_query = group_query.filter(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    groups_data = group_query.all()

    if not groups_data:
        return BaseResponse(data=[], msg="未找到相关数据")

    # 2. 获取所有 subject_id 对应的 subject_name
    subject_ids = list(set([g.subject_id for g in groups_data if g.subject_id]))
    subject_names = {}
    if subject_ids:
        subjects = new_session.query(Subject.subject_id, Subject.subject_name).filter(
            Subject.subject_id.in_(subject_ids)
        ).all()
        subject_names = {s.subject_id: s.subject_name for s in subjects}

    # 3. 为每个题组计算统计信息
    results = []
    for i, group in enumerate(groups_data):
        subject_id = group.subject_id
        ques_group_id = group.ques_group_id
        subject_name = subject_names.get(subject_id, "未知科目")

        # 获取题组名称和试题总分
        group_info = new_session.query(
            HumanMarkGroup.group_name,
            ExamQuestion.ques_score
        ).outerjoin(
            ExamQuestion,
            HumanMarkGroup.ques_code == ExamQuestion.ques_code
        ).filter(
            HumanMarkGroup.group_id == ques_group_id
        ).first()

        group_name = group_info.group_name if group_info else "未知题组"
        total_score = float(group_info.ques_score) if group_info and group_info.ques_score else 0.0

        # 4. 计算阅卷总量
        total_count = 0
        task_filters = [HumanReadTask.project_id == query.project_id]
        if subject_id:
            task_filters.append(HumanReadTask.subject_id == subject_id)
        if query.task_type is not None:
            task_filters.append(HumanReadTask.task_type == query.task_type)

        # 获取所有相关的 ques_code
        ques_codes = new_session.query(distinct(HumanReadTask.ques_code)).filter(*task_filters).all()
        ques_codes = [qc[0] for qc in ques_codes if qc[0]]  # 提取元组中的字符串值

        if ques_codes:
            group_ques_codes = new_session.query(HumanMarkGroup.ques_code).filter(
            HumanMarkGroup.group_id == ques_group_id).all()
            
            group_ques_code_list = [qc[0] for qc in group_ques_codes if qc[0]]

            if group_ques_code_list:
                # 计算该题组的阅卷量总和
                total_count_result = new_session.query(func.sum(QuesUsed.used_count)).filter(
                    QuesUsed.ques_code.in_(group_ques_code_list)
                ).scalar()
                total_count = int(total_count_result) if total_count_result else 0

        # 5. 计算已阅量
        reviewed_filters = [
            HumanStatisticsSmallGroup.statistics_type == 4,  # 已阅量
            HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
            HumanStatisticsSmallGroup.project_id == query.project_id
        ]
        if subject_id:
            reviewed_filters.append(HumanStatisticsSmallGroup.subject_id == subject_id)
        if query.task_type is not None:
            reviewed_filters.append(HumanStatisticsSmallGroup.task_type == query.task_type)
        if query.round_count is not None:
            reviewed_filters.append(HumanStatisticsSmallGroup.round_count == query.round_count)

        # 添加日期范围条件
        if start_date:
            reviewed_filters.append(HumanStatisticsSmallGroup.date >= start_date)
        if end_date:
            reviewed_filters.append(HumanStatisticsSmallGroup.date <= end_date)

        reviewed_result = new_session.query(
            func.sum(HumanStatisticsSmallGroup.statistics_result_1).label("total_reviewed")
        ).filter(*reviewed_filters).scalar()
        reviewed_count = int(reviewed_result) if reviewed_result else 0.0

        # 6. 计算未阅量
        unreviewed_count = total_count - reviewed_count

        # 7. 计算平均速度（份/时）和预估剩余时长
        try:
            # 查询工作时间
            work_time_filters = [
                HumanStatisticsSmallGroup.statistics_type == 10,  # 工作时间
                HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                HumanStatisticsSmallGroup.project_id == query.project_id
            ]
            if subject_id:
                work_time_filters.append(HumanStatisticsSmallGroup.subject_id == subject_id)
            if query.task_type is not None:
                work_time_filters.append(HumanStatisticsSmallGroup.task_type == query.task_type)
            if query.round_count is not None:
                work_time_filters.append(HumanStatisticsSmallGroup.round_count == query.round_count)

            # 添加日期范围条件
            if start_date:
                work_time_filters.append(HumanStatisticsSmallGroup.date >= start_date)
            if end_date:
                work_time_filters.append(HumanStatisticsSmallGroup.date <= end_date)

            # 查询工作时间总和
            work_time_data = new_session.query(
                func.sum(HumanStatisticsSmallGroup.statistics_result_1)
            ).filter(*work_time_filters)

            # 执行查询
            work_time_val = float(work_time_data.scalar()) if work_time_data.scalar() is not None else 0.0

            # 计算平均速度：已阅量 / (工作时间/3600)
            if work_time_val > 0:
                average_speed = reviewed_count / (work_time_val / 3600)
                average_speed = round_half_up(average_speed, 2)
            else:
                average_speed = 0.0

            # 计算剩余时间：未阅量 / 平均速度（如果平均速度为0，则设为0）
            if average_speed > 0:
                remaining_time = unreviewed_count / average_speed
                remaining_time = round_half_up(remaining_time, 2)
            else:
                remaining_time = 0.0

        except Exception as e:
            # 出现异常时，设置默认值
            average_speed = 0.0
            remaining_time = 0.0

        # 8. 计算参与专家人数
        expert_count = 0
        group_info = new_session.query(HumanStatisticsSmallGroup.group_id).filter(
            HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
            HumanStatisticsSmallGroup.project_id == query.project_id
        ).first()
        if group_info:
            group_id = group_info.group_id
            expert_count = new_session.query(func.count(distinct(HumanGroupMember.user_id))).filter(
                HumanGroupMember.group_id == group_id,
                HumanGroupMember.member_role == 2
            ).scalar()
            expert_count = int(expert_count) if expert_count else 0

        # 9. 计算平均分
        avg_score_filters = [
            HumanStatisticsSmallGroup.statistics_type == 14,  # 平均分
            HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
            HumanStatisticsSmallGroup.project_id == query.project_id
        ]
        if subject_id:
            avg_score_filters.append(HumanStatisticsSmallGroup.subject_id == subject_id)
        if query.task_type is not None:
            avg_score_filters.append(HumanStatisticsSmallGroup.task_type == query.task_type)
        if query.round_count is not None:
            avg_score_filters.append(HumanStatisticsSmallGroup.round_count == query.round_count)

        # 添加日期范围条件
        if start_date:
            avg_score_filters.append(HumanStatisticsSmallGroup.date >= start_date)
        if end_date:
            avg_score_filters.append(HumanStatisticsSmallGroup.date <= end_date)

        avg_score_result = new_session.query(
            func.avg(HumanStatisticsSmallGroup.statistics_result_1).label("avg_score")
        ).filter(*avg_score_filters).scalar()
        average_score = round_half_up(float(avg_score_result) if avg_score_result else 0.0, 2)

        # 10. 计算问题卷数量
        exception_count = 0
        try:
            # 通过ques_group_id获取试题编号
            ques_codes = new_session.query(HumanMarkGroup.ques_code).filter(
                HumanMarkGroup.group_id == ques_group_id
            ).all()

            if ques_codes:
                ques_code_list = [q.ques_code for q in ques_codes]

                # 通过试题编号和任务类型获取task_id
                tasks = new_session.query(HumanReadTask).filter(
                    HumanReadTask.ques_code.in_(ques_code_list),
                    HumanReadTask.task_type == query.task_type
                ).all()

                if tasks:
                    task_ids = [task.task_id for task in tasks]
                    # 直接统计所有问题卷数量（不分类）
                    exception_query = new_session.query(
                        func.count(case((HumanAnswerException.exception_type != 0, HumanAnswerException.answer_exception_id)))
                    ).filter(HumanAnswerException.task_id.in_(task_ids))

                    # 添加日期范围条件
                    if start_date:
                        exception_query = exception_query.filter(HumanAnswerException.date >= start_date)
                    if end_date:
                        exception_query = exception_query.filter(HumanAnswerException.date <= end_date)

                    # 执行查询获取总数
                    exception_result = exception_query.scalar()
                    exception_count = int(exception_result) if exception_result else 0
        except Exception as e:
            logger.error(f"计算问题卷数量失败：{e}")
            exception_count = 0

        # 满分率和零分率（通过HumanPersonDistriAnswer表获取）
        full_score_rate = 0.0
        zero_score_rate = 0.0

        if  total_score > 0:
            # 获取该题组对应的所有试题编号
            ques_codes_for_group = new_session.query(HumanMarkGroup.ques_code).filter(
                HumanMarkGroup.group_id == ques_group_id
            ).all()

            if ques_codes_for_group:
                ques_code_list = [q.ques_code for q in ques_codes_for_group]

                # 直接通过ques_code关联HumanPersonDistriAnswer表获取相关数据
                # 这样可以避免通过task_id的复杂关联
                try:
                    # 根据 stu_secret_num 对 mark_score 求和，然后统计满分和零分的数量
                    # 先按 stu_secret_num 分组求和
                    score_query = select(
                        HumanPersonDistriAnswer.stu_secret_num,
                        func.sum(HumanPersonDistriAnswer.mark_score).label('mark_total_score')
                    ).filter(
                        HumanPersonDistriAnswer.ques_code.in_(ques_code_list)
                    ).group_by(HumanPersonDistriAnswer.stu_secret_num)

                    score_results = new_session.execute(score_query).all()

                    # 统计满分和零分的数量
                    full_score_count = 0
                    zero_score_count = 0

                    for result in score_results:
                        score = float(result.mark_total_score) if result.mark_total_score else 0.0
                        if score == total_score:
                            full_score_count += 1
                        elif score == 0:
                            zero_score_count += 1

                    
                    full_score_rate = round_half_up(full_score_count / total_count * 100, 2)
                    zero_score_rate = round_half_up(zero_score_count / total_count * 100, 2)
                except Exception as e:
                    logger.error(f"通过HumanPersonDistriAnswer获取满分和零分数据失败：{e}")
                    # 出现异常时，设置默认值
                    full_score_rate = 0.0
                    zero_score_rate = 0.0

        # 构造返回数据
        result_item = {
            "subject_id": subject_id,
            "subject_name": subject_name,
            "ques_group_id": ques_group_id,
            "group_name": group_name,
            "total_count": total_count,
            "reviewed_count": reviewed_count,
            "unreviewed_count": unreviewed_count,
            "average_speed": average_speed,
            "remaining_time": remaining_time,
            "expert_count": expert_count,
            "average_score": average_score,
            "exception_count": exception_count,
            "full_score_rate": full_score_rate,
            "zero_score_rate": zero_score_rate
        }
        results.append(result_item)

    # 按 subject_name 和 group_name 排序
    sorted_results = sorted(results, key=lambda x: (x['subject_name'], x['group_name']))

    # 计算总数
    ques_group_count = len(set([item['ques_group_id'] for item in results]))
    
    # 修改：计算该资格下所有题小组的专家去重后数量
    expert_total = 0
    if results:
        # 获取所有题组对应的group_id
        group_ids = []
        for item in results:
            # 从HumanStatisticsSmallGroup表中获取group_id
            group_info = new_session.query(HumanStatisticsSmallGroup.group_id).filter(
                HumanStatisticsSmallGroup.ques_group_id == item['ques_group_id'],
                HumanStatisticsSmallGroup.project_id == query.project_id
            ).first()
            if group_info and group_info.group_id:
                group_ids.append(group_info.group_id)
        
        # 去重group_id
        unique_group_ids = list(set(group_ids))
        if unique_group_ids:
            # 从HumanGroupMember表中统计专家数量
            expert_total = new_session.query(func.count(distinct(HumanGroupMember.user_id))).filter(
                HumanGroupMember.group_id.in_(unique_group_ids),
                HumanGroupMember.member_role == 2
            ).scalar()
            expert_total = int(expert_total) if expert_total else 0
    
    exception_total = sum([item['exception_count'] for item in results])
    total_count_total = sum([item['total_count'] for item in results])

    total = {
        "ques_group_count": ques_group_count,
        "expert_total": expert_total,
        "exception_total": exception_total,
        "total_count_total": total_count_total
    }
    # 构造返回数据
    response_data = {
        "group_data": sorted_results,
        "total": total
    }
    try:
        # 构造图表数据
        x_data = []
        y1_data = []  # 已阅量
        y2_data = []  # 未阅量

        for item in sorted_results:
            # x_data中的每个元素是包含项目和科目信息的对象
            x_data.append({
                "subject_id": item["subject_id"],
                "subject_name": item["subject_name"],
                "ques_group_id": item["ques_group_id"],
                "ques_group_name": item["group_name"]
            })
            y1_data.append(item["reviewed_count"])
            y2_data.append(item["unreviewed_count"])

        response_data["workload_data"] = {
            "legend": ["已阅量", "未阅量"],
            "x_data": x_data,
            "y1_data": y1_data,
            "y2_data": y2_data
        }
    except Exception as e:
        logger.error(f"构造workload_data失败：{e}")
        # 出现异常时，设置默认值
        response_data["workload_data"] = {
            "legend": ["已阅量", "未阅量"],
            "x_data": [],
            "y1_data": [],
            "y2_data": []
        }

    logger.info(f"资格组长页面：获取评卷统计信息完成，共 {len(sorted_results)} 个题组")
    return BaseResponse(data=response_data, msg="资格组长页面：获取评卷统计信息成功")


@p_score_analysis_router.post(path="/get_subject_score", response_model=BaseResponse, summary="获取科目成绩情况")
async def get_subject_score_api(
        query: GetSubjectScoreReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 获取科目成绩情况: project_id={query.project_id}, subject_id={query.subject_id}")

    try:
        # 1. 从t_subject表中获取科目总分和及格分数
        subject_info = new_session.query(Subject.subject_total_score, Subject.subject_pass_score).filter(
            Subject.subject_id == query.subject_id
        ).first()

        if not subject_info:
            return BaseResponse(data={}, msg="未找到科目信息")

        total_score = float(subject_info.subject_total_score) if subject_info.subject_total_score else 0.0
        pass_score = float(subject_info.subject_pass_score) if subject_info.subject_pass_score else 0.0

        result = new_session.query(
            func.count(HumanStudentSubjectGrade.score).label('total_count'),
            func.sum(case(
                (HumanStudentSubjectGrade.score == total_score, 1),
                else_=0
            )).label('full_score_count'),
            func.sum(case(
                (HumanStudentSubjectGrade.score == 0.0, 1),
                else_=0
            )).label('zero_score_count'),
            func.sum(case(
                (HumanStudentSubjectGrade.score >= pass_score, 1),
                else_=0
            )).label('pass_count')
        ).filter(
            HumanStudentSubjectGrade.project_id == query.project_id,
            HumanStudentSubjectGrade.subject_id == query.subject_id,
            HumanStudentSubjectGrade.score.isnot(None)
        ).first()

        # 3. 提取统计结果
        total_count = int(result.total_count) if result.total_count else 0
        full_score_count = int(result.full_score_count) if result.full_score_count else 0
        zero_score_count = int(result.zero_score_count) if result.zero_score_count else 0
        pass_count = int(result.pass_count) if result.pass_count else 0

        # 4. 计算比率（避免除零错误）
        full_score_rate = round_half_up(full_score_count / total_count * 100,2) if total_count > 0 else 0.0
        zero_score_rate = round_half_up(zero_score_count / total_count * 100,2) if total_count > 0 else 0.0
        pass_rate = round_half_up(pass_count / total_count * 100,2) if total_count > 0 else 0.0

        # 5. 构造返回数据
        result_data = {
            "full_score_rate": full_score_rate,
            "zero_score_rate": zero_score_rate,
            "pass_rate": pass_rate
        }

        logger.info(f"科目成绩情况获取成功: {result_data}")
        return BaseResponse(data=result_data, msg="获取科目成绩情况成功")

    except Exception as e:
        logger.error(f"获取科目成绩情况失败: {e}")
        return BaseResponse(data={}, msg=f"获取科目成绩情况失败: {str(e)}")


@p_score_analysis_router.post(path="/get_total_statistics", response_model=BaseResponse, summary="阅卷组长页面：获取评卷统计信息")
async def get_total_statistics_api(
        query: TGetReviewStatisticsReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']}阅卷组长页面： 获取评卷统计信息")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 1. 获取所有相关的 project_id 和 subject_id
    # 通过 project_id 筛选 HumanStatisticsSmallGroup 中的所有 subject_id 和 ques_group_id
    group_query = new_session.query(
        HumanStatisticsSmallGroup.project_id,
        HumanStatisticsSmallGroup.subject_id
    ).filter(
        HumanStatisticsSmallGroup.project_id.isnot(None)
    ).distinct(
        HumanStatisticsSmallGroup.project_id,
        HumanStatisticsSmallGroup.subject_id
    )

    # 添加日期范围条件
    if start_date:
        group_query = group_query.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        group_query = group_query.filter(HumanStatisticsSmallGroup.date <= end_date)
    if query.task_type is not None:
        group_query = group_query.filter(HumanStatisticsSmallGroup.task_type == query.task_type)
    if query.round_count is not None:
        group_query = group_query.filter(HumanStatisticsSmallGroup.round_count == query.round_count)

    groups_data = group_query.all()

    if not groups_data:
        return BaseResponse(data=[], msg="未找到相关数据")

    # 2. 获取所有 subject_id 对应的 subject_name
    subject_ids = list(set([g.subject_id for g in groups_data if g.subject_id]))
    subject_names = {}
    if subject_ids:
        subjects = new_session.query(Subject.subject_id, Subject.subject_name).filter(
            Subject.subject_id.in_(subject_ids)
        ).all()
        subject_names = {s.subject_id: s.subject_name for s in subjects}

    # 3. 为每个项目和科目计算统计信息
    results = []
    for group in groups_data:
        project_id = group.project_id
        subject_id = group.subject_id
        subject_name = subject_names.get(subject_id, "未知科目")

        # 获取科目总分和及格分数
        subject_info = new_session.query(Subject.subject_total_score, Subject.subject_pass_score).filter(
            Subject.subject_id == subject_id
        ).first()
        if not subject_info:
            total_score = 0.0
            pass_score = 0.0
        else:
            total_score = float(subject_info.subject_total_score) if subject_info.subject_total_score else 0.0
            pass_score = float(subject_info.subject_pass_score) if subject_info.subject_pass_score else 0.0

        # 4. 计算阅卷总量
        total_count = 0
        task_filters = [HumanReadTask.project_id == project_id]
        if subject_id:
            task_filters.append(HumanReadTask.subject_id == subject_id)
        if query.task_type is not None:
            task_filters.append(HumanReadTask.task_type == query.task_type)

        # 获取所有相关的 ques_code
        ques_codes = new_session.query(distinct(HumanReadTask.ques_code)).filter(*task_filters).all()
        ques_codes = [qc[0] for qc in ques_codes if qc[0]]  # 提取元组中的字符串值

        if ques_codes:
            used_counts = new_session.query(func.sum(QuesUsed.used_count).label("total_used")).filter(
                QuesUsed.ques_code.in_(ques_codes)
            ).scalar()
            total_count = int(used_counts) if used_counts else 0.0

        # 5. 计算已阅量
        reviewed_filters = [
            HumanStatisticsSmallGroup.statistics_type == 4,  # 已阅量
            HumanStatisticsSmallGroup.project_id == project_id,
            HumanStatisticsSmallGroup.subject_id == subject_id
        ]
        if query.task_type is not None:
            reviewed_filters.append(HumanStatisticsSmallGroup.task_type == query.task_type)
        if query.round_count is not None:
            reviewed_filters.append(HumanStatisticsSmallGroup.round_count == query.round_count)

        # 添加日期范围条件
        if start_date:
            reviewed_filters.append(HumanStatisticsSmallGroup.date >= start_date)
        if end_date:
            reviewed_filters.append(HumanStatisticsSmallGroup.date <= end_date)

        reviewed_result = new_session.query(
            func.sum(HumanStatisticsSmallGroup.statistics_result_1).label("total_reviewed")
        ).filter(*reviewed_filters).scalar()
        reviewed_count = int(reviewed_result) if reviewed_result else 0.0

        # 6. 计算未阅量
        unreviewed_count = total_count - reviewed_count

        # 7. 计算平均速度（份/时）和预估剩余时长
        try:
            # 查询工作时间
            work_time_filters = [
                HumanStatisticsSmallGroup.statistics_type == 10,  # 工作时间
                HumanStatisticsSmallGroup.project_id == project_id,
                HumanStatisticsSmallGroup.subject_id == subject_id
            ]
            if query.task_type is not None:
                work_time_filters.append(HumanStatisticsSmallGroup.task_type == query.task_type)
            if query.round_count is not None:
                work_time_filters.append(HumanStatisticsSmallGroup.round_count == query.round_count)

            # 添加日期范围条件
            if start_date:
                work_time_filters.append(HumanStatisticsSmallGroup.date >= start_date)
            if end_date:
                work_time_filters.append(HumanStatisticsSmallGroup.date <= end_date)

            # 查询工作时间总和
            work_time_data = new_session.query(
                func.sum(HumanStatisticsSmallGroup.statistics_result_1)
            ).filter(*work_time_filters)

            # 执行查询
            work_time_val = float(work_time_data.scalar()) if work_time_data.scalar() is not None else 0.0

            # 计算平均速度：已阅量 / (工作时间/3600)
            if work_time_val > 0:
                average_speed = reviewed_count / (work_time_val / 3600)
                average_speed = round_half_up(average_speed, 2)
            else:
                average_speed = 0.0

            # 计算剩余时间：未阅量 / 平均速度（如果平均速度为0，则设为0）
            if average_speed > 0:
                remaining_time = unreviewed_count / average_speed
                remaining_time = round_half_up(remaining_time, 2)
            else:
                remaining_time = 0.0

        except Exception as e:
            logger.error(f"计算平均速度和剩余时间失败：{e}")
            # 出现异常时，设置默认值
            average_speed = 0.0
            remaining_time = 0.0

        # 8. 计算参与专家人数
        expert_count = 0
        # 通过 HumanGroupMember 表查询参与该科目阅卷的专家数量
        expert_count = new_session.query(func.count(distinct(HumanGroupMember.user_id))).filter(
            HumanGroupMember.group_id.in_(
                new_session.query(HumanStatisticsSmallGroup.group_id).filter(
                    HumanStatisticsSmallGroup.project_id == project_id,
                    HumanStatisticsSmallGroup.subject_id == subject_id
                ).distinct()
            ),
            HumanGroupMember.member_role == 2  # 专家角色
        ).scalar()
        expert_count = int(expert_count) if expert_count else 0

        # 9. 计算及格率、满分率、零分率
        full_score_rate = 0.0
        zero_score_rate = 0.0
        pass_rate = 0.0
        if total_count > 0:
            # 计算满分率和零分率
            full_score_result = new_session.query(func.count(HumanStudentSubjectGrade.score).label("full_count")).filter(
                HumanStudentSubjectGrade.project_id == project_id,
                HumanStudentSubjectGrade.subject_id == subject_id,
                HumanStudentSubjectGrade.score == total_score
            ).scalar()
            full_score_count = int(full_score_result) if full_score_result else 0
            full_score_rate = round_half_up(full_score_count / total_count * 100,2) if total_count > 0 else 0.0

            # 获取零分数量
            zero_score_result = new_session.query(func.count(HumanStudentSubjectGrade.score).label("zero_count")).filter(
                HumanStudentSubjectGrade.project_id == project_id,
                HumanStudentSubjectGrade.subject_id == subject_id,
                HumanStudentSubjectGrade.score == 0.0
            ).scalar()
            zero_score_count = int(zero_score_result) if zero_score_result else 0
            zero_score_rate = round_half_up(zero_score_count / total_count * 100,2) if total_count > 0 else 0.0

            # 获取及格数量
            pass_result = new_session.query(func.count(HumanStudentSubjectGrade.score).label("pass_count")).filter(
                HumanStudentSubjectGrade.project_id == project_id,
                HumanStudentSubjectGrade.subject_id == subject_id,
                HumanStudentSubjectGrade.score >= pass_score
            ).scalar()
            pass_count = int(pass_result) if pass_result else 0
            pass_rate = round_half_up(pass_count / total_count * 100,2) if total_count > 0 else 0.0

        # 获取所有 project_id 对应的 project_name
        project_ids = list(set([g.project_id for g in groups_data if g.project_id]))
        project_names = {}
        if project_ids:
            projects = new_session.query(Project.project_id, Project.project_name).filter(
                Project.project_id.in_(project_ids)
            ).all()
            project_names = {p.project_id: p.project_name for p in projects}

        # 构造返回数据
        result_item = {
            "project_id": project_id,
            "project_name": project_names.get(project_id, "未知项目"),
            "subject_id": subject_id,
            "subject_name": subject_name,
            "total_count": total_count,
            "reviewed_count": reviewed_count,
            "unreviewed_count": unreviewed_count,
            "average_speed": average_speed,
            "remaining_time": remaining_time,
            "expert_count": expert_count,
            "pass_rate": pass_rate,
            "full_score_rate": full_score_rate,
            "zero_score_rate": zero_score_rate
        }
        results.append(result_item)

    # 按 subject_name 排序
    sorted_results = sorted(results, key=lambda x: x['subject_name'])

    # 计算总数
    project_count = len(set([item['project_id'] for item in results]))
    subject_count = len(set([item['subject_id'] for item in results]))
    expert_total = new_session.query(func.count(distinct(UserInfo.user_id))).select_from(UserRole).outerjoin(UserInfo, UserRole.user_id == UserInfo.user_id
    ).filter(UserRole.role_id == '3', UserInfo.already_login == True  ).scalar()
    expert_total = int(expert_total) if expert_total else 0

    total_count_total = sum([item['total_count'] for item in results])
    reviewed_count_total = sum([item['reviewed_count'] for item in results])
    unreviewed_count_total = sum([item['unreviewed_count'] for item in results])
    # 计算评分进度
    progress = 0.0
    if total_count > 0:
        progress = round_half_up(reviewed_count_total / total_count_total * 100, 2)
    total1 = {
        "project_count": project_count,
        "subject_count":subject_count,
        "expert_total": expert_total,
        "total_count_total": total_count_total
    }
    total2 = {
        "reviewed_count_total": reviewed_count_total,
        "unreviewed_count_total": unreviewed_count_total,
        "progress":progress
    }

    # 构造返回数据
    response_data = {
        "group_data": sorted_results,
        "total1": total1,
        "total2": total2,
    }
    try:
        # 构造图表数据
        x_data = []
        y1_data = []  # 已阅量
        y2_data = []  # 未阅量
        
        for item in sorted_results:
            # x_data中的每个元素是包含项目和科目信息的对象
            x_data.append({
                "project_id": item["project_id"],
                "project_name": item["project_name"],
                "subject_id": item["subject_id"],
                "subject_name": item["subject_name"]
            })
            y1_data.append(item["reviewed_count"])
            y2_data.append(item["unreviewed_count"])
            
        response_data["workload_data"] = {
            "legend": ["已阅量", "未阅量"],
            "x_data": x_data,
            "y1_data": y1_data,
            "y2_data": y2_data
        }
    except Exception as e:
        logger.error(f"构造workload_data失败：{e}")
        # 出现异常时，设置默认值
        response_data["workload_data"] = {
            "legend": ["已阅量", "未阅量"],
            "x_data": [],
            "y1_data": [],
            "y2_data": []
        }

    logger.info(f"阅卷组长页面：获取评卷统计信息完成，共 {len(sorted_results)} 个科目")
    return BaseResponse(data=response_data, msg="阅卷组长页面：获取评卷统计信息成功")
