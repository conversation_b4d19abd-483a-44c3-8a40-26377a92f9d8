# -*- mode: python ; coding: utf-8 -*-

datas = []

a = Analysis(
    ['main.py','winjob.py'],
    pathex=[],
    binaries=[('Redis-x64-3.0.504\\redis-server.exe', 'Redis-x64-3.0.504'),
    ('Redis-x64-3.0.504\\redis-cli.exe',   'Redis-x64-3.0.504'),('定时任务V1.0.exe', '.')],
    datas=datas,
    hiddenimports=['passlib.handlers.bcrypt', 'main'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='卓帆电子化考试阅卷管理系统V1.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['logo.ico'],
)
