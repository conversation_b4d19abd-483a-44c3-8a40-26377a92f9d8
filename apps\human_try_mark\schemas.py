from typing import Optional, List

from pydantic import Field, BaseModel


class CreateTryMarkTask(BaseModel):
    task_name: str = Field(..., description="人工阅卷试评任务名称")
    project_id: str = Field(..., max_length=50, description="项目id")
    subject_id: str = Field(..., max_length=50, description="科目id")
    ques_type_code: str = Field(..., description="阅卷题型简码")
    business_type_name: Optional[str] = Field(None, comment="业务题型名称")
    ques_id: str = Field(..., max_length=50, description="试题id")
    ques_code: str = Field(..., max_length=30, description="试题编号")
    business_id: Optional[str] = Field(None, max_length=50, description="业务题型id")
    mark_score_step: Optional[float] = Field(1, description="评分步长，打分间隔")
    try_mark_ques_num: int = Field(..., description="试评作答数")
    allow_diff_score: float = Field(..., description="允许偏差分数")
    group_id_list: list = Field(..., description="阅卷小组id列表")




class CreateTryMarkTasksReq(BaseModel):
    human_task_list: List[CreateTryMarkTask] = Field(..., description="人工阅卷试评任务列表")
