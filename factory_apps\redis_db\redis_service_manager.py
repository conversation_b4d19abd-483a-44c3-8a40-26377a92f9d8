"""
Redis服务管理模块
负责Redis服务的启动、停止、状态检查等功能
"""
import os
import sys
import time
import socket
import subprocess
import threading
import signal
from pathlib import Path
from typing import Optional, Tuple
import redis
from settings import configs
from settings.logger import logger


class RedisServiceManager:
    """Redis服务管理器"""

    def __init__(self):
        self.redis_process: Optional[subprocess.Popen] = None
        self.redis_host = configs.REDIS_CONFIG["HOST"]
        self.redis_port = configs.REDIS_CONFIG["PORT"]
        self.redis_db = configs.REDIS_CONFIG["DATABASE"]
        self.max_connections = configs.REDIS_CONFIG["MAX_CONNECTIONS"]

        # Redis可执行文件路径
        if "_MEI" in configs.ROOT_PATH:
            # 打包后的路径
            self.redis_exe_path = os.path.join(
                sys._MEIPASS, "Redis-x64-3.0.504", "redis-server.exe")
        else:
            # 开发环境路径
            self.redis_exe_path = os.path.join(
                configs.PROJECT_PATH, "Redis-x64-3.0.504", "redis-server.exe")

        # Redis配置文件路径
        self.redis_conf_path = os.path.join(
            os.path.dirname(self.redis_exe_path), "redis.conf")

    def is_port_available(self, host: str, port: int) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result != 0
        except Exception as e:
            logger.error(f"检查端口可用性时出错: {e}")
            return False

    def is_redis_running(self) -> bool:
        """检查Redis服务是否正在运行"""
        try:
            r = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_db,
                socket_connect_timeout=2,
                socket_timeout=2
            )
            r.ping()
            return True
        except Exception:
            return False

    def kill_existing_redis(self) -> bool:
        """杀死现有的Redis进程"""
        try:
            if os.name == 'nt':  # Windows
                # 查找并杀死Redis进程
                result = subprocess.run(
                    ['tasklist', '/FI', 'IMAGENAME eq redis-server.exe'],
                    capture_output=True,
                    text=True
                )
                if 'redis-server.exe' in result.stdout:
                    subprocess.run(['taskkill', '/F', '/IM', 'redis-server.exe'],
                                   capture_output=True)
                    time.sleep(2)  # 等待进程完全关闭
                    logger.info("已杀死现有的Redis进程")
                    return True
            else:  # Linux/Mac
                subprocess.run(['pkill', '-f', 'redis-server'],
                               capture_output=True)
                time.sleep(2)
                logger.info("已杀死现有的Redis进程")
                return True
        except Exception as e:
            logger.error(f"杀死Redis进程时出错: {e}")
        return False

    def create_redis_config(self) -> str:
        """创建Redis配置文件"""
        config_content = f"""# Redis配置文件 - 自动生成
port {self.redis_port}
bind {self.redis_host}
timeout 0
tcp-keepalive 300
daemonize no
supervised no
pidfile ""
loglevel notice
logfile ""
databases 16
save ""
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./
maxmemory-policy allkeys-lru
appendonly no
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
lua-time-limit 5000
slowlog-log-slower-than 10000
slowlog-max-len 128
latency-monitor-threshold 0
notify-keyspace-events ""
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
aof-rewrite-incremental-fsync yes
"""

        # 确保配置文件目录存在
        os.makedirs(os.path.dirname(self.redis_conf_path), exist_ok=True)

        # 写入配置文件
        with open(self.redis_conf_path, 'w', encoding='utf-8') as f:
            f.write(config_content)

        logger.info(f"Redis配置文件已创建: {self.redis_conf_path}")
        return self.redis_conf_path

    def start_redis_service(self) -> Tuple[bool, str]:
        """启动Redis服务"""
        try:
            logger.info(
                f"开始启动Redis服务，配置信息: Host={self.redis_host}, Port={self.redis_port}")

            # 检查Redis可执行文件是否存在
            if not os.path.exists(self.redis_exe_path):
                error_msg = f"Redis可执行文件不存在: {self.redis_exe_path}"
                logger.error(error_msg)
                logger.info("请确保Redis文件已正确放置在项目目录中")
                return False, error_msg

            logger.info(f"Redis可执行文件检查通过: {self.redis_exe_path}")

            # 检查Redis是否已经在运行
            if self.is_redis_running():
                logger.info("Redis服务已在运行，跳过启动步骤")
                return True, "Redis服务已在运行"

            # 检查端口是否被占用
            if not self.is_port_available(self.redis_host, self.redis_port):
                logger.warning(f"端口 {self.redis_port} 被占用，尝试杀死现有Redis进程")
                kill_success = self.kill_existing_redis()
                if not kill_success:
                    logger.warning("无法杀死现有Redis进程，但将尝试继续启动")

                # 再次检查端口
                time.sleep(2)  # 等待端口释放
                if not self.is_port_available(self.redis_host, self.redis_port):
                    error_msg = f"端口 {self.redis_port} 仍被占用，无法启动Redis"
                    logger.error(error_msg)
                    logger.info("请手动关闭占用该端口的程序，或修改配置文件中的Redis端口")
                    return False, error_msg

            # 创建Redis配置文件
            logger.info("正在创建Redis配置文件...")
            config_path = self.create_redis_config()
            logger.info(f"Redis配置文件创建完成: {config_path}")

            # 启动Redis服务
            logger.info(f"正在启动Redis服务: {self.redis_exe_path}")
            logger.info(f"启动命令: {self.redis_exe_path} {config_path}")

            # 使用配置文件启动Redis
            cmd = [self.redis_exe_path, config_path]

            try:
                self.redis_process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                logger.info(f"Redis进程已启动，PID: {self.redis_process.pid}")
            except Exception as e:
                error_msg = f"启动Redis进程失败: {str(e)}"
                logger.error(error_msg)
                return False, error_msg

            # 等待Redis启动
            max_wait_time = 15  # 最大等待15秒
            wait_interval = 0.5  # 每0.5秒检查一次
            waited_time = 0

            logger.info(f"等待Redis服务启动完成（最大等待时间: {max_wait_time}秒）...")

            while waited_time < max_wait_time:
                if self.is_redis_running():
                    logger.info(f"✓ Redis服务启动成功！")
                    logger.info(f"  - 主机: {self.redis_host}")
                    logger.info(f"  - 端口: {self.redis_port}")
                    logger.info(f"  - 数据库: {self.redis_db}")
                    logger.info(f"  - 进程ID: {self.redis_process.pid}")
                    return True, "Redis服务启动成功"

                # 检查进程是否还在运行
                if self.redis_process.poll() is not None:
                    stdout, stderr = self.redis_process.communicate()
                    stdout_text = stdout.decode(
                        'utf-8', errors='ignore') if stdout else ""
                    stderr_text = stderr.decode(
                        'utf-8', errors='ignore') if stderr else ""

                    error_msg = f"Redis进程意外退出（退出码: {self.redis_process.returncode}）"
                    logger.error(error_msg)
                    if stdout_text:
                        logger.error(f"标准输出: {stdout_text}")
                    if stderr_text:
                        logger.error(f"错误输出: {stderr_text}")

                    return False, error_msg

                time.sleep(wait_interval)
                waited_time += wait_interval

                # 每2秒输出一次等待状态
                if int(waited_time * 2) % 4 == 0:
                    logger.info(
                        f"等待中... ({waited_time:.1f}s/{max_wait_time}s)")

            # 超时处理
            error_msg = f"Redis服务启动超时（{max_wait_time}秒）"
            logger.error(error_msg)

            # 尝试获取进程输出信息
            if self.redis_process and self.redis_process.poll() is None:
                try:
                    stdout, stderr = self.redis_process.communicate(timeout=2)
                    stdout_text = stdout.decode(
                        'utf-8', errors='ignore') if stdout else ""
                    stderr_text = stderr.decode(
                        'utf-8', errors='ignore') if stderr else ""

                    if stdout_text:
                        logger.error(f"Redis标准输出: {stdout_text}")
                    if stderr_text:
                        logger.error(f"Redis错误输出: {stderr_text}")
                except subprocess.TimeoutExpired:
                    logger.warning("无法获取Redis进程输出信息（超时）")
                except Exception as e:
                    logger.warning(f"获取Redis进程输出信息时出错: {e}")

            return False, error_msg

        except Exception as e:
            error_msg = f"启动Redis服务时出错: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def stop_redis_service(self) -> bool:
        """停止Redis服务"""
        try:
            if self.redis_process and self.redis_process.poll() is None:
                # 优雅关闭Redis进程
                if os.name == 'nt':  # Windows
                    self.redis_process.terminate()
                else:  # Linux/Mac
                    self.redis_process.send_signal(signal.SIGTERM)

                # 等待进程结束
                try:
                    self.redis_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # 强制杀死进程
                    self.redis_process.kill()
                    self.redis_process.wait()

                logger.info("Redis服务已停止")
                return True
            else:
                # 尝试杀死系统中的Redis进程
                return self.kill_existing_redis()

        except Exception as e:
            logger.error(f"停止Redis服务时出错: {e}")
            return False

    def get_redis_connection(self) -> Optional[redis.Redis]:
        """获取Redis连接"""
        try:
            r = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_db,
                max_connections=self.max_connections,
                decode_responses=True
            )
            r.ping()  # 测试连接
            return r
        except Exception as e:
            logger.error(f"获取Redis连接失败: {e}")
            return None


# 全局Redis服务管理器实例
redis_manager = RedisServiceManager()


def ensure_redis_running() -> Tuple[bool, str]:
    """确保Redis服务正在运行"""
    return redis_manager.start_redis_service()


def stop_redis() -> bool:
    """停止Redis服务"""
    return redis_manager.stop_redis_service()


def get_redis_connection() -> Optional[redis.Redis]:
    """获取Redis连接"""
    return redis_manager.get_redis_connection()
