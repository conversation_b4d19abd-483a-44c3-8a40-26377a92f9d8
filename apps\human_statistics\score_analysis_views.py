import os
import traceback
import json
from typing import Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy import select, and_, func, text,distinct
from settings import logger
from pydantic import BaseModel
from sqlalchemy.orm import Session

from apps.users.services import get_current_user
from apps.human_statistics.models import HumanStatisticsPerson, HumanStatisticsSmallGroup
from apps.human_statistics.schemas import (
    CalculateReviewedCountReq,
    CalculateAverageScoreReq,
    CalculateMaxScoreReq,
    CalculateMinScoreReq,
    CalculateArbitrationCountReq,
    CalculateWorkloadReq,
    CalculateScoreDistributionReq,
    RecordSpyPaperScoreReq,
    CalculateEffectiveReviewCountReq,
    CalculateAverageSpeedReq,
    GetEffectiveReviewRankingReq,
    CalculateArbitrationPieChartReq,
    CalculateCumulativeAverageScoreWithTimeReq,
    CalculateAverageScoreBatchReq
)
from utils.utils import round_half_up
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from settings import configs
from apps.models.models import UserInfo, ManualReadTask

# 创建APIRouter
score_analysis_router = APIRouter()


@score_analysis_router.post(path="/calculate_reviewed_count", response_model=BaseResponse, summary="评卷员页面：计算已阅量")
async def calculate_reviewed_count_api(
        query: CalculateReviewedCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：计算已阅量")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件，排除未提供的可选参数
    filters = [
        HumanStatisticsPerson.user_id == query.user_id,
        HumanStatisticsPerson.round_count == query.round_count,
        HumanStatisticsPerson.task_type == query.task_type,
        HumanStatisticsPerson.statistics_type == 4,
    ]
    if query.task_id:
        filters.append(HumanStatisticsPerson.task_id == query.task_id)
    if query.subject_id:
        filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
    if query.group_id:
        filters.append(HumanStatisticsPerson.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsPerson.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

    data = new_session.query(func.sum(HumanStatisticsPerson.statistics_result_1)).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsPerson.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsPerson.date <= end_date)

    result = data.scalar()

    # 如果结果为None，则总和为0，并转换为列表格式以符合BaseResponse的data字段类型要求
    total_count = [{"count": int(result)}] if result is not None else []
    logger.info(f"评卷员页面：计算已阅量，总数: {result}")
    return BaseResponse(data=total_count, msg="已阅量")


@score_analysis_router.post(path="/calculate_max_score", response_model=BaseResponse, summary="评卷员页面：计算最高分")
async def calculate_max_score_api(
        query: CalculateMaxScoreReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：计算最高分")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件，排除未提供的可选参数
    filters = [
        HumanStatisticsPerson.user_id == query.user_id,
        HumanStatisticsPerson.round_count == query.round_count,
        HumanStatisticsPerson.task_type == query.task_type,
        HumanStatisticsPerson.statistics_type == 2,
    ]
    if query.task_id:
        filters.append(HumanStatisticsPerson.task_id == query.task_id)
    if query.subject_id:
        filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
    if query.group_id:
        filters.append(HumanStatisticsPerson.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsPerson.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

    data = new_session.query(func.max(HumanStatisticsPerson.statistics_result_1)).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsPerson.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsPerson.date <= end_date)

    result = data.scalar()
    logger.info(f"评卷员页面：计算最高分，结果: {result}")
    return BaseResponse(data=[result], msg="最高分")


@score_analysis_router.post(path="/calculate_min_score", response_model=BaseResponse, summary="评卷员页面：计算最低分")
async def calculate_min_score_api(
        query: CalculateMinScoreReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：计算最低分")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件，排除未提供的可选参数
    filters = [
        HumanStatisticsPerson.user_id == query.user_id,
        HumanStatisticsPerson.round_count == query.round_count,
        HumanStatisticsPerson.task_type == query.task_type,
        HumanStatisticsPerson.statistics_type == 3,
    ]
    if query.task_id:
        filters.append(HumanStatisticsPerson.task_id == query.task_id)
    if query.subject_id:
        filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
    if query.group_id:
        filters.append(HumanStatisticsPerson.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsPerson.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

    data = new_session.query(func.min(HumanStatisticsPerson.statistics_result_1)).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsPerson.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsPerson.date <= end_date)

    result = data.scalar()
    logger.info(f"评卷员页面：计算最低分，结果: {result}")
    return BaseResponse(data=[result], msg="最低分")


@score_analysis_router.post(path="/calculate_arbitration_count", response_model=BaseResponse, summary="评卷员页面：计算仲裁率")
async def calculate_arbitration_count_api(
        query: CalculateArbitrationCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：计算仲裁率")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 检查是否为多天数据
    is_multiple_days = False
    if start_date and end_date:
        # 计算日期差
        date_diff = (end_date - start_date).days + 1
        if date_diff > 1:
            is_multiple_days = True

    # 如果没有传入日期范围参数，则使用多天数据的计算逻辑
    if not query.date_range:
        is_multiple_days = True

    if is_multiple_days:
        # 多天数据：计算仲裁量总和 / 已阅量总和

        # 仲裁量 (statistics_type == 7)
        filters_arbitration = [
            HumanStatisticsPerson.user_id == query.user_id,
            HumanStatisticsPerson.round_count == query.round_count,
            HumanStatisticsPerson.task_type == query.task_type,
            HumanStatisticsPerson.statistics_type == 7,
        ]
        if query.task_id:
            filters_arbitration.append(HumanStatisticsPerson.task_id == query.task_id)
        if query.subject_id:
            filters_arbitration.append(HumanStatisticsPerson.subject_id == query.subject_id)
        if query.group_id:
            filters_arbitration.append(HumanStatisticsPerson.group_id == query.group_id)
        if query.project_id:
            filters_arbitration.append(HumanStatisticsPerson.project_id == query.project_id)
        if query.ques_group_id:
            filters_arbitration.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

        arbitration_data = new_session.query(
            func.sum(HumanStatisticsPerson.statistics_result_1)
        ).filter(*filters_arbitration)

        if start_date:
            arbitration_data = arbitration_data.filter(HumanStatisticsPerson.date >= start_date)
        if end_date:
            arbitration_data = arbitration_data.filter(HumanStatisticsPerson.date <= end_date)

        arbitration_sum = arbitration_data.scalar() or 0.0

        # 已阅量 (statistics_type == 4)
        filters_reviewed = [
            HumanStatisticsPerson.user_id == query.user_id,
            HumanStatisticsPerson.round_count == query.round_count,
            HumanStatisticsPerson.task_type == query.task_type,
            HumanStatisticsPerson.statistics_type == 4,
        ]
        if query.task_id:
            filters_reviewed.append(HumanStatisticsPerson.task_id == query.task_id)
        if query.subject_id:
            filters_reviewed.append(HumanStatisticsPerson.subject_id == query.subject_id)
        if query.group_id:
            filters_reviewed.append(HumanStatisticsPerson.group_id == query.group_id)
        if query.project_id:
            filters_reviewed.append(HumanStatisticsPerson.project_id == query.project_id)
        if query.ques_group_id:
            filters_reviewed.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

        reviewed_data = new_session.query(
            func.sum(HumanStatisticsPerson.statistics_result_1)
        ).filter(*filters_reviewed)

        if start_date:
            reviewed_data = reviewed_data.filter(HumanStatisticsPerson.date >= start_date)
        if end_date:
            reviewed_data = reviewed_data.filter(HumanStatisticsPerson.date <= end_date)

        reviewed_sum = reviewed_data.scalar() or 0.0

        arbitration_rate = round((float(arbitration_sum) / float(reviewed_sum) * 100), 2) if reviewed_sum > 0 else 0.0

    else:
        # 单天数据：直接读取已计算好的仲裁率 (statistics_type == 9)
        filters_single = [
            HumanStatisticsPerson.user_id == query.user_id,
            HumanStatisticsPerson.round_count == query.round_count,
            HumanStatisticsPerson.task_type == query.task_type,
            HumanStatisticsPerson.statistics_type == 9,
        ]
        if query.task_id:
            filters_single.append(HumanStatisticsPerson.task_id == query.task_id)
        if query.subject_id:
            filters_single.append(HumanStatisticsPerson.subject_id == query.subject_id)
        if query.group_id:
            filters_single.append(HumanStatisticsPerson.group_id == query.group_id)
        if query.project_id:
            filters_single.append(HumanStatisticsPerson.project_id == query.project_id)
        if query.ques_group_id:
            filters_single.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

        data = new_session.query(
            func.sum(HumanStatisticsPerson.statistics_result_1)
        ).filter(*filters_single)

        if start_date:
            data = data.filter(HumanStatisticsPerson.date >= start_date)
        if end_date:
            data = data.filter(HumanStatisticsPerson.date <= end_date)

        result = data.scalar()
        arbitration_rate = float(result) if result is not None else 0.0

    logger.info(f"评卷员页面：计算仲裁率，结果: {arbitration_rate}%")
    return BaseResponse(data=[arbitration_rate], msg="仲裁率")

@score_analysis_router.post(path="/calculate_workload", response_model=BaseResponse, summary="评卷员页面：计算工作量")
async def calculate_workload_api(
        query: CalculateWorkloadReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']}评卷员页面： 计算工作量监控柱状图")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件，排除未提供的可选参数
    filters_workload = [
        HumanStatisticsPerson.round_count == query.round_count,
        HumanStatisticsPerson.task_type == query.task_type,
        HumanStatisticsPerson.statistics_type == 11,
    ]
    if query.user_id:
        filters_workload.append(HumanStatisticsPerson.user_id == query.user_id)
    if query.task_id:
        filters_workload.append(HumanStatisticsPerson.task_id == query.task_id)
    if query.subject_id:
        filters_workload.append(HumanStatisticsPerson.subject_id == query.subject_id)
    if query.group_id:
        filters_workload.append(HumanStatisticsPerson.group_id == query.group_id)
    if query.project_id:
        filters_workload.append(HumanStatisticsPerson.project_id == query.project_id)
    if query.ques_group_id:
        filters_workload.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

    data = new_session.query(
        HumanStatisticsPerson.date,
        HumanStatisticsPerson.statistics_result_1,
        HumanStatisticsPerson.statistics_result_2
    ).filter(*filters_workload).order_by(HumanStatisticsPerson.date)

    # 添加日期范围条件
    if start_date and end_date:
        # 如果同时有start_date和end_date，使用BETWEEN
        data = data.filter(HumanStatisticsPerson.date.between(start_date, end_date))
    elif start_date:
        # 只有start_date
        data = data.filter(HumanStatisticsPerson.date >= start_date)
    elif end_date:
        # 只有end_date
        data = data.filter(HumanStatisticsPerson.date <= end_date)

    results = data.all()

    # 初始化workload_data变量，避免UnboundLocalError
    workload_data = []

    # 检查是否传入了mark_time参数
    if query.mark_time:
        # 尝试解析mark_time日期
        try:
            mark_date = datetime.strptime(query.mark_time, '%Y-%m-%d').date()
            # 直接查询指定日期的数据
            filters_workload.append(HumanStatisticsPerson.date == mark_date)

            specific_data_list = new_session.query(
                HumanStatisticsPerson.date,
                HumanStatisticsPerson.statistics_result_2
            ).filter(*filters_workload).all()

            hourly_sums = {}
            for specific_data in specific_data_list:
                if specific_data and specific_data.statistics_result_2:
                    try:
                        raw_data = json.loads(specific_data.statistics_result_2)
                        hourly_data = raw_data.get("hourly_distribution", {})
                        for hour, count in hourly_data.items():
                            hourly_sums[hour] = hourly_sums.get(hour, 0) + count
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.error(f"解析工作量数据失败: {e}, 数据: {specific_data.statistics_result_2}")
            
            if hourly_sums:
                workload_data = {
                    "legend": ["工作量监控"],
                    "x_data": list(hourly_sums.keys()),
                    "y_data": list(hourly_sums.values())
                }
                logger.info(f"找到匹配时间的数据: {workload_data}")
            else:
                logger.info("未找到统计数据")
        except ValueError:
            workload_data = None
    else:
        # 如果没有mark_time，返回每一天的statistics_result_1中的具体数值，不进行求和
        workload_data = []
        for result in results:
            if result.statistics_result_1:
                try:
                    workload_data.append({
                        "date": result.date.strftime('%Y-%m-%d') if result.date else None,
                        "count": int(result.statistics_result_1)
                    })
                except Exception as e:
                    logger.error(f"解析工作量数据失败: {e}, 数据: {result.statistics_result_1}")

    logger.info(f"评卷员页面：计算工作量监控柱状图，结果: {workload_data}")
    return BaseResponse(data=workload_data, msg="获取评卷员界面：工作量监控柱状图")


@score_analysis_router.post(path="/calculate_score_distribution", response_model=BaseResponse, summary="评卷员页面：计算分数分布")
async def calculate_score_distribution_api(
        query: CalculateScoreDistributionReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：计算分数分布柱状图")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件，排除未提供的可选参数
    filters = [
        HumanStatisticsPerson.user_id == query.user_id,
        HumanStatisticsPerson.round_count == query.round_count,
        HumanStatisticsPerson.task_type == query.task_type,
        HumanStatisticsPerson.statistics_type == 13,
    ]
    if query.task_id:
        filters.append(HumanStatisticsPerson.task_id == query.task_id)
    if query.subject_id:
        filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
    if query.group_id:
        filters.append(HumanStatisticsPerson.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsPerson.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

    data = new_session.query(
        HumanStatisticsPerson.statistics_result_1,
        HumanStatisticsPerson.statistics_result_2
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsPerson.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsPerson.date <= end_date)

    results = data.all()

    # 判断是否是多天数据
    is_multiple_days = False
    if start_date and end_date:
        # 计算日期差
        date_diff = (end_date - start_date).days + 1
        if date_diff > 1:
            is_multiple_days = True

    # 如果没有传入日期范围参数，则使用多天数据的计算逻辑
    if not query.date_range:
        is_multiple_days = True

    # 格式化数据
    distribution_data = []

    if is_multiple_days:
        # 多天数据：对相同分数范围的值进行求和汇总
        distribution_sum = {}

        for result in results:
            # 解析JSON数据
            distribution_dict = {}
            if result.statistics_result_2:
                try:
                    distribution_dict = json.loads(result.statistics_result_2)
                except json.JSONDecodeError:
                    logger.warning(f"无法解析JSON数据: {result.statistics_result_2}")
                    distribution_dict = {}

            # 将字典转换为列表格式并进行求和汇总
            for score_range, count in distribution_dict.items():
                count_value = float(count) if count is not None else 0
                if score_range in distribution_sum:
                    distribution_sum[score_range] += count_value
                else:
                    distribution_sum[score_range] = count_value

        # 将汇总后的字典转换为列表格式
        for score_range, total_count in distribution_sum.items():
            distribution_item = {
                "score_range": score_range,
                "count": total_count
            }
            distribution_data.append(distribution_item)
    else:
        # 单天数据：保持原有逻辑
        for result in results:
            # 解析JSON数据
            distribution_dict = {}
            if result.statistics_result_2:
                try:
                    distribution_dict = json.loads(result.statistics_result_2)
                except json.JSONDecodeError:
                    logger.warning(f"无法解析JSON数据: {result.statistics_result_2}")
                    distribution_dict = {}

            # 将字典转换为列表格式
            for score_range, count in distribution_dict.items():
                distribution_item = {
                    "score_range": score_range,
                    "count": int(count) if count is not None else 0
                }
                distribution_data.append(distribution_item)

    # 构建返回数据
    data = {
        "legend": ["分数分布"],
        "x_data": [item["score_range"] for item in distribution_data] if distribution_data and isinstance(distribution_data, list) else [],
        "y_data": [item["count"] for item in distribution_data] if distribution_data and isinstance(distribution_data, list) else []
    }

    logger.info(f"评卷员页面：计算分数分布柱状图，结果数量: {len(distribution_data)}")
    return BaseResponse(data=data, msg="获取评卷员界面：分数分布柱状图")


@score_analysis_router.post(path="/record_spy_paper_score", response_model=BaseResponse, summary="评卷员页面：记录间谍卷评分")
async def record_spy_paper_score_api(
        query: RecordSpyPaperScoreReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：评阅稳定性趋势图")

    # 动态构建过滤条件，排除未提供的可选参数
    filters = [
        HumanStatisticsPerson.user_id == query.user_id,
        HumanStatisticsPerson.round_count == query.round_count,
        HumanStatisticsPerson.task_type == query.task_type,
        HumanStatisticsPerson.statistics_type == 12,
    ]
    if query.task_id:
        filters.append(HumanStatisticsPerson.task_id == query.task_id)
    if query.subject_id:
        filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
    if query.group_id:
        filters.append(HumanStatisticsPerson.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsPerson.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

    data = new_session.query(
        HumanStatisticsPerson.statistics_result_2
    ).filter(*filters).all()

    # 提取统计结果2的值
    result_data = [item.statistics_result_2 for item in data if item.statistics_result_2 is not None]

    # 创建轮次、当前分数、上次分数和差异数据
    round = []
    current_score = []
    last_score = []
    difference = []

    # 处理每个专家的数据
    for item in result_data:
        try:
            # 尝试解析JSON数据
            if isinstance(item, str):
                data_list = json.loads(item)
            else:
                data_list = item

            # 确保data_list是一个列表
            if isinstance(data_list, list):
                # 遍历列表中的每个数据项
                for data_dict in data_list:
                    # 提取数据
                    expert_round = data_dict.get("round", "")
                    expert_current_score = data_dict.get("current_score", 0)
                    expert_last_score = data_dict.get("last_score", 0)
                    expert_difference = data_dict.get("difference", 0)

                    # 添加到列表
                    round.append(expert_round)
                    current_score.append(expert_current_score)
                    last_score.append(expert_last_score)
                    difference.append(expert_difference)
        except Exception as e:
            logger.error(f"处理专家数据时出错: {e}, 数据: {item}")
            continue

    # 构建返回数据
    data = {
        "legend": ["当前分数", "上次分数", "差异数据"],
        "x_data": round,
        "y1_data": current_score,
        "y2_data": last_score,
        "y3_data": difference
    }

    logger.info("获取评卷员界面：评阅稳定性趋势图")
    return BaseResponse(data=data, msg="获取评卷员界面：评阅稳定性趋势图")


@score_analysis_router.post(path="/calculate_invalid_reviewed_count", response_model=BaseResponse, summary="评卷员页面：计算无效评卷量")
async def calculate_invalid_reviewed_count_api(
        query: CalculateReviewedCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：计算无效评卷量")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件，排除未提供的可选参数
    filters = [
        HumanStatisticsPerson.user_id == query.user_id,
        HumanStatisticsPerson.round_count == query.round_count,
        HumanStatisticsPerson.task_type == query.task_type,
        HumanStatisticsPerson.statistics_type == 8,
    ]
    if query.task_id:
        filters.append(HumanStatisticsPerson.task_id == query.task_id)
    if query.subject_id:
        filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
    if query.group_id:
        filters.append(HumanStatisticsPerson.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsPerson.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

    data = new_session.query(
        func.sum(HumanStatisticsPerson.statistics_result_1)
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsPerson.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsPerson.date <= end_date)

    result = data.scalar()

    # 如果结果为None，则总和为0，并转换为列表格式以符合BaseResponse的data字段类型要求
    total_count = [{"count": int(result)}] if result is not None else []
    logger.info(f"评卷员页面：计算无效评卷量，总数: {result}")
    return BaseResponse(data=total_count, msg="获取评卷员页面：无效评卷量")


@score_analysis_router.post(path="/calculate_effective_review_count", response_model=BaseResponse, summary="评卷员页面：计算有效评卷量")
async def calculate_effective_review_count_api(
        query: CalculateEffectiveReviewCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：计算有效评卷量")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件，排除未提供的可选参数
    # 有效评卷量 (type=5)
    filters5 = [
        HumanStatisticsPerson.user_id == query.user_id,
        HumanStatisticsPerson.round_count == query.round_count,
        HumanStatisticsPerson.task_type == query.task_type,
        HumanStatisticsPerson.statistics_type == 5,
    ]
    if query.task_id:
        filters5.append(HumanStatisticsPerson.task_id == query.task_id)
    if query.subject_id:
        filters5.append(HumanStatisticsPerson.subject_id == query.subject_id)
    if query.group_id:
        filters5.append(HumanStatisticsPerson.group_id == query.group_id)
    if query.project_id:
        filters5.append(HumanStatisticsPerson.project_id == query.project_id)
    if query.ques_group_id:
        filters5.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

    data5 = new_session.query(func.sum(HumanStatisticsPerson.statistics_result_1)).filter(*filters5)

    # 仲裁成功量 (type=6)
    filters6 = [
        HumanStatisticsPerson.user_id == query.user_id,
        HumanStatisticsPerson.round_count == query.round_count,
        HumanStatisticsPerson.task_type == query.task_type,
        HumanStatisticsPerson.statistics_type == 6,
    ]
    if query.task_id:
        filters6.append(HumanStatisticsPerson.task_id == query.task_id)
    if query.subject_id:
        filters6.append(HumanStatisticsPerson.subject_id == query.subject_id)
    if query.group_id:
        filters6.append(HumanStatisticsPerson.group_id == query.group_id)
    if query.project_id:
        filters6.append(HumanStatisticsPerson.project_id == query.project_id)
    if query.ques_group_id:
        filters6.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

    data6 = new_session.query(func.sum(HumanStatisticsPerson.statistics_result_1)).filter(*filters6)

    # 添加日期范围条件
    if start_date:
        data5 = data5.filter(HumanStatisticsPerson.date >= start_date)
        data6 = data6.filter(HumanStatisticsPerson.date >= start_date)
    if end_date:
        data5 = data5.filter(HumanStatisticsPerson.date <= end_date)
        data6 = data6.filter(HumanStatisticsPerson.date <= end_date)

    result5 = data5.scalar() or 0
    result6 = data6.scalar() or 0
    total = result5 + result6
    total_count = [{"count": int(total)}] if total is not None else []
    logger.info(f"评卷员页面：计算有效评卷量，总数: {total}")
    return BaseResponse(data=total_count, msg="获取评卷员页面：有效评卷量")


@score_analysis_router.post(path="/calculate_average_speed", response_model=BaseResponse, summary="评卷员页面：计算平均速度")
async def calculate_average_speed_api(
        query: CalculateAverageSpeedReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：计算平均速度")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 判断是单天还是多天查询
    is_single_day = False
    if start_date and end_date:
        # 计算日期差
        date_diff = (end_date - start_date).days + 1
        if date_diff == 1:
            is_single_day = True

    # 如果没有传入日期范围参数，则使用多天数据的计算逻辑
    if not query.date_range:
        is_single_day = False

    try:
        if is_single_day:
            # 单天数据：动态构建过滤条件
            filters_review = [
                HumanStatisticsPerson.user_id == query.user_id,
                HumanStatisticsPerson.round_count == query.round_count,
                HumanStatisticsPerson.task_type == query.task_type,
                HumanStatisticsPerson.statistics_type == 4,
                HumanStatisticsPerson.date == start_date
            ]
            filters_work = [
                HumanStatisticsPerson.user_id == query.user_id,
                HumanStatisticsPerson.statistics_type == 10,
                HumanStatisticsPerson.date == start_date
            ]

            # 添加可选参数过滤条件
            for filters in [filters_review, filters_work]:
                if query.task_id:
                    filters.append(HumanStatisticsPerson.task_id == query.task_id)
                if query.subject_id:
                    filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
                if query.group_id:
                    filters.append(HumanStatisticsPerson.group_id == query.group_id)
                if query.project_id:
                    filters.append(HumanStatisticsPerson.project_id == query.project_id)
                if query.ques_group_id:
                    filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

            # 查询已阅量 (statistics_type == 4)
            reviewed_count = new_session.query(
                HumanStatisticsPerson.statistics_result_1
            ).filter(*filters_review).first()

            # 查询工作时间 (statistics_type == 10)
            work_time = new_session.query(
                HumanStatisticsPerson.statistics_result_1
            ).filter(*filters_work).first()

            reviewed_count_val = float(reviewed_count.statistics_result_1) if reviewed_count and reviewed_count.statistics_result_1 is not None else 0.0
            work_time_val = float(work_time.statistics_result_1) if work_time and work_time.statistics_result_1 is not None else 0.0
        else:
            # 多天数据：查询日期范围内所有天的数据，分别累加已阅量和工作时间
            # 查询已阅量总和 (statistics_type == 4)
            # 动态构建有效评卷量查询 (statistics_type == 4)
            reviewed_filters = [
                HumanStatisticsPerson.user_id == query.user_id,
                HumanStatisticsPerson.round_count == query.round_count,
                HumanStatisticsPerson.task_type == query.task_type,
                HumanStatisticsPerson.statistics_type == 4
            ]
            if query.task_id:
                reviewed_filters.append(HumanStatisticsPerson.task_id == query.task_id)
            if query.subject_id:
                reviewed_filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
            if query.group_id:
                reviewed_filters.append(HumanStatisticsPerson.group_id == query.group_id)
            if query.project_id:
                reviewed_filters.append(HumanStatisticsPerson.project_id == query.project_id)
            if query.ques_group_id:
                reviewed_filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

            reviewed_data = new_session.query(
                func.sum(HumanStatisticsPerson.statistics_result_1)
            ).filter(*reviewed_filters)

            # 动态构建工作时间查询 (statistics_type == 10)
            work_time_filters = [
                HumanStatisticsPerson.user_id == query.user_id,
                HumanStatisticsPerson.round_count == query.round_count,
                HumanStatisticsPerson.task_type == query.task_type,
                HumanStatisticsPerson.statistics_type == 10
            ]
            if query.task_id:
                work_time_filters.append(HumanStatisticsPerson.task_id == query.task_id)
            if query.subject_id:
                work_time_filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
            if query.group_id:
                work_time_filters.append(HumanStatisticsPerson.group_id == query.group_id)
            if query.project_id:
                work_time_filters.append(HumanStatisticsPerson.project_id == query.project_id)
            if query.ques_group_id:
                work_time_filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

            work_time_data = new_session.query(
                func.sum(HumanStatisticsPerson.statistics_result_1)
            ).filter(*work_time_filters)

            # 添加日期范围条件
            if start_date:
                reviewed_data = reviewed_data.filter(HumanStatisticsPerson.date >= start_date)
                work_time_data = work_time_data.filter(HumanStatisticsPerson.date >= start_date)
            if end_date:
                reviewed_data = reviewed_data.filter(HumanStatisticsPerson.date <= end_date)
                work_time_data = work_time_data.filter(HumanStatisticsPerson.date <= end_date)

            reviewed_count_val = float(reviewed_data.scalar()) if reviewed_data and reviewed_data.scalar() is not None else 0.0
            work_time_val = float(work_time_data.scalar()) if work_time_data and work_time_data.scalar() is not None else 0.0

        # 计算平均速度：份/小时 = 已阅量 ÷ (工作时间 ÷ 3600)
        if work_time_val > 0:
            average_speed = reviewed_count_val / (work_time_val / 3600)
            result = round_half_up(average_speed, 2)
        else:
            # 如果工作时间为0，则返回0
            result = 0.0

        # 添加调试信息
        logger.info(f"评卷员页面：计算平均速度，结果: {result} 份/小时")
        return BaseResponse(data=[result], msg="平均速度（份/小时）")

    except Exception as e:
        logger.error(f"计算平均速度失败：{e}")
        traceback.print_exc()
        return BaseResponse(data=[], msg="计算平均速度失败")


@score_analysis_router.post(path="/get_effective_review_ranking", response_model=BaseResponse,
                            summary="评卷员监控、小组监控：获取评卷员有效评卷量排名")
async def get_effective_review_ranking_api(
        query: GetEffectiveReviewRankingReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员监控、小组监控：获取评卷员有效评卷量排名")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    try:
        # 动态构建查询条件
        filters = [
            HumanStatisticsPerson.round_count == query.round_count,
            HumanStatisticsPerson.task_type == query.task_type,
            HumanStatisticsPerson.statistics_type == 5  # 5表示有效评卷量统计类型
        ]
        if query.task_id:
            filters.append(HumanStatisticsPerson.task_id == query.task_id)
        if query.subject_id:
            filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
        if query.group_id:
            filters.append(HumanStatisticsPerson.group_id == query.group_id)
        if query.project_id:
            filters.append(HumanStatisticsPerson.project_id == query.project_id)
        if query.ques_group_id:
            filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

        # 查询有效评卷量统计数据
        stats_data = new_session.query(
            HumanStatisticsPerson.user_id,
            func.sum(HumanStatisticsPerson.statistics_result_1).label('total_count')
        ).filter(*filters)

        # 添加日期范围条件
        if start_date:
            stats_data = stats_data.filter(HumanStatisticsPerson.date >= start_date)
        if end_date:
            stats_data = stats_data.filter(HumanStatisticsPerson.date <= end_date)

        # 按有效评卷量降序排序
        stats_data = stats_data.group_by(HumanStatisticsPerson.user_id).order_by(
            func.sum(HumanStatisticsPerson.statistics_result_1).desc()
        )

        results = stats_data.all()

        # 获取用户信息
        user_ids = [result.user_id for result in results]
        user_info_map = {}
        if user_ids:
            # 构建基础查询
            user_info_query = new_session.query(
                UserInfo.user_id,
                UserInfo.name,
                UserInfo.id_card
            ).filter(UserInfo.user_id.in_(user_ids))

            # 如果提供了用户名，添加模糊查询条件
            if query.name:
                user_info_query = user_info_query.filter(UserInfo.name.like(f"%{query.name}%"))

            user_info_results = user_info_query.all()
            for user_info in user_info_results:
                user_info_map[user_info.user_id] = {
                    "name": user_info.name,
                    "id_card": user_info.id_card
                }

        # 计算每个用户的平均速度
        user_average_speed_map = {}
        for result in results:
            user_id = result.user_id

            # 判断是单天还是多天查询
            is_single_day = False
            if start_date and end_date:
                # 计算日期差
                date_diff = (end_date - start_date).days + 1
                if date_diff == 1:
                    is_single_day = True

            # 如果没有传入日期范围参数，则使用多天数据的计算逻辑
            if not query.date_range:
                is_single_day = False

            try:
                if is_single_day:
                    # 单天数据：直接查询指定日期的记录
                    # 动态构建已阅量查询条件
                    reviewed_filters = [
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.round_count == query.round_count,
                        HumanStatisticsPerson.task_type == query.task_type,
                        HumanStatisticsPerson.statistics_type == 4,
                        HumanStatisticsPerson.date == start_date
                    ]
                    if query.task_id:
                        reviewed_filters.append(HumanStatisticsPerson.task_id == query.task_id)
                    if query.subject_id:
                        reviewed_filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
                    if query.group_id:
                        reviewed_filters.append(HumanStatisticsPerson.group_id == query.group_id)
                    if query.project_id:
                        reviewed_filters.append(HumanStatisticsPerson.project_id == query.project_id)
                    if query.ques_group_id:
                        reviewed_filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

                    # 查询已阅量 (statistics_type == 4)
                    reviewed_count = new_session.query(
                        HumanStatisticsPerson.statistics_result_1
                    ).filter(*reviewed_filters).first()

                    # 动态构建工作时间查询条件
                    work_time_filters = [
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.round_count == query.round_count,
                        HumanStatisticsPerson.task_type == query.task_type,
                        HumanStatisticsPerson.statistics_type == 10,
                        HumanStatisticsPerson.date == start_date
                    ]
                    if query.task_id:
                        work_time_filters.append(HumanStatisticsPerson.task_id == query.task_id)
                    if query.subject_id:
                        work_time_filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
                    if query.group_id:
                        work_time_filters.append(HumanStatisticsPerson.group_id == query.group_id)
                    if query.project_id:
                        work_time_filters.append(HumanStatisticsPerson.project_id == query.project_id)
                    if query.ques_group_id:
                        work_time_filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

                    # 查询工作时间 (statistics_type == 10)
                    work_time = new_session.query(
                        HumanStatisticsPerson.statistics_result_1
                    ).filter(*work_time_filters).first()

                    reviewed_count_val = float(
                        reviewed_count.statistics_result_1) if reviewed_count and reviewed_count.statistics_result_1 is not None else 0.0
                    work_time_val = float(
                        work_time.statistics_result_1) if work_time and work_time.statistics_result_1 is not None else 0.0
                else:
                    # 多天数据：查询日期范围内所有天的数据，分别累加已阅量和工作时间
                    # 动态构建已阅量查询条件
                    reviewed_data_filters = [
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.round_count == query.round_count,
                        HumanStatisticsPerson.task_type == query.task_type,
                        HumanStatisticsPerson.statistics_type == 4
                    ]
                    if query.task_id:
                        reviewed_data_filters.append(HumanStatisticsPerson.task_id == query.task_id)
                    if query.subject_id:
                        reviewed_data_filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
                    if query.group_id:
                        reviewed_data_filters.append(HumanStatisticsPerson.group_id == query.group_id)
                    if query.project_id:
                        reviewed_data_filters.append(HumanStatisticsPerson.project_id == query.project_id)
                    if query.ques_group_id:
                        reviewed_data_filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

                    # 查询已阅量总和 (statistics_type == 4)
                    reviewed_data = new_session.query(
                        func.sum(HumanStatisticsPerson.statistics_result_1)
                    ).filter(*reviewed_data_filters)

                    # 动态构建工作时间查询条件
                    work_time_data_filters = [
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.round_count == query.round_count,
                        HumanStatisticsPerson.task_type == query.task_type,
                        HumanStatisticsPerson.statistics_type == 10
                    ]
                    if query.task_id:
                        work_time_data_filters.append(HumanStatisticsPerson.task_id == query.task_id)
                    if query.subject_id:
                        work_time_data_filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
                    if query.group_id:
                        work_time_data_filters.append(HumanStatisticsPerson.group_id == query.group_id)
                    if query.project_id:
                        work_time_data_filters.append(HumanStatisticsPerson.project_id == query.project_id)
                    if query.ques_group_id:
                        work_time_data_filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

                    # 查询工作时间总和 (statistics_type == 10)
                    work_time_data = new_session.query(
                        func.sum(HumanStatisticsPerson.statistics_result_1)
                    ).filter(*work_time_data_filters)

                    # 添加日期范围条件
                    if start_date:
                        reviewed_data = reviewed_data.filter(HumanStatisticsPerson.date >= start_date)
                        work_time_data = work_time_data.filter(HumanStatisticsPerson.date >= start_date)
                    if end_date:
                        reviewed_data = reviewed_data.filter(HumanStatisticsPerson.date <= end_date)
                        work_time_data = work_time_data.filter(HumanStatisticsPerson.date <= end_date)

                    reviewed_count_val = float(
                        reviewed_data.scalar()) if reviewed_data and reviewed_data.scalar() is not None else 0.0
                    work_time_val = float(
                        work_time_data.scalar()) if work_time_data and work_time_data.scalar() is not None else 0.0

                # 计算平均速度：份/小时 = 已阅量 ÷ (工作时间 ÷ 3600)
                if work_time_val > 0:
                    average_speed = reviewed_count_val / (work_time_val / 3600)
                    result_speed = round_half_up(average_speed, 2)
                else:
                    # 如果工作时间为0，则返回0
                    result_speed = 0.0

                user_average_speed_map[user_id] = result_speed

            except Exception as e:
                logger.error(f"计算用户 {user_id} 平均速度失败：{e}")
                user_average_speed_map[user_id] = 0.0

        # 构建返回数据
        ranking_data = []
        for idx, result in enumerate(results, 1):
            user_info = user_info_map.get(result.user_id, {})
            # 获取身份证后六位
            id_card_last_six = user_info.get("id_card", "")[-6:] if user_info.get("id_card") else ""

            # 获取平均速度
            average_speed = user_average_speed_map.get(result.user_id, 0.0)

            ranking_item = {
                "rank": idx,
                "user_id": result.user_id,
                "name": user_info.get("name", ""),
                "id_card_last_six": id_card_last_six,
                "effective_count": int(result.total_count) if result.total_count else 0,
                "average_speed": average_speed
            }
            ranking_data.append(ranking_item)

        logger.info(f"评卷员监控、小组监控：获取评卷员有效评卷量排名，共 {len(ranking_data)} 条记录")
        return BaseResponse(data=ranking_data, msg="评卷员监控、小组监控：获取评卷员有效评卷量排名")

    except Exception as e:
        logger.error(f"获取评卷员监控、小组监控：获取评卷员有效评卷量排名排名失败：{e}")
        traceback.print_exc()
        return BaseResponse(data=[], msg="获取评卷员监控、小组监控：获取评卷员有效评卷量排名失败")


@score_analysis_router.post(path="/calculate_arbitration_pie_chart", response_model=BaseResponse,
                            summary="评卷员页面：计算仲裁饼图")
async def calculate_arbitration_pie_chart_api(
        query: CalculateArbitrationPieChartReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：计算仲裁饼图")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    try:
        # 仲裁量 (statistics_type == 7)
        filters_arbitration = [
            HumanStatisticsPerson.user_id == query.user_id,
            HumanStatisticsPerson.round_count == query.round_count,
            HumanStatisticsPerson.task_type == query.task_type,
            HumanStatisticsPerson.statistics_type == 7,
        ]
        if query.task_id:
            filters_arbitration.append(HumanStatisticsPerson.task_id == query.task_id)
        if query.subject_id:
            filters_arbitration.append(HumanStatisticsPerson.subject_id == query.subject_id)
        if query.group_id:
            filters_arbitration.append(HumanStatisticsPerson.group_id == query.group_id)
        if query.project_id:
            filters_arbitration.append(HumanStatisticsPerson.project_id == query.project_id)
        if query.ques_group_id:
            filters_arbitration.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

        arbitration_data = new_session.query(
            func.sum(HumanStatisticsPerson.statistics_result_1)
        ).filter(*filters_arbitration)

        # 有效评卷量 (statistics_type == 5)
        filters_effective = [
            HumanStatisticsPerson.user_id == query.user_id,
            HumanStatisticsPerson.round_count == query.round_count,
            HumanStatisticsPerson.task_type == query.task_type,
            HumanStatisticsPerson.statistics_type == 5,
        ]
        if query.task_id:
            filters_effective.append(HumanStatisticsPerson.task_id == query.task_id)
        if query.subject_id:
            filters_effective.append(HumanStatisticsPerson.subject_id == query.subject_id)
        if query.group_id:
            filters_effective.append(HumanStatisticsPerson.group_id == query.group_id)
        if query.project_id:
            filters_effective.append(HumanStatisticsPerson.project_id == query.project_id)
        if query.ques_group_id:
            filters_effective.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

        effective_data = new_session.query(
            func.sum(HumanStatisticsPerson.statistics_result_1)
        ).filter(*filters_effective)

        # 添加日期范围条件
        if start_date:
            arbitration_data = arbitration_data.filter(HumanStatisticsPerson.date >= start_date)
            effective_data = effective_data.filter(HumanStatisticsPerson.date >= start_date)
        if end_date:
            arbitration_data = arbitration_data.filter(HumanStatisticsPerson.date <= end_date)
            effective_data = effective_data.filter(HumanStatisticsPerson.date <= end_date)

        arbitration_sum = int(arbitration_data.scalar()) if arbitration_data.scalar() is not None else 0.0
        effective_sum = int(effective_data.scalar()) if effective_data.scalar() is not None else 0.0

        data = {
            "quality_data": {
                "x_data": ["仲裁", "评阅通过"],
                "y_data": [
                    {"value": arbitration_sum, "name": "仲裁"},
                    {"value": effective_sum, "name": "评阅通过"}
                ]
            }
        }

        logger.info(f"评卷员页面：计算仲裁饼图，仲裁: {arbitration_sum}, 评阅通过: {effective_sum}")
        return BaseResponse(data=data, msg="仲裁饼图数据")
    except Exception as e:
        logger.error(f"计算仲裁饼图失败：{e}")
        traceback.print_exc()
        return BaseResponse(data={}, msg="计算仲裁饼图失败")


@score_analysis_router.post(path="/calculate_cumulative_average_score_with_time", response_model=BaseResponse,
                            summary="评卷员页面：计算累计平均分（带时间）")
async def calculate_cumulative_average_score_with_time_api(
        query: CalculateCumulativeAverageScoreWithTimeReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：计算累计平均分（带时间）")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
            logger.info(f"日期范围解析成功: start_date={start_date}, end_date={end_date}")
        except ValueError as e:
            logger.warning(f"日期范围解析失败: {e}, date_range={query.date_range}")
            pass

    try:
        # 动态构建HumanStatisticsPerson查询条件
        logger.info("开始查询HumanStatisticsPerson表数据")
        person_filters = [
            HumanStatisticsPerson.user_id == query.user_id,
            HumanStatisticsPerson.round_count == query.round_count,
            HumanStatisticsPerson.task_type == query.task_type,
            HumanStatisticsPerson.statistics_type == 14
        ]
        if query.task_id:
            person_filters.append(HumanStatisticsPerson.task_id == query.task_id)
        if query.subject_id:
            person_filters.append(HumanStatisticsPerson.subject_id == query.subject_id)
        if query.group_id:
            person_filters.append(HumanStatisticsPerson.group_id == query.group_id)
        if query.project_id:
            person_filters.append(HumanStatisticsPerson.project_id == query.project_id)
        if query.ques_group_id:
            person_filters.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

        person_data = new_session.query(
            HumanStatisticsPerson.date,
            HumanStatisticsPerson.statistics_result_2
        ).filter(*person_filters)

        # 动态构建HumanStatisticsSmallGroup查询条件
        logger.info("开始查询HumanStatisticsSmallGroup表数据")
        group_filters = [
            HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
            HumanStatisticsSmallGroup.statistics_type == 14
        ]
        if query.task_id:
            group_filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
        if query.subject_id:
            group_filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
        if query.group_id:
            group_filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
        if query.project_id:
            group_filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
        if query.ques_group_id:
            group_filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

        group_data = new_session.query(
            HumanStatisticsSmallGroup.date,
            HumanStatisticsSmallGroup.statistics_result_2
        ).filter(*group_filters)

        # 添加日期范围条件
        if start_date:
            person_data = person_data.filter(HumanStatisticsPerson.date >= start_date)
            group_data = group_data.filter(HumanStatisticsSmallGroup.date >= start_date)
        if end_date:
            person_data = person_data.filter(HumanStatisticsPerson.date <= end_date)
            group_data = group_data.filter(HumanStatisticsSmallGroup.date <= end_date)

        # 获取查询结果 - 修复查询逻辑
        person_results = person_data.all()
        group_results = group_data.all()

        logger.info(f"个人查询结果数量: {len(person_results)}")
        logger.info(f"小组查询结果数量: {len(group_results)}")

        # 解析数据并合并 - 取两个表的date和time的并集
        merged_data = {}
        all_datetime_keys = set()

        # 收集所有可能的datetime键
        # 处理个人数据
        logger.info("开始收集个人数据时间键")
        for result in person_results:
            logger.info(f"处理个人数据: date={result.date}, statistics_result_2={result.statistics_result_2}")

            if result.statistics_result_2:
                try:
                    parsed_data = json.loads(result.statistics_result_2)
                    logger.info(f"个人数据JSON解析成功: {parsed_data}")

                    # 处理两种不同的JSON格式
                    if isinstance(parsed_data, dict) and "data" in parsed_data:
                        # 格式: {"data": [...]}
                        data_list = parsed_data["data"]
                        logger.info(f"使用格式1: data_list = {data_list}")
                    elif isinstance(parsed_data, list):
                        # 格式: [...]
                        data_list = parsed_data
                        logger.info(f"使用格式2: data_list = {data_list}")
                    else:
                        logger.warning(f"未知的JSON格式: {parsed_data}")
                        continue

                    if isinstance(data_list, list):
                        for item in data_list:
                            logger.info(f"处理个人数据项: {item}")
                            if isinstance(item, dict):
                                date_str = result.date.strftime('%Y-%m-%d') if result.date else ""
                                time_str = item.get("time", "")
                                datetime_str = f"{date_str} {time_str}" if date_str and time_str else ""
                                average_value = item.get("average_value", 0)

                                logger.info(f"生成datetime_str: {datetime_str}, average_value: {average_value}")

                                if datetime_str:
                                    all_datetime_keys.add(datetime_str)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(f"解析个人累计平均分数据失败: {e}, 数据: {result.statistics_result_2}")

        # 处理小组数据
        logger.info("开始收集小组数据时间键")
        for result in group_results:
            logger.info(f"处理小组数据: date={result.date}, statistics_result_2={result.statistics_result_2}")

            if result.statistics_result_2:
                try:
                    parsed_data = json.loads(result.statistics_result_2)
                    logger.info(f"小组数据JSON解析成功: {parsed_data}")

                    # 处理两种不同的JSON格式
                    if isinstance(parsed_data, dict) and "data" in parsed_data:
                        # 格式: {"data": [...]}
                        data_list = parsed_data["data"]
                        logger.info(f"使用格式1: data_list = {data_list}")
                    elif isinstance(parsed_data, list):
                        # 格式: [...]
                        data_list = parsed_data
                        logger.info(f"使用格式2: data_list = {data_list}")
                    else:
                        logger.warning(f"未知的JSON格式: {parsed_data}")
                        continue

                    if isinstance(data_list, list):
                        for item in data_list:
                            logger.info(f"处理小组数据项: {item}")
                            if isinstance(item, dict):
                                date_str = result.date.strftime('%Y-%m-%d') if result.date else ""
                                time_str = item.get("time", "")
                                datetime_str = f"{date_str} {time_str}" if date_str and time_str else ""
                                average_value = item.get("average_value", 0)

                                logger.info(f"生成datetime_str: {datetime_str}, average_value: {average_value}")

                                if datetime_str:
                                    all_datetime_keys.add(datetime_str)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(f"解析小组累计平均分数据失败: {e}, 数据: {result.statistics_result_2}")

        # 使用收集到的所有datetime键初始化merged_data
        logger.info(f"收集到 {len(all_datetime_keys)} 个唯一的时间键")
        for datetime_key in all_datetime_keys:
            merged_data[datetime_key] = {
                "date": datetime_key,
                "person_average_value": None,
                "group_average_value": None
            }

        # 再次处理个人数据，填充person_average_value
        logger.info("开始填充个人数据")
        for result in person_results:
            logger.info(f"处理个人数据: date={result.date}, statistics_result_2={result.statistics_result_2}")

            if result.statistics_result_2:
                try:
                    parsed_data = json.loads(result.statistics_result_2)
                    logger.info(f"个人数据JSON解析成功: {parsed_data}")

                    # 处理两种不同的JSON格式
                    if isinstance(parsed_data, dict) and "data" in parsed_data:
                        # 格式: {"data": [...]}
                        data_list = parsed_data["data"]
                        logger.info(f"使用格式1: data_list = {data_list}")
                    elif isinstance(parsed_data, list):
                        # 格式: [...]
                        data_list = parsed_data
                        logger.info(f"使用格式2: data_list = {data_list}")
                    else:
                        logger.warning(f"未知的JSON格式: {parsed_data}")
                        continue

                    if isinstance(data_list, list):
                        for item in data_list:
                            logger.info(f"处理个人数据项: {item}")
                            if isinstance(item, dict):
                                date_str = result.date.strftime('%Y-%m-%d') if result.date else ""
                                time_str = item.get("time", "")
                                datetime_str = f"{date_str} {time_str}" if date_str and time_str else ""
                                average_value = item.get("average_value", 0)

                                logger.info(f"生成datetime_str: {datetime_str}, average_value: {average_value}")

                                if datetime_str and datetime_str in merged_data:
                                    merged_data[datetime_str]["person_average_value"] = average_value
                                    logger.info(f"更新个人数据项: {datetime_str}")
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(f"解析个人累计平均分数据失败: {e}, 数据: {result.statistics_result_2}")

        # 再次处理小组数据，填充group_average_value
        logger.info("开始填充小组数据")
        for result in group_results:
            logger.info(f"处理小组数据: date={result.date}, statistics_result_2={result.statistics_result_2}")

            if result.statistics_result_2:
                try:
                    parsed_data = json.loads(result.statistics_result_2)
                    logger.info(f"小组数据JSON解析成功: {parsed_data}")

                    # 处理两种不同的JSON格式
                    if isinstance(parsed_data, dict) and "data" in parsed_data:
                        # 格式: {"data": [...]}
                        data_list = parsed_data["data"]
                        logger.info(f"使用格式1: data_list = {data_list}")
                    elif isinstance(parsed_data, list):
                        # 格式: [...]
                        data_list = parsed_data
                        logger.info(f"使用格式2: data_list = {data_list}")
                    else:
                        logger.warning(f"未知的JSON格式: {parsed_data}")
                        continue

                    if isinstance(data_list, list):
                        for item in data_list:
                            logger.info(f"处理小组数据项: {item}")
                            if isinstance(item, dict):
                                date_str = result.date.strftime('%Y-%m-%d') if result.date else ""
                                time_str = item.get("time", "")
                                datetime_str = f"{date_str} {time_str}" if date_str and time_str else ""
                                average_value = item.get("average_value", 0)

                                logger.info(f"生成datetime_str: {datetime_str}, average_value: {average_value}")

                                if datetime_str and datetime_str in merged_data:
                                    merged_data[datetime_str]["group_average_value"] = average_value
                                    logger.info(f"更新小组数据项: {datetime_str}")
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(f"解析小组累计平均分数据失败: {e}, 数据: {result.statistics_result_2}")

        # 转换为列表并按时间排序
        logger.info(f"合并数据完成，共 {len(merged_data)} 条记录")
        result_list = list(merged_data.values())
        result_list.sort(key=lambda x: x["date"] if x["date"] else "")
        logger.info(f"排序后数据: {result_list}")

        # 处理mark_time参数
        if query.mark_time:
            try:
                logger.info(f"处理mark_time参数: {query.mark_time}")
                # 尝试解析mark_time日期
                mark_date = datetime.strptime(query.mark_time, '%Y-%m-%d').date()
                logger.info(f"mark_date解析成功: {mark_date}")

                # 筛选匹配日期的数据
                filtered_data = []
                for item in result_list:
                    if item["date"] and item["date"].startswith(mark_date.strftime('%Y-%m-%d')):
                        filtered_data.append({
                            "time": item["date"].split(" ")[1] if " " in item["date"] else "",
                            "person_average_value": item["person_average_value"],
                            "group_average_value": item["group_average_value"]
                        })
                        logger.info(f"筛选数据项: {item}")

                logger.info(f"筛选后数据数量: {len(filtered_data)}")
                result_list = filtered_data
            except ValueError as e:
                logger.warning(f"mark_time解析失败: {e}, mark_time={query.mark_time}")
                # 如果mark_time不是有效日期格式，返回空列表
                result_list = []
        else:
            # 如果没有mark_time，保持原有格式
            logger.info("未提供mark_time参数，保持原有格式")

        logger.info(f"评卷员页面：计算累计平均分（带时间），共 {len(result_list)} 条记录")
        logger.info(f"最终返回数据: {result_list}")
        return BaseResponse(data=result_list, msg="累计平均分（带时间）")

    except Exception as e:
        logger.error(f"计算累计平均分（带时间）失败：{e}")
        logger.error(f"异常堆栈信息: {traceback.format_exc()}")
        return BaseResponse(data=[], msg="计算累计平均分（带时间）失败")


@score_analysis_router.post(path="/calculate_average_score_batch", response_model=BaseResponse, summary="评卷员页面：批量计算平均分")
async def calculate_average_score_batch_api(
        query: CalculateAverageScoreBatchReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 评卷员页面：批量计算平均分")

    # 批量处理每个用户任务组合
    results = []
    for user_task in query.user_task_list:
        # 构建查询参数
        user_id = user_task.get("user_id")
        task_id = user_task.get("task_id")
        round_count = user_task.get("round_count")
        task_type = user_task.get("task_type")
        
        # 构建过滤条件
        filters_score = [
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 1,
        ]
        if user_id:
            filters_score.append(HumanStatisticsPerson.user_id == user_id)
        if task_id:
            filters_score.append(HumanStatisticsPerson.task_id == task_id)
        if query.subject_id:
            filters_score.append(HumanStatisticsPerson.subject_id == query.subject_id)
        if query.group_id:
            filters_score.append(HumanStatisticsPerson.group_id == query.group_id)
        if query.project_id:
            filters_score.append(HumanStatisticsPerson.project_id == query.project_id)
        if query.ques_group_id:
            filters_score.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

        score_data = new_session.query(HumanStatisticsPerson.date, HumanStatisticsPerson.statistics_result_1).filter(*filters_score)

        # 添加日期范围条件
        if query.date_range:
            try:
                start_date_str, end_date_str = query.date_range.split('至')
                start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
                score_data = score_data.filter(HumanStatisticsPerson.date >= start_date)
                score_data = score_data.filter(HumanStatisticsPerson.date <= end_date)
            except ValueError:
                pass

        score_results = score_data.all()

        # 构建工作量过滤条件
        filters_workload = [
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 4,
        ]
        if user_id:
            filters_workload.append(HumanStatisticsPerson.user_id == user_id)
        if task_id:
            filters_workload.append(HumanStatisticsPerson.task_id == task_id)
        if query.subject_id:
            filters_workload.append(HumanStatisticsPerson.subject_id == query.subject_id)
        if query.group_id:
            filters_workload.append(HumanStatisticsPerson.group_id == query.group_id)
        if query.project_id:
            filters_workload.append(HumanStatisticsPerson.project_id == query.project_id)
        if query.ques_group_id:
            filters_workload.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)

        workload_data = new_session.query(HumanStatisticsPerson.date, HumanStatisticsPerson.statistics_result_1).filter(*filters_workload)

        # 添加日期范围条件
        if query.date_range:
            try:
                start_date_str, end_date_str = query.date_range.split('至')
                start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
                workload_data = workload_data.filter(HumanStatisticsPerson.date >= start_date)
                workload_data = workload_data.filter(HumanStatisticsPerson.date <= end_date)
            except ValueError:
                pass

        workload_results = workload_data.all()

        # 将工作量结果按日期组织成字典，方便查找
        workload_dict = {}
        for result in workload_results:
            workload_dict[result.date] = float(result.statistics_result_1) if result.statistics_result_1 else 0.0

        # 计算加权平均分：每一天的平均分 * 该天工作量求和，然后除以总工作量
        total_weighted_score = 0.0
        total_workload = 0

        for result in score_results:
            daily_avg_score = float(result.statistics_result_1) if result.statistics_result_1 else 0.0
            # 从工作量字典中获取对应日期的工作量
            daily_workload = workload_dict.get(result.date, 0.0)

            total_weighted_score += daily_avg_score * daily_workload
            total_workload += daily_workload

        # 避免除以0，并保留两位小数
        final_avg_score = round_half_up(total_weighted_score / total_workload, 2) if total_workload > 0 else 0.0

        # 添加到结果列表
        results.append({
            "user_id": user_id,
            "task_id": task_id,
            "round_count": round_count,
            "task_type": task_type,
            "weighted_average_score": final_avg_score
        })

    logger.info(f"评卷员页面：批量计算平均分，共处理 {len(results)} 个用户任务组合")
    return BaseResponse(data=results, msg="批量计算平均分")


@score_analysis_router.post(path="/get_all_review_dates", response_model=BaseResponse, summary="获取所有去重后的阅卷时间（天）")
async def get_all_review_dates_api(
        query: CalculateWorkloadReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 获取所有去重后的阅卷时间（天）")

    try:
        # 查询所有不重复的日期
        dates = new_session.query(
            distinct(HumanStatisticsPerson.date)
        ).filter(
            HumanStatisticsPerson.date.isnot(None)
        ).order_by(HumanStatisticsPerson.date).all()

        # 提取日期并格式化为字符串
        date_list = []
        for date_result in dates:
            if date_result[0]:  # 确保日期不为None
                date_list.append(date_result[0].strftime('%Y-%m-%d'))

        logger.info(f"获取到 {len(date_list)} 个不同的阅卷日期: {date_list}")
        return BaseResponse(data=date_list, msg="获取所有去重后的阅卷时间成功")
        
    except Exception as e:
        logger.error(f"获取所有去重后的阅卷时间失败：{e}")
        traceback.print_exc()
        return BaseResponse(data=[], msg="获取所有去重后的阅卷时间失败")
