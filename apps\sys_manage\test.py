import json
import os.path

import requests
from bs4 import BeautifulSoup
from settings import logger

from apps.models.models import StuAnswer, SameStuAnswerGroup, ExamQuestion, OperationStep, OperationStepGroup
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from settings import configs
from utils.utils import create_file_base64

# 数据库连接配置
# sqlalchemy_db_uri = rf"mysql+pymysql://sa:zhu<PERSON>an@192.168.1.66:3309/pf_gzsdzk_20242"
sqlalchemy_db_uri = rf"mysql+pymysql://root:qweasdzxc888@192.168.1.234:3306/new_read_paper"

# 创建数据库引擎
engine = create_engine(sqlalchemy_db_uri,
                       pool_size=500,  # 最小连接数
                       max_overflow=100,  # 允许连接数超过 pool_size 多少，可用于设置最大连接数
                       echo=False,  # 打印执行的 sql 语句
                       pool_pre_ping=True,  # 开启连接断开检测
                       pool_recycle=1800,  # 连接过期时间，单位为秒
                       connect_args={
                           "connect_timeout": 7200  # 服务器等待连接握手超时时间，单位为秒
                       })


# 设置 MySQL 全局变量 max_connections
def set_max_connections():
    try:
        with engine.connect() as connection:
            connection.execute(text(f"select 1"))
    except Exception as e:
        print(f"数据库连接失败：请检查数据库配置")


# 调用函数设置 max_connections 数量
set_max_connections()

db_session = sessionmaker(bind=engine)


def session_depend():
    new_session = db_session()
    try:
        yield new_session
    finally:
        new_session.close()


def deal_answer_data():
    new_session = next(session_depend())
    all_rows_length = new_session.query(StuAnswer.answer_id).filter(StuAnswer.ques_type_code == "E").count()
    print("总条数：", all_rows_length)
    a_batch_num = 200
    batch_count = (int(all_rows_length / a_batch_num) + 1) if (all_rows_length % a_batch_num != 0) else (
            all_rows_length / a_batch_num)  # 总共分成多少批
    print("batch_count", batch_count)
    for batch in range(batch_count):
        limit = batch
        offset = limit * a_batch_num
        print(f"第 {batch + 1}/{batch_count} 批")
        answer_info = new_session.query(StuAnswer.answer_id, StuAnswer.stu_answer).filter(
            StuAnswer.ques_type_code == "E").limit(a_batch_num).offset(offset).all()
        for answer_id, raw_answer in answer_info:
            if raw_answer and "stuAnswer" in raw_answer:
                stu_answer = json.loads(raw_answer)["stuAnswer"]
                new_session.query(StuAnswer).filter(StuAnswer.answer_id == answer_id).update({
                    StuAnswer.stu_answer: stu_answer
                })
        new_session.commit()


def deal_answer_group_data():
    new_session = next(session_depend())
    all_rows_length = new_session.query(SameStuAnswerGroup.same_answer_group_id).filter(
        SameStuAnswerGroup.ques_type_code == "E").count()
    print("总条数：", all_rows_length)
    a_batch_num = 200
    batch_count = (int(all_rows_length / a_batch_num) + 1) if (all_rows_length % a_batch_num != 0) else (
            all_rows_length / a_batch_num)  # 总共分成多少批
    for batch in range(batch_count):
        limit = batch
        offset = limit * a_batch_num
        print(f"第 {batch + 1}/{batch_count} 批")
        answer_info = new_session.query(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.stu_answer).filter(
            SameStuAnswerGroup.ques_type_code == "E").limit(a_batch_num).offset(offset).all()
        for answer_id, raw_answer in answer_info:
            if raw_answer and "stuAnswer" in raw_answer:
                stu_answer = json.loads(raw_answer)["stuAnswer"]
                new_session.query(SameStuAnswerGroup).filter(
                    SameStuAnswerGroup.same_answer_group_id == answer_id).update({
                    SameStuAnswerGroup.stu_answer: stu_answer
                })
        new_session.commit()
    new_session.close()


def get_image_base64(html):
    base64_list = []
    soup = BeautifulSoup(html, "lxml")
    img_tags = soup.find_all("img")
    # 修改 src 属性
    for img_tag in img_tags:
        src = img_tag["src"].replace("/File/Download?id=", "server_static/upload/images/").replace("//", "/")
        path = os.path.join(r"E:\workSpace\ai_question_system\code\ReadPaperSystem", src)
        image_base64 = create_file_base64(path)
        base64_list.append(image_base64)
    return base64_list


def get_ques_image():
    all_image_base64_list = []
    new_session = next(session_depend())
    ques_info = new_session.query(ExamQuestion.ques_id, ExamQuestion.parent_ques_id, ExamQuestion.ques_desc) \
        .filter(ExamQuestion.ques_type_code.in_(["D", "E", "F"])).all()

    for ques_id, parent_ques_id, ques_desc in ques_info:
        json_item = {
            "ques_id": ques_id,
            "images_base64_list": []
        }
        if parent_ques_id:
            parent_ques_desc = new_session.query(ExamQuestion.ques_desc).filter(
                ExamQuestion.ques_id == parent_ques_id).scalar()
            parent_ques_html = parent_ques_desc.get("html")
            if parent_ques_html:
                base64_list = get_image_base64(parent_ques_html)
                json_item["images_base64_list"].extend(base64_list)
        ques_html = ques_desc.get("html")

        if ques_html:
            base64_list = get_image_base64(ques_html)
            json_item["images_base64_list"].extend(base64_list)

        all_image_base64_list.append(json_item)
    with open(r"E:\workSpace\ai_question_system\code\ReadPaperSystem\image.json", "w", encoding="utf-8") as f:
        f.write(json.dumps(all_image_base64_list))


if __name__ == '__main__':
    # deal_answer_data()
    # print("---------------------------")
    # deal_answer_group_data()
    # get_ques_image()

    print(any([None, []]))
