"""
评分接口模块
"""
import json

from fastapi import APIRouter, Depends
from sqlalchemy import func, select, and_, text, literal
from sqlalchemy.orm import Session
from typing import Any

from apps.base.human_global_cache import get_round_group_member
from apps.human_mark_group.models import HumanGroupMember
from apps.human_official_mark.mark_redis_services import mark_process
from apps.human_official_mark.schemas import BaseRoundIdListReq
from apps.human_official_mark.services import repeat_record_condition, get_my_mark_round_process, get_official_task_process
from apps.human_official_mark.schemas import MarkStuListReq, GetMyOfficialTaskListReq, ReviewerMarkStuReq, GetMarkRecordReq
from apps.human_official_mark.services import get_stu_answer_info, mark_record_condition
from apps.human_repeat_mark.models import HumanRepeatAnswerScore, HumanRepeatTaskRound, HumanRepeatRoundDetail, HumanRepeatTask
from apps.human_task_manage.services import get_group_reviewer_load
from apps.models.models import ExamPaper, Project, Subject, UserInfo, ExamQuestion, WorkFlowMainProcess
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.human_task_manage.models import HumanPersonDistriAnswer, HumanReadRoundGroup, HumanReadTask, HumanReadTaskRound
from settings import logger
from factory_apps import session_depend
from utils.time_func import format_now_time

human_mark_router = APIRouter()


@human_mark_router.post(path="/get_my_human_task_list", response_model=BaseResponse, summary="获取评阅员的任务列表")
async def get_my_human_task_list(query: GetMyOfficialTaskListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取评阅员的任务列表")
    current_page, page_size, task_type, task_name, round_state_list = query.model_dump().values()
    curr_user_id = user["user_id"]
    role_id = user["role"][0]

    member_role = 2 if role_id == "3" else 1

    group_id_info = new_session.execute(select(HumanGroupMember.group_id).where(and_(HumanGroupMember.member_role == member_role, HumanGroupMember.user_id == curr_user_id))).scalars().all()
    group_id_list = list(group_id_info)

    # 获取所在小组被分配到的任务
    task_round_info = new_session.query(HumanReadRoundGroup.task_id, HumanReadRoundGroup.round_id) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanReadRoundGroup.task_id) \
        .filter(HumanReadRoundGroup.group_id.in_(group_id_list), HumanReadTask.task_type == task_type).all()
    if not task_round_info:
        data = {"data": [], "total": 0}
        return BaseResponse(data=data)

    task_id_list, round_id_list = [], []
    for task_id, round_id in task_round_info:
        task_id_list.append(task_id)
        round_id_list.append(round_id)

    # 只获取已阅量
    process_data = get_my_mark_round_process(new_session, round_id_list, only_marked=True)

    # todo: 批量获取任务轮次平均速度，待出接口

    limit = current_page - 1
    offset = limit * page_size

    task_name_condition = HumanReadTask.task_name.ilike(f"%{task_name}%") if task_name else True
    condition = and_(HumanReadTask.task_type == task_type, task_name_condition, HumanReadTaskRound.round_id.in_(round_id_list))

    new_session.execute(text("SET SESSION group_concat_max_len = 9999999;"))
    total = new_session.query(HumanReadTask.task_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .filter(condition).count()

    stmt = select(HumanReadTask.task_id, HumanReadTask.task_name, HumanReadTask.project_id, HumanReadTask.subject_id, HumanReadTask.paper_id,
                  HumanReadTask.task_type, HumanReadTask.business_type_name, HumanReadTask.ques_id, HumanReadTask.ques_code, HumanReadTaskRound.round_id,
                  HumanReadTaskRound.round_count, HumanReadTaskRound.process_id, HumanReadTaskRound.round_state, HumanReadTaskRound.mark_score_step,
                  HumanReadTaskRound.created_time, HumanReadTaskRound.updated_time, UserInfo.name, WorkFlowMainProcess.process_name, Project.project_name,
                  Subject.subject_name, Subject.exam_mode, ExamPaper.paper_name, ExamQuestion.ques_score, ExamQuestion.ques_type_code) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .outerjoin(WorkFlowMainProcess, WorkFlowMainProcess.process_id == HumanReadTaskRound.process_id) \
        .outerjoin(ExamPaper, ExamPaper.paper_id == HumanReadTask.paper_id) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(ExamQuestion, ExamQuestion.ques_id == HumanReadTask.ques_id) \
        .join(UserInfo, UserInfo.user_id == HumanReadTaskRound.c_user_id) \
        .filter(condition) \
        .limit(page_size).offset(offset)

    result = new_session.execute(stmt).all()

    no_mark_stu_list = [dict(row._mapping) for row in result]

    for mark_stu in no_mark_stu_list:
        round_id = mark_stu["round_id"]
        mark_stu["marked_count"] = process_data.get(round_id, {}).get("marked_count")

    data = {
        "total": total,
        "data": no_mark_stu_list
    }

    return BaseResponse(data=data, msg="获取评阅员的任务列表成功")


@human_mark_router.post(path="/get_mark_stu_list", response_model=BaseResponse, summary="获取未评、已评考生列表")
async def check_human_task_exist(query: MarkStuListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取未评、已评考生列表")
    current_page, page_size, round_id, ques_code, stu_type = query.model_dump().values()

    curr_user_id = user["user_id"]

    limit = current_page - 1
    offset = limit * page_size

    condition = and_(HumanPersonDistriAnswer.round_id == round_id, HumanPersonDistriAnswer.ques_code == ques_code,
                     HumanPersonDistriAnswer.user_id == curr_user_id, HumanPersonDistriAnswer.is_answer_marked == stu_type)

    select_fields = [
        HumanPersonDistriAnswer.stu_secret_num,
        func.group_concat(HumanPersonDistriAnswer.answer_id).label("answer_id_str"),
        func.group_concat(HumanPersonDistriAnswer.ques_id).label("ques_id_str"),
        func.max(HumanPersonDistriAnswer.is_again_mark).label("is_again_mark"),
        func.group_concat(func.coalesce(HumanPersonDistriAnswer.round_distri_id, 0)).label("round_distri_id_str")
    ]

    if stu_type == 0:
        stmt = select(*select_fields).filter(condition) \
            .group_by(HumanPersonDistriAnswer.stu_secret_num) \
            .limit(page_size).offset(offset)
        total = new_session.query(HumanPersonDistriAnswer.stu_secret_num.distinct()).filter(condition).count()
    else:
        select_fields.extend([
            func.sum(HumanPersonDistriAnswer.mark_score).label("mark_score_sum"),
            func.group_concat(HumanPersonDistriAnswer.mark_score).label("mark_score_str"),
            func.group_concat(HumanPersonDistriAnswer.mark_point_score_list, "-").label("mark_point_score_list_str"),
            func.group_concat(HumanPersonDistriAnswer.cost_time).label("cost_time_str"),
            func.group_concat(HumanPersonDistriAnswer.mark_time).label("mark_time_str")
        ])
        stmt = select(*select_fields).filter(condition) \
            .group_by(HumanPersonDistriAnswer.stu_secret_num, HumanPersonDistriAnswer.is_again_mark) \
            .order_by(func.max(HumanPersonDistriAnswer.mark_time).desc()) \
            .limit(page_size).offset(offset)
        total = new_session.query(HumanPersonDistriAnswer.stu_secret_num.distinct()).filter(condition).count()
    result = new_session.execute(stmt).all()

    if not result:
        return BaseResponse(data={"total": 0, "data": []})

    stu_answer_dict = get_stu_answer_info(new_session, ques_code)

    no_mark_stu_list = []
    for row in result:
        answer_data = []
        answer_id_list, ques_id_list, is_again_mark = row.answer_id_str.split(","), row.ques_id_str.split(","), row.is_again_mark
        answer_items = []
        if stu_type == 0:
            # 未评
            for answer_id, ques_id in zip(answer_id_list, ques_id_list):
                answer_info = stu_answer_dict[answer_id]
                answer_item = {
                    "answer_id": answer_id,
                    "ques_id": ques_id,
                    "ques_order": answer_info["ques_order"],
                    "answer_image_path": answer_info["answer_image_path"],
                    "word_count": answer_info["word_count"],
                    "is_do": answer_info["is_do"]
                }
                answer_items.append(answer_item)
        else:
            # 已评
            mark_point_score_list_list, cost_time_list, mark_time_list = row.mark_point_score_list_str.split("-,"), row.cost_time_str.split(","), row.mark_time_str.split(",")
            mark_score_list = row.mark_score_str.split(",") if row.mark_score_str else [None for _ in range(len(mark_time_list))]
            mark_point_score_lists = [i.replace("-", "") for i in mark_point_score_list_list]

            for answer_id, ques_id, mark_score, mark_point_score_list, cost_time, mark_time in zip(answer_id_list, ques_id_list, mark_score_list, mark_point_score_lists, cost_time_list, mark_time_list):
                answer_info = stu_answer_dict[answer_id]
                answer_item = {
                    "answer_id": answer_id,
                    "ques_id": ques_id,
                    "ques_order": answer_info["ques_order"],
                    "answer_image_path": answer_info["answer_image_path"],
                    "word_count": answer_info["word_count"],
                    "is_do": answer_info["is_do"],
                    "mark_score": float(mark_score) if mark_score is not None else None,
                    "mark_point_score_list": json.loads(mark_point_score_list),
                    "cost_time": int(cost_time),
                    "mark_time": mark_time
                }
                answer_items.append(answer_item)
        # 按 ques_order 升序排序
        answer_items.sort(key=lambda x: x["ques_order"])
        answer_data.extend(answer_items)
        item = {
            "stu_secret_num": row.stu_secret_num,
            "ques_code": ques_code,
            "answer_data": answer_data,
            "is_again_mark": is_again_mark
        }
        if stu_type == 1:
            item["mark_score_sum"] = row.mark_score_sum
            item["mark_time"] = row.mark_time_str.split(",")[0] if row.mark_time_str else None
            item["cost_time"] = int(row.cost_time_str.split(",")[0]) if row.cost_time_str else None
        no_mark_stu_list.append(item)

    data = {
        "round_id": round_id,
        "total": total,
        "data": no_mark_stu_list
    }

    return BaseResponse(data=data, msg="获取未评、已评考生列表成功")


@human_mark_router.post(path="/reviewer_mark_stu", response_model=BaseResponse, summary="评卷员评分（按每考生每大题）")
async def reviewer_mark_stu(query: ReviewerMarkStuReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 评卷员评分")
    curr_user_id = user["user_id"]
    round_id, mark_type, cost_time, mark_info, is_again_mark = query.round_id, query.mark_type, query.cost_time, query.mark_info, query.is_again_mark

    mark_time = format_now_time()

    for mark_item in mark_info:
        answer_id, mark_point_score_list, mark_score = mark_item.model_dump().values()
        condition = and_(HumanPersonDistriAnswer.round_id == round_id, HumanPersonDistriAnswer.answer_id == answer_id,
                         HumanPersonDistriAnswer.is_again_mark == is_again_mark, HumanPersonDistriAnswer.user_id == curr_user_id)
        new_session.query(HumanPersonDistriAnswer).filter(condition).update({
            HumanPersonDistriAnswer.mark_point_score_list: mark_point_score_list,
            HumanPersonDistriAnswer.mark_score: mark_score,
            HumanPersonDistriAnswer.cost_time: cost_time,
            HumanPersonDistriAnswer.is_answer_marked: 1,
            HumanPersonDistriAnswer.mark_time: mark_time
        })
        # 将评分数据存储在 redis 中
        mark_process(round_id, answer_id, curr_user_id, mark_point_score_list, mark_score, cost_time, mark_time)
    new_session.commit()

    if mark_type == 1:
        # 获取该评阅员属于哪个小组
        round_group_member = get_round_group_member(new_session, round_id)
        for group_id, member_id_list in round_group_member.items():
            if curr_user_id in member_id_list:
                # 该评阅员负载数量 -1
                reviewer_load = get_group_reviewer_load(group_id, round_id)
                reviewer_load[curr_user_id] -= 1
                break

    return BaseResponse(msg="评分成功")


@human_mark_router.post(path="/get_mark_task_process", response_model=BaseResponse, summary="获取正评任务总体进度")
async def get_mark_task_process(query: BaseRoundIdListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取正评任务总体进度")
    round_id_list = query.round_id_list

    process_data = get_official_task_process(new_session, round_id_list)

    return BaseResponse(data=process_data)


@human_mark_router.post(path="/get_mark_record", response_model=BaseResponse, summary="查询评分记录")
async def get_mark_record(query: GetMarkRecordReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 查询评分记录")

    (current_page, page_size, subject_id, task_type, round_count, ques_code, user_id, stu_secret_num,
     start_time, end_time, mark_score_range) = query.model_dump().values()

    limit = current_page - 1
    offset = limit * page_size
    condition = mark_record_condition(subject_id,task_type, round_count, ques_code, user_id, stu_secret_num,
     start_time, end_time, mark_score_range)
    repeat_condition = repeat_record_condition(subject_id,task_type, round_count, ques_code, user_id, stu_secret_num,
     start_time, end_time, mark_score_range)
    #先查复评，再查正评 1是正平 3是复评
    total  = 0
    mark_record_data = []
    # if task_type is None:
        # 正评查询部分
    official_stmt = select(
        func.max(HumanPersonDistriAnswer.person_distri_id).label("id"),
        Project.project_id,
        Project.project_name,
        Subject.subject_id,
        Subject.subject_name,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_count,
        HumanPersonDistriAnswer.stu_secret_num,
        HumanPersonDistriAnswer.ques_code,
        literal(1).label('task_type'),
        HumanPersonDistriAnswer.user_id,
        UserInfo.name.label('m_user_name'),
        func.sum(HumanPersonDistriAnswer.mark_score).label('mark_score'),  # 聚合大题总分
        func.max(HumanPersonDistriAnswer.mark_time).label('mark_time')
    ) \
        .join(HumanReadTaskRound, HumanReadTaskRound.round_id == HumanPersonDistriAnswer.round_id) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(Project, Project.project_id == Subject.project_id) \
        .join(UserInfo, UserInfo.user_id == HumanPersonDistriAnswer.user_id) \
        .where(and_(condition, HumanReadTask.task_type == 1)) \
        .group_by(
        HumanPersonDistriAnswer.stu_secret_num,
        HumanPersonDistriAnswer.ques_code,
        HumanReadTaskRound.round_count,
        HumanPersonDistriAnswer.user_id,
        Project.project_id,
        Project.project_name,
        Subject.subject_id,
        Subject.subject_name,
        HumanReadTask.task_name,
    ) \
        # .order_by(func.max(HumanPersonDistriAnswer.mark_time).desc())
    # 复评查询部分
    repeat_stmt = select(
        func.max(HumanRepeatAnswerScore.repeat_answer_socre_id).label("id"),
        Project.project_id,
        Project.project_name,
        Subject.subject_id,
        Subject.subject_name,
        HumanRepeatTaskRound.task_name,
        HumanRepeatTask.round_count,
        HumanRepeatRoundDetail.stu_secret_num,
        HumanRepeatAnswerScore.ques_code,
        HumanRepeatTaskRound.task_type,
        HumanRepeatRoundDetail.repeat_user_id.label('user_id'),
        UserInfo.name.label("m_user_name"),
        func.sum(HumanRepeatAnswerScore.mark_score).label('mark_score'),  # 聚合大题总分
        func.max(HumanRepeatAnswerScore.mark_time).label('mark_time')
    ) \
        .join(HumanRepeatRoundDetail,
              HumanRepeatRoundDetail.repeat_round_detail_id == HumanRepeatAnswerScore.repeat_round_detail_id) \
        .join(HumanRepeatTask, HumanRepeatTask.repeat_task_id == HumanRepeatRoundDetail.repeat_task_id) \
        .join(Subject, Subject.subject_id == HumanRepeatTask.subject_id) \
        .join(Project, Project.project_id == Subject.project_id) \
        .join(HumanRepeatTaskRound,
              HumanRepeatTaskRound.repeat_task_round_id == HumanRepeatTask.repeat_task_round_id) \
        .join(UserInfo, UserInfo.user_id == HumanRepeatRoundDetail.repeat_user_id) \
        .where(and_(repeat_condition, HumanRepeatTaskRound.task_type == 3)) \
        .group_by(
        HumanRepeatRoundDetail.stu_secret_num,
        HumanRepeatTask.round_count,
        HumanRepeatRoundDetail.repeat_user_id,
        HumanRepeatAnswerScore.ques_code,
        Project.project_id,
        Subject.subject_id,
        HumanRepeatTaskRound.task_name,
        HumanRepeatTaskRound.task_type
    ) \
        # .order_by(func.max(HumanRepeatAnswerScore.mark_time).desc())
    # 使用UNION合并两个查询
    # 先执行UNION查询
    union_stmt = official_stmt.union(repeat_stmt)
    # 使用子查询包装，避免在UNION后直接排序聚合函数
    subquery = union_stmt.subquery()
    # 重新构建查询，使用子查询的字段进行排序
    final_stmt = select(subquery.c.id, subquery.c.project_id, subquery.c.project_name,
                        subquery.c.subject_id, subquery.c.subject_name, subquery.c.task_name,
                        subquery.c.round_count, subquery.c.stu_secret_num, subquery.c.ques_code,
                        subquery.c.task_type, subquery.c.user_id, subquery.c.m_user_name,
                        subquery.c.mark_score, subquery.c.mark_time) \
        .order_by(subquery.c.mark_time.desc()) \
        .limit(page_size).offset(offset)
    total_union = new_session.execute(union_stmt).fetchall()
    total = len(total_union)  # 实际union查询返回的记录数
        # 获取总数
        # total_official = new_session.query(HumanPersonDistriAnswer.person_distri_id) \
        #     .join(HumanReadTaskRound, HumanReadTaskRound.round_id == HumanPersonDistriAnswer.round_id) \
        #     .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        #     .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        #     .join(Project, Project.project_id == Subject.project_id) \
        #     .join(StuAnswer, StuAnswer.answer_id == HumanPersonDistriAnswer.answer_id) \
        #     .filter(and_(condition, HumanReadTask.task_type == 1)) \
        #     .count()
        #
        # total_repeat = new_session.query(HumanRepeatAnswerScore.answer_id) \
        #     .join(HumanRepeatRoundDetail, HumanRepeatRoundDetail.repeat_round_detail_id == HumanRepeatAnswerScore.repeat_round_detail_id)\
        #     .join(HumanRepeatTask, HumanRepeatTask.repeat_task_id == HumanRepeatRoundDetail.repeat_task_id)\
        #     .join(Subject, Subject.subject_id == HumanRepeatTask.subject_id) \
        #     .join(Project, Project.project_id == Subject.project_id) \
        #     .join(HumanRepeatTaskRound,
        #           HumanRepeatTaskRound.repeat_task_round_id == HumanRepeatTask.repeat_task_round_id) \
        #     .join(UserInfo, UserInfo.user_id == HumanRepeatRoundDetail.repeat_user_id) \
        #     .filter(and_(repeat_condition, HumanRepeatTaskRound.task_type == 3)) \
        #     .count()
        #
        # total = total_official + total_repeat

    mark_record_data = []
    result = list(new_session.execute(final_stmt))
    for row in result:
        ques_name_info = new_session.query(ExamQuestion.knowledge_show).filter(and_(ExamQuestion.ques_code == row.ques_code,ExamQuestion.knowledge_show.isnot(None))).first()
        mark_record_item = {
            "project_id": row.project_id,
            "project_name": row.project_name,
            "subject_id": row.subject_id,
            "subject_name": row.subject_name,
            "task": row.task_name,
            "stu_secret_num": row.stu_secret_num,
            "round_count": row.round_count,
            "ques_name": ques_name_info.knowledge_show if ques_name_info else None,
            "ques_code": row.ques_code,
            "mark_state": row.task_type,
            "m_user_name": row.m_user_name,
            "mark_score": row.mark_score,
            "mark_time": row.mark_time and str(row.mark_time).replace("T"," "),
        }
        mark_record_data.append(mark_record_item)
    if mark_score_range and len(mark_score_range) == 2:
        min_score, max_score = mark_score_range

        # 重新执行 union 查询，但添加分数范围过滤条件
        # 为了保持 total 与返回数据一致，需要重新构建查询

        # 或者更简单：先在内存中过滤，然后重新计算总数
        filtered_data = [item for item in mark_record_data if min_score <= item['mark_score'] <= max_score]
        mark_record_data = filtered_data

        # 这里需要重新构建一个查询来计算符合分数范围的总数
        # 由于复杂性，最简单的方式是：
        total = len(filtered_data)
    return BaseResponse(msg="查询评分记录成功", data={
        "total": total,
        "data": mark_record_data
    })
    # elif task_type == 1:
    #
    #     total = new_session.query(HumanPersonDistriAnswer.person_distri_id) \
    #         .join(HumanReadTaskRound, HumanReadTaskRound.round_id == HumanPersonDistriAnswer.round_id) \
    #         .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
    #         .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
    #         .join(Project, Project.project_id == Subject.project_id) \
    #         .join(StuAnswer, StuAnswer.answer_id == HumanPersonDistriAnswer.answer_id) \
    #         .filter(and_(condition, HumanReadTask.task_type == 1)) \
    #         .count()
    #     stmt = select(
    #         HumanPersonDistriAnswer.person_distri_id,
    #         Project.project_id,
    #         Project.project_name,
    #         Subject.subject_id,
    #         Subject.subject_name,
    #         HumanReadTask.task_name,
    #         HumanReadTaskRound.round_count,
    #         HumanPersonDistriAnswer.stu_secret_num,
    #         HumanPersonDistriAnswer.ques_code,
    #         literal(1).label('task_type'),  # 正评标识
    #         HumanPersonDistriAnswer.user_id,
    #         UserInfo.name.label('m_user_name'),
    #         HumanPersonDistriAnswer.mark_score,
    #         HumanPersonDistriAnswer.mark_time,
    #     ) \
    #         .join(HumanReadTaskRound, HumanReadTaskRound.round_id == HumanPersonDistriAnswer.round_id) \
    #         .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
    #         .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
    #         .join(Project, Project.project_id == Subject.project_id) \
    #         .join(UserInfo, UserInfo.user_id == HumanPersonDistriAnswer.user_id) \
    #         .where(and_(condition, HumanReadTask.task_type == 1)) \
    #         .order_by(HumanPersonDistriAnswer.mark_time)\
    #         .limit(page_size).offset(offset)
    #     mark_record_data = []
    #     result = list(new_session.execute(stmt))
    #     for row in result:
    #         mark_record_item = {
    #             "project_id": row.project_id,
    #             "project_name": row.project_name,
    #             "subject_id": row.subject_id,
    #             "subject_name": row.subject_name,
    #             "task": row.task_name,
    #             "stu_secret_num": row.stu_secret_num,
    #             "round_count": row.round_count,
    #             "ques_code": row.ques_code,
    #             "mark_state": row.task_type,
    #             "m_user_name": row.m_user_name,
    #             "mark_score": row.mark_score,
    #             "mark_time": row.mark_time,
    #         }
    #         mark_record_data.append(mark_record_item)
    # elif task_type == 3:
    #     total = new_session.query(HumanRepeatAnswerScore.repeat_answer_socre_id) \
    #         .join(HumanRepeatRoundDetail, HumanRepeatRoundDetail.repeat_round_detail_id == HumanRepeatAnswerScore.repeat_round_detail_id)\
    #         .join(HumanRepeatTask, HumanRepeatTask.repeat_task_id == HumanRepeatRoundDetail.repeat_task_id)\
    #         .join(Subject, Subject.subject_id == HumanRepeatTask.subject_id) \
    #         .join(Project, Project.project_id == Subject.project_id) \
    #         .join(HumanRepeatTaskRound,
    #               HumanRepeatTaskRound.repeat_task_round_id == HumanRepeatTask.repeat_task_round_id) \
    #         .join(UserInfo, UserInfo.user_id == HumanRepeatRoundDetail.repeat_user_id) \
    #         .filter(and_(repeat_condition)) \
    #         .count()
    #
    #     stmt = select(
    #         HumanRepeatAnswerScore.repeat_answer_socre_id,
    #         Project.project_id,
    #         Project.project_name,
    #         Subject.subject_id,
    #         Subject.subject_name,
    #         HumanRepeatTaskRound.task_name,
    #         HumanRepeatTaskRound.round_count,
    #         HumanRepeatRoundDetail.stu_secret_num,
    #         HumanRepeatAnswerScore.ques_code,
    #         HumanRepeatRoundDetail.repeat_user_id,  # 需要从复评任务中获取实际用户ID
    #         UserInfo.username.label("m_user_name"),  # 需要从复评任务中获取实际用户名
    #         HumanRepeatAnswerScore.mark_score,
    #         HumanRepeatAnswerScore.mark_time,
    #     ) \
    #         .join(HumanRepeatRoundDetail,
    #               HumanRepeatRoundDetail.repeat_round_detail_id == HumanRepeatAnswerScore.repeat_round_detail_id) \
    #         .join(HumanRepeatTask, HumanRepeatTask.repeat_task_id == HumanRepeatRoundDetail.repeat_task_id) \
    #         .join(Subject, Subject.subject_id == HumanRepeatTask.subject_id) \
    #         .join(Project, Project.project_id == Subject.project_id) \
    #         .join(HumanRepeatTaskRound,
    #               HumanRepeatTaskRound.repeat_task_round_id == HumanRepeatTask.repeat_task_round_id) \
    #         .join(UserInfo, UserInfo.user_id == HumanRepeatRoundDetail.repeat_user_id) \
    #         .where(and_(repeat_condition)) \
    #         .order_by(HumanRepeatAnswerScore.mark_time).limit(page_size).offset(offset)
    #     mark_record_data = []
    #     result = list(new_session.execute(stmt))
    #     for row in result:
    #         mark_record_item = {
    #             "project_id": row.project_id,
    #             "project_name": row.project_name,
    #             "subject_id": row.subject_id,
    #             "subject_name": row.subject_name,
    #             "task": row.task_name,
    #             "stu_secret_num": row.stu_secret_num,
    #             "round_count": row.round_count,
    #             "ques_code": row.ques_code,
    #             "mark_state": task_type,
    #             "m_user_name": row.m_user_name,
    #             "mark_score": row.mark_score,
    #             "mark_time": row.mark_time,
    #             "answer_id": row.repeat_answer_socre_id
    #         }
    #         mark_record_data.append(mark_record_item)

