import os
import traceback
import json
from typing import Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy import select, and_, func, case, or_, Integer
from settings import logger, configs
from sqlalchemy.orm import Session

from apps.users.services import get_current_user
from apps.human_statistics.models import HumanStatisticsPerson, HumanStatisticsSmallGroup
from apps.human_statistics.schemas import PersonSurveyMonitorReq, GroupSurveyMonitorReq, QGroupSurveyMonitorReq, TryMarkMonitorReq, UpdateTryMarkResultReq, UpdateTryIsOfficeMarkReq, TryMarkResultReq, \
    QualityMonitorReq
from apps.base.schemas import BaseResponse
from helper import response_utils
from helper.excel_export_utils import ExcelExportService
from apps.models.models import UserInfo, ManualReadTask, Project, Subject, ExamQuestion
from apps.human_mark_group.models import HumanMarkGroup, HumanGroupMember
from apps.human_try_mark.models import HumanTryMarkResult
from apps.human_task_manage.models import HumanPersonDistriAnswer, HumanReadRoundGroup, HumanReadTask, HumanReadTaskRound, HumanRoundDistriAnswer, HumanQualityDistriAnswer
from factory_apps import session_depend
from utils.utils import round_half_up

# 创建APIRouter
survey_monitor_router = APIRouter()


@survey_monitor_router.post(path="/person_survey_monitor", response_model=BaseResponse, summary="正评监控:评卷员界面")
async def person_survey_monitor_api(
        query: PersonSurveyMonitorReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 正评监控:评卷员界面")
    # 解构查询参数
    current_page, page_size, task_type, project_id, subject_id, task_name, round_state_list, round_count, group_id, ques_group_id, name, task_id_list, round_id_list = query.model_dump().values()

    # 统计信息子查询（优化条件和表达式）
    stats_subquery = new_session.query(
        HumanStatisticsPerson.task_id,
        HumanStatisticsPerson.round_count,
        HumanStatisticsPerson.group_id,
        HumanStatisticsPerson.user_id,
        # 已阅量统计
        func.sum(case((HumanStatisticsPerson.statistics_type == 4, HumanStatisticsPerson.statistics_result_1), else_=0)).label('reviewed_count'),
        # 平均数
        func.avg(case((HumanStatisticsPerson.statistics_type == 14, HumanStatisticsPerson.statistics_result_1), else_=None)).label('average_score'),
        # 平均速度（份/时）
        (func.sum(case((HumanStatisticsPerson.statistics_type == 4, HumanStatisticsPerson.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsPerson.statistics_type == 10, HumanStatisticsPerson.statistics_result_1), else_=0)), 0) * 3600).label('average_speed1'),

        # 平均速度（秒/份）
        (func.sum(case((HumanStatisticsPerson.statistics_type == 10, HumanStatisticsPerson.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsPerson.statistics_type == 4, HumanStatisticsPerson.statistics_result_1), else_=0)), 0)).label('average_speed2'),
        # 最短速度（秒/份）
        func.min(case((HumanStatisticsPerson.statistics_type == 16, HumanStatisticsPerson.statistics_result_1), else_=None)).label('min_speed'),
        # 最长速度（秒/份）
        func.max(case((HumanStatisticsPerson.statistics_type == 15, HumanStatisticsPerson.statistics_result_1), else_=0)).label('max_speed'),
        func.min(case((HumanStatisticsPerson.statistics_type == 3, HumanStatisticsPerson.statistics_result_1), else_=None)).label('min_score'),
        func.max(case((HumanStatisticsPerson.statistics_type == 2, HumanStatisticsPerson.statistics_result_1), else_=0)).label('max_score')
    ).group_by(
        HumanStatisticsPerson.task_id,
        HumanStatisticsPerson.round_count,
        HumanStatisticsPerson.group_id,
        HumanStatisticsPerson.user_id
    ).subquery()

    # 构建最终查询（优化JOIN条件和字段引用）
    main_query = select(
        HumanReadTask.project_id,
        Project.project_name,
        HumanReadTask.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_count,
        HumanReadTaskRound.round_id,
        HumanReadRoundGroup.group_id,
        HumanMarkGroup.group_name,
        HumanMarkGroup.parent_group_id,
        HumanReadTaskRound.round_state,
        HumanGroupMember.user_id,
        UserInfo.name,
        # 使用coalesce处理NULL值
        func.coalesce(stats_subquery.c.reviewed_count, 0).label('reviewed_count'),
        func.coalesce(stats_subquery.c.average_score, 0).label('average_score'),
        func.coalesce(stats_subquery.c.average_speed1, 0).label('average_speed1'),
        func.coalesce(stats_subquery.c.average_speed2, 0).label('average_speed2'),
        func.coalesce(stats_subquery.c.min_speed, 0).label('min_speed'),
        func.coalesce(stats_subquery.c.max_speed, 0).label('max_speed'),
        func.coalesce(stats_subquery.c.min_score, 0).label('min_score'),
        func.coalesce(stats_subquery.c.max_score, 0).label('max_score')
    ).select_from(HumanReadTask) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanReadRoundGroup, HumanReadRoundGroup.round_id == HumanReadTaskRound.round_id) \
        .outerjoin(HumanMarkGroup, HumanMarkGroup.group_id == HumanReadRoundGroup.group_id) \
        .join(HumanGroupMember, and_(HumanGroupMember.group_id == HumanMarkGroup.group_id, HumanGroupMember.member_role == 2)) \
        .outerjoin(UserInfo, UserInfo.user_id == HumanGroupMember.user_id) \
        .outerjoin(stats_subquery,
                   and_(
                       stats_subquery.c.task_id == HumanReadTask.task_id,
                       stats_subquery.c.round_count == HumanReadTaskRound.round_count,
                       stats_subquery.c.group_id == HumanMarkGroup.group_id,
                       stats_subquery.c.user_id == HumanGroupMember.user_id
                   )).distinct()

    # 动态构建筛选条件
    conditions = []

    # 基础条件
    if query.task_type:
        conditions.append(HumanReadTask.task_type == query.task_type)
    if query.project_id:
        conditions.append(HumanReadTask.project_id == query.project_id)
    if query.subject_id:
        conditions.append(HumanReadTask.subject_id == query.subject_id)
    if query.task_name:
        conditions.append(HumanReadTask.task_name == query.task_name)
    if query.name:
        conditions.append(UserInfo.name == query.name)
    # 轮次条件
    if query.round_state_list:
        conditions.append(HumanReadTaskRound.round_state.in_(query.round_state_list))
    if query.round_count:
        conditions.append(HumanReadTaskRound.round_count == query.round_count)
    # 统计条件
    if query.ques_group_id:
        conditions.append(HumanMarkGroup.parent_group_id == query.ques_group_id)
    if query.group_id:
        conditions.append(HumanMarkGroup.group_id == query.group_id)

    # 应用筛选条件
    if conditions:
        main_query = main_query.filter(and_(*conditions))

    if page_size == -1:
        if task_id_list and round_id_list:
            combined_conditions = and_(
                HumanReadTask.task_id.in_(task_id_list),
                HumanReadTaskRound.round_id.in_(round_id_list)
            )
            if conditions:
                main_query = main_query.filter(and_(*conditions, combined_conditions))
            else:
                main_query = main_query.filter(combined_conditions)
        else:
            if conditions:
                main_query = main_query.filter(and_(*conditions))
        # 获取总数
        count_query = select(func.count()).select_from(main_query.subquery())
        total = new_session.execute(count_query).scalar()
        # 获取数据
        result = new_session.execute(main_query).all()
    else:
        # 分页模式
        # 先获取去重后的记录数
        distinct_query = main_query.with_only_columns(
            func.distinct(func.concat(HumanReadTask.task_id, '-', UserInfo.name)).label('task_user')
        ).subquery()
        total = new_session.execute(select(func.count()).select_from(distinct_query)).scalar()

        # 获取数据
        limit = current_page - 1
        offset = limit * page_size
        result = new_session.execute(
            main_query.order_by(HumanReadTask.task_name.desc()).limit(page_size).offset(offset)
        ).all()

    # 构建返回数据
    data_list = []
    for row in result:
        ques_group_name = row.group_name
        ques_group_id_result = row.group_id
        if row.group_id:
            group_info = new_session.query(HumanMarkGroup.parent_group_id).filter(
                HumanMarkGroup.group_id == row.group_id
            ).first()

            if group_info and group_info.parent_group_id:
                ques_group = new_session.query(HumanMarkGroup.group_name, HumanMarkGroup.group_id).filter(
                    HumanMarkGroup.group_id == group_info.parent_group_id,
                    HumanMarkGroup.group_level == 2
                ).first()
                if ques_group:
                    ques_group_name = ques_group.group_name
                    ques_group_id_result = ques_group.group_id

        data_list.append({
            "project_name": row.project_name,
            "project_id": row.project_id,
            "subject_name": row.subject_name,
            "subject_id": row.subject_id,
            "task_id": row.task_id,
            "task_name": row.task_name,
            "round_count": row.round_count,
            "round_id": row.round_id,
            "name": row.name,
            "user_id": row.user_id,
            "max_speed": round_half_up(float(row.max_speed), 2) if row.max_speed and row.max_speed > 0 else 0,
            "min_speed": round_half_up(float(row.min_speed), 2) if row.min_speed and row.min_speed > 0 else 0,
            "max_score": round_half_up(float(row.max_score), 2) if row.max_score and row.max_score > 0 else 0,
            "min_score": round_half_up(float(row.min_score), 2) if row.min_score and row.min_score > 0 else 0,
            "ques_group_name": ques_group_name,
            "ques_group_id": ques_group_id_result,
            "group_name": row.group_name,
            "group_id": row.group_id,
            "round_state": row.round_state,
            "reviewed_count": int(row.reviewed_count),
            "average_score": round_half_up(float(row.average_score), 2) if row.average_score and row.average_score > 0 else 0,
            "average_speed1": round_half_up(float(row.average_speed1), 2) if row.average_speed1 and row.average_speed1 > 0 else 0,
            "average_speed2": round_half_up(float(row.average_speed2), 2) if row.average_speed2 and row.average_speed2 > 0 else 0,
        })

    return BaseResponse(data={"total": total, "data": data_list}, msg="获取正评监控:评卷员界面成功")


@survey_monitor_router.post(path="/person_survey_monitor_export", response_model=BaseResponse, summary="正评监控:评卷员界面导出")
async def person_survey_monitor_export_api(
        query: PersonSurveyMonitorReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 正评监控:评卷员界面")
    # 解构查询参数
    current_page, page_size, task_type, project_id, subject_id, task_name, round_state_list, round_count, group_id, ques_group_id, name, task_id_list, round_id_list = query.model_dump().values()
    excel_export_service = ExcelExportService()
    # 统计信息子查询（优化条件和表达式）
    stats_subquery = new_session.query(
        HumanStatisticsPerson.task_id,
        HumanStatisticsPerson.round_count,
        HumanStatisticsPerson.group_id,
        HumanStatisticsPerson.user_id,
        # 已阅量统计
        func.sum(case((HumanStatisticsPerson.statistics_type == 4, HumanStatisticsPerson.statistics_result_1), else_=0)).label('reviewed_count'),
        # 平均数
        func.avg(case((HumanStatisticsPerson.statistics_type == 14, HumanStatisticsPerson.statistics_result_1), else_=None)).label('average_score'),
        # 平均速度（份/时）
        (func.sum(case((HumanStatisticsPerson.statistics_type == 4, HumanStatisticsPerson.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsPerson.statistics_type == 10, HumanStatisticsPerson.statistics_result_1), else_=0)), 0) * 3600).label('average_speed1'),

        # 平均速度（秒/份）
        (func.sum(case((HumanStatisticsPerson.statistics_type == 10, HumanStatisticsPerson.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsPerson.statistics_type == 4, HumanStatisticsPerson.statistics_result_1), else_=0)), 0)).label('average_speed2'),
        # 最短速度（秒/份）
        func.min(case((HumanStatisticsPerson.statistics_type == 16, HumanStatisticsPerson.statistics_result_1), else_=None)).label('min_speed'),
        # 最长速度（秒/份）
        func.max(case((HumanStatisticsPerson.statistics_type == 15, HumanStatisticsPerson.statistics_result_1), else_=0)).label('max_speed'),
        func.min(case((HumanStatisticsPerson.statistics_type == 3, HumanStatisticsPerson.statistics_result_1), else_=None)).label('min_score'),
        func.max(case((HumanStatisticsPerson.statistics_type == 2, HumanStatisticsPerson.statistics_result_1), else_=0)).label('max_score')
    ).group_by(
        HumanStatisticsPerson.task_id,
        HumanStatisticsPerson.round_count,
        HumanStatisticsPerson.group_id,
        HumanStatisticsPerson.user_id
    ).subquery()

    # 构建最终查询（优化JOIN条件和字段引用）
    main_query = select(
        HumanReadTask.project_id,
        Project.project_name,
        HumanReadTask.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_count,
        HumanReadTaskRound.round_id,
        HumanReadRoundGroup.group_id,
        HumanMarkGroup.group_name,
        HumanMarkGroup.parent_group_id,
        HumanReadTaskRound.round_state,
        HumanGroupMember.user_id,
        UserInfo.name,
        # 使用coalesce处理NULL值
        func.coalesce(stats_subquery.c.reviewed_count, 0).label('reviewed_count'),
        func.coalesce(stats_subquery.c.average_score, 0).label('average_score'),
        func.coalesce(stats_subquery.c.average_speed1, 0).label('average_speed1'),
        func.coalesce(stats_subquery.c.average_speed2, 0).label('average_speed2'),
        func.coalesce(stats_subquery.c.min_speed, 0).label('min_speed'),
        func.coalesce(stats_subquery.c.max_speed, 0).label('max_speed'),
        func.coalesce(stats_subquery.c.min_score, 0).label('min_score'),
        func.coalesce(stats_subquery.c.max_score, 0).label('max_score')

    ).select_from(HumanReadTask) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanReadRoundGroup, HumanReadRoundGroup.round_id == HumanReadTaskRound.round_id) \
        .outerjoin(HumanMarkGroup, HumanMarkGroup.group_id == HumanReadRoundGroup.group_id) \
        .join(HumanGroupMember, and_(HumanGroupMember.group_id == HumanMarkGroup.group_id, HumanGroupMember.member_role == 2)) \
        .outerjoin(UserInfo, UserInfo.user_id == HumanGroupMember.user_id) \
        .outerjoin(stats_subquery,
                   and_(
                       stats_subquery.c.task_id == HumanReadTask.task_id,
                       stats_subquery.c.round_count == HumanReadTaskRound.round_count,
                       stats_subquery.c.group_id == HumanMarkGroup.group_id,
                       stats_subquery.c.user_id == HumanGroupMember.user_id
                   )).distinct()
    # 动态构建筛选条件
    conditions = []

    # 基础条件
    if query.task_type:
        conditions.append(HumanReadTask.task_type == query.task_type)
    if query.project_id:
        conditions.append(HumanReadTask.project_id == query.project_id)
    if query.subject_id:
        conditions.append(HumanReadTask.subject_id == query.subject_id)
    if query.task_name:
        conditions.append(HumanReadTask.task_name == query.task_name)
    if query.name:
        conditions.append(UserInfo.name == query.name)
    # 轮次条件
    if query.round_state_list:
        conditions.append(HumanReadTaskRound.round_state.in_(query.round_state_list))
    if query.round_count:
        conditions.append(HumanReadTaskRound.round_count == query.round_count)
    # 统计条件
    if query.ques_group_id:
        conditions.append(HumanMarkGroup.parent_group_id == query.ques_group_id)
    if query.group_id:
        conditions.append(HumanMarkGroup.group_id == query.group_id)

    # 应用筛选条件
    if conditions:
        main_query = main_query.filter(and_(*conditions))

    if page_size == -1:
        if task_id_list and round_id_list:
            combined_conditions = and_(
                HumanReadTask.task_id.in_(task_id_list),
                HumanReadTaskRound.round_id.in_(round_id_list)
            )
            if conditions:
                main_query = main_query.filter(and_(*conditions, combined_conditions))
            else:
                main_query = main_query.filter(combined_conditions)
        else:
            if conditions:
                main_query = main_query.filter(and_(*conditions))
        # 获取总数
        count_query = select(func.count()).select_from(main_query.subquery())
        total = new_session.execute(count_query).scalar()
        # 获取数据
        result = new_session.execute(main_query).all()
    else:
        # 分页模式
        # 先获取去重后的记录数
        distinct_query = main_query.with_only_columns(
            func.distinct(func.concat(HumanReadTask.task_id, '-', UserInfo.name)).label('task_user')
        ).subquery()
        total = new_session.execute(select(func.count()).select_from(distinct_query)).scalar()

        # 获取数据
        limit = current_page - 1
        offset = limit * page_size
        result = new_session.execute(
            main_query.order_by(HumanReadTask.task_name.desc()).limit(page_size).offset(offset)
        ).all()

    # 构建返回数据
    data_list = []
    for row in result:
        ques_group_name = row.group_name
        ques_group_id_result = row.group_id
        if row.group_id:
            group_info = new_session.query(HumanMarkGroup.parent_group_id).filter(
                HumanMarkGroup.group_id == row.group_id
            ).first()

            if group_info and group_info.parent_group_id:
                ques_group = new_session.query(HumanMarkGroup.group_name, HumanMarkGroup.group_id).filter(
                    HumanMarkGroup.group_id == group_info.parent_group_id,
                    HumanMarkGroup.group_level == 2
                ).first()
                if ques_group:
                    ques_group_name = ques_group.group_name
                    ques_group_id_result = ques_group.group_id

        data_list.append({
            "资格": row.project_name,
            "科目": row.subject_name,
            "任务名称": row.task_name,
            "所属题组": ques_group_name,
            "小组": row.group_name,
            "评阅员": row.name,
            "已阅量": int(row.reviewed_count),
            "平均分": round_half_up(float(row.average_score), 2) if row.average_score and row.average_score > 0 else 0,
            "最长评分时间（秒）": round_half_up(float(row.max_speed), 2) if row.max_speed and row.max_speed > 0 else 0,
            "最短评分时间（秒）": round_half_up(float(row.min_speed), 2) if row.min_speed and row.min_speed > 0 else 0,
            "最高分": round_half_up(float(row.max_score), 2) if row.max_score and row.max_score > 0 else 0,
            "最低分": round_half_up(float(row.min_score), 2) if row.min_score and row.min_score > 0 else 0,
            "平均速度（份/时）": round_half_up(float(row.average_speed1), 2) if row.average_speed1 and row.average_speed1 > 0 else 0,
            "平均速度（秒/份）": round_half_up(float(row.average_speed2), 2) if row.average_speed2 and row.average_speed2 > 0 else 0,
        })

    # 当page_size == -1时，导出数据到Excel
    if page_size == -1:
        return await excel_export_service.export_to_excel_stream(
            data=data_list,
            file_name="评阅员监控.xlsx"
        )
    else:
        return BaseResponse(data={"total": total, "data": data_list}, msg="获取正评监控:评卷员界面成功")


@survey_monitor_router.post(path="/group_survey_monitor", response_model=BaseResponse, summary="正评监控:小组监控")
async def group_survey_monitor_api(
        query: GroupSurveyMonitorReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 正评监控:小组监控")
    current_page, page_size, task_type, project_id, subject_id, task_name, round_state_list, round_count, group_id, ques_group_id, task_id_list, round_id_list = query.model_dump().values()
    # 统计信息子查询（优化条件和表达式）
    stats_subquery = new_session.query(
        HumanStatisticsSmallGroup.task_id,
        HumanStatisticsSmallGroup.round_count,
        HumanStatisticsSmallGroup.group_id,
        # 已阅量统计
        func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('reviewed_count'),
        func.avg(case((HumanStatisticsSmallGroup.statistics_type == 14, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('average_score'),
        # 平均速度（份/时）
        (func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsSmallGroup.statistics_type == 10, HumanStatisticsSmallGroup.statistics_result_1), else_=0)), 0) * 3600).label('average_speed1'),
        # 平均速度（秒/份）
        (func.sum(case((HumanStatisticsSmallGroup.statistics_type == 10, HumanStatisticsSmallGroup.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)), 0)).label('average_speed2'),
        # 最短速度（秒/份）
        func.min(case((HumanStatisticsSmallGroup.statistics_type == 16, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('min_speed'),
        # 最长速度（秒/份）
        func.max(case((HumanStatisticsSmallGroup.statistics_type == 15, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('max_speed'),
        func.min(case((HumanStatisticsSmallGroup.statistics_type == 3, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('min_score'),
        func.max(case((HumanStatisticsSmallGroup.statistics_type == 2, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('max_score')
    ).group_by(
        HumanStatisticsSmallGroup.task_id,
        HumanStatisticsSmallGroup.round_count,
        HumanStatisticsSmallGroup.group_id
    ).subquery()

    # 构建最终查询（优化JOIN条件和字段引用）
    final_stmt = select(
        HumanReadTask.project_id,
        Project.project_name,
        HumanReadTask.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_count,
        HumanReadTaskRound.round_id,
        HumanMarkGroup.group_id,
        HumanMarkGroup.group_name,
        HumanMarkGroup.parent_group_id,
        HumanReadTaskRound.round_state,
        # 使用coalesce处理NULL值
        func.coalesce(stats_subquery.c.reviewed_count, 0).label('reviewed_count'),
        func.coalesce(stats_subquery.c.average_score, 0).label('average_score'),
        func.coalesce(stats_subquery.c.average_speed1, 0).label('average_speed1'),
        func.coalesce(stats_subquery.c.average_speed2, 0).label('average_speed2'),
        func.coalesce(stats_subquery.c.min_speed, 0).label('min_speed'),
        func.coalesce(stats_subquery.c.max_speed, 0).label('max_speed'),
        func.coalesce(stats_subquery.c.min_score, 0).label('min_score'),
        func.coalesce(stats_subquery.c.max_score, 0).label('max_score')
    ).select_from(HumanReadTask) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanPersonDistriAnswer, HumanPersonDistriAnswer.round_id == HumanReadTaskRound.round_id) \
        .outerjoin(HumanMarkGroup, HumanMarkGroup.group_id == HumanPersonDistriAnswer.group_id) \
        .outerjoin(stats_subquery,
                   and_(
                       stats_subquery.c.task_id == HumanReadTask.task_id,
                       stats_subquery.c.round_count == HumanReadTaskRound.round_count,
                       stats_subquery.c.group_id == HumanPersonDistriAnswer.group_id
                   ))

    # 动态构建筛选条件
    conditions = []

    # 基础条件
    if query.task_type:
        conditions.append(HumanReadTask.task_type == query.task_type)
    if query.project_id:
        conditions.append(HumanReadTask.project_id == query.project_id)
    if query.subject_id:
        conditions.append(HumanReadTask.subject_id == query.subject_id)
    if query.task_name:
        conditions.append(HumanReadTask.task_name == query.task_name)

    # 轮次条件
    if query.round_state_list:
        conditions.append(HumanReadTaskRound.round_state.in_(query.round_state_list))
    if query.round_count:
        conditions.append(HumanReadTaskRound.round_count == query.round_count)
    # 统计条件
    if query.ques_group_id:
        conditions.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)
    if query.group_id:
        conditions.append(HumanMarkGroup.group_id == query.group_id)

    # 分页模式条件
    if page_size == -1:
        if task_id_list and round_id_list:
            combined_conditions = and_(
                HumanReadTask.task_id.in_(task_id_list),
                HumanReadTaskRound.round_id.in_(round_id_list)
            )
            if conditions:
                final_stmt = final_stmt.filter(and_(*conditions, combined_conditions))
            else:
                final_stmt = final_stmt.filter(combined_conditions)
        else:
            if conditions:
                final_stmt = final_stmt.filter(and_(*conditions))
        # 获取总数
        count_query = select(func.count()).select_from(final_stmt.subquery())
        total = new_session.execute(count_query).scalar()
        # 获取数据
        result = new_session.execute(final_stmt.distinct()).all()
    else:
        limit = current_page - 1
        offset = limit * page_size

        subq = final_stmt.filter(and_(*conditions)).with_only_columns(func.distinct(func.concat(HumanReadTask.task_id, '-', HumanMarkGroup.group_id)).label('task_group')).subquery()
        total = new_session.execute(
            select(func.count()).select_from(subq)
        ).scalar()
        result = new_session.execute(final_stmt.filter(and_(*conditions)).order_by(HumanReadTask.task_name.desc()).distinct().limit(page_size).offset(offset)).all()

    # 构建返回数据
    data_list = []
    for row in result:
        ques_group_name = row.group_name
        ques_group_id_result = row.group_id

        if row.group_id:
            group_info = new_session.query(HumanMarkGroup.parent_group_id).filter(
                HumanMarkGroup.group_id == row.group_id
            ).first()

            if group_info and group_info.parent_group_id:
                ques_group = new_session.query(HumanMarkGroup.group_name, HumanMarkGroup.group_id).filter(
                    HumanMarkGroup.group_id == group_info.parent_group_id,
                    HumanMarkGroup.group_level == 2
                ).first()
                if ques_group:
                    ques_group_name = ques_group.group_name
                    ques_group_id_result = ques_group.group_id

        data_list.append({
            "project_name": row.project_name,
            "project_id": row.project_id,
            "subject_name": row.subject_name,
            "subject_id": row.subject_id,
            "task_id": row.task_id,
            "task_name": row.task_name,
            "round_count": row.round_count,
            "round_id": row.round_id,
            "ques_group_name": ques_group_name,
            "ques_group_id": ques_group_id_result,
            "group_name": row.group_name,
            "group_id": row.group_id,
            "round_state": row.round_state,
            "reviewed_count": int(row.reviewed_count),
            "average_score": round_half_up(float(row.average_score), 2) if row.average_score and row.average_score > 0 else 0,
            "average_speed1": round_half_up(float(row.average_speed1), 2) if row.average_speed1 and row.average_speed1 > 0 else 0,
            "average_speed2": round_half_up(float(row.average_speed2), 2) if row.average_speed2 and row.average_speed2 > 0 else 0,
            "max_speed": round_half_up(float(row.max_speed), 2) if row.max_speed and row.max_speed > 0 else 0,
            "min_speed": round_half_up(float(row.min_speed), 2) if row.min_speed and row.min_speed > 0 else 0,
            "max_score": round_half_up(float(row.max_score), 2) if row.max_score and row.max_score > 0 else 0,
            "min_score": round_half_up(float(row.min_score), 2) if row.min_score and row.min_score > 0 else 0
        })
    return BaseResponse(data={"total": total, "data": data_list}, msg="获取正评监控:小组监控成功")


@survey_monitor_router.post(path="/group_survey_monitor_export", response_model=BaseResponse, summary="正评监控:小组监控导出")
async def group_survey_monitor_export_api(
        query: GroupSurveyMonitorReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 正评监控:小组监控导出")
    current_page, page_size, task_type, project_id, subject_id, task_name, round_state_list, round_count, group_id, ques_group_id, task_id_list, round_id_list = query.model_dump().values()
    excel_export_service = ExcelExportService()
    # 统计信息子查询（优化条件和表达式）
    stats_subquery = new_session.query(
        HumanStatisticsSmallGroup.task_id,
        HumanStatisticsSmallGroup.round_count,
        HumanStatisticsSmallGroup.group_id,
        # 已阅量统计
        func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('reviewed_count'),
        func.avg(case((HumanStatisticsSmallGroup.statistics_type == 14, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('average_score'),
        # 平均速度（份/时）
        (func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsSmallGroup.statistics_type == 10, HumanStatisticsSmallGroup.statistics_result_1), else_=0)), 0) * 3600).label('average_speed1'),

        # 平均速度（秒/份）
        (func.sum(case((HumanStatisticsSmallGroup.statistics_type == 10, HumanStatisticsSmallGroup.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)), 0)).label('average_speed2'),
        # 最短速度（秒/份）
        func.min(case((HumanStatisticsSmallGroup.statistics_type == 16, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('min_speed'),
        # 最长速度（秒/份）
        func.max(case((HumanStatisticsSmallGroup.statistics_type == 15, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('max_speed'),
        func.min(case((HumanStatisticsSmallGroup.statistics_type == 3, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('min_score'),
        func.max(case((HumanStatisticsSmallGroup.statistics_type == 2, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('max_score')
    ).group_by(
        HumanStatisticsSmallGroup.task_id,
        HumanStatisticsSmallGroup.round_count,
        HumanStatisticsSmallGroup.group_id
    ).subquery()

    # 构建最终查询（优化JOIN条件和字段引用）
    final_stmt = select(
        HumanReadTask.project_id,
        Project.project_name,
        HumanReadTask.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_count,
        HumanReadTaskRound.round_id,
        HumanMarkGroup.group_id,
        HumanMarkGroup.group_name,
        HumanMarkGroup.parent_group_id,
        HumanReadTaskRound.round_state,
        # 使用coalesce处理NULL值
        func.coalesce(stats_subquery.c.reviewed_count, 0).label('reviewed_count'),
        func.coalesce(stats_subquery.c.average_score, 0).label('average_score'),
        func.coalesce(stats_subquery.c.average_speed1, 0).label('average_speed1'),
        func.coalesce(stats_subquery.c.average_speed2, 0).label('average_speed2'),
        func.coalesce(stats_subquery.c.min_speed, 0).label('min_speed'),
        func.coalesce(stats_subquery.c.max_speed, 0).label('max_speed'),
        func.coalesce(stats_subquery.c.min_score, 0).label('min_score'),
        func.coalesce(stats_subquery.c.max_score, 0).label('max_score')
    ).select_from(HumanReadTask) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanPersonDistriAnswer, HumanPersonDistriAnswer.round_id == HumanReadTaskRound.round_id) \
        .outerjoin(HumanMarkGroup, HumanMarkGroup.group_id == HumanPersonDistriAnswer.group_id) \
        .outerjoin(stats_subquery,
                   and_(
                       stats_subquery.c.task_id == HumanReadTask.task_id,
                       stats_subquery.c.round_count == HumanReadTaskRound.round_count,
                       stats_subquery.c.group_id == HumanPersonDistriAnswer.group_id
                   ))

    # 动态构建筛选条件
    conditions = []

    # 基础条件
    if query.task_type:
        conditions.append(HumanReadTask.task_type == query.task_type)
    if query.project_id:
        conditions.append(HumanReadTask.project_id == query.project_id)
    if query.subject_id:
        conditions.append(HumanReadTask.subject_id == query.subject_id)
    if query.task_name:
        conditions.append(HumanReadTask.task_name == query.task_name)

    # 轮次条件
    if query.round_state_list:
        conditions.append(HumanReadTaskRound.round_state.in_(query.round_state_list))
    if query.round_count:
        conditions.append(HumanReadTaskRound.round_count == query.round_count)
    # 统计条件
    if query.ques_group_id:
        conditions.append(HumanStatisticsPerson.ques_group_id == query.ques_group_id)
    if query.group_id:
        conditions.append(HumanMarkGroup.group_id == query.group_id)

    # 分页模式条件
    if page_size == -1:
        if task_id_list and round_id_list:
            combined_conditions = and_(
                HumanReadTask.task_id.in_(task_id_list),
                HumanReadTaskRound.round_id.in_(round_id_list)
            )
            if conditions:
                final_stmt = final_stmt.filter(and_(*conditions, combined_conditions))
            else:
                final_stmt = final_stmt.filter(combined_conditions)
        else:
            if conditions:
                final_stmt = final_stmt.filter(and_(*conditions))
        # 获取总数
        count_query = select(func.count()).select_from(final_stmt.subquery())
        total = new_session.execute(count_query).scalar()
        # 获取数据
        result = new_session.execute(final_stmt.distinct()).all()
    else:
        limit = current_page - 1
        offset = limit * page_size

        subq = final_stmt.filter(and_(*conditions)).with_only_columns(func.distinct(func.concat(HumanReadTask.task_id, '-', HumanMarkGroup.group_id)).label('task_group')).subquery()
        total = new_session.execute(
            select(func.count()).select_from(subq)
        ).scalar()
        result = new_session.execute(final_stmt.filter(and_(*conditions)).order_by(HumanReadTask.task_name.desc()).distinct().limit(page_size).offset(offset)).all()

    # 构建返回数据
    data_list = []
    for row in result:
        ques_group_name = row.group_name
        ques_group_id_result = row.group_id

        if row.group_id:
            group_info = new_session.query(HumanMarkGroup.parent_group_id).filter(
                HumanMarkGroup.group_id == row.group_id
            ).first()

            if group_info and group_info.parent_group_id:
                ques_group = new_session.query(HumanMarkGroup.group_name, HumanMarkGroup.group_id).filter(
                    HumanMarkGroup.group_id == group_info.parent_group_id,
                    HumanMarkGroup.group_level == 2
                ).first()
                if ques_group:
                    ques_group_name = ques_group.group_name
                    ques_group_id_result = ques_group.group_id

        data_list.append({
            "资格": row.project_name,
            "科目": row.subject_name,
            "任务名称": row.task_name,
            "所属题组": ques_group_name,
            "小组": row.group_name,
            "已阅量": int(row.reviewed_count),
            "平均分": round_half_up(float(row.average_score), 2) if row.average_score and row.average_score > 0 else 0,
            "最长评分时间（秒）": round_half_up(float(row.max_speed), 2) if row.max_speed and row.max_speed > 0 else 0,
            "最短评分时间（秒）": round_half_up(float(row.min_speed), 2) if row.min_speed and row.min_speed > 0 else 0,
            "最高分": round_half_up(float(row.max_score), 2) if row.max_score and row.max_score > 0 else 0,
            "最低分": round_half_up(float(row.min_score), 2) if row.min_score and row.min_score > 0 else 0,
            "平均速度（份/时）": round_half_up(float(row.average_speed1), 2) if row.average_speed1 and row.average_speed1 > 0 else 0,
            "平均速度（秒/份）": round_half_up(float(row.average_speed2), 2) if row.average_speed2 and row.average_speed2 > 0 else 0,
        })

    # 当page_size == -1时，导出数据到Excel
    if page_size == -1:
        return await excel_export_service.export_to_excel_stream(
            data=data_list,
            file_name="小组监控.xlsx"
        )
    else:
        return BaseResponse(data={"total": total, "data": data_list}, msg="获取正评监控:小组监控导出成功")


@survey_monitor_router.post(path="/ques_group_survey_monitor", response_model=BaseResponse, summary="正评监控:题组监控")
async def ques_group_survey_monitor_api(
        query: QGroupSurveyMonitorReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 正评监控:题组监控")
    current_page, page_size, task_type, project_id, subject_id, task_name, round_state_list, round_count, ques_group_id, task_id_list, round_id_list = query.model_dump().values()
    # 统计信息子查询（优化条件和表达式）
    stats_subquery = new_session.query(
        HumanStatisticsSmallGroup.task_id,
        HumanStatisticsSmallGroup.round_count,
        HumanStatisticsSmallGroup.ques_group_id,
        # 已阅量统计
        func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('reviewed_count'),
        func.avg(case((HumanStatisticsSmallGroup.statistics_type == 14, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('average_score'),
        # 平均速度（份/时）
        (func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsSmallGroup.statistics_type == 10, HumanStatisticsSmallGroup.statistics_result_1), else_=0)), 0) * 3600).label('average_speed1'),

        # 平均速度（秒/份）
        (func.sum(case((HumanStatisticsSmallGroup.statistics_type == 10, HumanStatisticsSmallGroup.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)), 0)).label('average_speed2'),
        # 最短速度（秒/份）
        func.min(case((HumanStatisticsSmallGroup.statistics_type == 16, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('min_speed'),
        # 最长速度（秒/份）
        func.max(case((HumanStatisticsSmallGroup.statistics_type == 15, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('max_speed'),
        func.min(case((HumanStatisticsSmallGroup.statistics_type == 3, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('min_score'),
        func.max(case((HumanStatisticsSmallGroup.statistics_type == 2, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('max_score')
    ).group_by(
        HumanStatisticsSmallGroup.task_id,
        HumanStatisticsSmallGroup.round_count,
        HumanStatisticsSmallGroup.ques_group_id
    ).subquery()

    # 总量统计子查询
    total_subquery = new_session.query(
        HumanReadTask.task_id,
        (func.count(HumanRoundDistriAnswer.round_id.distinct()) *
         func.count(HumanRoundDistriAnswer.stu_secret_num.distinct())).label('total_count')
    ).join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanRoundDistriAnswer, HumanRoundDistriAnswer.round_id == HumanReadTaskRound.round_id) \
        .group_by(HumanReadTask.task_id).subquery()

    # 构建最终查询（优化JOIN条件和字段引用）
    final_stmt = select(
        HumanReadTask.project_id,
        Project.project_name,
        HumanReadTask.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_count,
        HumanReadTaskRound.round_id,
        HumanMarkGroup.group_id,
        HumanMarkGroup.group_name,
        HumanReadTaskRound.round_state,
        # 使用coalesce处理NULL值
        func.coalesce(stats_subquery.c.reviewed_count, 0).label('reviewed_count'),
        func.coalesce(stats_subquery.c.average_score, 0).label('average_score'),
        func.coalesce(stats_subquery.c.average_speed1, 0).label('average_speed1'),
        func.coalesce(stats_subquery.c.average_speed2, 0).label('average_speed2'),
        func.coalesce(stats_subquery.c.min_speed, 0).label('min_speed'),
        func.coalesce(stats_subquery.c.max_speed, 0).label('max_speed'),
        func.coalesce(stats_subquery.c.min_score, 0).label('min_score'),
        func.coalesce(stats_subquery.c.max_score, 0).label('max_score'),
        func.coalesce(total_subquery.c.total_count, 0).label('total_count'),
        (func.coalesce(total_subquery.c.total_count, 0) -
         func.coalesce(stats_subquery.c.reviewed_count, 0)).label('unreviewed_count')
    ).select_from(HumanReadTask) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanMarkGroup, and_(HumanMarkGroup.ques_code == HumanReadTask.ques_code, HumanMarkGroup.group_level == 2)) \
        .outerjoin(stats_subquery,
                   and_(
                       stats_subquery.c.task_id == HumanReadTask.task_id,
                       stats_subquery.c.round_count == HumanReadTaskRound.round_count,
                   )) \
        .outerjoin(total_subquery, total_subquery.c.task_id == HumanReadTask.task_id)

    # 动态构建筛选条件
    conditions = []

    # 基础条件
    if query.task_type:
        conditions.append(HumanReadTask.task_type == query.task_type)
    if query.project_id:
        conditions.append(HumanReadTask.project_id == query.project_id)
    if query.subject_id:
        conditions.append(HumanReadTask.subject_id == query.subject_id)
    if query.task_name:
        conditions.append(HumanReadTask.task_name == query.task_name)

    # 轮次条件
    if query.round_state_list:
        conditions.append(HumanReadTaskRound.round_state.in_(query.round_state_list))
    if query.round_count:
        conditions.append(HumanReadTaskRound.round_count == query.round_count)
    # 统计条件
    if query.ques_group_id:
        conditions.append(HumanMarkGroup.group_id == query.ques_group_id)

    # 分页模式条件
    if page_size == -1:
        if task_id_list and round_id_list:
            combined_conditions = and_(
                HumanReadTask.task_id.in_(task_id_list),
                HumanReadTaskRound.round_id.in_(round_id_list)
            )
            if conditions:
                final_stmt = final_stmt.filter(and_(*conditions, combined_conditions))
            else:
                final_stmt = final_stmt.filter(combined_conditions)
        else:
            if conditions:
                final_stmt = final_stmt.filter(and_(*conditions))
        # 获取总数
        count_query = select(func.count()).select_from(final_stmt.subquery())
        total = new_session.execute(count_query).scalar()
        # 获取数据
        result = new_session.execute(final_stmt.distinct()).all()
    else:
        limit = current_page - 1
        offset = limit * page_size

        subq = final_stmt.filter(and_(*conditions)).with_only_columns(func.distinct(func.concat(HumanReadTask.task_id, '-', HumanMarkGroup.group_id)).label('task_group')).subquery()
        total = new_session.execute(
            select(func.count()).select_from(subq)
        ).scalar()
        result = new_session.execute(final_stmt.filter(and_(*conditions)).order_by(HumanReadTask.task_name.desc()).distinct().limit(page_size).offset(offset)).all()

    # 构建返回数据
    data_list = [{
        "project_name": row.project_name,
        "project_id": row.project_id,
        "subject_name": row.subject_name,
        "subject_id": row.subject_id,
        "task_id": row.task_id,
        "task_name": row.task_name,
        "round_count": row.round_count,
        "round_id": row.round_id,
        "ques_group_name": row.group_name,
        "ques_group_id": row.group_id,
        "round_state": row.round_state,
        "total_count": max(0, int(row.total_count)),
        "reviewed_count": int(row.reviewed_count),
        "unreviewed_count": max(0, int(row.total_count) - int(row.reviewed_count)),
        "rate": round_half_up(float(row.reviewed_count) / float(row.total_count) * 100 if row.total_count and row.total_count > 0 else 0, 2),
        "average_score": round_half_up(float(row.average_score), 2) if row.average_score and row.average_score > 0 else 0,
        "average_speed1": round_half_up(float(row.average_speed1), 2) if row.average_speed1 and row.average_speed1 > 0 else 0,  # 平均速度（份/时）
        "average_speed2": round_half_up(float(row.average_speed2), 2) if row.average_speed2 and row.average_speed2 > 0 else 0,  # 平均速度（秒/份）
        "max_speed": round_half_up(float(row.max_speed), 2) if row.max_speed and row.max_speed > 0 else 0,
        "min_speed": round_half_up(float(row.min_speed), 2) if row.min_speed and row.min_speed > 0 else 0,
        "max_score": round_half_up(float(row.max_score), 2) if row.max_score and row.max_score > 0 else 0,
        "min_score": round_half_up(float(row.min_score), 2) if row.min_score and row.min_score > 0 else 0,
        "time": round_half_up(float(row.unreviewed_count) / float(row.average_speed1) if row.average_speed1 and row.average_speed1 > 0 else 0, 2),
    } for row in result]
    return BaseResponse(data={"total": total, "data": data_list}, msg="获取正评监控:题组监控成功")


@survey_monitor_router.post(path="/ques_group_survey_monitor_export", response_model=BaseResponse, summary="正评监控:题组监控导出")
async def ques_group_survey_monitor_export_api(
        query: QGroupSurveyMonitorReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 正评监控:题组监控导出")
    current_page, page_size, task_type, project_id, subject_id, task_name, round_state_list, round_count, ques_group_id, task_id_list, round_id_list = query.model_dump().values()
    excel_export_service = ExcelExportService()
    # 统计信息子查询（优化条件和表达式）
    stats_subquery = new_session.query(
        HumanStatisticsSmallGroup.task_id,
        HumanStatisticsSmallGroup.round_count,
        HumanStatisticsSmallGroup.ques_group_id,
        # 已阅量统计
        func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('reviewed_count'),
        func.avg(case((HumanStatisticsSmallGroup.statistics_type == 14, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('average_score'),
        # 平均速度（份/时）
        (func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsSmallGroup.statistics_type == 10, HumanStatisticsSmallGroup.statistics_result_1), else_=0)), 0) * 3600).label('average_speed1'),

        # 平均速度（秒/份）
        (func.sum(case((HumanStatisticsSmallGroup.statistics_type == 10, HumanStatisticsSmallGroup.statistics_result_1), else_=0)
                  ) / func.nullif(func.sum(case((HumanStatisticsSmallGroup.statistics_type == 4, HumanStatisticsSmallGroup.statistics_result_1), else_=0)), 0)).label('average_speed2'),
        # 最短速度（秒/份）
        func.min(case((HumanStatisticsSmallGroup.statistics_type == 16, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('min_speed'),
        # 最长速度（秒/份）
        func.max(case((HumanStatisticsSmallGroup.statistics_type == 15, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('max_speed'),
        func.min(case((HumanStatisticsSmallGroup.statistics_type == 3, HumanStatisticsSmallGroup.statistics_result_1), else_=None)).label('min_score'),
        func.max(case((HumanStatisticsSmallGroup.statistics_type == 2, HumanStatisticsSmallGroup.statistics_result_1), else_=0)).label('max_score')
    ).group_by(
        HumanStatisticsSmallGroup.task_id,
        HumanStatisticsSmallGroup.round_count,
        HumanStatisticsSmallGroup.ques_group_id
    ).subquery()

    # 总量统计子查询
    total_subquery = new_session.query(
        HumanReadTask.task_id,
        (func.count(HumanRoundDistriAnswer.round_id.distinct()) *
         func.count(HumanRoundDistriAnswer.stu_secret_num.distinct())).label('total_count')
    ).join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanRoundDistriAnswer, HumanRoundDistriAnswer.round_id == HumanReadTaskRound.round_id) \
        .group_by(HumanReadTask.task_id).subquery()

    # 构建最终查询（优化JOIN条件和字段引用）
    final_stmt = select(
        HumanReadTask.project_id,
        Project.project_name,
        HumanReadTask.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_count,
        HumanReadTaskRound.round_id,
        HumanMarkGroup.group_id,
        HumanMarkGroup.group_name,
        HumanReadTaskRound.round_state,
        # 使用coalesce处理NULL值
        func.coalesce(stats_subquery.c.reviewed_count, 0).label('reviewed_count'),
        func.coalesce(stats_subquery.c.average_score, 0).label('average_score'),
        func.coalesce(stats_subquery.c.average_speed1, 0).label('average_speed1'),
        func.coalesce(stats_subquery.c.average_speed2, 0).label('average_speed2'),
        func.coalesce(stats_subquery.c.min_speed, 0).label('min_speed'),
        func.coalesce(stats_subquery.c.max_speed, 0).label('max_speed'),
        func.coalesce(stats_subquery.c.min_score, 0).label('min_score'),
        func.coalesce(stats_subquery.c.max_score, 0).label('max_score'),
        func.coalesce(total_subquery.c.total_count, 0).label('total_count'),
        (func.coalesce(total_subquery.c.total_count, 0) -
         func.coalesce(stats_subquery.c.reviewed_count, 0)).label('unreviewed_count')
    ).select_from(HumanReadTask) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanMarkGroup, and_(HumanMarkGroup.ques_code == HumanReadTask.ques_code, HumanMarkGroup.group_level == 2)) \
        .outerjoin(stats_subquery,
                   and_(
                       stats_subquery.c.task_id == HumanReadTask.task_id,
                       stats_subquery.c.round_count == HumanReadTaskRound.round_count,
                   )) \
        .outerjoin(total_subquery, total_subquery.c.task_id == HumanReadTask.task_id)

    # 动态构建筛选条件
    conditions = []

    # 基础条件
    if query.task_type:
        conditions.append(HumanReadTask.task_type == query.task_type)
    if query.project_id:
        conditions.append(HumanReadTask.project_id == query.project_id)
    if query.subject_id:
        conditions.append(HumanReadTask.subject_id == query.subject_id)
    if query.task_name:
        conditions.append(HumanReadTask.task_name == query.task_name)

    # 轮次条件
    if query.round_state_list:
        conditions.append(HumanReadTaskRound.round_state.in_(query.round_state_list))
    if query.round_count:
        conditions.append(HumanReadTaskRound.round_count == query.round_count)
    # 统计条件
    if query.ques_group_id:
        conditions.append(HumanMarkGroup.group_id == query.ques_group_id)

    # 分页模式条件
    if page_size == -1:
        if task_id_list and round_id_list:
            combined_conditions = and_(
                HumanReadTask.task_id.in_(task_id_list),
                HumanReadTaskRound.round_id.in_(round_id_list)
            )
            if conditions:
                final_stmt = final_stmt.filter(and_(*conditions, combined_conditions))
            else:
                final_stmt = final_stmt.filter(combined_conditions)
        else:
            if conditions:
                final_stmt = final_stmt.filter(and_(*conditions))
        # 获取总数
        count_query = select(func.count()).select_from(final_stmt.subquery())
        total = new_session.execute(count_query).scalar()
        # 获取数据
        result = new_session.execute(final_stmt.distinct()).all()
    else:
        limit = current_page - 1
        offset = limit * page_size

        subq = final_stmt.filter(and_(*conditions)).with_only_columns(func.distinct(func.concat(HumanReadTask.task_id, '-', HumanMarkGroup.group_id)).label('task_group')).subquery()
        total = new_session.execute(
            select(func.count()).select_from(subq)
        ).scalar()
        result = new_session.execute(final_stmt.filter(and_(*conditions)).order_by(HumanReadTask.task_name.desc()).distinct().limit(page_size).offset(offset)).all()

    # 构建返回数据
    data_list = [{
        "资格": row.project_name,
        "科目": row.subject_name,
        "任务名称": row.task_name,
        "所属题组": row.group_name,
        "阅卷总量": max(0, int(row.total_count)),
        "已阅量": int(row.reviewed_count),
        "未阅量": max(0, int(row.total_count) - int(row.reviewed_count)),
        "完成率": round_half_up(float(row.reviewed_count) / float(row.total_count) * 100 if row.total_count and row.total_count > 0 else 0, 2),
        "最高分": round_half_up(float(row.max_score), 2) if row.max_score and row.max_score > 0 else 0,
        "最低分": round_half_up(float(row.min_score), 2) if row.min_score and row.min_score > 0 else 0,
        "平均分": round_half_up(float(row.average_score), 2) if row.average_score and row.average_score > 0 else 0,
        "最长评分时间（秒）": round_half_up(float(row.max_speed), 2) if row.max_speed and row.max_speed > 0 else 0,
        "最短评分时间（秒）": round_half_up(float(row.min_speed), 2) if row.min_speed and row.min_speed > 0 else 0,
        "平均速度（份/时）": round_half_up(float(row.average_speed1), 2) if row.average_speed1 and row.average_speed1 > 0 else 0,
        "平均速度（秒/份）": round_half_up(float(row.average_speed2), 2) if row.average_speed2 and row.average_speed2 > 0 else 0,
        "预估时间": round_half_up(float(row.unreviewed_count) / float(row.average_speed1) if row.average_speed1 and row.average_speed1 > 0 else 0, 2),
    } for row in result]

    # 当page_size == -1时，导出数据到Excel
    if page_size == -1:
        return await excel_export_service.export_to_excel_stream(
            data=data_list,
            file_name="题组监控.xlsx"
        )
    else:
        return BaseResponse(data={"total": total, "data": data_list}, msg="获取正评监控:题组监控导出成功")


@survey_monitor_router.post(path="/try_mark_monitor", response_model=BaseResponse, summary="试评监控")
async def try_mark_monitor_api(
        query: TryMarkMonitorReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 试评监控")
    current_page, page_size, task_type, round_count, project_id, subject_id, task_name, name, round_state_list, is_office_mark, try_mark_result, ques_group_id, mark_score_range, task_id_list, round_id_list = query.model_dump().values()
    excel_export_service = ExcelExportService()
    # 主查询：获取用户级别的试评监控数据
    final_stmt = select(
        HumanReadTask.project_id,
        Project.project_name,
        HumanReadTask.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_count,
        HumanReadTaskRound.round_id,
        HumanMarkGroup.group_id,
        HumanMarkGroup.group_name,
        HumanPersonDistriAnswer.user_id,
        UserInfo.name,
        HumanReadTaskRound.round_state,
        # 已阅量统计
        func.count(func.distinct(case((HumanPersonDistriAnswer.is_answer_marked == 1, HumanPersonDistriAnswer.stu_secret_num), else_=None))).label('reviewed_count'),
        # 平均速度（份/时）：已阅量 / (已阅量耗时总和 / 3600)
        (func.count(func.distinct(case((HumanPersonDistriAnswer.is_answer_marked == 1, HumanPersonDistriAnswer.stu_secret_num), else_=None))) /
         func.nullif(func.sum(case((HumanPersonDistriAnswer.is_answer_marked == 1, HumanPersonDistriAnswer.cost_time), else_=0)) / 3600.0, 0)).label('average_speed'),
        # 试评结果统计
        HumanTryMarkResult.is_office_mark,
        HumanTryMarkResult.try_mark_result,
        # 允许的偏差分数
        HumanReadTaskRound.allow_diff_score
    ).select_from(HumanReadTask) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanPersonDistriAnswer, HumanPersonDistriAnswer.round_id == HumanReadTaskRound.round_id) \
        .outerjoin(UserInfo, UserInfo.user_id == HumanPersonDistriAnswer.user_id) \
        .join(HumanMarkGroup, and_(HumanMarkGroup.ques_code == HumanReadTask.ques_code, HumanMarkGroup.group_level == 2)) \
        .outerjoin(HumanTryMarkResult, and_(HumanTryMarkResult.task_id == HumanReadTask.task_id,
                                            HumanTryMarkResult.round_id == HumanReadTaskRound.round_id,
                                            HumanTryMarkResult.user_id == HumanPersonDistriAnswer.user_id)) \
        .group_by(
        HumanReadTask.project_id,
        Project.project_name,
        HumanReadTask.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_count,
        HumanReadTaskRound.round_id,
        HumanMarkGroup.group_id,
        HumanMarkGroup.group_name,
        HumanPersonDistriAnswer.user_id,
        UserInfo.name,
        HumanReadTaskRound.round_state,
        HumanTryMarkResult.is_office_mark,
        HumanTryMarkResult.try_mark_result,
        HumanReadTaskRound.allow_diff_score,
    )

    # 动态构建筛选条件
    conditions = []

    # 基础条件
    if query.task_type:
        conditions.append(HumanReadTask.task_type == query.task_type)
    if query.project_id:
        conditions.append(HumanReadTask.project_id == query.project_id)
    if query.subject_id:
        conditions.append(HumanReadTask.subject_id == query.subject_id)
    if query.task_name:
        conditions.append(HumanReadTask.task_name == query.task_name)
    if query.name:
        conditions.append(UserInfo.name == query.name)
    if query.is_office_mark is not None:
        if query.is_office_mark:
            conditions.append(HumanTryMarkResult.is_office_mark == True)
        else:
            conditions.append(HumanTryMarkResult.is_office_mark.is_(None))
    if query.try_mark_result:
        conditions.append(HumanTryMarkResult.try_mark_result == query.try_mark_result)
    # 轮次条件
    if query.round_state_list:
        conditions.append(HumanReadTaskRound.round_state.in_(query.round_state_list))
    if query.round_count:
        conditions.append(HumanReadTaskRound.round_count == query.round_count)
    # 统计条件
    if query.ques_group_id:
        conditions.append(HumanMarkGroup.group_id == query.ques_group_id)

    # 分页模式条件
    if page_size == -1:
        conditions = and_(HumanReadTask.task_id.in_(task_id_list), HumanReadTaskRound.round_id.in_(round_id_list))
        final_stmt = final_stmt.filter(and_(*conditions)).distinct()
        total = len(final_stmt)
    else:
        limit = current_page - 1
        offset = limit * page_size

        subq = final_stmt.filter(and_(*conditions)).with_only_columns(func.distinct(func.concat(HumanReadTask.task_id, '-', HumanMarkGroup.group_id,'-', HumanPersonDistriAnswer.user_id)).label('task_group')).subquery()
        total = new_session.execute(select(func.count()).select_from(subq)).scalar()
        result = new_session.execute(final_stmt.filter(and_(*conditions)).order_by(HumanReadTask.task_name.desc()).distinct().limit(page_size).offset(offset)).all()

    # 构建返回数据
    data_list = []
    for row in result:
        # 初始化超出允许偏差范围的份数
        exceed_count = 0
        try_query = TryMarkResultReq(
            task_id=row.task_id,
            round_id=row.round_id,
            user_id=row.user_id
        )
        try_mark_result = await try_mark_result_api(query=try_query, user=user, new_session=new_session)
        # 计算超出允许偏差范围的份数
        if try_mark_result.data and "data" in try_mark_result.data and "y3_data" in try_mark_result.data["data"]:
            y3_data = try_mark_result.data["data"]["y3_data"]
            allow_diff_score = row.allow_diff_score if row.allow_diff_score else 0
            exceed_count = 0
            for diff in y3_data:
                if abs(float(diff)) > float(allow_diff_score):
                    exceed_count += 1
        data_list.append({
            "project_name": row.project_name,
            "project_id": row.project_id,
            "subject_name": row.subject_name,
            "subject_id": row.subject_id,
            "task_id": row.task_id,
            "task_name": row.task_name,
            "round_count": row.round_count,
            "round_id": row.round_id,
            "ques_group_name": row.group_name,
            "ques_group_id": row.group_id,
            "user_id": row.user_id,
            "name": row.name,
            "round_state": row.round_state,
            "reviewed_count": int(row.reviewed_count),
            "average_speed": round_half_up(float(row.average_speed), 2) if row.average_speed and row.average_speed > 0 else 0,
            "is_office_mark": row.is_office_mark,
            "try_mark_result": row.try_mark_result,
            "exceed_count": exceed_count
        })

    if query.diff_count:
        min_diff = min(query.diff_count)
        max_diff = max(query.diff_count)
        data_list = [item for item in data_list if min_diff <= item["exceed_count"] <= max_diff]

    # 当page_size == -1时，导出数据到Excel
    if page_size == -1:
        return await excel_export_service.export_to_excel_stream(
            data=data_list,
            file_name="试评监控.xlsx"
        )
    else:
        return BaseResponse(data={"total": total, "data": data_list}, msg="获取试评监控成功")


@survey_monitor_router.post(path="/update_try_mark_result", response_model=BaseResponse, summary="更新试评结果")
async def update_try_mark_result_api(
    query: UpdateTryMarkResultReq,
    user: Any = Depends(get_current_user),
    new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 更新试评结果")
    try:
        # 获取传入的试评结果列表
        try_mark_result_list = query.try_mark_result_list

        # 分离需要更新和创建的数据
        update_data_list = []
        create_data_list = []
        
        # 构建查询条件
        conditions = []
        for item in try_mark_result_list:
            conditions.append(
                and_(
                    HumanTryMarkResult.task_id == item.task_id,
                    HumanTryMarkResult.round_id == item.round_id,
                    HumanTryMarkResult.user_id == item.user_id,
                    HumanTryMarkResult.ques_group_id == item.ques_group_id
                )
            )
        
        # 批量查询已存在的记录
        existing_records = new_session.query(HumanTryMarkResult).filter(or_(*conditions)).all()
        
        # 构建已存在记录的映射
        existing_map = {}
        for record in existing_records:
            key = (record.task_id, record.round_id, record.user_id, record.ques_group_id)
            existing_map[key] = record
        
        # 分离更新和创建数据
        for item in try_mark_result_list:
            key = (item.task_id, item.round_id, item.user_id, item.ques_group_id)
            if key in existing_map:
                # 更新数据
                update_data_list.append({
                    'try_mark_result_id': existing_map[key].try_mark_result_id,
                    'try_mark_result': item.try_mark_result
                })
            else:
                # 创建新数据
                create_data_list.append({
                    'try_mark_result_id': configs.snow_worker.get_id(),
                    'task_id': item.task_id,
                    'round_id': item.round_id,
                    'user_id': item.user_id,
                    'ques_group_id': item.ques_group_id,
                    'try_mark_result': item.try_mark_result
                })
        
        # 批量更新
        if update_data_list:
            new_session.bulk_update_mappings(HumanTryMarkResult, update_data_list)
        
        # 批量创建
        if create_data_list:
            new_session.bulk_insert_mappings(HumanTryMarkResult, create_data_list)
        
        # 提交事务
        new_session.commit()

        return BaseResponse(msg=f"成功更新 {len(update_data_list)} 条试评结果记录，创建 {len(create_data_list)} 条新记录")

    except Exception as e:
        logger.error(f"更新试评结果失败: {str(e)}")
        return BaseResponse(code=response_utils.server_error, msg="更新试评结果失败")



@survey_monitor_router.post(path="/update_is_office_mark", response_model=BaseResponse, summary="更新是否转为正评结果")
async def update_is_office_mark_api(
    query: UpdateTryIsOfficeMarkReq,
    user: Any = Depends(get_current_user),
    new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 更新是否转为正评结果")
    try:
        # 获取传入的试评结果列表
        is_office_mark_list = query.is_office_mark_list

        # 分离需要更新和创建的数据
        update_data_list = []
        create_data_list = []
        
        # 构建查询条件
        conditions = []
        for item in is_office_mark_list:
            conditions.append(
                and_(
                    HumanTryMarkResult.task_id == item.task_id,
                    HumanTryMarkResult.round_id == item.round_id,
                    HumanTryMarkResult.user_id == item.user_id,
                    HumanTryMarkResult.ques_group_id == item.ques_group_id
                )
            )
        
        # 批量查询已存在的记录
        existing_records = new_session.query(HumanTryMarkResult).filter(or_(*conditions)).all()
        
        # 构建已存在记录的映射
        existing_map = {}
        for record in existing_records:
            key = (record.task_id, record.round_id, record.user_id, record.ques_group_id)
            existing_map[key] = record
        
        # 分离更新和创建数据
        for item in is_office_mark_list:
            key = (item.task_id, item.round_id, item.user_id, item.ques_group_id)
            if key in existing_map:
                # 更新数据
                update_data_list.append({
                    'try_mark_result_id': existing_map[key].try_mark_result_id,
                    'is_office_mark': item.is_office_mark
                })
            else:
                # 创建新数据
                create_data_list.append({
                    'try_mark_result_id': configs.snow_worker.get_id(),
                    'task_id': item.task_id,
                    'round_id': item.round_id,
                    'user_id': item.user_id,
                    'ques_group_id': item.ques_group_id,
                    'is_office_mark': item.is_office_mark
                })
        
        # 批量更新
        if update_data_list:
            new_session.bulk_update_mappings(HumanTryMarkResult, update_data_list)
        
        # 批量创建
        if create_data_list:
            new_session.bulk_insert_mappings(HumanTryMarkResult, create_data_list)
        
        # 提交事务
        new_session.commit()

        return BaseResponse(msg=f"成功更新 {len(update_data_list)} 条是否转为正评记录，创建 {len(create_data_list)} 条新记录")

    except Exception as e:
        logger.error(f"更新是否转为正评失败: {str(e)}")
        return BaseResponse(code=response_utils.server_error, msg="更新是否转为正评失败")

@survey_monitor_router.post(path="/try_mark_result", response_model=BaseResponse, summary="获取评阅员的试评结果")
async def try_mark_result_api(
        query: TryMarkResultReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 获取评阅员的试评结果")
    # logger.info(f"请求参数: task_id={query.task_id}, round_id={query.round_id}, user_id={query.user_id}, group_id={query.group_id}")

    # 1. 判断每个ques_code是否都被评分并计算平均分
    all_ques_scored_subquery = new_session.query(
        HumanReadTaskRound.round_id,
        HumanPersonDistriAnswer.ques_code,
        HumanPersonDistriAnswer.stu_secret_num,
        func.count(HumanPersonDistriAnswer.stu_secret_num).label('total_students'),
        func.count(HumanPersonDistriAnswer.mark_score).label('scored_students'),
        (func.count(HumanPersonDistriAnswer.mark_score) == func.count(HumanPersonDistriAnswer.stu_secret_num)).label('all_scored'),
        (func.sum(HumanPersonDistriAnswer.mark_score) / func.count(HumanPersonDistriAnswer.user_id.distinct())).label('avg_score')
    ).join(HumanReadTaskRound, HumanReadTaskRound.round_id == HumanPersonDistriAnswer.round_id) \
        .group_by(HumanReadTaskRound.round_id, HumanPersonDistriAnswer.ques_code, HumanPersonDistriAnswer.stu_secret_num).subquery()

    # 3. 构建最终查询，获取评阅员的试评结果
    final_stmt = select(
        HumanReadTask.task_id,
        HumanReadTaskRound.round_id,
        HumanPersonDistriAnswer.user_id,
        HumanPersonDistriAnswer.group_id,
        HumanPersonDistriAnswer.ques_code,
        HumanPersonDistriAnswer.mark_score,
        all_ques_scored_subquery.c.avg_score,
        HumanPersonDistriAnswer.stu_secret_num
    ).select_from(HumanReadTask) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanPersonDistriAnswer, HumanPersonDistriAnswer.round_id == HumanReadTaskRound.round_id) \
        .join(all_ques_scored_subquery, and_(
        all_ques_scored_subquery.c.ques_code == HumanPersonDistriAnswer.ques_code,
        all_ques_scored_subquery.c.round_id == HumanPersonDistriAnswer.round_id,
        all_ques_scored_subquery.c.stu_secret_num == HumanPersonDistriAnswer.stu_secret_num
    )).filter(all_ques_scored_subquery.c.all_scored == True)

    # 动态构建筛选条件
    conditions = []

    # 基础条件
    if query.task_id:
        conditions.append(HumanReadTask.task_id == query.task_id)
    if query.round_id:
        conditions.append(HumanPersonDistriAnswer.round_id == query.round_id)
    if query.user_id:
        conditions.append(HumanPersonDistriAnswer.user_id == query.user_id)
    if query.group_id:
        conditions.append(HumanPersonDistriAnswer.group_id == query.group_id)
    # 应用筛选条件
    if conditions:
        final_stmt = final_stmt.filter(and_(*conditions))

    result = new_session.execute(final_stmt).all()

    # 按考生密号分组，计算每个考生的题目总分
    student_scores = {}
    student_avg_scores = {}  # 存储每个考生的平均分
    for row in result:
        stu_secret_num = row.stu_secret_num
        mark_score = float(row.mark_score) if row.mark_score else 0
        avg_score = float(row.avg_score) if row.avg_score else 0

        if stu_secret_num not in student_scores:
            student_scores[stu_secret_num] = 0
            student_avg_scores[stu_secret_num] = avg_score
        student_scores[stu_secret_num] += mark_score

    # 组织返回数据
    x_data = list(student_scores.keys())
    y1_data = [round_half_up(float(score), 2) for score in student_scores.values()]
    y2_data = [round_half_up(float(avg_score), 2) for avg_score in student_avg_scores.values()]
    y3_data = [round_half_up(float(y1 - y2), 2) for y1, y2 in zip(y1_data, y2_data)]

    # 构建返回数据结构
    chart_data = {
        "legend": ["评阅员评分", "小组平均分", "差异分"],
        "x_data": x_data,
        "y1_data": y1_data,
        "y2_data": y2_data,
        "y3_data": y3_data
    }
    return BaseResponse(data={"data": chart_data}, msg="获取评阅员试评结果成功")


@survey_monitor_router.post(path="/quality_monitor", response_model=BaseResponse, summary="质检监控")
async def quality_monitor_api(
        query: QualityMonitorReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 质检监控")
    # 解构查询参数
    current_page, page_size, project_id, subject_id, task_name, name, diff_count = query.model_dump().values()
    excel_export_service = ExcelExportService()
    # 构建主查询
    main_query = select(
        Project.project_id,
        Project.project_name,
        Subject.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_id,
        HumanReadTaskRound.round_count,
        HumanQualityDistriAnswer.quality_user_id,
        UserInfo.name,
        HumanRoundDistriAnswer.ques_code,
        ExamQuestion.knowledge_show,
        # 抽查量
        func.count(HumanQualityDistriAnswer.stu_secret_num.distinct()).label('spot_check_count'),
        # 退回重评数量
        func.count(func.distinct(case((HumanQualityDistriAnswer.quality_type == 3, HumanQualityDistriAnswer.stu_secret_num), else_=None))).label('return_count'),
        # 重评修改分数数量
        func.count(func.distinct(case((HumanQualityDistriAnswer.quality_type == 2, HumanQualityDistriAnswer.stu_secret_num), else_=None))).label('change_count'),
        # 通过质检量
        func.count(func.distinct(case((HumanQualityDistriAnswer.quality_result == 1, HumanQualityDistriAnswer.stu_secret_num), else_=None))).label('pass_count')
    ).select_from(HumanQualityDistriAnswer) \
        .outerjoin(HumanRoundDistriAnswer, HumanRoundDistriAnswer.distri_id == HumanQualityDistriAnswer.round_distri_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.round_id == HumanQualityDistriAnswer.round_id) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(ExamQuestion, ExamQuestion.ques_id == HumanRoundDistriAnswer.ques_code) \
        .outerjoin(UserInfo, UserInfo.user_id == HumanQualityDistriAnswer.quality_user_id) \
        .group_by(
        Project.project_id,
        Project.project_name,
        Subject.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_id,
        HumanReadTaskRound.round_count,
        HumanQualityDistriAnswer.quality_user_id,
        UserInfo.name,
        HumanRoundDistriAnswer.ques_code,
        ExamQuestion.knowledge_show
    )

    # 动态构建筛选条件
    conditions = []
    # 基础条件
    if query.project_id:
        conditions.append(HumanReadTask.project_id == query.project_id)
    if query.subject_id:
        conditions.append(HumanReadTask.subject_id == query.subject_id)
    if query.task_name:
        conditions.append(HumanReadTask.task_name == query.task_name)
    if query.name:
        conditions.append(UserInfo.name == query.name)

    # 应用筛选条件
    if conditions:
        main_query = main_query.filter(and_(*conditions))

    # 分页处理
    if page_size == -1:
        # 不分页模式
        count_query = select(func.count()).select_from(main_query.subquery())
        total = new_session.execute(count_query).scalar()
        result = new_session.execute(main_query).all()
    else:
        # 分页模式
        # 先获取总数
        distinct_query = main_query.with_only_columns(
            func.distinct(func.concat(Project.project_id, '-', Subject.subject_id, '-', HumanQualityDistriAnswer.quality_user_id, '-', HumanRoundDistriAnswer.ques_code)).label('key')
        ).subquery()
        total = new_session.execute(select(func.count()).select_from(distinct_query)).scalar()
        # 获取数据
        limit = current_page - 1
        offset = limit * page_size
        result = new_session.execute(
            main_query.order_by(HumanRoundDistriAnswer.ques_code.desc()).limit(page_size).offset(offset)
        ).all()

    # 构建返回数据
    data_list = []
    for row in result:
        # 计算质检通过率
        spot_check_count = int(row.spot_check_count)
        pass_count = int(row.pass_count)
        pass_rate = round_half_up(float(pass_count) / spot_check_count * 100, 2) if spot_check_count > 0 else 0.0

        data_list.append({
            "project_id": row.project_id,
            "project_name": row.project_name,
            "subject_id": row.subject_id,
            "subject_name": row.subject_name,
            "ques_code": row.ques_code,
            "knowledge_show": row.knowledge_show,
            "quality_user_id": row.quality_user_id,
            "quality_user_name": row.name,
            "task_id": row.task_id,
            "task_name": row.task_name,
            "spot_check_count": spot_check_count,
            "return_count": int(row.return_count),
            "change_count": int(row.change_count),
            "pass_count": pass_count,
            "pass_rate": pass_rate
        })

    if query.diff_count:
        min_diff = min(query.diff_count)
        max_diff = max(query.diff_count)
        data_list = [item for item in data_list if min_diff <= item["pass_rate"] <= max_diff]
    return BaseResponse(data={"total": total, "data": data_list}, msg="获取质检监控成功")


@survey_monitor_router.post(path="/quality_monitor_export", response_model=BaseResponse, summary="质检监控导出")
async def quality_monitor_export_api(
        query: QualityMonitorReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 质检监控导出")
    # 解构查询参数
    current_page, page_size, project_id, subject_id, task_name, name, diff_count = query.model_dump().values()
    excel_export_service = ExcelExportService()
    # 构建主查询
    main_query = select(
        Project.project_id,
        Project.project_name,
        Subject.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_id,
        HumanReadTaskRound.round_count,
        HumanQualityDistriAnswer.quality_user_id,
        UserInfo.name,
        HumanRoundDistriAnswer.ques_code,
        ExamQuestion.knowledge_show,
        # 抽查量
        func.count(HumanQualityDistriAnswer.stu_secret_num.distinct()).label('spot_check_count'),
        # 退回重评数量
        func.count(func.distinct(case((HumanQualityDistriAnswer.quality_type == 3, HumanQualityDistriAnswer.stu_secret_num), else_=None))).label('return_count'),
        # 重评修改分数数量
        func.count(func.distinct(case((HumanQualityDistriAnswer.quality_type == 2, HumanQualityDistriAnswer.stu_secret_num), else_=None))).label('change_count'),
        # 通过质检量
        func.count(func.distinct(case((HumanQualityDistriAnswer.quality_result == 1, HumanQualityDistriAnswer.stu_secret_num), else_=None))).label('pass_count')
    ).select_from(HumanQualityDistriAnswer) \
        .outerjoin(HumanRoundDistriAnswer, HumanRoundDistriAnswer.distri_id == HumanQualityDistriAnswer.round_distri_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.round_id == HumanQualityDistriAnswer.round_id) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(ExamQuestion, ExamQuestion.ques_id == HumanRoundDistriAnswer.ques_code) \
        .outerjoin(UserInfo, UserInfo.user_id == HumanQualityDistriAnswer.quality_user_id) \
        .group_by(
        Project.project_id,
        Project.project_name,
        Subject.subject_id,
        Subject.subject_name,
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTaskRound.round_id,
        HumanReadTaskRound.round_count,
        HumanQualityDistriAnswer.quality_user_id,
        UserInfo.name,
        HumanRoundDistriAnswer.ques_code,
        ExamQuestion.knowledge_show
    )

    # 动态构建筛选条件
    conditions = []
    # 基础条件
    if query.project_id:
        conditions.append(HumanReadTask.project_id == query.project_id)
    if query.subject_id:
        conditions.append(HumanReadTask.subject_id == query.subject_id)
    if query.task_name:
        conditions.append(HumanReadTask.task_name == query.task_name)
    if query.name:
        conditions.append(UserInfo.name == query.name)

    # 应用筛选条件
    if conditions:
        main_query = main_query.filter(and_(*conditions))

    # 分页处理
    if page_size == -1:
        # 不分页模式
        count_query = select(func.count()).select_from(main_query.subquery())
        total = new_session.execute(count_query).scalar()
        result = new_session.execute(main_query).all()
    else:
        # 分页模式
        # 先获取总数
        distinct_query = main_query.with_only_columns(
            func.distinct(func.concat(Project.project_id, '-', Subject.subject_id, '-', HumanQualityDistriAnswer.quality_user_id, '-', HumanRoundDistriAnswer.ques_code)).label('key')
        ).subquery()
        total = new_session.execute(select(func.count()).select_from(distinct_query)).scalar()
        # 获取数据
        limit = current_page - 1
        offset = limit * page_size
        result = new_session.execute(
            main_query.order_by(HumanRoundDistriAnswer.ques_code.desc()).limit(page_size).offset(offset)
        ).all()

    # 构建返回数据
    data_list = []
    for row in result:
        # 计算质检通过率
        spot_check_count = int(row.spot_check_count)
        pass_count = int(row.pass_count)
        pass_rate = round_half_up(float(pass_count) / spot_check_count * 100, 2) if spot_check_count > 0 else 0.0

        data_list.append({
            "资格名称": row.project_name,
            "科目名称": row.subject_name,
            "试题号": row.knowledge_show,
            "抽查人": row.name,
            "任务名称": row.task_name,
            "抽查量": spot_check_count,
            "退回重评数量": int(row.return_count),
            "重评修改分数数量": int(row.change_count),
            "通过质检量": pass_count,
            "质检通过率": pass_rate
        })

    if query.diff_count:
        min_diff = min(query.diff_count)
        max_diff = max(query.diff_count)
        data_list = [item for item in data_list if min_diff <= item["质检通过率"] <= max_diff]
    if page_size == -1:
        return await excel_export_service.export_to_excel_stream(
            data=data_list,
            file_name="质检监控.xlsx",
        )
    else:
        return BaseResponse(data={"total": total, "data": data_list}, msg="获取质检监控导出成功")
