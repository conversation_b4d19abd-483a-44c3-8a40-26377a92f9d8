import redis
from settings import configs
from settings.logger import logger

# 创建连接池
pool = redis.ConnectionPool(
    host=configs.REDIS_CONFIG["HOST"],
    port=configs.REDIS_CONFIG["PORT"],
    db=configs.REDIS_CONFIG["DATABASE"],
    max_connections=configs.REDIS_CONFIG["MAX_CONNECTIONS"],
    decode_responses=True
)


def redis_session():
    """生成 Redis 会话的上下文管理器"""
    r = redis.Redis(connection_pool=pool)
    try:
        # 关闭 redis 持久化存储
        r.config_set("save", "")
        r.config_set("appendonly", "no")
    except Exception as e:
        logger.warning(f"设置Redis配置时出错: {e}")

    try:
        yield r
    finally:
        pass  # 连接池自动管理连接


def init_redis_connection():
    """初始化Redis连接，如果连接失败则尝试启动Redis服务"""
    try:
        r = next(redis_session())
        r.ping()
        logger.info("Redis连接成功")
        return True
    except Exception as e:
        logger.warning(f"Redis连接失败: {e}")

        # 尝试启动Redis服务
        try:
            from factory_apps.redis_db.redis_service_manager import ensure_redis_running
            success, message = ensure_redis_running()
            if success:
                logger.info(f"Redis服务启动成功: {message}")
                # 再次尝试连接
                try:
                    r = next(redis_session())
                    r.ping()
                    logger.info("Redis重新连接成功")
                    return True
                except Exception as retry_e:
                    logger.error(f"Redis重新连接失败: {retry_e}")
                    return False
            else:
                logger.error(f"Redis服务启动失败: {message}")
                return False
        except ImportError as import_e:
            logger.error(f"导入Redis服务管理器失败: {import_e}")
            return False
        except Exception as start_e:
            logger.error(f"启动Redis服务时出错: {start_e}")
            return False


# 初始化Redis连接
redis_connection_status = init_redis_connection()


def redis_transaction():
    """生成 Redis 事务的上下文管理器"""
    r = redis.Redis(connection_pool=pool)
    pipe = r.pipeline()
    try:
        yield pipe
        pipe.execute()
    except Exception as e:
        pipe.reset()
        raise e


def main():
    # 基本操作
    with redis_session() as r:
        r.set('name', 'Alice')
        name = r.get('name')
        print(f"Name: {name}")  # 输出: Name: Alice

    # 事务操作
    try:
        with redis_transaction() as pipe:
            pipe.set('transaction_key1', 'value1')
            pipe.set('transaction_key2', 'value2')
            # 模拟异常
            # raise Exception("模拟事务失败")
    except Exception as e:
        print(f"事务失败: {e}")

    # 验证事务结果
    with redis_session() as r:
        val1 = r.get('transaction_key1')
        val2 = r.get('transaction_key2')
        print(f"transaction_key1: {val1}, transaction_key2: {val2}")
        # 输出:
        # 如果事务成功:
        # transaction_key1: value1, transaction_key2: value2
        # 如果事务失败（如上面模拟异常）:
        # transaction_key1: None, transaction_key2: None


if __name__ == "__main__":
    main()
