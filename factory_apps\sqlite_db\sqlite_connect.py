import os

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from settings import configs


# 指定数据库文件的完整路径
db_path = configs.SQLITE_DB_PATH

# 确保父目录存在
os.makedirs(os.path.dirname(db_path), exist_ok=True)

# 创建引擎
engine = create_engine(f'sqlite:///{db_path}')

# 创建会话类，autoflush=False 防止自动刷新
Session = sessionmaker(bind=engine, autoflush=False)


def sqlite_session():
    """创建一个数据库会话"""
    new_session = Session()
    try:
        yield new_session
    finally:
        new_session.close()


def test_session_handling():
    new_session = next(sqlite_session())
    result = new_session.execute(text("SELECT name FROM sqlite_master WHERE type='table';"))
    tables = result.fetchall()
    print("现有表:", tables)


if __name__ == '__main__':
    test_session_handling()
