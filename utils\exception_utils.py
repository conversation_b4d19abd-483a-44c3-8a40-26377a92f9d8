from aiohttp import ClientConnectorError
from fastapi import Request
from settings import logger
from requests.exceptions import Timeout
from starlette.responses import JSONResponse

from apps.base.schemas import BaseResponse
from helper import response_utils


async def aiohttp_connector_error_handler(request: Request, exc: ClientConnectorError):
    logger.info(f"连接异常:{exc.host}:{exc.port},{exc.os_error}")
    return JSONResponse(
        status_code=503,
        content=BaseResponse(
            code=response_utils.timeout,
            msg=f"服务器异常,异常码{response_utils.timeout}",
            data=f"连接异常:{exc.host}:{exc.port},{exc.os_error}",
        ).model_dump(),
    )


async def timeout_exception_handler(request: Request, exc: Timeout):
    logger.info(f"同步请求超时: {exc.request.url.path}")
    return JSONResponse(
        status_code=408,
        content=BaseResponse(
            code=response_utils.timeout,
            msg="服务器异常,异常码10019",
            data=f"请求超时: {exc.request.url.path}",
        ).model_dump(),
    )


class AiModelError(Exception):
    def __init__(self, message="请求大模型异常"):
        self.message = message
        super().__init__(self.message)


async def ai_model_handler(request, exc: Exception):
    logger.info(f"大模型请求错误: {exc}")
    return JSONResponse(
        status_code=200,
        content=BaseResponse(
            code=response_utils.ai_model_error,
            msg=f"请求大模型异常，错误码{response_utils.ai_model_error}",
            data=f"请求大模型异常: {exc}",
        ).model_dump(),
    )
