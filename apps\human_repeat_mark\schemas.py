from typing import Optional, Literal, List, Union
from pydantic import BaseModel, Field, constr, conint
from datetime import datetime

from apps.base.schemas import PaginationModel


class GetSampleStuReq(BaseModel):
    repeat_task_id: str = Field(..., description="复评任务id")


class GetStuReq(BaseModel):
    ids_list: list[str] = Field(..., description="[id1,id2]")
    # ids_list: list[list[str]] = Field(..., description="[id1,id2]")
    score_list: list[list[Union[None, int]]] = Field(..., description="总得分区间列表，示例：[[10,56],[12,33]]")


class CreateRepeatMark(BaseModel):
    stu_list: list[object] = Field(..., description="考生列表")


class DeleteRepeatMark(BaseModel):
    stu_list: list[object] = Field(..., description="考生列表")


class GetRepeatTaskReq(PaginationModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    round_count: Optional[int] = Field(None, description="轮次")
    is_repeater: Optional[int] = Field(None, description="是否是复评员：0 不是：1 是")
    task_type: int = Field(..., description="任务类型，1 表示正评任务，2 表示试评任务，3 表示复评任务;4 技术验收；5 管理验收")
    task_state: Optional[int] = Field(None, description="任务状态，1 为未发起，2 为正在进行中，3 为已完成，4 为已暂停，5 为已结束;验收：1 未抽取；2 待验收；3 验收中；4 已完成")
    repeat_user_id_list: Optional[list[str]] = Field(None, description="复评人用户id")


class RepeatTaskIdReq(BaseModel):
    repeat_task_id: str = Field(..., description="复评任务id")
    new_state: int = Field(..., description="更新后组状态")


class GetRepeatTaskStuReq(PaginationModel):
    repeat_task_id: str = Field(..., description="复评任务id")
    stu_secret_num: Optional[str] = Field(None, description="考生密号")


class ReviewerMarkReq(BaseModel):
    answer_id: str = Field(..., description="作答id")
    mark_point_score_list: list = Field([], description="评分标准得分")
    mark_score: float = Field(..., description="评分分数")
    ques_code: Optional[str] = Field(None, description="试题编码")
    cost_time: int = Field(..., description="评分耗时（秒）")
    mark_type: Literal[1, 2, 3] = Field(1, description="评分类型，1 表示正常评分，2 表示重评，3 表示回评卷回评")


class ReviewerMarkStuReq(BaseModel):
    # round_id: str = Field(..., description="轮次id")
    repeat_score: float = Field(..., description="大题分数")
    mark_info: List[ReviewerMarkReq] = Field(..., description="评分数据")


class RepeatScoreReq(BaseModel):
    is_repeat: Optional[int] = Field(None, description="是否再次复评：0 否：1 是")
    repeat_round_detail_id: str = Field(..., description="复评轮次详情表id")
    big_ques_list: List[ReviewerMarkStuReq] = Field(..., description="大题评分列表")


class GetRepeatRoundDetailReq(PaginationModel):
    repeat_task_id: str = Field(..., description="复评任务id")
    subject_id: str = Field(..., description="所属科目id")
    # exam_session: int = Field(..., description="场次")


class UpdateScoreThresholdReq(BaseModel):
    repeat_task_id: str = Field(..., description="复评任务id")
    score_threshold: float = Field(..., description="分差阈值")
