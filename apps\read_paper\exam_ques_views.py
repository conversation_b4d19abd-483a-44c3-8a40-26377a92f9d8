import re
import threading
import traceback
from decimal import Decimal
from typing import Any

from fastapi import APIRouter, Depends

from apps.base.global_cache import get_redis_ques_info_dict
from apps.ques_manage.services import valid_d_out_of_group
from settings import logger
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import Session

from apps.base.schemas import BaseResponse
from apps.models.models import ExamQuestion, QuesType, ExamPaper, PaperDetail, BusinessQuesType, QuesUsed, Subject
from apps.operation_ques.op_manual_mark_services import format_op_step_info, create_op_step_group, update_op_ques
from apps.read_paper import GetQuesReq, UpdateQuesReq, DeleteQuesReq
from apps.read_paper.common_services import format_info_by_ques_type, download_ques_image, instead_src_to_path, \
    splice_image_path, get_ques_score_list, splice_table_style
from apps.users.services import get_current_user
from factory_apps import session_depend
from helper import response_utils
from helper.rich_text_utils import parse_html_entities
from utils.utils import sum_with_precision, round_half_up

ques_router = APIRouter()


@ques_router.post(path="/get_ques", response_model=BaseResponse, summary="获取试题信息")
async def get_ques_info(query: GetQuesReq, user: Any = Depends(get_current_user),
                        new_session: Session = Depends(session_depend)):
    project_id, subject_id, paper_id, ques_type_code, read_state, format_by_ques_type, is_get_all_info, is_paper, business_id = query.model_dump().values()
    logger.info(f"{user['username']} 获取试题信息")
    if is_paper:
        if not paper_id:
            return BaseResponse(code=response_utils.params_error, msg="抽卷考试缺少 试卷 参数")
    else:
        if not project_id or not subject_id:
            return BaseResponse(code=response_utils.params_error, msg="抽参考试缺少 项目或科目 参数")
    ques_data = []
    try:
        # op_ques_dict = format_op_step_info(new_session, paper_id)
        # final_op_ques_dict = create_op_step_group(op_ques_dict)

        select_field = [ExamQuestion.ques_id, ExamQuestion.ques_code, PaperDetail.paper_detail_id, PaperDetail.paper_id,
                        PaperDetail.paper_code, ExamPaper.subject_id, PaperDetail.ques_order, ExamQuestion.ques_desc,
                        ExamQuestion.ques_choices, PaperDetail.ques_score_list, ExamQuestion.ques_difficulty,
                        ExamQuestion.standard_answer, ExamQuestion.standard_choices_code, ExamQuestion.standard_parse,
                        ExamQuestion.ques_mark_point, ExamQuestion.created_time, ExamQuestion.updated_time,
                        QuesType.ques_type_name, ExamQuestion.e_mark_rule, ExamQuestion.d_out_of_order_group,
                        PaperDetail.manual_read_state, ExamQuestion.standard_answer_html, ExamQuestion.weight,
                        PaperDetail.parent_ques_id, ExamQuestion.ques_type_code, BusinessQuesType.ques_type_score,
                        BusinessQuesType.business_ques_type_id, Subject.subject_name,
                        ExamQuestion.manual_read_state.label("ques_read_state")]

        # 添加查询条件
        read_state_condition = True

        business_condition = ExamQuestion.business_ques_type_id == business_id if business_id else True
        if read_state:
            if read_state == 1 or read_state == 2:
                if is_paper:
                    read_state_condition = PaperDetail.manual_read_state == read_state
                else:
                    if read_state == 2:
                        read_state_condition = ExamQuestion.manual_read_state == 2
                    else:
                        read_state_condition = or_(ExamQuestion.manual_read_state.is_(None), ExamQuestion.manual_read_state == 1)

        if ques_type_code:
            if ques_type_code != "F":
                if is_paper:
                    condition = and_(PaperDetail.paper_id == paper_id, ExamQuestion.ques_type_code == ques_type_code,
                                     or_(PaperDetail.parent_ques_id.is_(None), PaperDetail.parent_ques_id == ""))
                    order_by_fields = [PaperDetail.manual_read_state, PaperDetail.ques_order]
                else:
                    business_info = new_session.query(BusinessQuesType.business_ques_type_id).filter(
                        and_(BusinessQuesType.project_id == project_id, BusinessQuesType.subject_id == subject_id,
                             BusinessQuesType.ques_type_code == ques_type_code,
                             or_(BusinessQuesType.parent_ques_type_id.is_(None), BusinessQuesType.parent_ques_type_id == ""))).all()
                    business_id_list = [i[0] for i in business_info] if business_info else []
                    condition = ExamQuestion.business_ques_type_id.in_(business_id_list)
                    order_by_fields = [ExamQuestion.ques_id, ExamQuestion.small_ques_num]
            else:
                if is_paper:
                    condition = and_(ExamQuestion.ques_type_code == ques_type_code, PaperDetail.paper_id == paper_id)
                    order_by_fields = [PaperDetail.manual_read_state, PaperDetail.ques_order]
                else:
                    business_info = new_session.query(BusinessQuesType.business_ques_type_id).filter(
                        and_(BusinessQuesType.project_id == project_id, BusinessQuesType.subject_id == subject_id,
                             BusinessQuesType.ques_type_code == ques_type_code)).all()
                    business_id_list = [i[0] for i in business_info] if business_info else []
                    condition = ExamQuestion.business_ques_type_id.in_(business_id_list)
                    order_by_fields = [ExamQuestion.ques_id, ExamQuestion.small_ques_num]

        else:
            if is_paper:
                condition = PaperDetail.paper_id == paper_id
                order_by_fields = [PaperDetail.ques_order]
            else:
                business_info = new_session.query(BusinessQuesType.business_ques_type_id).filter(
                    and_(BusinessQuesType.project_id == project_id, BusinessQuesType.subject_id == subject_id)).all()
                business_id_list = [i[0] for i in business_info] if business_info else []
                condition = and_(ExamQuestion.business_ques_type_id.in_(business_id_list))
                order_by_fields = [ExamQuestion.ques_id, ExamQuestion.small_ques_num]

        if is_paper:
            ques_stmt = select(*select_field) \
                .join(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
                .join(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
                .join(Subject, Subject.subject_id == ExamPaper.subject_id) \
                .outerjoin(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
                .join(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
                .where(and_(condition, read_state_condition)) \
                .order_by(*order_by_fields)
        else:
            # 抽参没有使用过的题不显示
            ques_stmt = select(*select_field) \
                .outerjoin(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
                .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
                .outerjoin(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
                .outerjoin(Subject, Subject.subject_id == BusinessQuesType.subject_id) \
                .join(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
                .join(QuesUsed, QuesUsed.ques_code == ExamQuestion.ques_code) \
                .where(and_(condition, read_state_condition, QuesUsed.used_count != 0, business_condition),ExamQuestion.parent_ques_id.is_(None)) \
                .distinct(ExamQuestion.ques_id)  \
                .order_by(ExamQuestion.ques_id)

        result = new_session.execute(ques_stmt)
        for row in result:
            ques_id, ques_score_list, manual_read_state = row.ques_id, row.ques_score_list, row.manual_read_state

            if is_paper:
                if manual_read_state == 1:
                    manual_read_state = "未分配"
                else:
                    manual_read_state = "已分配"
            else:
                if row.ques_read_state == 2:
                    manual_read_state = "已分配"
                else:
                    manual_read_state = "未分配"

            if ques_type_code != "F":
                if is_paper:
                    total_score = 0
                    for i in ques_score_list:
                        total_score += round_half_up(i, 2)
                else:
                    total_score = row.ques_type_score
            else:
                if is_paper:
                    ques_score_info = new_session.query(PaperDetail.ques_score_list).filter(
                        and_(PaperDetail.paper_id == paper_id, PaperDetail.parent_ques_id == ques_id)).all()
                    ques_score_list_list = [i[0] for i in ques_score_info]
                    ques_score_sum_list = []
                    for ques_score_list in ques_score_list_list:
                        single_total_score = 0
                        for i in ques_score_list:
                            single_total_score += Decimal(i)
                        ques_score_sum_list.append(single_total_score)
                    ques_score_list = [str(sorted(ques_score_sum_list)[0])]
                    total_score = sum_with_precision(ques_score_sum_list)
                else:
                    total_score = row.ques_type_score

            if is_get_all_info:
                # 拼接图片路径
                ques_desc, ques_choices, standard_answer = row.ques_desc, row.ques_choices, row.standard_answer
                ques_desc, ques_choices = splice_image_path(ques_desc, ques_choices)
                standard_parse = splice_table_style(row.standard_parse)

                ques_item = {
                    "paper_detail_id": row.paper_detail_id,
                    "ques_id": ques_id,
                    "ques_code": row.ques_code,
                    "paper_id": row.paper_id,
                    "paper_code": row.paper_code,
                    "subject_id": row.subject_id,
                    "ques_order": re.sub(r'^0+', '', row.ques_order) if row.ques_order else None,
                    "ques_desc": ques_desc,
                    "ques_choices": ques_choices,
                    "ques_score_list": ques_score_list,
                    "ques_difficulty": row.ques_difficulty,
                    "standard_answer": standard_answer,
                    "standard_choices_code": row.standard_choices_code,
                    "standard_parse": standard_parse,
                    "ques_mark_point": row.ques_mark_point,
                    "created_time": row.created_time and str(row.created_time).replace("T", " "),
                    "updated_time": row.updated_time and str(row.updated_time).replace("T", " "),
                    "ques_type_name": row.ques_type_name,
                    "e_mark_rule": row.e_mark_rule,
                    "manual_read_state": manual_read_state,
                    "standard_answer_html": row.standard_answer_html,
                    "total_score": total_score,
                    "weight": row.weight,
                    "d_out_of_order_group": str(row.d_out_of_order_group) if row.d_out_of_order_group else None,
                    "parent_ques_id": row.parent_ques_id,
                    "ques_type_code": row.ques_type_code,
                    # "op_step_list": final_op_ques_dict.get(ques_id, {}).get("op_step_list", None),
                    # "op_score_rule": final_op_ques_dict.get(ques_id, {}).get("score_rule", None),
                    # "op_total_score_gene": final_op_ques_dict.get(ques_id, {}).get("op_total_score_gene", None),
                    # "knowledge_group_id": final_op_ques_dict.get(ques_id, {}).get("knowledge_group_id", None),
                    "business_ques_type_id": row.business_ques_type_id,
                    "subject_name": row.subject_name
                }
            else:
                ques_item = {
                    "paper_detail_id": row.paper_detail_id,
                    "ques_id": ques_id,
                    "ques_code": row.ques_code,
                    "paper_code": row.paper_code,
                    "subject_id": row.subject_id,
                    "ques_order": re.sub(r'^0+', '', row.ques_order) if row.ques_order else None,
                    "ques_type_name": row.ques_type_name,
                    "ques_score_list": ques_score_list,
                    "total_score": total_score,
                    "parent_ques_id": row.parent_ques_id,
                    "ques_type_code": row.ques_type_code
                }
            ques_data.append(ques_item)
        total = len(ques_data)
    except Exception as e:
        logger.error(f"获取试题信息列表失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg=f"获取试题信息失败")
    logger.info("获取试题信息成功")

    if format_by_ques_type:
        # 按照试题类型格式化数据
        ques_data = format_info_by_ques_type(ques_data)
    data = {
        "data": ques_data,
        "total": total
    }
    return BaseResponse(msg="获取试题信息成功", data=data)


@ques_router.post(path="/update_ques", response_model=BaseResponse, summary="编辑试题信息")
async def update_ques(query: UpdateQuesReq, user: Any = Depends(get_current_user),
                      new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 编辑试题信息")
    (paper_id, ques_id, ques_code, ques_order, ques_desc, ques_choices, total_score, weight, d_out_of_order_group,
     ques_difficulty, standard_answer, standard_answer_html, standard_parse, e_mark_rule, ques_mark_point,
     knowledge_group_id, op_step_list, op_score_rule_dict, op_total_score_gene) = query.model_dump().values()

    paper_data = new_session.query(ExamPaper.lock_state, ExamPaper.paper_code).filter(
        ExamPaper.paper_id == paper_id).first()
    if not paper_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该试卷")
    lock_state, paper_code = paper_data
    if lock_state == 2:
        return BaseResponse(code=response_utils.permission_deny, msg="该试卷已被使用，无法修改")

    if d_out_of_order_group:
        result, msg, d_out_of_order_group = valid_d_out_of_group(d_out_of_order_group)
        if not result:
            return BaseResponse(code=response_utils.server_error, msg=msg)

    ques_info = new_session.query(ExamQuestion.ques_type_code, ExamQuestion.standard_answer, ExamQuestion.e_mark_rule,
                                  PaperDetail.ques_score_list, ExamQuestion.weight) \
        .filter(ExamQuestion.ques_id == ques_id) \
        .join(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id).first()
    ques_type_code, raw_standard_answer, raw_mark_rule, raw_ques_score_list, raw_weight = ques_info or (
        None, None, None, None)

    if ques_type_code != "B":
        if ques_choices:
            for choice in ques_choices:
                choice_html = choice.get("html")
                if not choice_html:
                    choice_html = choice["options"]
                    choice_text = parse_html_entities(choice_html)[0]
                    choice["options"] = choice_text
                    choice["html"] = choice_html

        standard_choices_code = []
        if standard_answer:
            for answer in standard_answer:
                for choice in ques_choices:
                    if choice["options"].startswith(answer):
                        standard_choices_code.append(choice["code"])
    else:
        standard_choices_code = standard_answer

    try:
        ques_desc_html = ques_desc.get("html")
        if not ques_desc_html:
            ques_desc_html = ques_desc["text"]
        ques_desc_text, img_base64_src_list = parse_html_entities(ques_desc_html)
        if img_base64_src_list:
            # 将图片保存到本地
            relative_file_path_list = download_ques_image(img_base64_src_list)
            if relative_file_path_list:
                ques_desc_html = instead_src_to_path(ques_desc_html, relative_file_path_list)
        ques_desc = {
            "text": ques_desc_text,
            "html": ques_desc_html
        }

        update_standard_answer = [parse_html_entities(i)[0] for i in
                                  standard_answer_html] if standard_answer_html else [parse_html_entities(i)[0] for i in
                                                                                      standard_answer]

        update_dict = {
            ExamQuestion.ques_desc: ques_desc,
            ExamQuestion.ques_choices: ques_choices,
            ExamQuestion.ques_difficulty: ques_difficulty,
            ExamQuestion.standard_answer: update_standard_answer,
            ExamQuestion.standard_answer_html: standard_answer_html if standard_answer_html else [],
            ExamQuestion.standard_choices_code: standard_choices_code,
            ExamQuestion.standard_parse: standard_parse,
            ExamQuestion.e_mark_rule: e_mark_rule,
            ExamQuestion.weight: weight,
            ExamQuestion.d_out_of_order_group: d_out_of_order_group,
            ExamQuestion.ques_mark_point: ques_mark_point,
            ExamQuestion.u_user_id: user.get("user_id")
        }
        new_session.query(ExamQuestion).filter(ExamQuestion.ques_id == ques_id).update(update_dict)

        if ques_type_code == "D":
            # 填空题权重有变化需要更新试题每空分数
            if weight != raw_weight:
                ques_score = sum_with_precision(raw_ques_score_list)
                space_weight_list, total_weight = weight[0], float(weight[1][0])
                ques_score_list = get_ques_score_list(ques_score, total_weight, space_weight_list)
                new_session.query(PaperDetail).filter(PaperDetail.ques_id == ques_id).update({
                    PaperDetail.ques_score_list: ques_score_list
                })

        if ques_type_code == "G":
            update_op_ques(new_session, ques_id, knowledge_group_id, op_total_score_gene, op_score_rule_dict,
                           op_step_list)
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"编辑试题信息失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg=f"编辑试题信息失败")
    logger.info("编辑试题信息成功")
    get_redis_ques_info_dict(new_session, is_reload=True)
    return BaseResponse(msg="编辑试题信息成功")


@ques_router.post(path="/delete_ques", response_model=BaseResponse, summary="删除试题")
async def delete_paper(query: DeleteQuesReq, user: Any = Depends(get_current_user),
                       new_session: Session = Depends(session_depend)):
    paper_id, ques_id = query.paper_id, query.ques_id
    logger.info(f"{user['username']} 删除试题信息，id 为 {ques_id}")
    paper_data = new_session.query(ExamPaper.lock_state).filter(ExamPaper.paper_id == paper_id).first()
    if not paper_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该试卷")

    if paper_data.lock_state == 2:
        return BaseResponse(code=response_utils.permission_deny, msg="该试卷已被使用，试题无法删除")

    ques_data = new_session.query(ExamQuestion.ques_id).filter(ExamQuestion.ques_id == ques_id).first()
    if not ques_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该试题")

    try:
        new_session.query(ExamQuestion).filter(ExamQuestion.ques_id == ques_id).delete()
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除试题失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除试题失败")
    logger.info("删除试题成功")
    return BaseResponse(msg="删除试题成功")
