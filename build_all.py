"""
统一打包脚本
分别打包主程序、定时任务程序和统一启动器
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(cmd, description):
    """运行命令并处理结果"""
    print(f"\n{'='*50}")
    print(f"正在执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print("执行成功!")
        if result.stdout:
            print("输出:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"执行失败: {e}")
        if e.stdout:
            print("标准输出:", e.stdout)
        if e.stderr:
            print("错误输出:", e.stderr)
        return False


def check_redis_files():
    """检查Redis文件是否存在"""
    redis_dir = Path("Redis-x64-3.0.504")
    redis_server = redis_dir / "redis-server.exe"
    redis_cli = redis_dir / "redis-cli.exe"
    
    if not redis_server.exists():
        print(f"警告: Redis服务器文件不存在: {redis_server}")
        print("请从 https://github.com/microsoftarchive/redis/releases 下载Redis Windows版本")
        return False
    
    if not redis_cli.exists():
        print(f"警告: Redis客户端文件不存在: {redis_cli}")
        print("请从 https://github.com/microsoftarchive/redis/releases 下载Redis Windows版本")
        return False
    
    print("Redis文件检查通过")
    return True


def build_main_program():
    """打包主程序"""
    cmd = 'pyinstaller --onefile --name "卓帆电子化考试阅卷管理系统V1.0" --icon logo.ico main.py'
    return run_command(cmd, "打包主程序")


def build_schedule_program():
    """打包定时任务程序"""
    cmd = 'pyinstaller --onefile --name "定时任务V1.0" --icon logo.ico schedule_main.py'
    return run_command(cmd, "打包定时任务程序")


def build_unified_launcher():
    """打包统一启动器"""
    # 首先检查是否已经有打包好的主程序和定时任务程序
    main_exe = Path("dist/卓帆电子化考试阅卷管理系统V1.0.exe")
    schedule_exe = Path("dist/定时任务V1.0.exe")
    
    if not main_exe.exists():
        print("错误: 主程序exe不存在，请先打包主程序")
        return False
    
    if not schedule_exe.exists():
        print("错误: 定时任务程序exe不存在，请先打包定时任务程序")
        return False
    
    # 复制exe文件到根目录以便spec文件引用
    shutil.copy2(main_exe, "卓帆电子化考试阅卷管理系统V1.0.exe")
    shutil.copy2(schedule_exe, "定时任务V1.0.exe")
    
    cmd = 'pyinstaller 卓帆智能定标阅卷系统V1.0.spec'
    success = run_command(cmd, "打包统一启动器")
    
    # 清理临时文件
    try:
        os.remove("卓帆电子化考试阅卷管理系统V1.0.exe")
        os.remove("定时任务V1.0.exe")
    except:
        pass
    
    return success


def clean_build_files():
    """清理构建文件"""
    dirs_to_clean = ["build", "__pycache__"]
    files_to_clean = ["*.pyc"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")
    
    # 清理pyc文件
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(".pyc"):
                os.remove(os.path.join(root, file))


def main():
    """主函数"""
    print("卓帆智能阅卷系统 - 统一打包脚本")
    print("="*60)
    
    # 检查Redis文件
    if not check_redis_files():
        print("\n请先下载并放置Redis文件，然后重新运行此脚本")
        return False
    
    # 清理之前的构建文件
    print("\n正在清理之前的构建文件...")
    clean_build_files()
    
    success_count = 0
    total_steps = 3
    
    # 1. 打包主程序
    if build_main_program():
        success_count += 1
        print("✓ 主程序打包成功")
    else:
        print("✗ 主程序打包失败")
    
    # 2. 打包定时任务程序
    if build_schedule_program():
        success_count += 1
        print("✓ 定时任务程序打包成功")
    else:
        print("✗ 定时任务程序打包失败")
    
    # 3. 打包统一启动器
    if build_unified_launcher():
        success_count += 1
        print("✓ 统一启动器打包成功")
    else:
        print("✗ 统一启动器打包失败")
    
    # 总结
    print(f"\n{'='*60}")
    print(f"打包完成: {success_count}/{total_steps} 个组件成功")
    
    if success_count == total_steps:
        print("🎉 所有组件打包成功!")
        print("\n生成的文件:")
        print("- dist/卓帆电子化考试阅卷管理系统V1.0.exe (主程序)")
        print("- dist/定时任务V1.0.exe (定时任务程序)")
        print("- dist/卓帆智能阅卷系统统一启动器V1.0.exe (统一启动器)")
        print("\n使用说明:")
        print("1. 单独运行: 分别运行各个exe文件")
        print("2. 统一运行: 运行统一启动器，它会自动启动Redis、主程序和定时任务")
        return True
    else:
        print("❌ 部分组件打包失败，请检查错误信息")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
