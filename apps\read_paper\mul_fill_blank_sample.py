import requests


ai_data = {"ques_id": "123456789",
           "ques_type": "D",
           "ques_desc": "心脏是人体的重要器官之一，人的心脏分为______、______、______、______。",
           "std_answer": ["左心房", "右心房", "左心室", "右心室"],
           "stu_answer": ["", "左心房", "盆地", "心肌壁"],
           "rule": 1,
           "mark_count": 1,
           "std_score": ["1", "1", "1", "1"],
           "subject": "生物"
           }


if __name__ == '__main__':
    url = "http://192.168.1.45:7860/ques_review_mulity"
    res = requests.post(url, json=ai_data)
    res_code = res.status_code
    if res_code == 200:
        result_data = res.json()
        print(result_data)
    else:
        print(f"AI 评分失败，状态码为：{res_code}")

