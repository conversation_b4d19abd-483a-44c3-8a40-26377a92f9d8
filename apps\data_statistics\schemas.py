from typing import Optional

from pydantic import BaseModel, Field


class ManualReadTaskIdScoreReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    ques_score_list: list = Field(..., title="试题分数列表")


class ManualReadTaskIdTimeReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    search_date: list = Field(..., title="查询日期")


class ManualReadTaskIdScoreTimeReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    ques_score_list: Optional[list] = Field([], title="试题分数列表")
    search_date: list = Field(None, title="查询日期")
    ques_code: Optional[str] = Field(None, title="试题编号")


class ScoreAnalysisReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    grade_type: Optional[int] = Field(2, title="成绩类型，1=AI成绩，2=人工阅卷成绩")


class QuestionScoreReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    ques_id: Optional[str] = Field(None, title="题目ID，可选")
    grade_type: Optional[int] = Field(2, title="成绩类型")


class MarkProgressReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")


class QualityAnalysisReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    pass_score: Optional[float] = Field(60, title="及格分数线，默认60分")
    full_score: Optional[float] = Field(100, title="满分分值，默认100分")

