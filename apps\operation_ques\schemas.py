from typing import Optional

from pydantic import BaseModel, Field

from apps.base.schemas import PaginationModel


class DownloadMaterialReq(BaseModel):
    ques_code: str = Field(..., title="试题编号，用于命名下载的文件")
    stu_secret_num: str = Field(..., title="考生密号，用于查询考生考场，找到材料文件夹")
    file_path: str = Field(..., title="op 文件路径")


class GetOpQuesReq(PaginationModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_code: Optional[str] = Field(None, title="试卷编号")
    ques_code: Optional[str] = Field(None, title="试题编号")
    ques_order: Optional[str] = Field(None, title="试题序号")
    stu_answer: Optional[str] = Field(None, title="考生答案")
    mark_result: Optional[int] = Field(None, title="判断结果")
    mark_state: list = Field([], title="评分状态")
    stu_score_range: Optional[list] = Field(None, title="考生分数范围")
    search_time: Optional[list] = Field([], title="评分时间范围")


class StartOpEngineMarkReq(BaseModel):
    record_id: str = Field(..., title="操作题引擎评分的记录id")
    answer_data_list: list = Field([], title="作答数据列表")
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_code: Optional[str] = Field(None, title="试卷编号")
    ques_code: Optional[str] = Field(None, title="试题编号")
    ques_order: Optional[str] = Field(None, title="试题序号")
    stu_answer: Optional[str] = Field(None, title="考生答案")
    mark_result: Optional[int] = Field(None, title="判断结果")
    mark_state: list = Field([], title="评分状态")
    stu_score_range: Optional[list] = Field(None, title="考生分数范围")
    search_time: Optional[list] = Field([], title="评分时间范围")


class GetOpMarkProgressReq(BaseModel):
    record_id: Optional[str] = Field(None, title="任务记录id")


class RecordIdReq(BaseModel):
    record_id: str = Field(None, title="任务记录id")


class ExecuteEngineReq(BaseModel):
    record_id: str = Field(None, title="任务记录id")
    file_id: str = Field(None, title="批次记录文件id")
    ques_id: str = Field(..., title="试题id")
    ques_file_name: str = Field(None, title="试题文件名（.jvbqt）")
    answer_file_name: str = Field(None, title="考生作答文件名（.jdet）")


class OpFileNameReq(BaseModel):
    ques_id: str = Field(..., title="试题id")
    file_name: str = Field(..., title="文件名")
