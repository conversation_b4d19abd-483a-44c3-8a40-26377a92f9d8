import math
import re
from decimal import Decimal

from fastapi import APIRouter, Depends
from settings import logger
from sqlalchemy import and_, func, case
from sqlalchemy.orm import Session
from typing import Any

from apps.base.global_cache import get_small_ques_count, get_stu_num_by_task_id
from apps.data_statistics.schemas import ManualReadTaskIdTimeReq, ManualReadTaskIdScoreTimeReq
from apps.data_statistics.services import get_curr_user_task_list
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.models.models import ManualReadTask, ManualDistributeAnswer, ManualMark, ManualArbitrateQuality, \
    ExamQuestion, BusinessQuesType
from factory_apps.mysql_db.databases import session_depend
from helper import response_utils
from utils.time_func import add_days_to_date
from utils.utils import round_half_up, sum_with_precision

expert_data_router = APIRouter()

"""
统计图返回格式：
柱状图、折线图返回格式：
    "legend": ["数量"],
    "x_data": ["任务A", "任务B", "任务C"]
    "y1_data": [100, 30, 50]
    "y2_data": [100, 30, 50]
    
饼图返回格式：
    "x_data": ["个人完成", "其他人完成"],
    "y_data": [
        {"value": 13, "name": "个人完成"},
        {"value": 36, "name": "其他人完成"}
    ]
"""


@expert_data_router.post(path="/task_distri_situation_bar", response_model=BaseResponse, summary="专家主页：任务分配情况柱状图")
async def task_distri_situation_bar(user: Any = Depends(get_current_user),
                                    new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取专家主页：任务分配情况柱状图")
    curr_user_id = user["user_id"]
    x_task_info = get_curr_user_task_list(new_session, curr_user_id, "3", only_id=False, show_not_launch=False)

    task_name_list, distri_count_list, not_distri_count_list = [], [], []
    for task in x_task_info:
        m_read_task_id, task_name, project_id, subject_id, paper_id, ques_id, \
            ques_code = (task["m_read_task_id"], task["m_read_task_name"], task["project_id"], task["subject_id"],
                         task["paper_id"], task["ques_id"], task["ques_code"])

        task_name_list.append(task_name)

        small_ques_count = get_small_ques_count(ques_code)

        # 查询全部任务已分配条数
        distri_count = new_session.query(ManualDistributeAnswer.distri_answer_id) \
            .filter(ManualDistributeAnswer.m_read_task_id == m_read_task_id).count()
        need_distri_count = distri_count / (small_ques_count if small_ques_count else 1)
        distri_count_list.append(need_distri_count)

        # 查询每个任务总的需要分配的条数也就是考生数量
        stu_count = get_stu_num_by_task_id(new_session, m_read_task_id, project_id, subject_id, paper_id, ques_id, ques_code)
        not_distri_count_list.append((stu_count - need_distri_count))

    data = {
        "legend": ["已分配", "未分配"],
        "x_data": task_name_list,
        "y1_data": distri_count_list,
        "y2_data": not_distri_count_list
    }
    logger.info("获取专家主页：任务分配情况柱状图成功")
    return BaseResponse(data=data, msg="获取专家主页：任务分配情况柱状图成功")


@expert_data_router.post(path="/entirety_manual_number_bar", response_model=BaseResponse, summary="专家主页：整体阅卷数量柱状图")
async def entirety_manual_number_bar(user: Any = Depends(get_current_user),
                                     new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取专家主页：整体阅卷数量柱状图")
    curr_user_id = user["user_id"]
    morning_count_list, afternoon_count_list = [], []
    x_task_info = get_curr_user_task_list(new_session, curr_user_id, "3", only_id=False)
    for task in x_task_info:
        m_read_task_id, ques_code = task["m_read_task_id"], task["ques_code"]
        small_ques_count = get_small_ques_count(ques_code)
        small_ques_count = small_ques_count if small_ques_count else 1

        expert_read_time = new_session.query(ManualMark.created_time).filter(
            and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id)).all()
        task_forenoon_count, task_afternoon_count = 0, 0
        if expert_read_time:
            for read_item in expert_read_time:
                read_time = read_item[0]
                read_time_hour = read_time.hour
                if read_time_hour < 11:
                    task_forenoon_count += 1
                else:
                    task_afternoon_count += 1
        morning_count_list.append(int(task_forenoon_count / small_ques_count))
        afternoon_count_list.append(int(task_afternoon_count / small_ques_count))
    data = {
        "legend": ["数量"],
        "x_data": ["上午", "下午"],
        "y_data": [sum(morning_count_list), sum(afternoon_count_list)]
    }
    logger.info("获取专家主页：整体阅卷数量柱状图成功")
    return BaseResponse(data=data, msg="获取专家主页：整体阅卷数量柱状图成功")


@expert_data_router.post(path="/entirety_finish_number_pie", response_model=BaseResponse, summary="专家主页：整体完成情况饼图")
async def entirety_finish_number_pie(user: Any = Depends(get_current_user),
                                     new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取专家主页：整体完成情况饼图")
    curr_user_id = user["user_id"]
    task_list = get_curr_user_task_list(new_session, curr_user_id, "3", only_id=False)

    total, all_curr_user_do_count = 0, 0
    for task in task_list:
        m_read_task_id, ques_code = task["m_read_task_id"], task["ques_code"]
        # manual_group_info = new_session.query(ManualReadTaskGroup.manual_group_id).filter(ManualReadTaskGroup.m_read_task_id == m_read_task_id).first()
        # first_manual_group_id = manual_group_info[0]
        # expert_num = new_session.query(ManualGroupUser.user_id) \
        #     .filter(and_(ManualGroupUser.manual_group_id == first_manual_group_id, ManualGroupUser.role_id == "3")).count()
        task_total = new_session.query(ManualMark.manual_mark_id).filter(ManualMark.m_read_task_id == m_read_task_id).count()
        curr_user_do_count = new_session.query(ManualMark.manual_mark_id).filter(and_(
            ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id)).count()
        small_ques_count = get_small_ques_count(ques_code)
        small_ques_count = small_ques_count if small_ques_count else 1

        total += int(task_total / small_ques_count)
        all_curr_user_do_count += int(curr_user_do_count / small_ques_count)

    data = {
        "x_data": ["个人完成", "其他人完成"],
        "y_data": [
            {"value": all_curr_user_do_count, "name": "个人完成"},
            {"value": total - all_curr_user_do_count, "name": "其他人完成"}
        ]
    }
    logger.info("获取专家主页：整体完成情况饼图成功")
    return BaseResponse(data=data, msg="获取专家主页：整体完成情况饼图成功")


@expert_data_router.post(path="/task_finish_situation_bar", response_model=BaseResponse, summary="专家主页：任务完成情况柱状图")
async def task_finish_situation_bar(user: Any = Depends(get_current_user),
                                    new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取专家主页：任务完成情况柱状图")
    curr_user_id = user["user_id"]
    task_id_list = get_curr_user_task_list(new_session, curr_user_id, "3", only_id=True, show_not_launch=False)
    x_task_info = new_session.query(ManualReadTask.m_read_task_id, ManualReadTask.m_read_task_name,
                                    ManualReadTask.ques_type_code, ManualReadTask.ques_code) \
        .filter(ManualReadTask.m_read_task_id.in_(task_id_list)).order_by(ManualReadTask.m_read_task_id).all()

    # 查询全部任务已分配条数
    all_distri_info = new_session.query(ManualDistributeAnswer.m_read_task_id,
                                        func.count(ManualDistributeAnswer.distri_answer_id)) \
        .filter(ManualDistributeAnswer.m_read_task_id.in_(task_id_list)) \
        .group_by(ManualDistributeAnswer.m_read_task_id) \
        .order_by(ManualDistributeAnswer.m_read_task_id).all()

    task_distri_answer_num_dict = {}
    if all_distri_info:
        for m_read_task_id, distri_answer_count in all_distri_info:
            task_distri_answer_num_dict[m_read_task_id] = distri_answer_count

    # 查询每个任务已评分完成的条数，也就是 final_mark_score 不为 None 的条数
    marked_distri_info = new_session.query(ManualDistributeAnswer.m_read_task_id, func.coalesce(func.sum(
        case((ManualDistributeAnswer.final_mark_score != None, 1), else_=0)), 0)) \
        .filter(ManualDistributeAnswer.m_read_task_id.in_(task_id_list)) \
        .group_by(ManualDistributeAnswer.m_read_task_id) \
        .order_by(ManualDistributeAnswer.m_read_task_id).all()

    task_marked_dict = {}
    if marked_distri_info:
        for m_read_task_id, marked_count in marked_distri_info:
            task_marked_dict[m_read_task_id] = marked_count

    task_name_list, distri_count_list, marked_count_list = [], [], []
    if x_task_info and all_distri_info:
        for m_read_task_id, m_read_task_name, ques_type_code, ques_code in x_task_info:
            task_name_list.append(m_read_task_name)

            if ques_type_code == "F":
                small_ques_count = get_small_ques_count(ques_code)
                task_distri_answer_num_dict[m_read_task_id] = int(task_distri_answer_num_dict[m_read_task_id] / small_ques_count)
                task_marked_dict[m_read_task_id] = int(task_marked_dict[m_read_task_id] / small_ques_count)

            distri_count_list.append(task_distri_answer_num_dict[m_read_task_id])
            marked_count_list.append(task_marked_dict[m_read_task_id])

    data = {
        "legend": ["完成", "剩余"],
        "x_data": task_name_list,
        "y1_data": marked_count_list,
        "y2_data": [i - j for i, j in zip(distri_count_list, marked_count_list)]
    }
    logger.info("获取专家主页：任务完成情况柱状图成功")
    return BaseResponse(data=data, msg="获取专家主页：任务完成情况柱状图成功")


@expert_data_router.post(path="/task_score_trend_line", response_model=BaseResponse, summary="专家主页：单一任务得分走向折线图")
async def task_score_trend_line(query: ManualReadTaskIdTimeReq, user: Any = Depends(get_current_user),
                                new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取专家主页：单一任务得分走向折线图")
    curr_user_id = user.get("user_id")
    m_read_task_id, search_date = query.m_read_task_id, query.search_date
    date_query_condition = True
    if search_date:
        if len(search_date) != 2:
            return BaseResponse(code=response_utils.params_error, msg="请输入合法的日期区间")
        date_query_condition = ManualMark.created_time.between(search_date[0], add_days_to_date(search_date[1], 1))

    expert_read_info = new_session.query(ManualMark.expert_mark_score, ManualMark.created_time).filter(
        and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id,
             date_query_condition)).all()
    time_score_dict = {}
    if expert_read_info:
        for mark_score, read_time in expert_read_info:
            read_time_hour = re.sub(r":.*", ":00:00", str(read_time))
            if read_time_hour in time_score_dict:
                time_score_dict[read_time_hour].append(mark_score)
            else:
                time_score_dict[read_time_hour] = [mark_score]

    sorted_time_score_dict = dict(sorted(time_score_dict.items()))
    hour_list = list(sorted_time_score_dict.keys())
    score_list = list(sorted_time_score_dict.values())
    average_score_list = [round_half_up(float(Decimal(sum_with_precision(score)) / len(score)), 2) for score in score_list]

    data = {
        "legend": ["平均分"],
        "x_data": hour_list,
        "y_data": average_score_list
    }
    logger.info("获取专家主页：单一任务得分走向柱状图成功")
    return BaseResponse(data=data, msg="获取专家主页：单一任务得分走向折线图成功")


@expert_data_router.post(path="/task_score_distribution_bar", response_model=BaseResponse, summary="专家主页：单一任务得分布柱状图")
async def task_score_distribution_bar(query: ManualReadTaskIdScoreTimeReq, user: Any = Depends(get_current_user),
                                      new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取专家主页：单一任务得分分布柱状图")
    curr_user_id = user.get("user_id")
    m_read_task_id, ques_score_list, search_date, ques_code = query.model_dump().values()

    date_query_condition = True
    if search_date:
        if len(search_date) != 2:
            return BaseResponse(code=response_utils.params_error, msg="请输入合法的日期区间")
        date_query_condition = ManualMark.created_time.between(search_date[0], add_days_to_date(search_date[1], 1))

    if ques_score_list:
        ques_score = sum_with_precision(ques_score_list)
    else:
        ques_id = new_session.query(ManualReadTask.ques_id).filter(ManualReadTask.m_read_task_id == m_read_task_id).scalar()
        business_id = new_session.query(ExamQuestion.business_ques_type_id).filter(ExamQuestion.ques_id == ques_id).scalar()
        ques_score = new_session.query(BusinessQuesType.ques_type_score).filter(BusinessQuesType.business_ques_type_id == business_id).scalar()

    score_dict = {}
    per_ques_score_list = math.ceil(ques_score + 1)
    for i in range(per_ques_score_list):
        score_dict[i] = 0
    score_list = list(score_dict.keys())
    small_ques_count = get_small_ques_count(ques_code)

    # 获取该阅卷专家对整道题的评分
    if not small_ques_count:
        # 非组合题
        expert_read_info = new_session.query(ManualMark.expert_mark_score) \
            .filter(and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id,
                         date_query_condition)).all()
    else:
        # 组合题
        expert_read_info = new_session.query(func.sum(ManualMark.expert_mark_score)) \
            .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualMark.distri_answer_id) \
            .filter(and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id,
                         date_query_condition)).group_by(ManualDistributeAnswer.stu_secret_num).all()

    if expert_read_info:
        for mark_score in expert_read_info:
            mark_score = int(mark_score[0])
            try:
                index = score_list.index(mark_score)
            except ValueError:
                continue
            score_dict[index] += 1

    sorted_score_dict = dict(sorted(score_dict.items()))

    data = {
        "legend": ["人数"],
        "x_data": list(sorted_score_dict.keys()),
        "y_data": list(sorted_score_dict.values())
    }
    logger.info("获取专家主页：单一任务得分布柱状图")
    return BaseResponse(data=data, msg="获取专家主页：单一任务得分布柱状图")


@expert_data_router.post(path="/task_marked_speed_line", response_model=BaseResponse, summary="专家主页：单一任务阅卷速度折线图")
async def task_marked_speed_line(query: ManualReadTaskIdTimeReq, user: Any = Depends(get_current_user),
                                 new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取专家主页：单一任务阅卷速度折线图")
    curr_user_id = user.get("user_id")
    m_read_task_id, search_date = query.m_read_task_id, query.search_date
    date_query_condition = True
    if search_date:
        if len(search_date) != 2:
            return BaseResponse(code=response_utils.params_error, msg="请输入合法的日期区间")
        date_query_condition = ManualMark.created_time.between(search_date[0], add_days_to_date(search_date[1], 1))

    expert_read_info = new_session.query(ManualMark.created_time).filter(
        and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id,
             date_query_condition)).all()
    time_marked_count_dict = {}
    if expert_read_info:
        for read_item in expert_read_info:
            read_time = read_item[0]
            read_time_hour = re.sub(r":.*", ":00:00", str(read_time))
            if read_time_hour in time_marked_count_dict:
                time_marked_count_dict[read_time_hour] += 1
            else:
                time_marked_count_dict[read_time_hour] = 1

    sorted_time_score_dict = dict(sorted(time_marked_count_dict.items()))
    hour_list = list(sorted_time_score_dict.keys())
    marked_count = list(sorted_time_score_dict.values())

    data = {
        "legend": ["速度"],
        "x_data": hour_list,
        "y_data": marked_count
    }
    logger.info("获取专家主页：单一任务阅卷速度折线图成功")
    return BaseResponse(data=data, msg="获取专家主页：单一任务阅卷速度折线图成功")


@expert_data_router.post(path="/task_arbitrate_pie", response_model=BaseResponse, summary="专家主页：仲裁情况饼图")
async def task_arbitrate_pie(query: ManualReadTaskIdTimeReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取专家主页：仲裁情况饼图")
    curr_user_id = user.get("user_id")
    m_read_task_id, search_date = query.m_read_task_id, query.search_date
    date_query_condition = True
    if search_date:
        if len(search_date) != 2:
            return BaseResponse(code=response_utils.params_error, msg="请输入合法的日期区间")
        date_query_condition = ManualMark.created_time.between(search_date[0], add_days_to_date(search_date[1], 1))

    expert_read_info = new_session.query(ManualMark.distri_answer_id).filter(
        and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id,
             date_query_condition)).all()
    distri_answer_id_list = [i[0] for i in expert_read_info]
    need_arbitrate_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 1,
             ManualArbitrateQuality.distri_answer_id.in_(distri_answer_id_list))).count()

    data = {
        "x_data": ["需仲裁", "无需仲裁"],
        "y_data": [
            {"value": need_arbitrate_count, "name": "需仲裁"},
            {"value": len(distri_answer_id_list) - need_arbitrate_count, "name": "无需仲裁"}
        ]
    }
    logger.info("获取专家主页：仲裁情况饼图成功")
    return BaseResponse(data=data, msg="获取专家主页：仲裁情况饼图成功")


@expert_data_router.post(path="/task_quality_pie", response_model=BaseResponse, summary="专家主页：质检情况饼图")
async def task_arbitrate_pie(query: ManualReadTaskIdTimeReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取专家主页：质检情况饼图")
    curr_user_id = user.get("user_id")
    m_read_task_id, search_date = query.m_read_task_id, query.search_date
    date_query_condition = True
    if search_date:
        if len(search_date) != 2:
            return BaseResponse(code=response_utils.params_error, msg="请输入合法的日期区间")
        date_query_condition = ManualArbitrateQuality.created_time.between(search_date[0], add_days_to_date(search_date[1], 1))

    # expert_read_info = new_session.query(ManualMark.distri_answer_id).filter(
    #     and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id,
    #          date_query_condition)).all()
    # distri_answer_id_list = [i[0] for i in expert_read_info]

    need_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 2,
             date_query_condition)).count()

    wait_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 2,
             date_query_condition, ManualArbitrateQuality.aq_state == 3)).count()

    quality_pass_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 2,
             ManualArbitrateQuality.aq_state == 4, ManualArbitrateQuality.aq_result == 1, date_query_condition)).count()

    data = {
        "quality_data": {
            "x_data": ["已质检", "未质检"],
            "y_data": [
                {"value": need_quality_count - wait_quality_count, "name": "已质检"},
                {"value": wait_quality_count, "name": "未质检"}
            ]
        },
        "quality_pass_data": {
            "x_data": ["通过", "未通过"],
            "y_data": [
                {"value": quality_pass_count, "name": "通过"},
                {"value": need_quality_count - wait_quality_count - quality_pass_count, "name": "未通过"}
            ]
        }
    }
    logger.info("获取专家主页：质检情况饼图成功")
    return BaseResponse(data=data, msg="获取专家主页：质检情况饼图成功")
