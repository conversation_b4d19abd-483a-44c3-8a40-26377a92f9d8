import json

from sqlalchemy import func, select

from apps.human_mark_group.models import HumanGroupMember
from apps.human_task_manage.models import HumanReadRoundGroup
from factory_apps import redis_session


def get_round_group_member(new_session, round_id, is_reload=False):
    """
    获取任务轮次的 group_id 里的成员id
    """
    r = next(redis_session())
    group_member_dict = r.get(f"group_member_{round_id}")
    if group_member_dict and not is_reload:
        group_member_dict = json.loads(group_member_dict)
    else:
        group_id_list = list(new_session.execute(select(HumanReadRoundGroup.group_id).where(HumanReadRoundGroup.round_id == round_id)).scalars().all())
        group_user_info = new_session.query(HumanGroupMember.group_id, func.group_concat(HumanGroupMember.user_id).label("user_id_str")) \
            .filter(HumanGroupMember.group_id.in_(group_id_list)) \
            .group_by(HumanGroupMember.group_id)
        group_member_dict = {row.group_id: row.user_id_str.split(",") for row in group_user_info}
        r.set(f"group_member_{round_id}", json.dumps(group_member_dict))
    return group_member_dict
