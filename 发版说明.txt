版本号：广州智能定标阅卷系统1.1.50905.3
处理人：黄佳凯
【数据库升级】，升级语句：
ALTER TABLE t_human_person_distri_answer ADD COLUMN round_distri_id VARCHAR(50) COMMENT '作答信息分配id';
ALTER TABLE t_human_person_distri_answer ADD COLUMN ques_id VARCHAR(50) COMMENT '试题id';
ALTER TABLE t_student_subject_grade DROP COLUMN exam_session;


------------------------------------------------------------------------------------------------------

版本号：广州智能定标阅卷系统1.1.50905.2
处理人：黄佳凯
【数据库升级】，升级语句：
ALTER TABLE t_human_mark_group DROP INDEX group_name;

【bug修复】
5643【试评管理】当试评的小组没有评阅员时，点击开始系统直接报”发起人工阅卷任务失败“，正评任务点击开始时会提示
5571【我的试评】通过任务名称查询时无法正常过滤数据
5596【试评任务】试评通过的人，再创建第二轮时，无需再试评
5632【科目成绩}阅卷员评完分，科目成绩表没有生成数据

处理人：刘羽芳
5322【小组管理】创建小组页面基本信息设置完点保存弹框直接关闭了应该跳转至组员设置页
试题编辑，小小题序号修改取值字段

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50905
处理人：黄佳凯
【数据库升级】，升级语句：
ALTER TABLE t_exam_question MODIFY small_ques_int VARCHAR(30) NULL COMMENT '小小题序号';
ALTER TABLE t_exam_question MODIFY small_ques_num VARCHAR(30) NULL COMMENT '组合题小题序号';
ALTER TABLE t_human_answer_exception MODIFY exception_type INTEGER NULL COMMENT '异常类型：0 为无问题，1 为图像错误，2 为作答位置错误，3 为作答合并，4 为其他';
ALTER TABLE t_human_repeat_task MODIFY score_threshold DECIMAL(10, 2) NULL COMMENT '分差阈值';
ALTER TABLE t_import_record ADD COLUMN project_id VARCHAR(50) COMMENT '所属项目id';
ALTER TABLE t_import_record ADD COLUMN subject_id VARCHAR(50) COMMENT '所属科目id';
ALTER TABLE t_import_record ADD COLUMN check_result TEXT COMMENT '校验结果';
ALTER TABLE t_human_answer_exception ADD COLUMN handle_time DateTime COMMENT 处理时间';
ALTER TABLE t_human_answer_exception ADD COLUMN mark_point_score_list JSON COMMENT '评分标准分数列表';
ALTER TABLE t_human_answer_exception ADD COLUMN mark_score DECIMAL(10, 2) NULL COMMENT '评分分数';

【功能开发】
1.新增问题卷。

【bug修复】
5675【质检评分】质检评分得分点也需更新
5669【评分】评分页面图片错位会导致数据库存的得分明细也错乱


处理人：邱林骏
5597 【评分查询】 筛选考生密号过滤错误
修改编辑小小题排序bug
5600【评分查询】只筛选资格筛选不到数据
5603【评分查询】分数筛选过滤错误
5402【试题管理】使用考次的查询条件参数好像没传，导致查询结果不对

处理人：吴琎
【功能开发】
1. 修复并优化excel导出的公共方法。
2. 修改核验数据查询逻辑。

【bug修改】
5521【科目管理】作答数据导入之后，编辑科目页面科目编码没有显示数据

处理人：曹华
5580【技术验收】验收页面点击提交本题提示复评考生失败，提交不了且验收提示的复评
5590【技术验收】验收列表页面参考人数不正确
5588【技术验收】技术验收每个科目应该只能默认有一条数据
5580【技术验收】验收页面点击提交本题提示复评考生失败，提交不了且验收提示的复评
5574【技术验收】验收抽取后提示抽取数量是0，但点击开始验收还可以验收考生数据
5496【数据准备】初始化数据后，在数据准备中还显示了被初始化的导入记录
5471【评分准备】导入作答数据时，t_exam_question表，大题的parent_ques_id需置为null


处理人：曾翔
5668【数据总览】阅卷工作量的具体数据指标中的时间需要加上前缀和单位，现在看起来不明了
处理人：覃红
【功能开发】
1.新增问题卷。
【bug修改】
5549【我的阅卷任务】评分页面分数显示需统一下，目前有的有小数点有的无有的两位有的一位
5556【我的阅卷任务】点击开始阅卷按钮页面加载中时会看到任务已完成
5577【复评任务】复评页面作答字数往左调一些
5640【我的质检】质检抽样页面题得分还可以输入负数
5657【试评任务】列表中需要增加一列“允许偏差分数”
5658【我的试评】在已试评的列表中重新评分，提交后弹出的提示语不正确


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50904.2
处理人：黄佳凯
【数据库升级】，升级语句：
ALTER TABLE t_human_quality_distri_answer ADD COLUMN round_distri_id VARCHAR(50) COMMENT '轮次作答信息分配id';
ALTER TABLE t_human_quality_distri_answer ADD COLUMN person_distri_id VARCHAR(50) COMMENT '评阅员作答信息分配id';
ALTER TABLE t_human_quality_distri_answer DROP COLUMN distri_id;

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50904
处理人：黄佳凯
【数据库升级】，升级语句：
ALTER TABLE t_business_log MODIFY op_content VARCHAR(500) NULL COMMENT '操作内容';
ALTER TABLE t_human_person_distri_answer ADD COLUMN is_again_mark INTEGER COMMENT '是否为质检打回重阅的数据，0 表示否，1 表示是';
ALTER TABLE t_human_repeat_round_detail ADD COLUMN last_score DECIMAL(10, 2) COMMENT '整卷评分(正评、复评)';
ALTER TABLE t_human_repeat_round_detail MODIFY stu_score DECIMAL(10, 2) NULL COMMENT '整卷评分(复评、验收)';
ALTER TABLE t_human_repeat_task ADD COLUMN task_type INTEGER COMMENT '任务类型，1 表示正评任务，2 表示试评任务，3 表示复评任务;4 技术验收；5 管理验收';
ALTER TABLE t_human_repeat_task ADD COLUMN exam_count INTEGER COMMENT '实际考试数量';
ALTER TABLE t_human_repeat_task ADD COLUMN pass_count INTEGER COMMENT '通过量';
ALTER TABLE t_human_repeat_task ADD COLUMN bias_count INTEGER COMMENT '阅卷偏差允许份数';
ALTER TABLE t_human_repeat_task MODIFY repeat_task_count INTEGER NULL COMMENT '复评量(已复评量)';
ALTER TABLE t_human_repeat_task DROP COLUMN repeat_task_complete_count;
ALTER TABLE t_human_repeat_task ADD COLUMN example_count INTEGER COMMENT '抽取数量';
【功能开发】
1.开发评分质检模块

处理人：邱林骏
5597 【评分查询】 筛选考生密号过滤错误
修改编辑小小题排序bug

处理人：曾翔
【功能优化】
1.系统主题： 头部去掉面包屑，将tab切换菜单合并到最上面
处理人：覃红
【功能开发】
1.新增评分质检。


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50903
处理人：刘羽芳
5478【试题管理】新增试题，小小题的序号样式默认为（1）且要能允许用户修改
5551【试题管理】列表中增加一列“试题名称”
5548【科目成绩查询】列表页场次列的宽度需调小一些
5480【试题管理】试题编辑页面，第3小题在设置小小题评分标准，点了添加按钮试题导航又定位到了第1题
5456【试题管理】创建、编辑试题时，小题的序号样式需要支持读取配置文件
5489【试题管理】新增或编辑保存时需要增加评分标准与小题分数的校验
5506【小组管理】设置完组长保存成功后，再点进去查看组长的时候，数据没有回显，后端已返回

处理人：张妍
1.优化（验收管理，复评管理）

处理人：杨城
5495【评分查询】评分查询列表，评阅时间段结束时应该默认显示23：59：59秒
5430【修改信息】修改用户信息登录系统后，右上角的用户信息的名字没有更新

处理人：覃红
5483【我的阅卷任务】评分页面目前小小题有简答题的，前端算分数添加了检验，评完分之后后端显示的分数还是超过试题总分
5533【试评管理】创建试评任务页面允许偏差分值需校验不能大于试题分
5536【我的阅卷任务】提交本题后，步长消失
5550【我的阅卷任务】已评考生列表目前分页设置的是10条一页，但一页显示了12条，且点分页没有用
5554【试评任务】创建任务时，出现了仲裁及离差值
5557【我的试评】论文题没有显示出评分标准，无法根据得分点、总分进行评分
5568【试评任务】试评任务，在已评考生中点击列表重新评分，返回正评显示不对，应该是返回试评

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50902
处理人：黄佳凯
【功能调整】
试评不生成科目成绩

处理人：覃红
5381【正评任务】设置任务信息页面取分方式选择的平均分应该显示取平均分方式，且区分方式的注释与下拉框数据不一致
5482【我的阅卷任务】科目设置允许评分区间是0.5，目前评分页面还可以输入不是0.5的倍数
5484【我的阅卷任务】评分页面目前分数输入框还可以输入字母
5502【我的阅卷任务】评分页面填空题也需判断给的总分不能超过试题分
5503【我的阅卷任务】评分页面评分方式选择总分评分，鼠标需自动定位输入框
5505【我的阅卷任务】评分输入框需限制小数点位数
5510【我的阅卷任务】专家评分页面展开了已评考生，目前评完一个考生已评列表没显示最新评的数据
5532【试评管理】创建试评任务页面允许偏差分数换行显示了

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50901.4
处理人：黄佳凯
【数据库升级】，升级语句：
ALTER TABLE t_human_try_mark_result ADD COLUMN ques_group_id VARCHAR(50) COMMENT '阅卷题组id';
DROP INDEX ix_t_human_try_mark_result_created_time ON t_human_try_mark_result;
ALTER TABLE t_human_try_mark_result DROP COLUMN updated_time;
ALTER TABLE t_human_try_mark_result DROP COLUMN created_time;
ALTER TABLE t_human_try_mark_result DROP COLUMN end_time;
ALTER TABLE t_human_try_mark_result DROP COLUMN group_id;

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50901.3
处理人：黄佳凯
【数据库升级】，升级语句：
DROP INDEX ix_t_exam_paper_created_time ON t_exam_paper_copy1;
DROP INDEX ix_t_exam_paper_paper_name ON t_exam_paper_copy1;
DROP TABLE t_exam_paper_copy1;
ALTER TABLE t_human_repeat_task MODIFY task_state INTEGER NULL COMMENT '任务状态，1 为未发起，2 为正在进行中，3 为已完成，4 为已暂停，5 为已结束;验收：1 未抽取；2 待验收；3 验收中；4 已完成';
ALTER TABLE t_human_stu_grade_detail ADD COLUMN verification_status INTEGER COMMENT '核验状态：0 未核验，1 核验通过，2 核验不通过';

【功能开发】
正评任务、试评任务会定时更新科目成绩表和科目成绩详情表（科目成绩表的总分有邱林骏更新）


------------------------------------------------------------------------------------------------------




版本号：广州智能定标阅卷系统1.1.50901.2
处理人：曹华
【功能开发】
1.抽样定标

处理人：刘羽芳
【bug修改】
5526【科目管理】去掉多余文案
5454【管理员维护】更新人的字段数据没有返回
5518【小组管理】小组管理列表序号列的宽度需调小，小组名称列调宽一些
5500【小组管理】切换小组层级的时候，上一个级别的校验的内容需要清除或更新
5374【小组管理】设置组员列表应该默认未分配的排在最前面
处理人：张妍
【功能开发】
1.验收管理（技术验收，管理验收），复评管理，复评任务提测。

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50901
处理人：黄佳凯
【数据库升级】，升级语句：
ALTER TABLE t_human_answer_exception ADD COLUMN round_id VARCHAR(50) COMMENT '人工阅卷轮次id';
ALTER TABLE t_human_answer_exception ADD COLUMN reviewer_id VARCHAR(50) COMMENT '标记人id';
ALTER TABLE t_human_answer_exception ADD COLUMN handler_id VARCHAR(50) COMMENT '处理人id';
ALTER TABLE t_human_answer_exception MODIFY exception_type INTEGER NULL COMMENT '异常类型：1 为图像错误，2 为作答位置错误，3 为作答合并，4 为其他';
ALTER TABLE t_human_answer_exception COMMENT '问题卷表';
ALTER TABLE t_human_answer_exception DROP COLUMN user_id;
ALTER TABLE t_human_quality_distri_answer DROP COLUMN group_distri_id;
ALTER TABLE t_human_repeat_round_detail MODIFY stu_score DECIMAL(10, 2) NULL COMMENT '整卷评分';
ALTER TABLE t_human_repeat_task ADD COLUMN score_threshold INTEGER COMMENT '分差阈值';
ALTER TABLE t_human_repeat_task ADD COLUMN verify_result INTEGER COMMENT '验收结构：0 验收通过；1验收不通过';
ALTER TABLE t_human_repeat_task ADD COLUMN verify_time DATETIME COMMENT '验收时间';
ALTER TABLE t_human_repeat_task DROP COLUMN exam_session;
ALTER TABLE t_human_repeat_task_round ADD COLUMN task_type INTEGER COMMENT '任务类型，1 表示正评任务，2 表示试评任务，3 表示复评任务;4 技术验收；5 管理验收';
ALTER TABLE t_human_round_distri_answer ADD COLUMN reviewer_id VARCHAR(50) COMMENT '所取的评阅员id';
ALTER TABLE t_human_round_distri_answer MODIFY quality_state INTEGER NULL COMMENT '质检状态，0 表示未质检，1 表示已质检';
ALTER TABLE t_student_subject_grade ADD COLUMN verification_status INTEGER COMMENT '核验状态：0 未核验，1 核验通过，2 核验不通过';
ALTER TABLE t_human_quality_distri_answer ADD COLUMN distri_id VARCHAR(50) COMMENT '作答信息分配id';
ALTER TABLE t_human_quality_distri_answer ADD COLUMN quality_type INTEGER COMMENT '质检类型，1 表示质检通过，2 表示修改提交，3 表示退回重评';
ALTER TABLE t_human_quality_distri_answer ADD COLUMN quality_user_id VARCHAR(50) COMMENT '质检员id';
ALTER TABLE t_human_quality_distri_answer DROP COLUMN user_id;
ALTER TABLE t_human_round_distri_answer MODIFY reviewer_id VARCHAR(50) COLLATE utf8mb4_general_ci NULL COMMENT '所取分数的评阅员id';


处理人：刘羽芳
【功能开发】
1. 科目成绩查询（除了导出按钮没有接口外）。
处理人：曾翔
正评监控，试评监控，数据统计问题修改。
处理人：覃红
【bug修改】
5359【列表筛选】多选下拉框，多个值处理为数字显示，悬浮显示全部信息
5465【正评任务管理】创建任务时阅卷模式和阅卷小组默认选中第一个数据
5483【我的阅卷任务】评分页面目前小小题有简答题的，前端算分数添加了检验，评完分之后后端显示的分数还是超过试题总分

处理人：吴琎
【功能开发】
1.新增excel导出的公共方法。
2.登录新增错误密码输入次数限制，锁定时间，可通过配置文件配置。

【bug修改】
5513【科目管理】删除科目时没有判断科目是否已被使用，已被使用的不能删除
5474【科目管理】不管是资格表、科目表的创建人、更新人为空都不能影响列表的数据显示
5467【科目管理】编辑科目时弹出的提示语改成与创建时一致，去掉提示语中多余的文字
5461【科目管理】删除科目时，没有把该科目对应资格科目的锁定状态进行解锁，导致资格下无科目时也无法删除
5429【修改信息】修改用户信息保存的时候需要校验输入的新密码要与系统生成的默认密码不一致
5247【科目管理】总共11个科目会分页显示，在第2页设置一页显示50条，列表直接一条不显示
5228【资格管理】列表页和新增页有关项目文案改成资格（包括提示文案）最好全局搜索改



------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50829
处理人：覃红
【bug修改】
5404【阅卷任务】评分页面3个小小题有一个没添加评分标准，整个小题的都不会显示
5410【阅卷任务】评分页面，分数应该只保留两位小数
5441【我的阅卷任务】专家评第一个考生，还未开始评分页面显示了0分
5470【正评任务】任务操作栏置灰的应该不能点击，目前还可以操作
5493【我的试评任务】试评界面，每小题的分数应该显示出小题总得分，不应该是每个评分点的分数
5494【我的试评】提交时报错

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50828
处理人：黄佳凯
【bug修复】
5448【正评任务】任务列表页目前阅卷员评完分进度没有统计

处理人：覃红
【功能开发】
1.新增试评任务。
2.新增手动锁屏功能。

【bug修改】
5387【正评任务】批量创建任务页面滚动条与删除按钮有重叠
5435【我的阅卷任务】已评考生数据没有显示已评分数
5447【我的阅卷任务】提交分数时没有对分数进行校验，小题缺少分数显示

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50827
处理人：黄佳凯
【bug修复】
5434【我的阅卷任务】已评考生列表应该默认按时间倒序显示

处理人：邱林骏
【功能开发】
1.正评管理-评分查询。
2.试题管理

处理人：刘羽芳
【功能开发】
1.试题管理-创建功能。

处理人：覃红
【bug修改】
5229【角色管理】表头样式缺少底色。（已统一给所有表格增加底色）
5380【正评管理】设置任务信息页面数据过多会遮挡按钮
5444【我的阅卷任务】评分页面选择使用总分评分，目前鼠标点击分数输入框不会自动填充分数

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50825.1
处理人：覃红
【功能开发】
1.新增我的阅卷任务。

处理人：黄佳凯
【功能开发】
1.正评任务评分（进度条待开发）

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50825
【bug修复】
处理人：黄佳凯
5366【小组管理】专家入组没有限制不能删除

处理人：杨城
【功能调整】
1. 首次登录修改密码和姓名功能
【bug修改】
处理人：覃红
5226标签开了多个时，后面开的标签页显示不出来是空白页，出现了空白页再查看已加载的页面全都是空白页
5274【小组管理】编辑小组页面不需要连续添加按钮
5284【小组管理】创建小组未勾选连续添加，保存成功后返回至列表页没有刷新数据
5377【正评任务】批量创建任务页面列表分页设置100条一页，目前一页还是显示的10条
5371【小组管理】设置组员页面的连续添加按钮建议去除

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50818
【功能开发】
处理人：黄佳凯
1.正评任务管理发起任务完成

处理人：邱林骏
【功能调整】
1.试题管理改版。


处理人：覃红
【功能调整】
1.页面改为不缓存。

处理人：张妍
【功能调整】
1.试题管理改版。

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50813
【功能开发】
处理人：黄佳凯
1.正评任务管理（发起任务只调通了接口，后台逻辑暂未开发）

处理人：覃红
【功能开发】
1.正评任务管理


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50812
【功能开发】
处理人：黄佳凯
1.用户管理（excel导入人员没问题，但是导入小组还有点问题需要调试）
2.增加锁屏功能。

处理人：曹华
1.数据权限管理

处理人：邱林骏
1.基础管理

处理人：覃红
【功能开发/调整】
1.【小组管理】功能开发。
2.【用户管理】调整数据权限。
3.【评卷员管理】增加资格、科目等。
4.登录页面样式修改。
5.增加锁屏功能。（锁屏时间在前端platform-config.json文件中配置LockTime（分钟））

处理人：张妍
【科目管理】1.新增，编辑功能修改；2.增加状态操作功能。


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50616.3
处理人：黄佳凯
【数据库升级】，升级语句：
CREATE INDEX ix_t_manual_arbitrate_quality_m_read_task_id ON t_manual_arbitrate_quality (m_read_task_id);
CREATE INDEX ix_t_manual_distribute_answer_m_read_task_id ON t_manual_distribute_answer (m_read_task_id);
CREATE INDEX ix_t_manual_mark_m_read_task_id ON t_manual_mark (m_read_task_id);
CREATE INDEX ix_t_manual_mark_history_m_read_task_id ON t_manual_mark_history (m_read_task_id);
CREATE INDEX ix_t_manual_read_task_group_m_read_task_id ON t_manual_read_task_group (m_read_task_id);
CREATE INDEX ix_t_stu_total_grade_allow_exam_num ON t_stu_total_grade (allow_exam_num);
ALTER TABLE t_stu_traj MODIFY submit_type INTEGER NULL COMMENT '交卷类型： 0:未交卷；1:异常处理；2:自动交卷(考生端倒计时结束自动交卷)；3:统一收卷(监考端执行统一收卷（模考）)；4:作弊 
收卷，5:强制收卷；6:单机收卷(考生端导出作答包单机收卷)；7:备份收卷；8:导入作答；9:补考，10:主动交卷（考生主动点击交卷按钮交卷）；11:自动化交卷(自动化或时间受限导致无题可做的交卷)';
INSERT INTO`t_sys_func_point` (`func_point_id`, `func_point_flag`, `func_point_name`, `c_user_id`, `u_user_id`, `created_time`, `updated_time`, `deleted_time`, `func_point_value`, `parent_module_flag`, `rank`) VALUES ('32457516847297559126867968', 1750084030990376, '清除人工阅卷成绩', '30394923833607943230586880', NULL, '2025-06-16 22:27:11', NULL, NULL, 'score-result/clear', '1745475912113302', 4);
INSERT INTO `t_role_sys_module_flag` (`role_module_flag_id`, `role_id`, `module_flag`, `c_user_id`, `u_user_id`, `created_time`, `updated_time`, `deleted_time`, `is_builtin`) VALUES ('32457518080370162863702016', '1', 1750084030990376, '1', NULL, '2025-06-16 22:27:29', NULL, NULL, 1);


【功能开发】
1.开发清除指定考生的人工阅卷评分数据的功能

处理人：覃红
【功能调整】
1.  切换角色，系统自动定为到首页。
2.【人工阅卷】评分分数不允许输入负数。
3.【评分结果】增加清除人工阅卷成绩功能。

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50616.2
处理人：黄佳凯
ZNYJ-743【试题管理】试题管理。增加一列题型名称，以及搜索项。

处理人：覃红
【功能调整】
1.  搜索条件默认收起。
2.【试题管理】搜索条件框宽度改小。
3.【阅卷小组管理】创建阅卷小组->分派成员时，人员列表默认选择未分配。
4.【阅卷小组管理】解决创建阅卷小组时，拖动外层弹框后会导致“选择阅卷人员”弹框大小不正确的问题。
5.【阅卷任务管理】解决新建阅卷任务时，勾选了“连续新增”后关闭弹框列表不更新的问题。
6.【人工阅卷】进入页面时，默认聚焦到评分分数输入框。
7.【人工阅卷】进入页面时，试题信息显示滚动条时，自动滚动至末尾（即“评分解析”位置）。

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50616
处理人：黄佳凯
【功能开发】
1.发起任务时只有一组专家的时候直接一次性分完任务

处理人：覃红
【功能调整】
1.人工评分面板不符合校验条件时聚焦调整。

【bug修改】
ZNYJ-400【阅卷任务】评分历史记录页面分页模块位置调低些，考生数据显示区域调高些，分页添加上每页显示条数设置


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50615
处理人：黄佳凯
ZNYJ-772【作答管理】抽参导入作答数据目前分区耗时较久，需添加个进度条
ZNYJ-829【阅卷任务】新增阅卷任务页面分配状态选择已分配显示的还是未分配数据

处理人：覃红
ZNYJ-827【阅卷任务】历史记录重评页面专家重评后需刷新页面状态，且重评完点上一条或下一条重评分数需清空（目前保留了上一个重评分数）
ZNYJ-833【阅卷任务管理】编辑任务页面试题编号显示不全，且编辑页面已选择的试题行应该有个底色
ZNYJ-839【评分结果】评分结果试卷详情页面人工评分评析1.2.3.不需要显示
ZNYJ-843【搜索条件】查看完数据返回至列表页时科目显示的是id值
ZNYJ-844【仲裁历史记录】仲裁重评评分解析加载的还是上一题的数据


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50614.2
处理人：黄佳凯
【数据库升级】，升级语句：
ALTER TABLE t_import_data_record ADD COLUMN success_count BIGINT COMMENT '完成个数';
ALTER TABLE t_import_data_record ADD COLUMN total_count BIGINT COMMENT '总个数';
ALTER TABLE t_manual_arbitrate_quality MODIFY aq_suggestion TEXT NULL COMMENT '仲裁评析或质检意见';
ALTER TABLE t_exam_question ADD COLUMN manual_read_state INTEGER COMMENT '用于抽参：人工阅卷分配状态，1 表示未分配，2 表示已分配';


【bug修改】
ZNYJ-824 搜索条件，只根据项目查询不到数据，每个列表都有这个问题
ZNYJ-818 [人工仲裁】仲裁详情页面没有评分解析输入框，需加上(历史记录评分页面也需添加）
ZNYJ-762 【AI定标】定标列表页面目前根据项目和科目查询不到数据


处理人：覃红
ZNYJ-826【阅卷任务】历史记录质检重评页面，刚进来应该初始化
ZNYJ-827【阅卷任务】历史记录重评页面专家重评后需刷新页面状态，且重评完点上一条或下一条重评分数需清空（目前保留了上一个重评分数）
ZNYJ-828【阅卷任务管理】新增任务时批量添加任务时选择了多个试题试题编号显示的全是，，，，
ZNYJ-838【用户管理】角色列表页的描述列建议放在更新时间字段后面
ZNYJ-841【阅卷任务管理】列表页目前显示的是题号，建议与专家端一样显示试题编号，表头和数据都需调整（抽参和抽卷都可以显示数据）
ZNYJ-842【阅卷任务管理】新建任务时，抽卷的试题设置任务名称页面没有数据，导致新建不了任务

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50614
处理人：黄佳凯
ZNYJ-840【试题管理】列表页http://192.168.1.76:9557/v1/ques_manage/get_ques_list接口请求500
ZNYJ-795【人工阅卷】取任务，第二次取任务有误，目前65032个考生，499个组双评，第一次每个专家取到240，专家评完第一批后分配接近10000条任务

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50613.2
处理人：黄佳凯
ZNYJ-764【试题管理】试题管理列表需增加一个是否已添加评分标准筛选条件
ZNYJ-766【评分结果】考生试卷详情页面目前试题的序号显示不正确

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50613
处理人：黄佳凯
【数据库升级】，升级语句：
INSERT INTO `t_sys_func_point` (`func_point_id`, `func_point_flag`, `func_point_name`, `c_user_id`, `u_user_id`, `created_time`, `updated_time`, `deleted_time`, `func_point_value`, `parent_module_flag`, `rank`) VALUES ('32437140125152079847096320', 1749794460352254, '批量发起阅卷任务', '30394923833607943230586880', '30394923833607943230586880', '2025-06-13 14:01:00', '2025-06-13 14:01:05', NULL, 'marking-task/batchStart', '1718627764830579', 6);

【功能调整】
1.阅卷任务管理新建阅卷任务修改为批量新建。
2.增加批量发起阅卷任务功能。
【bug修改】
ZNYJ-823 [阅卷管理】新增阅卷任务需增加批量添加任务功能，试题可以多选，任务名称直接科目加上试题编号
ZNYJ-822【评分结果】试卷详情页面人工评析显示格式需调整
ZNYJ-821【用户管理】给用户设置数据权限没有项目和科目数据

处理人：覃红
【功能调整】
1.阅卷任务管理新建阅卷任务修改为批量新建。
2.增加批量发起阅卷任务功能。
【bug修改】
ZNYJ-462  分页，设置分页功能弹框建议调大些，目前横向和竖向都显示了滚动条
ZNYJ-793【人工阅卷】阅卷页面底部显示了很多空白区域，需将显示区域调大些
ZNYJ-811【人工阅卷】阅卷详情页面右上角的返回按钮建议改成“返回”按钮，目前不易发现且按钮较小


------------------------------------------------------------------------------------------------------

版本号：广州智能定标阅卷系统1.1.50612.1
处理人：黄佳凯
【功能调整】
1.人工阅卷和生成成绩添加专家评析。

处理人：覃红
ZNYJ-773【试题管理】试题编辑页面，参考答案和答案解析输入框的编辑器需显示在输入框的上面，目前编辑器会遮挡参考答案的内容
ZNYJ-798 专家评分，评分接口响应时间大概是2秒左右时，获取进度的接口需要4秒左右，目前页面是需要获取进度的接口返回成功后页面才能渲染，建议改成获取任务详情成功之后就不用再转圈
ZNYJ-808【搜索条件】所有列表页面有项目和科目的搜索项建议都将项目和科目放在搜索条件的最前面
ZNYJ-809【试题管理】试题编辑页面多选题的选项会有重叠
ZNYJ-812【人工评分】人工评分查看历史记录，/v1/manual_read/get_mark_history接口未返回数据时，加载中状态建议只有历史记录区域显示加载中（目前是整个页面都是加载中）
ZNYJ-813【人工阅卷】历史记录建议改成20条一页，目前还有很多区域未显示
ZNYJ-817【评分结果】试卷详情页面，评分评析没有数据时的高度需默认显示一行

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50612
处理人：黄佳凯
【数据库升级】，升级语句：
ALTER TABLE t_manual_read_task ADD COLUMN business_id VARCHAR(50) COMMENT '业务题型id';
ALTER TABLE t_manual_distribute_answer ADD COLUMN answer_parse JSON COMMENT '人工阅卷评析';

【bug修改】
ZNYJ-800 专家点击重评提示重评失败
ZNYJ-805【历史记录】历史记录详情页面需显示大题描述，默认折叠
ZNYJ-803【我的质检】质检员质检页面有多个小题，目前不是排在最前面的小题可以质检多次，导致历史记录有多条
ZNYJ-807【阅卷任务】目前专家评分完成有成绩之后再去重评，重评后进了仲裁，目前这道题没有分数，但是任务列表页面还是可以结束任务
ZNYJ-797【我的任务】历史记录质检员查看详情，会提示”获取人工评分历史记录详情失败，服务异常：获取分配的阅卷任务的具体信息失败“
ZNYJ-650【阅卷任务】分配任务页面需添加一个筛选实际题型条件

处理人：覃红
ZNYJ-753【人工阅卷】人工阅卷页面考生答案显示区域样式建议与别的区域区分一下，目前考生答案显示不突出

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50611.1
处理人：黄佳凯
【功能调整】
1.抽参时，阅卷任务管理新增、编辑任务时调整。

处理人：覃红
【功能调整】
1.抽参时，阅卷任务管理新增、编辑任务时调整。
【bug修改】
ZNYJ-487【阅卷任务】评分页面历史记录小屏幕分页功能显示不全
ZNYJ-744【试题管理】试题编辑页面选择题的选项内容与选项没有显示在同一水平线
ZNYJ-770【AI定标】定标列表页面定标异常列建议添加个排序和搜索条件，目前试题较多时查看定标异常较麻烦
ZNYJ-781【阅卷管理】添加任务页面弹框将试题编号列调宽之后样式需要调整，且新增任务页面建议添加全屏功能
ZNYJ-798 专家评分，评分接口响应时间大概是2秒左右时，获取进度的接口需要4秒左右，目前页面是需要获取进度的接口返回成功后页面才能渲染，建议改成获取任务详情成功之后就不用再转圈

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50610
处理人：黄佳凯
【数据库升级】，升级语句：
ALTER TABLE t_manual_mark_history MODIFY mark_score DECIMAL(10, 2) NULL COMMENT '评分、仲裁分数，如果为质检，0 表示不通过，1 表示通过';

【功能调整】
1.评分结果区分AI评分和人工评分链接。
2.导出评分结果时区分AI成绩和人工评分成绩。

【bug修改】
ZNYJ-383【试卷管理】试卷预览页面组合题小题是单选题没有显示大题和小题的分数
ZNYJ-783【个人审核】个人审核列表页面，数据排序建议根据进度倒序排，0%的排在最前面
ZNYJ-777【人工阅卷】人工阅卷查看阅卷结果，提示参数错误
ZNYJ-786【试题管理】抽参试题分数组合题大题的分数计算错误，需将每个小题分数累加
ZNYJ-749 [个人阅卷任务】质检员质检通过之后再改成不通过，历史记录页面审核结果还是显示的是通过
ZNYJ-792 [专家登录】每次登录会直接提示接口请求超时http://192.168.1.76:9557/v1/expert_data/task_finish_situation_bar
ZNYJ-756【人工阅卷】人工阅卷页面评分手动输入6.5分，点击确定之后历史记录显示的是7分

处理人：覃红
【功能调整】
1.评分结果区分AI评分和人工评分链接。
2.导出评分结果时区分AI成绩和人工评分成绩。

【bug修改】
ZNYJ-759【AI定标】AI定标列表页面组合题试题序号没有显示数据，抽参的列表添加小题序号列，和小题序号搜索条件
ZNYJ-763【试题管理】编辑试题时，不小心点到遮罩层抽题直接关闭了，导致编辑的内容还未保存
ZNYJ-775【阅卷任务】新增阅卷任务页面选择试题后点击评分预览目前没有显示分数
ZNYJ-782【阅卷管理】小组列表页面质检人员仲裁人员，阅卷模式列的宽度调窄一些，阅卷小组名称列宽一点
ZNYJ-794【人工阅卷】阅卷页面试题材料区域默认折叠


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50609
处理人：黄佳凯
新版本需升级数据库，升级语句：
DROP INDEX ix_t_stu_total_grade_created_time ON t_manual_stu_total_grade;
DROP TABLE t_manual_stu_total_grade;
ALTER TABLE t_stu_total_grade ADD COLUMN manual_a_grade DECIMAL(10, 2) COMMENT '人工单选题成绩';
ALTER TABLE t_stu_total_grade ADD COLUMN manual_b_grade DECIMAL(10, 2) COMMENT '人工多选题成绩';
ALTER TABLE t_stu_total_grade ADD COLUMN manual_c_grade DECIMAL(10, 2) COMMENT '人工判断题成绩';
ALTER TABLE t_stu_total_grade ADD COLUMN manual_d_grade DECIMAL(10, 2) COMMENT '人工填空题成绩';
ALTER TABLE t_stu_total_grade ADD COLUMN manual_e_grade DECIMAL(10, 2) COMMENT '人工简答题成绩';
ALTER TABLE t_stu_total_grade ADD COLUMN manual_f_grade DECIMAL(10, 2) COMMENT '人工组合题成绩';
ALTER TABLE t_stu_total_grade ADD COLUMN manual_total_grade DECIMAL(10, 1) COMMENT '人工总成绩';
CREATE INDEX ix_t_stu_total_grade_detail_allow_exam_num ON t_stu_total_grade_detail (allow_exam_num);
CREATE INDEX ix_t_stu_total_grade_detail_grade_type ON t_stu_total_grade_detail (grade_type);

ZNYJ-774【阅卷任务】添加人工阅卷任务页面，抽参只需显示被使用的试题，未被使用的试题不显示
ZNYJ-791【专家首页】任务分配情况会出现负数
ZNYJ-787【阅卷任务】编辑任务页面，任务新建时选择的是499个小组，编辑页面只返回了37个小组
ZNYJ-777【人工阅卷】人工阅卷查看阅卷结果，提示参数错误

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50606.2
处理人：黄佳凯
新版本需升级数据库，升级语句：
CREATE TABLE t_manual_stu_total_grade (
    grade_id VARCHAR(50) NOT NULL COMMENT '成绩id',
    allow_exam_num VARCHAR(30) NOT NULL COMMENT '准考证号',
    project_id VARCHAR(50) COMMENT '所属项目id',
    subject_id VARCHAR(50) COMMENT '所属科目id',
    paper_id VARCHAR(30) COMMENT '试卷id',
    a_grade DECIMAL(10, 2) COMMENT '单选题成绩',
    b_grade DECIMAL(10, 2) COMMENT '多选题成绩',
    c_grade DECIMAL(10, 2) COMMENT '判断题成绩',
    d_grade DECIMAL(10, 2) COMMENT '填空题成绩',
    e_grade DECIMAL(10, 2) COMMENT '简答题成绩',
    f_grade DECIMAL(10, 2) COMMENT '组合题成绩',
    total_grade DECIMAL(10, 1) COMMENT '总成绩',
    c_user_id VARCHAR(50) COMMENT '创建用户id',
    u_user_id VARCHAR(50) COMMENT '更新用户id',
    created_time DATETIME COMMENT '创建时间',
    updated_time DATETIME COMMENT '更新时间',
    deleted_time DATETIME COMMENT '删除时间，可以为空，如果非空，则为软删',
    from_tool INTEGER COMMENT '数据来源，0 系统（excel格式），1 导入工具，2 系统（sqlite格式）',
    PRIMARY KEY (grade_id)
);
CREATE INDEX ix_t_manual_stu_total_grade_created_time ON t_manual_stu_total_grade (created_time);
ALTER TABLE t_stu_total_grade DROP COLUMN grade_type;

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50606
处理人：黄佳凯
新版本需升级数据库，升级语句：
ALTER TABLE t_manual_read_task MODIFY ques_order VARCHAR(12) NULL COMMENT '试题序号';

ZNYJ-741【试题管理】试题管理列表科目未显示数据，后台接口没有返回数据
ZNYJ-740【试题管理】试题编辑页面组合题小题序号没有显示，且应该显示分数，目前抽参的不知道试题分数是多少
ZNYJ-771【AI定标】定标详情页面，人工定标点确定按钮提示参数错误

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50604
处理人：黄佳凯
新版本需升级数据库，升级语句：
ALTER TABLE t_stu_total_grade_detail ADD COLUMN ques_order INTEGER COMMENT '题序';

ZNYJ-742【试题管理】多选题没有勾选参考答案，后端有返回数据

处理人：覃红
ZNYJ-737【试题管理】填空题参考答案编辑，多个空的点击输入框编辑器的功能显示不出来
ZNYJ-738【试题管理】试题管理编辑试题页面评分规则没有显示输入框，应该可以修改
ZNYJ-739【试题管理】试题管理编辑页面取消保存按钮需固定悬浮在右下侧
ZNYJ-744【试题管理】试题编辑页面选择题的选项内容与选项没有显示在同一水平线
ZNYJ-747【人工阅卷】简答题考生答案显示了换行符，换行的样式没显示


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50528
处理人：黄佳凯
ZNYJ-726【AI定标详情】AI返回的分数会超过空的分数，AI需评对，后台也需校验

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50523
处理人：黄佳凯
ZNYJ-715【AI定标】取消定标定标异常数据也需清除
ZNYJ-718【AI定标】填空题多个空区分顺序时，发送给AI的数据应该只发一个空的标答
ZNYJ-729【AI定标】评分详情页面后台反馈空的个数不正确杨凌D00055
ZNYJ-714【AI定标】重新定标结束时间也需清空

------------------------------------------------------------------------------------------------------

版本号：广州智能定标阅卷系统1.1.50521
处理人：黄佳凯
新版本需升级数据库，升级语句：
ALTER TABLE t_stu_answer_detail MODIFY small_ques_order VARCHAR(100) NULL COMMENT '小题序号（填空题为空格序号），兼容可乱序填空题几个空放在一起，使用分隔符◎☆◎隔开';
CREATE INDEX ix_t_stu_answer_detail_small_ques_order ON t_stu_answer_detail (small_ques_order);

处理填空题答案不区分顺序的题型的AI评分


处理人：覃红
ZNYJ-712【试卷管理】简答题一个评分标准都没有是点加号没有用
ZNYJ-713【AI定标】详情页面评分规则显示了多余的总权重
ZNYJ-716【Ai定标、Ai 评分】列表页的评分状态下拉数据需支持多选
ZNYJ-717【AI定标】AI定标页面需要增加一个批量继续按钮
ZNYJ-720【AI定标】定标详情页面填空题试题分值显示错误，应该是10分
ZNYJ-723【AI定标】定标详情页面建议人数列添加排序功能

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50520.2
处理人：黄佳凯
ZNYJ-642【AI定标】定标详情页面左侧评分标准目前看不出哪些得分点是同一组数据
ZNYJ-609【评分结果】成绩生成后就没有生成成绩按钮了
ZNYJ-696【AI定标】定标暂停后，对异常定标的数据点重新定标，后台不会发送数据给AI
ZNYJ-699【AI定标】多个任务定标中暂停第一个，后面的定标中任务不会发送请求

处理人：覃红
ZNYJ-642【AI定标】定标详情页面左侧评分标准目前看不出哪些得分点是同一组数据
ZNYJ-647【评分结果】考生试卷详情页面，评分标准前面应该把评分规则加上（目前分组数据看不出来）
ZNYJ-710【AI定标】列表已完成定标的行没有重新定标按钮


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50520.1
处理人：黄佳凯
ZNYJ-700 [AI定标】部分填空题点击开始定标，后台未发送数据给AI
ZNYJ-633【AI定标】选择题型点开始定标，定标情况开始会有20秒时间分母显示的是0
ZNYJ-698【AI定标】简答题定标详情页面数据显示不了
ZNYJ-676【试题管理】搜索条件使用考次输入0-0，查询不到数据
ZNYJ-690【AI定标】搜索条件需添加一个试题序号查询

处理人：覃红
ZNYJ-693【AI定标】定标列表需要增加一个取消定标按钮,和全部取消按钮
ZNYJ-703【试卷管理】学考试题的图片显示不出来，图片地址拼接错误
ZNYJ-707【AI评分】列表加载中没有加载中状态，会一直显示空白


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50519.2
处理人：覃红
ZNYJ-610【试题管理】试题列表管理页面，删除按钮去掉，不能删除试题，且试题等需要支持修改标准答


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50519
处理人：黄佳凯
修改数据导入监控数据统计逻辑

处理人：覃红
ZNYJ-686【Ai 定标、AI评分、评分结果】列表数据建议改成100条一页
ZNYJ-687【AI定标】定标试题填空题多个空的空的顺序显示不正确


------------------------------------------------------------------------------------------------------



版本号：广州智能定标阅卷系统1.1.50516
处理人：黄佳凯
ZNYJ-682【试卷管理】通过数据导入工具导入的试卷，简答题试卷预览显示不了
ZNYJ-653【阅卷任务管理】人工阅卷任务点发起阅卷后台接口报500

处理人：覃红
ZNYJ-679【试卷管理】试卷预览，答案解析显示了样式代码
ZNYJ-681【试卷管理】试卷预览页面滚动条遮挡了部分输入框


------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50515
处理人：黄佳凯
ZNYJ-654【评分结果】成绩列表页面需增加成绩导出功能
ZNYJ-655【评分结果】考生试卷详情组合题大题没有显示题序

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50514
处理人：黄佳凯
新版本需升级数据库，升级语句：
ZNYJ-658 [Ai定标】点击开始定标或重新定标按钮，AI连接不上提示需更加明确些，目前只提示了超时
ZNYJ-656【AI定标】异常定标里的人工定标输入人工评分分数和解析，点确定按钮后提示人工评分成功但页面定标异常数据还存在定标异常里，且分数和解析数据都不是人工输入数据

处理人：覃红
ZNYJ-663【试卷管理】试卷预览页面左侧题卡显示不全且没有滚动条，另外题号建议数字框之间留点空隙，目前框刚好框住数字感觉很挤

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50513
处理人：黄佳凯
新版本需升级数据库，升级语句：
CREATE TABLE t_import_db_data_monitor (
    monitor_id VARCHAR(50) NOT NULL COMMENT '监控id',
    type_desc VARCHAR(50) COMMENT '导入数据类型描述',
    is_finish INTEGER COMMENT '是否导入完成，0:否，1:是',
    created_time DATETIME COMMENT '创建时间',
    updated_time DATETIME COMMENT '更新时间',
    deleted_time DATETIME COMMENT '删除时间，可以为空，如果非空，则为软删',
    from_tool INTEGER COMMENT '数据来源，0 系统（excel格式），1 导入工具，2 系统（sqlite格式）',
    PRIMARY KEY (monitor_id)
);

CREATE INDEX ix_t_import_db_data_monitor_created_time ON t_import_db_data_monitor (created_time);

ALTER TABLE t_import_db_data_monitor ADD COLUMN monitor_type INTEGER COMMENT '1 表示试题考试学生数汇总计算';

ALTER TABLE t_import_db_data_monitor ADD COLUMN yet_calculated INTEGER COMMENT '是否已进行汇总计算，0:否，1:是';

ALTER TABLE t_import_db_data_monitor DROP COLUMN type_desc;


CREATE TABLE t_ques_used (
    used_id VARCHAR(50) NOT NULL COMMENT 'id',
    ques_code VARCHAR(50) COMMENT '试题编号',
    used_count VARCHAR(50) COMMENT '考试人次数',
    created_time DATETIME COMMENT '创建时间',
    updated_time DATETIME COMMENT '更新时间',
    deleted_time DATETIME COMMENT '删除时间，可以为空，如果非空，则为软删',
    from_tool INTEGER COMMENT '数据来源，0 系统（excel格式），1 导入工具，2 系统（sqlite格式）',
    PRIMARY KEY (used_id)
);

CREATE INDEX ix_t_ques_used_created_time ON t_ques_used (created_time);

ZNYJ-651【试题管理】试题内容列表需增加一列考生使用次数“使用考次”，搜索条件也需增加筛选条件，可筛选使用次数（与分数查询一致）

处理人：覃红
ZNYJ-644【AI定标】定标详情筛选页面，搜索条件有数据点重置第一次点不会清空数据需点两次
ZNYJ-645【AI定标】从定标详情页面返回至列表页，会出现一两秒暂无数据然后才会加载出数据

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50512
处理人：黄佳凯
新版本需升级数据库，升级语句：
ALTER TABLE t_same_stu_answer_group ADD COLUMN manual_score DECIMAL(10, 2) COMMENT '手动定标评分，在 AI 定标详情里的人工定标，非人工阅卷';
ALTER TABLE t_same_stu_answer_group ADD COLUMN manual_answer_parse JSON COMMENT '手动定标评析';

ZNYJ-632【AI定标】定标详情页面筛选条件设置AI评分条件，设置好后查询数据全都为空
ZNYJ-649【评分结果】单独生成一个考生成绩，后台接口已经提示生成成绩成功了，前端进度条一直显示0%，前端还会发送请求进度接口后端返回的进度一直是0
ZNYJ-648【评分结果】考生成绩统计是7分，但试题的明细分数只有4分

处理人：覃红
ZNYJ-628【AI定标】列表页面点击批量暂停没有用
ZNYJ-646【AI定标】定标详情页面异常数据需要有人工定标功能，可手动输入分数和解析数据

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50509
处理人：黄佳凯
新版本需升级数据库，升级语句：
ALTER TABLE t_stu_subject MODIFY subject_id VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '科目id';
ALTER TABLE t_stu_subject DROP FOREIGN KEY t_stu_subject_ibfk_3;
ALTER TABLE t_stu_subject DROP FOREIGN KEY t_stu_subject_ibfk_1;
ALTER TABLE t_subject MODIFY project_id VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '所属项目id';
ALTER TABLE t_subject DROP FOREIGN KEY t_subject_ibfk_2;

处理人：覃红
ZNYJ-619【AI定标】评析详情页面建议添加上考生答案，目前看考生答案和评析还得关闭页面查看
ZNYJ-627【Ai定标】点击开始定标后，定标进度不会自动刷新需手动刷新页面进度条才会该表
ZNYJ-629【AI定标】定标详情页面点击批量定标应该判断是否勾选数据，未勾选需给提示，目前未勾选数据点批量定标直接全部重新定标了
ZNYJ-634【AI定标】列表页有定标中的数据时，页面会没20秒刷新一次，会导致操作数据会被自动刷新
ZNYJ-635【AI定标】定标详情页面AI评分列需要窄一些，多出的宽度调给聚类类别
ZNYJ-638【AI定标】定标详情页面大题描述建议默认折叠，可点击按钮展开，目前评分标准需要滚到最底部才能查看
ZNYJ-639【AI定标】定标详情页面试题评分文案建议改成“评分标准”


------------------------------------------------------------------------------------------------------



版本号：广州智能定标阅卷系统1.1.50508
处理人：黄佳凯
ZNYJ-641【AI定标】AI给的总分与得分点明细分数不一致，后端未校验，考生分数与具体明细需保持一致

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50507
处理人：黄佳凯
新版本需升级数据库，升级语句：
ALTER TABLE t_create_grade_record ADD COLUMN paper_id_list JSON COMMENT '统计成绩的试卷';
ALTER TABLE t_create_grade_record ADD COLUMN allow_exam_num VARCHAR(30) COMMENT '准考证号';

ZNYJ-611【AI定标】点击开始定标后，数据排在了最后，应该不改变顺序
ZNYJ-631【AI定标】定标详情页面重新定标聚类类别数据是空时，这条记录会一直显示在重新定标页面，后台未发送评分数据
ZNYJ-623【AI定标】试卷更改了评分标准，在定标详情页面显示的评分标准不是最新的
新增程序重新启动会自动执行重新定标的任务和正在定标的任务的功能

处理人：覃红
ZNYJ-616【AI定标】定标详情页面人数列太宽，建议改成支持7位数字即可
ZNYJ-618【AI定标】有定标中的数据关闭后台程序再打开，接口http://127.0.0.1:9557/v1/set_std/ques_set_std_detail一直报错
ZNYJ-620【AI定标】定标详情页面建议将已定标放在第一个排序
ZNYJ-621【AI定标】定标详情页面占比率字段建议去除
ZNYJ-630【AI定标】定标详情页面定标试题滚动条需常态化显示

------------------------------------------------------------------------------------------------------


版本号：广州智能定标阅卷系统1.1.50506
处理人：黄佳凯、覃红

1、开发了试题管理、试题详情、AI定标、AI定标详情、AI评分模块
