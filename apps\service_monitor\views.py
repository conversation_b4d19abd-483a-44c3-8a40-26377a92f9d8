from fastapi import HTTPException
from typing import List
from apps.base.schemas import BaseResponse
from datetime import datetime
import logging
import requests
from apps.service_monitor.schemas import (
    ServiceStatusRequest, 
    ServiceStatusResponse, 
    MonitorRecord
)
from apps.service_monitor.services import service_monitor
from fastapi import APIRouter


service_monitor_router = APIRouter()



@service_monitor_router.get("/state", response_model=ServiceStatusResponse)
async def get_latest_status():
    """
    获取最新的服务状态（供前端轮询调用）
    """


    return ServiceStatusResponse(
            status=1,
            error_message=None
        )

    # try:
    #     # 通过HTTP请求检查EXE服务的实际运行状态
    #     config = service_monitor.services_config.get("health_check_url")
    #     if not config:
    #         raise HTTPException(status_code=404, detail=f"未找到服务 {service_monitor.services_config} 的配置信息")
    
    #     # 直接通过HTTP请求检查服务状态
    #     try:
    #         # 发送健康检查请求到EXE服务
    #         response = requests.get(config["health_check_url"], timeout=5)
    #         print("response.status_code", response.status_code)
    #         if response.status_code == 200:
    #             # 服务运行正常
    #             status = 1
    #             error_message = None
    #         else:
    #             # 服务响应异常
    #             status = 0
    #             error_message = f"健康检查失败，状态码: {response.status_code}"
    #     except requests.exceptions.RequestException as e:
    #         # HTTP请求失败，服务可能未运行
    #         status = 0
    #         error_message = f"健康检查请求失败: {str(e)}"
    
    #     # 创建响应对象
    #     record = MonitorRecord(
    #         id=1,  # 简化处理
    #         service_name="health_check_url",
    #         status=status,
    #         last_check_time=datetime.now(),
    #         error_message=error_message
    #     )
    
    #     # 构造响应
    #     response = ServiceStatusResponse(
    #         status=status,
    #         error_message=record.error_message
    #     )
    
    #     return response
    # except Exception as e:
    #     raise HTTPException(status_code=500, detail=f"获取最新状态时发生错误: {str(e)}")


