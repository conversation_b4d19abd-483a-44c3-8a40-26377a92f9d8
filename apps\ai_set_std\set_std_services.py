import json
import re
from urllib.parse import urlparse, urlunparse

import requests
from pydantic_core.core_schema import no_info_wrap_validator_function
from sqlalchemy import and_, func

from apps.base.services import request_api, update_monitor_cal
from apps.models.models import ExamQuestion, QuesSetStdState, Project, Subject, ExamPaper, SameStuAnswerGroup, \
    StuAnswerDetail, \
    QuesUsed, StuAnswer, QuesAiMark, PaperDetail, BusinessQuesType, SmallExamQuestion
from apps.set_std_check.services import common_range_filter
from factory_apps import session_depend, redis_session
from settings import configs, logger
from utils.time_func import format_now_time


def get_set_std_ques_condition(project_id, subject_id, paper_id, ques_type_code_list, ques_code, ques_id_list, set_std_state_list, ques_name, ques_order, ques_desc, small_ques_num=None, has_mark_point=None, is_paper=True):
    if is_paper:
        project_query = Project.project_id == project_id if project_id else True
        subject_query = Subject.subject_id == subject_id if subject_id else True
    else:
        project_query = BusinessQuesType.project_id == project_id if project_id else True
        subject_query = BusinessQuesType.subject_id == subject_id if subject_id else True
    paper_query = ExamPaper.paper_id == paper_id if paper_id else True
    ques_type_code_query = ExamQuestion.ques_type_code.in_(ques_type_code_list) if ques_type_code_list else True
    ques_code_query = ExamQuestion.ques_code.ilike(f"%{ques_code}%") if ques_code else True
    ques_desc_query = func.json_extract(ExamQuestion.ques_desc, '$.text').ilike(f"%{ques_desc}%") if ques_desc else True
    ques_id_query = ExamQuestion.ques_id.in_(ques_id_list) if ques_id_list else True
    state_query = QuesSetStdState.set_std_state.in_(set_std_state_list) if set_std_state_list else True
    ques_name_query = ExamQuestion.knowledge_show.ilike(f"%{ques_name}%") if ques_name else True
    # used_count_query = QuesUsed.used_count.between(*used_count_list) if used_count_list else True
    small_ques_num_query = ExamQuestion.small_ques_num == small_ques_num if small_ques_num is not None else True
    mark_point_query = True
    if has_mark_point is not None:
        if has_mark_point:
            mark_point_query = ExamQuestion.ques_mark_point.isnot(None)
        else:
            mark_point_query = ExamQuestion.ques_mark_point.is_(None)
    ques_order_query = True
    if ques_order:
        new_session = next(session_depend())
        ques_id_info = new_session.query(PaperDetail.ques_id).filter(PaperDetail.ques_order.ilike(f"%{ques_order}%")).all()
        ques_id_list = [i[0] for i in ques_id_info] if ques_id_info else []
        ques_order_query = ExamQuestion.ques_id.in_(ques_id_list) if ques_id_list else True

    condition = and_(ques_id_query, project_query, subject_query, paper_query, ques_code_query,ques_desc_query, ques_type_code_query, state_query,
                     ques_order_query, small_ques_num_query, mark_point_query, ques_name_query)
    return condition


def get_ai_mark_condition(project_id, subject_id, paper_id, ques_type_code_list, ques_code, mark_state_list):
    project_query = Project.project_id == project_id if project_id else True
    subject_query = Subject.subject_id == subject_id if subject_id else True
    paper_query = ExamPaper.paper_id == paper_id if paper_id else True
    ques_type_code_query = ExamQuestion.ques_type_code.in_(ques_type_code_list) if ques_type_code_list else True
    ques_code_query = ExamQuestion.ques_code.ilike(f"%{ques_code}%") if ques_code else True
    mark_state_query = QuesAiMark.mark_state.in_(mark_state_list) if mark_state_list else True

    condition = and_(project_query, subject_query, paper_query, ques_code_query, ques_type_code_query, mark_state_query)
    return condition


def trans_set_std_state(set_std_state):
    """
    定标状态，1 表示未定标，2 表示定标中，3 表示已定标
    """
    if set_std_state == 1:
        state_str = "未定标"
    elif set_std_state == 2:
        state_str = "定标中"
    elif set_std_state == 3:
        state_str = "已定标"
    elif set_std_state == 4:
        state_str = "已暂停"
    elif set_std_state == 5:
        state_str = "已取消"
    elif set_std_state == 6:
        state_str = "待定标"
    else:
        state_str = "未知"
    return state_str


def trans_mark_state_as_set_std(mark_state):
    """
    评分结果状态：1 表示未评分，2 表示评分成功，3 表示评分失败，4 表示作答答案待人工判断
    """
    if mark_state == 1:
        state_str = "未定标"
    elif mark_state == 2:
        state_str = "定标成功"
    elif mark_state == 3:
        state_str = "定标失败"
    elif mark_state == 4:
        state_str = "待人工判断"
    else:
        state_str = "未知"
    return state_str


def set_answer_group_count_to_redis(new_session, ques_id_list: list, total_count_dict: dict):
    all_total_count = new_session.query(SameStuAnswerGroup.ques_id, func.count(SameStuAnswerGroup.same_answer_group_id)) \
        .filter(SameStuAnswerGroup.ques_id.in_(ques_id_list)) \
        .group_by(SameStuAnswerGroup.ques_id).all()

    redis = next(redis_session())
    for ques_id, total_count in all_total_count:
        total_count_dict[ques_id] = total_count
        redis.set(f"group_count_{ques_id}", total_count)
    return total_count_dict


def get_set_std_process(new_session, ques_id_state_dict, reload=False):
    """
    获取各个试题定标进度
    """
    total_count_dict = {}
    process_dict = {}

    redis = next(redis_session())

    ques_id_list = list(ques_id_state_dict.keys())
    cal_count_ques_id_list = []
    for ques_id in ques_id_list:
        if not reload:
            total_count = redis.get(f"group_count_{ques_id}")
            if total_count:
                total_count_dict[ques_id] = int(total_count)
            else:
                cal_count_ques_id_list.append(ques_id)
        else:
            cal_count_ques_id_list.append(ques_id)

    if cal_count_ques_id_list:
        total_count_dict = set_answer_group_count_to_redis(new_session, cal_count_ques_id_list, total_count_dict)

    running_ques_id_list = []
    for ques_id, set_std_state in ques_id_state_dict.items():
        if set_std_state == 1:
            process_dict[ques_id] = {
                "finish_count": 0,
                "total_count": total_count_dict.get(ques_id, 0),
                "progress": 0
            }
        elif set_std_state == 3:
            process_dict[ques_id] = {
                "finish_count": total_count_dict.get(ques_id, 0),
                "total_count": total_count_dict.get(ques_id, 0),
                "progress": 100
            }
        else:
            running_ques_id_list.append(ques_id)

    finish_ques_id_list = []
    if running_ques_id_list:
        all_finish_count = new_session.query(SameStuAnswerGroup.ques_id, func.count(SameStuAnswerGroup.same_answer_group_id)) \
            .filter(and_(SameStuAnswerGroup.ques_id.in_(running_ques_id_list), SameStuAnswerGroup.running_state == 3)) \
            .group_by(SameStuAnswerGroup.ques_id).all()
        finish_dict = {}
        for ques_id, finish_count in all_finish_count:
            finish_dict[ques_id] = finish_count

        for ques_id in running_ques_id_list:
            total_count = total_count_dict.get(ques_id, 0)
            finish_count = finish_dict.get(ques_id, 0)
            process_dict[ques_id] = {
                "finish_count": finish_count,
                "total_count": total_count,
                "progress": round((finish_count / total_count * 100), 2) if total_count != 0 else 0
            }
        #     if total_count and finish_count == total_count:
        #         finish_ques_id_list.append(ques_id)
        # if finish_ques_id_list:
        #     # 将定标状态改为已定标
        #     new_session.query(QuesSetStdState).filter(QuesSetStdState.ques_id.in_(finish_ques_id_list)).update({
        #         QuesSetStdState.set_std_state: 3,
        #         QuesSetStdState.running_state: 7,
        #         QuesSetStdState.set_std_end_time: format_now_time()
        #     })
        #     new_session.commit()
    return process_dict, finish_ques_id_list


def batch_commit_supp_data(new_session, update_data):
    batch_size = 2000  # 每批处理的数量
    total_batches = (len(update_data) + batch_size - 1) // batch_size

    for batch in range(total_batches):
        start = batch * batch_size
        end = start + batch_size
        batch_data = update_data[start:end]
        try:
            new_session.bulk_update_mappings(SameStuAnswerGroup, batch_data)
            new_session.commit()
            logger.info(f"批次 {batch + 1}/{total_batches} 补充数据成功")
        except Exception as e:
            logger.error(f"批次 {batch + 1}/{total_batches} 补充数据失败: {e}")
            new_session.rollback()


def supp_stu_count_data(monitor_id, monitor_type, index):
    """
    补充分组表的 stu_count 和 answer_percentage 字段
    """
    new_session = next(session_depend())
    update_monitor_cal(new_session, monitor_id, monitor_type, index, "2")
    ques_id_dict, update_data = {}, []
    group_id_info = new_session.query(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.ques_id) \
        .filter(SameStuAnswerGroup.stu_count.is_(None)).all()
    if group_id_info:
        for group_id, ques_id in group_id_info:
            if ques_id in ques_id_dict:
                ques_id_dict[ques_id].append(group_id)
            else:
                ques_id_dict[ques_id] = [group_id]

    for ques_id, group_id_list in ques_id_dict.items():
        ques_group_id_info = new_session.query(StuAnswerDetail.same_answer_group_id).filter(StuAnswerDetail.ques_id == ques_id).all()
        ques_group_id = [i[0] for i in ques_group_id_info] if ques_group_id_info else []
        # group_id_list_length = len(group_id_list)
        for group_id in group_id_list:
            stu_count = ques_group_id.count(group_id)
            # answer_percentage = round_half_up(stu_count / group_id_list_length * 100, 7)
            update_data.append({
                "same_answer_group_id": group_id,
                "ques_id": ques_id,
                "stu_count": stu_count
                # "answer_percentage": f"{answer_percentage:.7f}"  # 使用格式化字符串输出，避免科学计数法
            })

        if update_data:
            batch_commit_supp_data(new_session, update_data)
            update_data.clear()
            logger.info(f"试题 {ques_id} 补充作答数据全部完成")

    logger.info("补充作答数据全部完成")
    update_monitor_cal(new_session, monitor_id, monitor_type, index, "3")
    return True


def get_set_std_detail_condition(same_answer_group_id, answer_cluster, mark_state, stu_score, search_time, small_ques_order):
    # mark_state: 评分状态（对应 AI 定标的详情里的 AI 定标状态），1 表示未定标，2 表示已定标（定标成功），3 表示定标异常，4 表示重新定标
    group_id_query = SameStuAnswerGroup.same_answer_group_id == same_answer_group_id if same_answer_group_id else True
    if answer_cluster:
        # 支持使用*号座位通配符模糊匹配
        if "*" in answer_cluster:
            replace_pattern = answer_cluster.replace("*", "%")
        else:
            replace_pattern = f"%{answer_cluster}%"
        answer_cluster_query = SameStuAnswerGroup.stu_answer.ilike(replace_pattern)
    else:
        answer_cluster_query = True
    _, _, ai_score_query = common_range_filter(SameStuAnswerGroup.ai_score, stu_score) if stu_score else (True, None, True)
    mark_time_query = SameStuAnswerGroup.ai_mark_time.between(*search_time) if search_time else True
    small_ques_order_query = SameStuAnswerGroup.small_ques_order == small_ques_order if small_ques_order else True
    state_query = True
    if state_query:
        if mark_state in [1, 2]:
            state_query = and_(SameStuAnswerGroup.mark_state == mark_state, SameStuAnswerGroup.mark_priority.is_(None))
        elif mark_state == 3:
            state_query = and_(SameStuAnswerGroup.mark_state.in_([3, 4]), SameStuAnswerGroup.mark_priority.is_(None))
        else:
            state_query = SameStuAnswerGroup.mark_priority.isnot(None)
    condition = and_(group_id_query, answer_cluster_query, state_query, ai_score_query, mark_time_query, small_ques_order_query)
    return condition


def get_ques_set_std_state(new_session, ques_id: str):
    """
    获取试题当前运行状态
    任务状态，1 表示正在获取数据，2 表示正在标记中，3 表示进行中，4 表示暂停，5 表示正在取消中，6 表示已取消，7 表示已完成
    """
    running_state, pause_state, set_std_state = new_session.query(QuesSetStdState.running_state, QuesSetStdState.pause_state, QuesSetStdState.set_std_state).filter(
        QuesSetStdState.ques_id == ques_id).first()
    return running_state, pause_state, set_std_state


def get_ques_list_set_std_state(new_session, ques_id_list: list):
    """
    获取多个试题当前运行状态
    任务状态，1 表示正在获取数据，2 表示正在标记中，3 表示进行中，4 表示暂停，5 表示正在取消中，6 表示已取消，7 表示已完成
    """
    ques_data = []
    info = new_session.query(QuesSetStdState.state_id, QuesSetStdState.ques_id, QuesSetStdState.running_state,
                             QuesSetStdState.set_std_state, QuesSetStdState.pause_state).filter(
        QuesSetStdState.ques_id.in_(ques_id_list)).all()
    for state_id, ques_id, running_state, set_std_state, pause_state in info:
        item = {
            "state_id": state_id,
            "ques_id": ques_id,
            "running_state": running_state,
            "set_std_state": set_std_state,
            "pause_state": pause_state
        }
        ques_data.append(item)
    return ques_data


def get_again_set_std_detail(ques_id, same_answer_group_id, answer_cluster, mark_state, stu_score, search_time, small_ques_order, user):
    """
    获取重新AI定标的数据
    """
    req_data = {
        "page_size": -1,
        "ques_id": ques_id,
        "same_answer_group_id": same_answer_group_id,
        "stu_answer": answer_cluster,
        "mark_state": mark_state,
        "stu_score": stu_score,
        "search_time": search_time,
        "small_ques_order": small_ques_order
    }
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/set_std/ques_set_std_detail"
    res, msg = request_api(url, "POST", req_data, token, "重新AI定标的数据")
    return res, msg


def check_ai_terminal_alive():
    """
    检测 AI 服务是否存活
    """
    original_url = configs.SET_STD_URL

    # 解析 URL
    parsed_url = urlparse(original_url)

    # 替换路径部分
    new_parsed_url = parsed_url._replace(path="/health")

    # 重新组合 URL
    new_url = urlunparse(new_parsed_url)
    try:
        res = requests.get(new_url, timeout=1)
        if res.status_code == 200:
            return True
    except:
        return False


def get_set_std_error_num(new_session, ques_id_list):
    """
    获取 AI 定标失败的数量
    """
    error_num_dict = {}
    error_info = new_session.query(SameStuAnswerGroup.ques_id, SameStuAnswerGroup.same_answer_group_id).filter(
        and_(SameStuAnswerGroup.ques_id.in_(ques_id_list), SameStuAnswerGroup.mark_state.in_([3, 4]))).all()
    if error_info:
        for ques_id, group_id in error_info:
            if ques_id in error_num_dict:
                error_num_dict[ques_id] += 1
            else:
                error_num_dict[ques_id] = 1
    return error_num_dict


def create_ques_set_std_data(new_session, curr_user_id):
    """
    创建定标数据
    """
    ques_id_info = new_session.query(StuAnswer.ques_id).filter(StuAnswer.ques_type_code.in_(["D", "E", "F"])).all()
    ques_id_list = list(set([i[0] for i in ques_id_info])) if ques_id_info else []
    if ques_id_list:
        set_std_ques_info = new_session.query(QuesSetStdState.ques_id).all()
        set_std_ques_id = [i[0] for i in set_std_ques_info] if set_std_ques_info else []
        need_create_ques_id = [i for i in ques_id_list if i not in set_std_ques_id]
        need_create_ques_length = len(need_create_ques_id)
        add_list = []
        for ques_id in need_create_ques_id:
            add_item = QuesSetStdState(state_id=configs.snow_worker.get_id(), ques_id=ques_id, set_std_state=1, running_state=0, c_user_id=curr_user_id, from_tool=2, created_time=format_now_time())
            add_list.append(add_item)
            if len(add_list) % 1000 == 0 or len(add_list) == need_create_ques_length:
                new_session.add_all(add_list)
                new_session.commit()


def get_paper_detail_by_ques_id(new_session):
    paper_detail_dict = {}
    paper_detail_info = new_session.query(PaperDetail.paper_id, PaperDetail.ques_id, PaperDetail.ques_order,
                                          PaperDetail.ques_score_list).all()
    if not paper_detail_info:
        return True, None, {}

    for paper_id, ques_id, ques_order, ques_score_list in paper_detail_info:
        item = {
            "ques_order": re.sub(r'^0+', '', ques_order) if ques_order else None,
            "ques_score_list": ques_score_list
        }
        if paper_id not in paper_detail_dict:
            paper_detail_dict[paper_id] = {ques_id: item}
        else:
            paper_detail_dict[paper_id][ques_id] = item
    return True, None, paper_detail_dict


def check_has_std_answer(new_session, ques_id_list):
    """
    检查填空题是否有参考答案，简答题是否有得分点
    """
    small_ques_info = new_session.query(SmallExamQuestion.ques_code, SmallExamQuestion.ques_type_code,
                                        SmallExamQuestion.standard_answer, SmallExamQuestion.ques_mark_point).filter(
        SmallExamQuestion.parent_ques_id.in_(ques_id_list)).all()
    if small_ques_info:
        ques_info = small_ques_info
    else:
        ques_info = new_session.query(ExamQuestion.ques_code, ExamQuestion.ques_type_code, ExamQuestion.standard_answer,
                                      ExamQuestion.ques_mark_point).filter(ExamQuestion.ques_id.in_(ques_id_list)).all()
    d_ques_code_list, e_ques_code_list = [], []
    if ques_info:
        for ques_code, ques_type_code, standard_answer, ques_mark_point in ques_info:
            if ques_type_code == "D":
                if not standard_answer:
                    d_ques_code_list.append(ques_code)
            elif ques_type_code == "E":
                if not ques_mark_point:
                    e_ques_code_list.append(ques_code)
    if d_ques_code_list:
        d_ques_code_list = list(set(d_ques_code_list))
        return False, f"试题编号为：{'，'.join(d_ques_code_list)} 的试题没有参考答案，请补充"
    if e_ques_code_list:
        return False, f"试题编号为：{'，'.join(e_ques_code_list)} 的试题没有评分标准，请补充"
    return True, None


def get_has_answer_ques_id_list(new_session, is_reload=False):
    # 查询有考生作答信息的试题
    redis = next(redis_session())
    if not is_reload:
        has_answer_ques_id_list = redis.get("has_answer_ques_id_list")
        if has_answer_ques_id_list:
            return json.loads(has_answer_ques_id_list)
    ques_id_info = new_session.query(StuAnswer.ques_id).all()
    has_answer_ques_id_list = list(set([i[0] for i in ques_id_info])) if ques_id_info else []
    redis.set("has_answer_ques_id_list", json.dumps(has_answer_ques_id_list))
    return has_answer_ques_id_list


def get_ques_set_std_info(project_id, subject_id, paper_id, ques_type_code_list, ques_order, small_ques_num, ques_code, set_std_state_list, user):
    """
    获取AI定标的数据
    """
    req_data = {
        "page_size": -1,
        "project_id": project_id,
        "subject_id": subject_id,
        "paper_id": paper_id,
        "ques_type_code_list": ques_type_code_list,
        "ques_order": ques_order,
        "small_ques_num": small_ques_num,
        "ques_code": ques_code,
        "set_std_state_list": set_std_state_list
    }
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/set_std/get_ques_set_std_list"
    res, msg = request_api(url, "POST", req_data, token, "获取AI定标的试题列表")
    return res, msg


if __name__ == '__main__':
    print(check_ai_terminal_alive())


