import copy
import math
import re
from decimal import Decimal

import pandas as pd
from fastapi import APIRouter, Depends
from settings import logger
from sqlalchemy import and_, func
from sqlalchemy.orm import Session
from typing import Any

from apps.data_statistics.schemas import ManualReadTaskIdScoreTimeReq, ManualReadTaskIdTimeReq
from apps.data_statistics.services import get_curr_user_task_list
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.models.models import ManualDistributeAnswer, ManualMark, ManualArbitrateQuality, ManualReadTaskGroup, \
    ManualGroupUser, UserInfo
from factory_apps.mysql_db.databases import session_depend
from helper import response_utils
from utils.time_func import add_days_to_date
from utils.utils import round_half_up, sum_with_precision

arbitrate_data_router = APIRouter()


@arbitrate_data_router.post(path="/arbitrate_finish_situation_pie", response_model=BaseResponse,
                            summary="组长主页：整体完成情况环形图")
async def arbitrate_finish_situation_pie(user: Any = Depends(get_current_user),
                                         new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 组长主页：整体完成情况环形图")
    curr_user_id = user.get("user_id")
    task_id_list = get_curr_user_task_list(new_session, curr_user_id, "4")
    # 已仲裁数量
    arbitrated_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id.in_(task_id_list), ManualArbitrateQuality.aq_type == 1,
             ManualArbitrateQuality.aq_state == 2)).count()

    all_arbitrate_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(and_(
        ManualArbitrateQuality.m_read_task_id.in_(task_id_list), ManualArbitrateQuality.aq_type == 1)).count()

    data = {
        "x_data": ["已完成", "剩余"],
        "y_data": [
            {"value": arbitrated_count, "name": "已完成"},
            {"value": all_arbitrate_count - arbitrated_count, "name": "剩余"}
        ]
    }
    logger.info("获取组长主页：整体完成情况环形图成功")
    return BaseResponse(data=data, msg="获取组长主页：整体完成情况环形图成功")


@arbitrate_data_router.post(path="/entirety_arbitrate_number_bar", response_model=BaseResponse,
                            summary="组长主页：整体阅卷数量柱状图")
async def entirety_arbitrate_number_bar(user: Any = Depends(get_current_user),
                                        new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 组长主页：整体阅卷数量柱状图")
    curr_user_id = user.get("user_id")
    task_id_list = get_curr_user_task_list(new_session, curr_user_id, "4")
    # 已仲裁数量
    arbitrated_time = new_session.query(ManualArbitrateQuality.updated_time).filter(
        and_(ManualArbitrateQuality.m_read_task_id.in_(task_id_list), ManualArbitrateQuality.aq_type == 1,
             ManualArbitrateQuality.aq_state == 2)).all()

    forenoon_count, afternoon_count = 0, 0
    if arbitrated_time:
        for read_item in arbitrated_time:
            read_time = read_item[0]
            read_time_hour = read_time.hour
            if read_time_hour < 11:
                forenoon_count += 1
            else:
                afternoon_count += 1

    data = {
        "legend": ["数量"],
        "x_data": ["上午", "下午"],
        "y_data": [forenoon_count, afternoon_count]
    }
    logger.info("获取组长主页：整体阅卷数量柱状图成功")
    return BaseResponse(data=data, msg="获取组长主页：整体阅卷数量柱状图成功")


@arbitrate_data_router.post(path="/entirety_arbitrate_speed_line", response_model=BaseResponse,
                            summary="组长主页：整体仲裁速度折线图")
async def entirety_arbitrate_speed_line(user: Any = Depends(get_current_user),
                                        new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 组长主页：整体仲裁速度折线图")
    curr_user_id = user.get("user_id")
    task_id_list = get_curr_user_task_list(new_session, curr_user_id, "4")
    arbitrated_time = new_session.query(ManualArbitrateQuality.updated_time).filter(
        and_(ManualArbitrateQuality.m_read_task_id.in_(task_id_list), ManualArbitrateQuality.aq_type == 1,
             ManualArbitrateQuality.aq_state == 2)).all()
    time_marked_count_dict = {}
    if arbitrated_time:
        for read_item in arbitrated_time:
            read_time = read_item[0]
            read_time_hour = f"{read_time.hour + 1}:00"
            if read_time_hour in time_marked_count_dict:
                time_marked_count_dict[read_time_hour] += 1
            else:
                time_marked_count_dict[read_time_hour] = 1

    sorted_time_score_dict = dict(sorted(time_marked_count_dict.items()))
    hour_list = list(sorted_time_score_dict.keys())
    marked_count = list(sorted_time_score_dict.values())

    data = {
        "legend": ["完成数量"],
        "x_data": hour_list,
        "y_data": marked_count
    }

    logger.info("获取组长主页：整体仲裁速度折线图成功")
    return BaseResponse(data=data, msg="获取组长主页：整体仲裁速度折线图成功")


@arbitrate_data_router.post(path="/entirety_arbitrate_situation_pie", response_model=BaseResponse,
                            summary="组长主页：整体仲裁情况饼图")
async def entirety_arbitrate_situation_pie(user: Any = Depends(get_current_user),
                                           new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取组长主页：整体仲裁情况饼图")
    curr_user_id = user.get("user_id")
    task_id_list = get_curr_user_task_list(new_session, curr_user_id, "4")
    # 需仲裁数量
    all_arbitrate_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(and_(
        ManualArbitrateQuality.m_read_task_id.in_(task_id_list), ManualArbitrateQuality.aq_type == 1)).count()

    # 所有已分配的数量
    all_distri_count = new_session.query(ManualDistributeAnswer.distri_answer_id).filter(
        ManualDistributeAnswer.m_read_task_id.in_(task_id_list)).count()

    data = {
        "x_data": ["需仲裁数量", "无需仲裁数量"],
        "y_data": [
            {"value": all_arbitrate_count, "name": "需仲裁数量"},
            {"value": all_distri_count - all_arbitrate_count, "name": "无需仲裁数量"}
        ]
    }
    logger.info("获取组长主页：整体仲裁情况饼图")
    return BaseResponse(data=data, msg="获取组长主页：整体仲裁情况饼图")


@arbitrate_data_router.post(path="/task_per_expert_score_trend_line", response_model=BaseResponse,
                            summary="组长主页：单一任务各专家给分走向折线图")
async def task_per_expert_score_trend_line(query: ManualReadTaskIdScoreTimeReq, user: Any = Depends(get_current_user),
                                           new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取组长主页：单一任务各专家给分走向折线图")
    curr_user_id = user.get("user_id")
    m_read_task_id, ques_score_list, search_date, ques_code = query.model_dump().values()

    start_date, end_date = search_date[0], add_days_to_date(search_date[1], 1)
    if len(search_date) != 2:
        return BaseResponse(code=response_utils.params_error, msg="请输入合法的日期区间")
    date_query_condition = ManualMark.created_time.between(start_date, end_date)

    time_range = pd.date_range(start=start_date, end=f"{search_date[1]} 23:00", freq="H")
    time_score_dict = {str(i).replace(":00:00", ":00"): [] for i in time_range.to_list()}
    raw_time_score_dict = copy.deepcopy(time_score_dict)

    group_info = new_session.query(ManualReadTaskGroup.manual_group_id) \
        .join(ManualGroupUser, ManualGroupUser.manual_group_id == ManualReadTaskGroup.manual_group_id) \
        .filter(and_(ManualReadTaskGroup.m_read_task_id == m_read_task_id, ManualGroupUser.user_id == curr_user_id,
                     ManualGroupUser.role_id == "4")).all()
    group_id_list = [i[0] for i in group_info] if group_info else []

    expert_user_id_info = new_session.query(ManualGroupUser.user_id).filter(
        and_(ManualGroupUser.manual_group_id.in_(group_id_list), ManualGroupUser.role_id == "3")).all()

    expert_user_id_list = [i[0] for i in expert_user_id_info] if expert_user_id_info else []

    expert_name_list, all_expert_user_mark_list = [], []
    for expert_user_id in expert_user_id_list:
        # 获取该阅卷专家对整道题的评分
        expert_read_info = new_session.query(func.group_concat(ManualDistributeAnswer.distri_answer_id),
                                             func.sum(ManualMark.expert_mark_score), func.max(ManualMark.created_time),
                                             func.max(UserInfo.name)) \
            .join(ManualMark, ManualMark.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
            .join(UserInfo, UserInfo.user_id == ManualMark.mark_person_id) \
            .filter(
            and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == expert_user_id,
                 date_query_condition)) \
            .group_by(ManualDistributeAnswer.stu_secret_num).all()
        if expert_read_info:
            for _, mark_score, read_time, expert_name in expert_read_info:
                if expert_name not in expert_name_list:
                    expert_name_list.append(expert_name)
                read_time_hour = re.sub(r":.*", ":00", str(read_time))
                if read_time_hour in time_score_dict:
                    time_score_dict[read_time_hour].append(mark_score)
                else:
                    time_score_dict[read_time_hour] = [mark_score]

            expert_user_mark_list = []
            for score_list in list(time_score_dict.values()):
                if score_list:
                    average_score = round_half_up(sum_with_precision(score_list) / len(score_list), 2)
                    expert_user_mark_list.append(average_score)
                else:
                    expert_user_mark_list.append(None)
            time_score_dict = copy.deepcopy(raw_time_score_dict)
            all_expert_user_mark_list.append(expert_user_mark_list)

    time_list = list(time_score_dict.keys())

    data = {
        "legend": expert_name_list,
        "x_data": time_list,
        "y_data": all_expert_user_mark_list
    }
    logger.info("获取组长主页：单一任务各专家给分走向折线图成功")
    return BaseResponse(data=data, msg="获取组长主页：单一任务各专家给分走向折线图成功")


@arbitrate_data_router.post(path="/task_per_expert_score_distribution_bar",
                            response_model=BaseResponse, summary="组长主页：单一任务各专家给分分布柱状图")
async def task_per_expert_score_distribution_bar(query: ManualReadTaskIdScoreTimeReq,
                                                 user: Any = Depends(get_current_user),
                                                 new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取组长主页：单一任务各专家给分分布柱状图")
    curr_user_id = user.get("user_id")
    m_read_task_id, ques_score_list, search_date, ques_code = query.model_dump().values()

    date_query_condition = True
    if search_date:
        if len(search_date) != 2:
            return BaseResponse(code=response_utils.params_error, msg="请输入合法的日期区间")
        date_query_condition = ManualMark.created_time.between(search_date[0], add_days_to_date(search_date[1], 1))

    ques_score = sum_with_precision(ques_score_list)
    score_dict = {}
    per_ques_score_list = math.ceil(ques_score + 1)
    for i in range(per_ques_score_list):
        score_dict[i] = None
    score_list = list(score_dict.keys())
    raw_score_dict = copy.deepcopy(score_dict)

    group_info = new_session.query(ManualReadTaskGroup.manual_group_id) \
        .join(ManualGroupUser, ManualGroupUser.manual_group_id == ManualReadTaskGroup.manual_group_id) \
        .filter(and_(ManualReadTaskGroup.m_read_task_id == m_read_task_id, ManualGroupUser.user_id == curr_user_id,
                     ManualGroupUser.role_id == "4")).all()
    group_id_list = [i[0] for i in group_info] if group_info else []

    expert_user_id_info = new_session.query(ManualGroupUser.user_id).filter(
        and_(ManualGroupUser.manual_group_id.in_(group_id_list), ManualGroupUser.role_id == "3")).all()

    expert_user_id_list = [i[0] for i in expert_user_id_info] if expert_user_id_info else []

    expert_user_mark_list, expert_name_list = [], []
    for expert_user_id in expert_user_id_list:
        # 获取该阅卷专家对整道题的评分
        expert_read_info = new_session.query(func.group_concat(ManualDistributeAnswer.distri_answer_id),
                                             func.sum(ManualMark.expert_mark_score), func.max(UserInfo.name)) \
            .join(ManualMark, ManualMark.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
            .join(UserInfo, UserInfo.user_id == ManualMark.mark_person_id) \
            .filter(
            and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == expert_user_id,
                 date_query_condition)) \
            .group_by(ManualDistributeAnswer.stu_secret_num).all()
        if expert_read_info:
            for _, mark_score, expert_name in expert_read_info:
                if expert_name not in expert_name_list:
                    expert_name_list.append(expert_name)
                mark_score = int(mark_score)
                try:
                    index = score_list.index(mark_score)
                except ValueError:
                    continue
                if score_dict[index] is None:
                    score_dict[index] = 0
                score_dict[index] += 1
            expert_user_mark_list.append(list(score_dict.values()))
            score_dict = copy.deepcopy(raw_score_dict)

    data = {
        "legend": expert_name_list,
        "x_data": score_list,
        "y_data": expert_user_mark_list
    }
    logger.info("获取组长主页：单一任务各专家给分分布柱状图")
    return BaseResponse(data=data, msg="获取组长主页：单一任务各专家给分分布柱状图")


@arbitrate_data_router.post(path="/task_leader_arbitrate_bar", response_model=BaseResponse,
                            summary="组长主页：单一任务专家仲裁情况柱状图")
async def task_arbitrate_pie(query: ManualReadTaskIdTimeReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取组长主页：单一任务仲裁情况柱状图")
    curr_user_id = user.get("user_id")
    m_read_task_id, search_date = query.m_read_task_id, query.search_date
    if len(search_date) != 2:
        return BaseResponse(code=response_utils.params_error, msg="请输入合法的日期区间")
    date_query_condition = ManualArbitrateQuality.created_time.between(search_date[0],
                                                                       add_days_to_date(search_date[1], 1))

    group_info = new_session.query(ManualReadTaskGroup.manual_group_id) \
        .join(ManualGroupUser, ManualGroupUser.manual_group_id == ManualReadTaskGroup.manual_group_id) \
        .filter(and_(ManualReadTaskGroup.m_read_task_id == m_read_task_id, ManualGroupUser.user_id == curr_user_id,
                     ManualGroupUser.role_id == "4")).all()
    group_id = group_info[0][0]

    expert_user_id_info = new_session.query(ManualGroupUser.user_id, ManualGroupUser.manual_group_id).filter(
        and_(ManualGroupUser.manual_group_id == group_id, ManualGroupUser.role_id == "3")).all()
    expert_user_id_list = []
    expert_group_map = {}
    if expert_user_id_info:
        for user_id, group_id in expert_user_id_info:
            expert_group_map[user_id] = group_id
            expert_user_id_list.append(user_id)

    need_arbitrate_count = new_session.query(func.count(ManualArbitrateQuality.distri_answer_id)) \
        .join(ManualDistributeAnswer,
              ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
        .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 1,
                     date_query_condition, ManualDistributeAnswer.manual_group_id == group_id)).scalar()

    mark_date_query_condition = ManualMark.created_time.between(search_date[0],
                                                                add_days_to_date(search_date[1], 1))
    mark_info = new_session.query(func.group_concat(ManualMark.distri_answer_id.distinct()), func.max(UserInfo.name),
                                  func.max(UserInfo.user_id)) \
        .join(UserInfo, UserInfo.user_id == ManualMark.mark_person_id) \
        .filter(and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id.in_(expert_user_id_list),
                     mark_date_query_condition)) \
        .group_by(ManualMark.mark_person_id).all()

    distri_answer_count = new_session.query(ManualDistributeAnswer.distri_answer_id) \
        .filter(and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id,
                     ManualDistributeAnswer.manual_group_id == group_id)).count()

    # 专家姓名列表、专家评分需仲裁数量列表、专家评分无需仲裁数量列表
    expert_name_list, expert_arbitrate_count_list, expert_not_arbitrate_count_list = [], [], []
    for _, expert_name, expert_user_id in mark_info:
        expert_name_list.append(expert_name)
        expert_arbitrate_count_list.append(need_arbitrate_count)
        expert_not_arbitrate_count_list.append(distri_answer_count - need_arbitrate_count)

    data = {
        "legend": ["需仲裁数量", "无需仲裁数量"],
        "x_data": expert_name_list,
        "y1_data": expert_arbitrate_count_list,
        "y2_data": expert_not_arbitrate_count_list
    }
    logger.info("获取组长主页：单一任务仲裁情况柱状图成功")
    return BaseResponse(data=data, msg="获取组长主页：单一任务仲裁情况柱状图成功")


@arbitrate_data_router.post(path="/task_per_expert_speed_bar", response_model=BaseResponse,
                            summary="组长主页：单一任务各专家阅卷速度柱状图")
async def task_per_expert_speed_bar(query: ManualReadTaskIdTimeReq, user: Any = Depends(get_current_user),
                                    new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取组长主页：单一任务各专家阅卷速度柱状图")
    curr_user_id = user.get("user_id")
    m_read_task_id, search_date = query.m_read_task_id, query.search_date
    if len(search_date) != 2:
        return BaseResponse(code=response_utils.params_error, msg="请输入合法的日期区间")
    date_query_condition = ManualMark.created_time.between(search_date[0], add_days_to_date(search_date[1], 1))

    group_info = new_session.query(ManualReadTaskGroup.manual_group_id) \
        .join(ManualGroupUser, ManualGroupUser.manual_group_id == ManualReadTaskGroup.manual_group_id) \
        .filter(and_(ManualReadTaskGroup.m_read_task_id == m_read_task_id, ManualGroupUser.user_id == curr_user_id,
                     ManualGroupUser.role_id == "4")).all()
    group_id_list = [i[0] for i in group_info] if group_info else []

    expert_user_id_info = new_session.query(ManualGroupUser.user_id).filter(
        and_(ManualGroupUser.manual_group_id.in_(group_id_list), ManualGroupUser.role_id == "3")).all()

    expert_user_id_list = [i[0] for i in expert_user_id_info] if expert_user_id_info else []

    mark_info = new_session.query(func.count(ManualMark.manual_mark_id), func.max(UserInfo.name)) \
        .join(UserInfo, UserInfo.user_id == ManualMark.mark_person_id) \
        .filter(and_(ManualMark.m_read_task_id == m_read_task_id, date_query_condition,
                     ManualMark.mark_person_id.in_(expert_user_id_list))) \
        .group_by(ManualMark.mark_person_id).all()

    expert_name_list, expert_mark_count_list = [], []
    for expert_mark_count, expert_name in mark_info:
        expert_name_list.append(expert_name)
        expert_mark_count_list.append(expert_mark_count)

    data = {
        "legend": "数量",
        "x_data": expert_name_list,
        "y_data": expert_mark_count_list
    }
    logger.info("获取组长主页：单一任务各专家阅卷速度柱状图成功")
    return BaseResponse(data=data, msg="获取组长主页：单一任务各专家阅卷速度柱状图成功")
