from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import func, and_,case, distinct
from sqlalchemy.orm import Session, aliased
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
import logging
import traceback
from typing import Any, Optional
from apps.human_statistics.models import HumanStatisticsSmallGroup
from apps.human_mark_group.models import HumanMarkGroup
from apps.human_task_manage.models import HumanRoundDistriAnswer, HumanReadTask,HumanReadTaskRound
from apps.human_mark_exception.models import HumanAnswerException
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from settings import logger
from apps.models.models import UserInfo, ManualReadTask,Subject, UserDataPermission,Project,QuesUsed
from apps.users.services import get_current_user
from apps.human_statistics.schemas import GetTasksReq,GCalculateReviewedCountReq, QCalculateAverageSpeedReq, QCalculateMarkTotalReq, GCalculateEffectiveReviewCountReq, QCalculateArbitrationStatusReq, \
    TCalculateMarkTotalReq,TGroupProgressReq,ExceptionStatisticsReq,GetGroupIdByQuesCodeReq
from apps.human_statistics.g_score_analysis_views import calculate_effective_review_count_api
from utils.utils import round_half_up
q_score_analysis_router = APIRouter()

@q_score_analysis_router.post(path="/calculate_average_speed", response_model=BaseResponse, summary="题组页面：计算平均速度")
async def calculate_average_speed_api(
        query: QCalculateAverageSpeedReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 题组页面：计算平均速度")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 判断是单天还是多天查询
    is_single_day = False
    if start_date and end_date:
        # 计算日期差
        date_diff = (end_date - start_date).days + 1
        if date_diff == 1:
            is_single_day = True

    # 如果没有传入日期范围参数，则使用多天数据的计算逻辑
    if not query.date_range:
        is_single_day = False

    try:
        # 查询分组数据并计算平均速度
        reviewed_alias = aliased(HumanStatisticsSmallGroup)
        work_alias = aliased(HumanStatisticsSmallGroup)

        result = new_session.query(
            reviewed_alias.group_id,
            HumanMarkGroup.group_name,
            func.sum(reviewed_alias.statistics_result_1).label('total_reviewed'),
            func.sum(work_alias.statistics_result_1).label('total_work_seconds')
        ).outerjoin(
            work_alias,
            and_(
                reviewed_alias.group_id == work_alias.group_id,
                reviewed_alias.date == work_alias.date,
                work_alias.statistics_type == 10  # 工作时间类型
            )
        ).outerjoin(
            HumanMarkGroup,
            reviewed_alias.group_id == HumanMarkGroup.group_id
        ).filter(
            reviewed_alias.round_count == query.round_count,
            reviewed_alias.task_type == query.task_type,
            reviewed_alias.statistics_type == 4  # 已阅量类型
        )

        # 添加日期范围条件（单天或多天）
        if is_single_day:
            result = result.filter(reviewed_alias.date == start_date)
        else:
            if start_date:
                result = result.filter(reviewed_alias.date >= start_date)
            if end_date:
                result = result.filter(reviewed_alias.date <= end_date)

        # 添加可选参数过滤条件
        if query.task_id:
            result = result.filter(reviewed_alias.task_id == query.task_id)
        if query.subject_id:
            result = result.filter(reviewed_alias.subject_id == query.subject_id)
        if query.project_id:
            result = result.filter(reviewed_alias.project_id == query.project_id)
        if query.ques_group_id:
            result = result.filter(reviewed_alias.ques_group_id == query.ques_group_id)

        result = result.group_by(
            reviewed_alias.group_id,
            HumanMarkGroup.group_name
        )

        results = result.all()

        # 计算平均速度并构建响应数据
        group_data = []
        total_reviewed_all = 0
        total_work_all = 0

        for row in results:
            group_id, group_name, total_reviewed_val, total_work_val = row

            # 处理NULL值
            total_reviewed = float(total_reviewed_val) if total_reviewed_val else 0.0
            total_work_seconds = float(total_work_val) if total_work_val else 0.0

            # 累加全局统计
            total_reviewed_all += total_reviewed
            total_work_all += total_work_seconds

            # 计算小组速度（处理除零）
            speed = 0.0
            if total_work_seconds > 0:
                speed = round_half_up(total_reviewed / (total_work_seconds / 3600), 2)

            group_data.append({
                "group_id": group_id,
                "group_name": group_name or "未知小组",
                "average_speed": speed
            })

        # 计算整体平均速度（处理除零）
        overall_speed = 0.0
        if total_work_all > 0:
            overall_speed = round_half_up(total_reviewed_all / (total_work_all / 3600), 2)

        # 构建workload_data结构
        x_data = [item["group_name"] for item in group_data]
        y_data = [item["average_speed"] for item in group_data]

        # 构建响应数据
        response_data = {
            "workload_data": {
                "legend": ["评分速度"],
                "x_data": x_data,
                "y_data": [y_data]  # 包装成二维数组以支持多系列扩展
            },
            "average_speed": overall_speed
        }

        # 添加调试信息
        logger.info(f"题组页面：计算平均速度，结果: {response_data}")
        return BaseResponse(data=response_data, msg="平均速度统计结果（份/小时）")

    except Exception as e:
        logger.error(f"计算平均速度失败：{e}")
        traceback.print_exc()
        return BaseResponse(data=[], msg="计算平均速度失败")


@q_score_analysis_router.post(path="/calculate_mark_total", response_model=BaseResponse, summary="题组页面：计算阅卷总量和已阅量")
async def calculate_mark_total_api(
        query: QCalculateMarkTotalReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 题组页面：计算阅卷总量和已阅量")

    try:
        # 参数校验：ques_group_id和task_id必须二选一
        has_group_id = bool(query.ques_group_id)
        has_task_id = bool(query.task_id)
        if not (has_group_id ^ has_task_id):
            return BaseResponse(data={"total": 0, "reviewed": 0}, msg="必须且只能传入ques_group_id或task_id")

        # 处理日期范围
        start_date, end_date = None, None
        if query.date_range:
            try:
                start_date_str, end_date_str = query.date_range.split('至')
                start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
            except ValueError:
                pass

        # 4. 计算阅卷总量
        total_count = 0
        task_filters = [HumanReadTask.project_id == query.project_id]
        if query.subject_id:
            task_filters.append(HumanReadTask.subject_id == query.subject_id)
        if query.task_type is not None:
            task_filters.append(HumanReadTask.task_type == query.task_type)

        ques_codes = new_session.query(HumanMarkGroup.ques_code).filter(HumanMarkGroup.group_id == query.ques_group_id).all()
        if ques_codes:
            # 将ques_codes转换为列表，因为query返回的是对象列表
            ques_code_list = [qc.ques_code for qc in ques_codes]
            used_counts = new_session.query(func.sum(QuesUsed.used_count).label("total_used")).filter(
                QuesUsed.ques_code.in_(ques_code_list)
            ).scalar()
            total_count = int(used_counts) if used_counts else 0

        # 5. 计算已阅量
        reviewed_count = 0
        reviewed_filters = [
            HumanStatisticsSmallGroup.statistics_type == 4,  # 已阅量
            HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id
        ]
        if query.project_id:
            reviewed_filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
        if query.subject_id:
            reviewed_filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
        if query.task_type is not None:
            reviewed_filters.append(HumanStatisticsSmallGroup.task_type == query.task_type)
        if query.round_count is not None:
            reviewed_filters.append(HumanStatisticsSmallGroup.round_count == query.round_count)

        # 添加日期范围条件
        if start_date:
            reviewed_filters.append(HumanStatisticsSmallGroup.date >= start_date)
        if end_date:
            reviewed_filters.append(HumanStatisticsSmallGroup.date <= end_date)

        reviewed_result = new_session.query(
            func.sum(HumanStatisticsSmallGroup.statistics_result_1).label("total_reviewed")
        ).filter(*reviewed_filters).scalar()
        reviewed_count = int(reviewed_result) if reviewed_result else 0.0

        # 返回总量和已阅量
        data = {
            "total": total_count,
            "reviewed": reviewed_count
        }

        # 添加调试信息
        logger.info(f"题组页面：计算阅卷总量 {total_count}，已阅量 {reviewed_count}")
        return BaseResponse(data=data, msg="阅卷总量及已阅量统计成功")

    except Exception as e:
        logger.error(f"计算阅卷总量失败：{e}")
        traceback.print_exc()
        return BaseResponse(data={"total": 0, "reviewed": 0}, msg="计算阅卷总量失败")

# @q_score_analysis_router.post(path="/calculate_mark_total", response_model=BaseResponse, summary="题组页面：计算阅卷总量")#双评加仲裁
# async def calculate_mark_total_api(
#         query: QCalculateMarkTotalReq,
#         user: Any = Depends(get_current_user),
#         new_session: Session = Depends(session_depend)
# ):
#     logger.info(f"{user['username']} 题组页面：计算阅卷总量、评分进度")
#
#     try:
#         # 参数校验：ques_group_id和task_id必须二选一
#         has_group_id = bool(query.ques_group_id)
#         has_task_id = bool(query.task_id)
#         if not (has_group_id ^ has_task_id):
#             return BaseResponse(data={"total": 0}, msg="必须且只能传入ques_group_id或task_id")
#
#         if query.task_id:
#             task_id = query.task_id
#         else:
#             if not has_group_id:
#                 return BaseResponse(data={"total": 0}, msg="必须提供任务ID或试题组ID")
#
#             # 通过ques_group_id获取试题编号
#             ques_codes = new_session.query(HumanMarkGroup.ques_code).filter(
#                 HumanMarkGroup.group_id == query.ques_group_id
#             ).all()
#
#             if not ques_codes:
#                 return BaseResponse(data={"total": 0}, msg="未找到关联试题编号")
#
#             ques_code_list = [q.ques_code for q in ques_codes]
#
#             # 通过试题编号和任务类型获取task_id
#             task = new_session.query(HumanReadTask).filter(
#                 HumanReadTask.ques_code.in_(ques_code_list),
#                 HumanReadTask.task_type == query.task_type
#             ).first()
#
#             if not task:
#                 return BaseResponse(data={"total": 0}, msg="未找到对应任务")
#
#             task_id = task.task_id
#
#         # 通过round_count获取round_id
#         task_round = new_session.query(HumanReadTaskRound).filter(
#             HumanReadTaskRound.task_id == task_id,
#             HumanReadTaskRound.round_count == query.round_count
#         ).first()
#
#         if not task_round:
#             return BaseResponse(data={"total": 0}, msg="未找到对应轮次")
#
#         # 计算统计结果
#         total = new_session.query(
#             func.count(HumanRoundDistriAnswer.stu_secret_num.distinct())
#         ).filter(
#             HumanRoundDistriAnswer.round_id == task_round.round_id,
#         ).scalar() or 0
#
#        # 创建参数字典用于已阅量查询
#         effective_params = {
#             "round_count": query.round_count,
#             "task_type": query.task_type
#         }
#         for field in ["subject_id", "task_id", "group_id", "project_id", "ques_group_id","date_range"]:
#             value = getattr(query, field, None)
#             if value is not None:
#                 effective_params[field] = value
#
#         # 获取已阅量
#         effective_query = GCalculateEffectiveReviewCountReq(**effective_params)
#         effective_result = await calculate_effective_review_count_api(effective_query, user, new_session)
#         effective = effective_result.data.get("total_count", 0) if effective_result.data else 0
#
#         # 计算进度（处理除零情况）
#         progress = round_half_up(effective / total * 100, 2) if total > 0 else 0.0
#
#         # 返回总量和评分进度
#         data = {
#             "total": total,
#             "progress": progress
#         }
#
#         # 添加调试信息
#         logger.info(f"题组页面：计算阅卷总量 {total}，评分进度 {progress}")
#         return BaseResponse(data=data, msg="阅卷总量及评分进度统计成功")
#
#     except Exception as e:
#         logger.error(f"计算阅卷总量失败：{e}")
#         traceback.print_exc()
#         return BaseResponse(data={"total": 0, "progress": 0.0}, msg="计算阅卷总量失败")


@q_score_analysis_router.post(path="/calculate_pending_review_count", response_model=BaseResponse, summary="题组页面：计算待阅量、评分进度")
async def calculate_pending_review_count_api(
        query: QCalculateMarkTotalReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 题组页面：计算待阅量")

    try:
        # 先获取阅卷总量（复用calculate_mark_total的逻辑）
        total_result = await calculate_mark_total_api(query, user, new_session)
        total = total_result.data.get("total", 0) if total_result.data else 0

        # 创建参数字典，只包含非None值
        effective_params = {
            "round_count": query.round_count,
            "task_type": query.task_type
        }

        # 添加可选参数（只添加存在且不为None的字段）
        for field in ["subject_id", "task_id", "group_id", "project_id", "ques_group_id"]:
            value = getattr(query, field, None)
            if value is not None:
                effective_params[field] = value

        # 创建已阅量请求对象
        effective_query = GCalculateEffectiveReviewCountReq(**effective_params)

        # 获取已阅量
        effective_result = await calculate_effective_review_count_api(effective_query, user, new_session)
        effective = effective_result.data.get("total_count", 0) if effective_result.data else 0

        # 计算待阅量
        pending = int(total) - int(effective)


        data = {
            "pending": pending
        }

        logger.info(f"题组页面：计算待阅量: {pending}")
        return BaseResponse(data=data, msg="题组页面：待阅量统计成功")

    except Exception as e:
        logger.error(f"待阅量统计失败: {str(e)}", exc_info=True)
        traceback.print_exc()
        return BaseResponse(data={"pending": 0, "progress": 0}, msg=f"待阅量统计失败: {str(e)}")


@q_score_analysis_router.post(path="/calculate_effective_review_count", response_model=BaseResponse,
                              summary="题组页面：计算有效评卷量")
async def calculate_effective_review_count_api(
        query: GCalculateEffectiveReviewCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 题组页面：计算有效评卷量")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 类型5查询
    query5 = new_session.query(
        HumanStatisticsSmallGroup.group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name'),
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label('count5')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.group_id == HumanMarkGroup.group_id
    )

    # 类型11查询
    query11 = new_session.query(
        HumanStatisticsSmallGroup.group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name'),
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label('count11')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.group_id == HumanMarkGroup.group_id
    )

    # 公共过滤条件
    filters = [
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

    # 日期范围条件
    if start_date:
        filters.append(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        filters.append(HumanStatisticsSmallGroup.date <= end_date)

    # 应用过滤条件
    query5 = query5.filter(*filters, HumanStatisticsSmallGroup.statistics_type == 5)
    query11 = query11.filter(*filters, HumanStatisticsSmallGroup.statistics_type == 11)

    # 执行查询
    data5 = query5.group_by(
        HumanStatisticsSmallGroup.group_id,
        HumanMarkGroup.group_name
    ).all()

    data11 = query11.group_by(
        HumanStatisticsSmallGroup.group_id,
        HumanMarkGroup.group_name
    ).all()

    # 数据合并
    data_map = {}
    for row in data5:
        group_id, group_name, value = row
        data_map.setdefault(group_id, {
            'group_id': group_id,
            'group_name': group_name,
            'total_count': 0
        })['total_count'] += int(value or 0)

    for row in data11:
        group_id, group_name, value = row
        data_map.setdefault(group_id, {
            'group_id': group_id,
            'group_name': group_name,
            'total_count': 0
        })['total_count'] += int(value or 0)

    # 排序处理
    group_data = sorted(data_map.values(), key=lambda x: x['total_count'], reverse=True)

    # 总量查询
    total5 = new_session.query(
        func.sum(HumanStatisticsSmallGroup.statistics_result_1)
    ).filter(
        HumanStatisticsSmallGroup.statistics_type == 5,
        *filters
    ).scalar() or 0

    total11 = new_session.query(
        func.sum(HumanStatisticsSmallGroup.statistics_result_1)
    ).filter(
        HumanStatisticsSmallGroup.statistics_type == 11,
        *filters
    ).scalar() or 0

    total_count = int(total5 + total11)

    # 构建响应数据
    response_data = {
        "total_count": total_count,
        "group_data": group_data
    }

    logger.info(f"题组页面：计算有效评卷量，总数: {total_count}")
    return BaseResponse(data=response_data, msg="获取题组页面：有效评卷量")


@q_score_analysis_router.post(path="/calculate_reviewed_count", response_model=BaseResponse,
                              summary="题组页面：计算已阅量")
async def calculate_reviewed_count_api(
        query: GCalculateReviewedCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 题组页面：计算已阅量")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 类型5查询（有效评卷量）
    query5 = new_session.query(
        HumanStatisticsSmallGroup.group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name'),
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label('count5')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.group_id == HumanMarkGroup.group_id
    )

    # 类型6查询（需仲裁数量）
    query6 = new_session.query(
        HumanStatisticsSmallGroup.group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name'),
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label('count6')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.group_id == HumanMarkGroup.group_id
    )

    # 公共过滤条件
    filters = [
        HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

    # 日期范围条件
    if start_date:
        filters.append(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        filters.append(HumanStatisticsSmallGroup.date <= end_date)

    # 应用过滤条件
    query5 = query5.filter(*filters, HumanStatisticsSmallGroup.statistics_type == 5)
    query6 = query6.filter(*filters, HumanStatisticsSmallGroup.statistics_type == 6)

    # 执行查询
    data5 = query5.group_by(
        HumanStatisticsSmallGroup.group_id,
        HumanMarkGroup.group_name
    ).all()

    data6 = query6.group_by(
        HumanStatisticsSmallGroup.group_id,
        HumanMarkGroup.group_name
    ).all()

    # 数据合并
    data_map = {}
    for row in data5:
        group_id, group_name, value = row
        data_map.setdefault(group_id, {
            'group_id': group_id,
            'group_name': group_name,
            'total_count': 0
        })['total_count'] += int(value or 0)

    for row in data6:
        group_id, group_name, value = row
        data_map.setdefault(group_id, {
            'group_id': group_id,
            'group_name': group_name,
            'total_count': 0
        })['total_count'] += int(value or 0)

    # 排序处理
    group_data = sorted(data_map.values(), key=lambda x: x['total_count'], reverse=True)

    # 构建workload_data结构
    x_data = [item['group_name'] for item in group_data]
    y_data = [[item['total_count'] for item in group_data]]  # 包装成二维数组以支持多系列扩展

    # 构建响应数据
    response_data = {
        "workload_data": {
            "legend": ["已阅量"],
            "x_data": x_data,
            "y_data": y_data
        },
        "total_count": sum(item['total_count'] for item in group_data)
    }

    logger.info(f"题组页面：计算已阅量，结果: {response_data}")
    return BaseResponse(data=response_data, msg="已阅量统计结果")


@q_score_analysis_router.post(path="/calculate_arbitration_status", response_model=BaseResponse,
                              summary="题组页面：计算仲裁情况")
async def calculate_arbitration_status_api(
        query: QCalculateArbitrationStatusReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 题组页面：计算仲裁情况")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 类型6查询（需仲裁数量）
    query6 = new_session.query(
        HumanStatisticsSmallGroup.group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name'),
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label('need_arbitration')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.group_id == HumanMarkGroup.group_id
    )

    # 类型7查询（仲裁量）
    query7 = new_session.query(
        HumanStatisticsSmallGroup.group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name'),
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label('processed_arbitration')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.group_id == HumanMarkGroup.group_id
    )

    # 公共过滤条件
    filters = [
        HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

    # 日期范围条件
    if start_date:
        filters.append(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        filters.append(HumanStatisticsSmallGroup.date <= end_date)

    # 应用过滤条件
    query6 = query6.filter(*filters, HumanStatisticsSmallGroup.statistics_type == 6)
    query7 = query7.filter(*filters, HumanStatisticsSmallGroup.statistics_type == 7)

    # 执行查询
    data6 = query6.group_by(
        HumanStatisticsSmallGroup.group_id,
        HumanMarkGroup.group_name
    ).all()

    data7 = query7.group_by(
        HumanStatisticsSmallGroup.group_id,
        HumanMarkGroup.group_name
    ).all()

    # 数据合并处理
    data_map = {}

    # 处理需仲裁数据（类型6）
    for row in data6:
        group_id, group_name, value = row
        data_map.setdefault(group_id, {
            'group_id': group_id,
            'group_name': group_name,
            'need_arbitration': int(value or 0),
            'processed_arbitration': 0
        })['need_arbitration'] = int(value or 0)

    # 处理已仲裁数据（类型7）
    for row in data7:
        group_id, group_name, value = row
        data_map.setdefault(group_id, {
            'group_id': group_id,
            'group_name': group_name,
            'need_arbitration': 0,
            'processed_arbitration': int(value or 0)
        })['processed_arbitration'] = int(value or 0)

    # 计算未处理仲裁数量（类型6 - 类型7）
    group_data = []
    for group_id, values in data_map.items():
        group_data.append({
            'group_id': group_id,
            'group_name': values['group_name'],
            'need_arbitration': values['need_arbitration'],
            'processed_arbitration': values['processed_arbitration'],
            'unprocessed_arbitration': max(0, values['need_arbitration'] - values['processed_arbitration'])
        })

    # 按已处理仲裁数量降序排序
    group_data.sort(key=lambda x: x['processed_arbitration'], reverse=True)

    # 构建workload_data结构
    x_data = [item['group_name'] for item in group_data]
    y1_data = [item['processed_arbitration'] for item in group_data]  # 已处理仲裁
    y2_data = [item['unprocessed_arbitration'] for item in group_data]  # 未处理仲裁

    # 构建响应数据
    response_data = {
        "workload_data": {
            "legend": ["已处理仲裁", "未处理仲裁"],
            "x_data": x_data,
            "y1_data": y1_data,
            "y2_data": y2_data
        },
        "processed_arbitration": sum(item['processed_arbitration'] for item in group_data),
        "unprocessed_arbitration": sum(item['unprocessed_arbitration'] for item in group_data)
    }

    logger.info(f"题组页面：计算仲裁情况，结果: {response_data}")
    return BaseResponse(data=response_data, msg="仲裁情况统计结果")


@q_score_analysis_router.post(path="/grading_total", response_model=BaseResponse, summary="总监控：计算阅卷总量、已分配量、评分进度")
async def calculate_grading_total_api(
        query: TCalculateMarkTotalReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 总监控：计算阅卷总量、已分配量、评分进度")

    try:
        # 1. 通过subject_id获取所有关联的task_id
        task_id_list = new_session.scalars(
            new_session.query(HumanReadTask.task_id).filter(
                HumanReadTask.subject_id == query.subject_id,
                HumanReadTask.task_type == query.task_type
            ).distinct()
        ).all()

        # 2. 如果没有找到关联试题编码，直接返回0
        if not task_id_list:
            logger.warning(f"未找到科目ID {query.subject_id} 对应的任务id")
            return BaseResponse(data={"阅卷总量": 0}, msg="未找到对应的任务id")

        # 3. 构建基础查询条件
        base_query = new_session.query(HumanReadTaskRound).filter(
            HumanReadTaskRound.round_count == query.round_count,
            HumanReadTaskRound.task_id.in_(task_id_list)
        )
        round_id_list = [task_round.round_id for task_round in base_query.all()] if base_query else []
        # 4. 统计去重后的stu_secret_num数量
        stu_secret_count = new_session.query(
            func.count(HumanRoundDistriAnswer.stu_secret_num.distinct())
        ).filter(
            HumanRoundDistriAnswer.round_id.in_(round_id_list)
        ).scalar() or 0
        
        # 计算总量（round_id数量 * 去重后的stu_secret_num数量）
        total = len(round_id_list) * stu_secret_count
        
        # 统计已分配量（is_distri=1且在round_id列表中）
        assigned_total = new_session.query(
            func.count(HumanRoundDistriAnswer.stu_secret_num.distinct())
        ).filter(
            HumanRoundDistriAnswer.round_id.in_(round_id_list),
            HumanRoundDistriAnswer.is_distri == 1
        ).scalar() or 0

        # 创建参数字典用于已阅量查询
        effective_params = {
            "round_count": query.round_count,
            "task_type": query.task_type
        }
        for field in ["subject_id", "project_id", "date_range"]:
            value = getattr(query, field, None)
            if value is not None:
                effective_params[field] = value

        # 获取已阅量
        effective_query = GCalculateEffectiveReviewCountReq(**effective_params)
        effective_result = await calculate_effective_review_count_api(effective_query, user, new_session)
        effective = effective_result.data.get("total_count", 0) if effective_result.data else 0

        # 计算进度（处理除零情况）
        progress = round_half_up(effective / total * 100, 2) if total > 0 else 0.0

        # 9. 返回总量、已分配量和评分进度
        data = {
            "total": total,
            "assigned_total": assigned_total,
            "progress": progress
        }

        logger.info(f"总监控：计算结果 - 总量={total}, 已分配={assigned_total}, 进度={progress}")
        return BaseResponse(data=data, msg="总量、已分配量、评分进度统计成功")

    except Exception as e:
        logger.error(f"总监控：统计失败：{str(e)}", exc_info=True)
        return BaseResponse(data={"total": 0, "assigned_total": 0, "progress": 0.0}, msg=f"统计失败: {str(e)}")


@q_score_analysis_router.post(path="/calculate_pending_review_count_total", response_model=BaseResponse, summary="总监控页面：计算待阅量")
async def calculate_pending_review_count_total_api(
        query: TCalculateMarkTotalReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 总监控页面：计算待阅量")

    try:
        # 先获取阅卷总量（使用calculate_grading_total_api）
        total_result = await calculate_grading_total_api(query, user, new_session)
        total = total_result.data.get("total", 0) if total_result.data else 0

        # 创建参数字典，只包含非None值
        effective_params = {
            "round_count": query.round_count,
            "task_type": query.task_type
        }

        # 添加可选参数（只添加存在且不为None的字段）
        for field in ["subject_id", "task_id", "group_id", "project_id", "ques_group_id"]:
            value = getattr(query, field, None)
            if value is not None:
                effective_params[field] = value

        # 创建已阅量请求对象
        effective_query = GCalculateEffectiveReviewCountReq(**effective_params)

        # 获取已阅量
        effective_result = await calculate_effective_review_count_api(effective_query, user, new_session)
        effective = effective_result.data.get("total_count", 0) if effective_result.data else 0

        # 计算待阅量
        pending = int(total) - int(effective)

        data = {
            "pending": pending
        }

        logger.info(f"总监控页面：计算待阅量: {pending}")
        return BaseResponse(data=data, msg="总监控页面：待阅量统计成功")

    except Exception as e:
        logger.error(f"总监控页面：待阅量统计失败: {str(e)}", exc_info=True)
        traceback.print_exc()
        return BaseResponse(data={"pending": 0}, msg=f"总监控页面：待阅量统计失败: {str(e)}")


@q_score_analysis_router.post(path="/calculate_reviewed_count_total", response_model=BaseResponse,
                              summary="总监控页面：工作量监控")
async def calculate_reviewed_count_api(
        query: GCalculateReviewedCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 总监控页面：工作量监控")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 类型5查询（有效评卷量）
    query5 = new_session.query(
        HumanStatisticsSmallGroup.ques_group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name'),
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label('count5')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.ques_group_id == HumanMarkGroup.group_id
    )

    # 类型6查询（需仲裁数量）
    query6 = new_session.query(
        HumanStatisticsSmallGroup.ques_group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name'),
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label('count6')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.ques_group_id == HumanMarkGroup.group_id
    )

    # 公共过滤条件
    filters = [
        HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)


    # 日期范围条件
    if start_date:
        filters.append(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        filters.append(HumanStatisticsSmallGroup.date <= end_date)

    # 应用过滤条件
    query5 = query5.filter(*filters, HumanStatisticsSmallGroup.statistics_type == 5)
    query6 = query6.filter(*filters, HumanStatisticsSmallGroup.statistics_type == 6)

    # 执行查询
    data5 = query5.group_by(
        HumanStatisticsSmallGroup.ques_group_id,
        HumanMarkGroup.group_name
    ).all()

    data6 = query6.group_by(
        HumanStatisticsSmallGroup.ques_group_id,
        HumanMarkGroup.group_name
    ).all()

    # 数据合并
    data_map = {}
    for row in data5:
        ques_group_id, group_name, value = row
        data_map.setdefault(ques_group_id, {
            'group_id': ques_group_id,
            'group_name': group_name,
            'total_count': 0
        })['total_count'] += int(value or 0)

    for row in data6:
        ques_group_id, group_name, value = row
        data_map.setdefault(ques_group_id, {
            'group_id': ques_group_id,
            'group_name': group_name,
            'total_count': 0
        })['total_count'] += int(value or 0)

    # 排序处理
    group_data = sorted(data_map.values(), key=lambda x: x['total_count'], reverse=True)

    # 构建workload_data结构
    x_data = [item['group_name'] for item in group_data]
    y1_data = [item['total_count'] for item in group_data]  # 已阅量数据

    # 新增待阅量查询逻辑
    y2_data = []
    for ques_group_id in data_map.keys():
        # 创建参数字典
        pending_params = {
            "round_count": query.round_count,
            "task_type": query.task_type,
            "ques_group_id": ques_group_id
        }
        
        # 添加可选参数
        if query.subject_id is not None:
            pending_params["subject_id"] = query.subject_id
        if query.task_id:
            pending_params["task_id"] = query.task_id
        if query.project_id:
            pending_params["project_id"] = query.project_id

        # 调用待阅量接口
        pending_result = await calculate_pending_review_count_api(
            QCalculateMarkTotalReq(**pending_params), 
            user, 
            new_session
        )
        
        # 获取待阅量并添加到y2_data
        y2_data.append(pending_result.data.get("pending", 0))

     # 构建响应数据（双Y轴结构）
    response_data = {
        "workload_data": {
            "legend": ["已阅量", "待阅量"],
            "x_data": x_data,
            "y1_data": y1_data,
            "y2_data": y2_data
        },
        "total_reviewed": sum(item['total_count'] for item in group_data),
        "total_pending": sum(y2_data)
    }

    logger.info(f"总监控页面：工作量监控，结果: {response_data}")
    return BaseResponse(data=response_data, msg="工作量监控统计结果")


@q_score_analysis_router.post(path="/calculate_arbitration_status_total", response_model=BaseResponse,
                              summary="总监控页面：计算仲裁情况")
async def calculate_arbitration_status_api(
        query: QCalculateArbitrationStatusReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 总监控页面：计算仲裁情况")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 类型6查询（需仲裁数量）
    query6 = new_session.query(
        HumanStatisticsSmallGroup.ques_group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name'),
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label('need_arbitration')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.ques_group_id == HumanMarkGroup.group_id
    )

    # 类型7查询（仲裁量）
    query7 = new_session.query(
        HumanStatisticsSmallGroup.ques_group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name'),
        func.sum(HumanStatisticsSmallGroup.statistics_result_1).label('processed_arbitration')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.ques_group_id == HumanMarkGroup.group_id
    )

    # 公共过滤条件
    filters = [
        HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    
    # 日期范围条件
    if start_date:
        filters.append(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        filters.append(HumanStatisticsSmallGroup.date <= end_date)

    # 应用过滤条件
    query6 = query6.filter(*filters, HumanStatisticsSmallGroup.statistics_type == 6)
    query7 = query7.filter(*filters, HumanStatisticsSmallGroup.statistics_type == 7)

    # 执行查询
    data6 = query6.group_by(
        HumanStatisticsSmallGroup.ques_group_id,
        HumanMarkGroup.group_name
    ).all()

    data7 = query7.group_by(
        HumanStatisticsSmallGroup.ques_group_id,
        HumanMarkGroup.group_name
    ).all()

    # 数据合并处理
    data_map = {}

    # 处理需仲裁数据（类型6）
    for row in data6:
        ques_group_id, group_name, value = row
        data_map.setdefault(ques_group_id, {
            'group_id': ques_group_id,
            'group_name': group_name,
            'need_arbitration': int(value or 0),
            'processed_arbitration': 0
        })['need_arbitration'] = int(value or 0)

    # 处理已仲裁数据（类型7）
    for row in data7:
        ques_group_id, group_name, value = row
        data_map.setdefault(ques_group_id, {
            'group_id': ques_group_id,
            'group_name': group_name,
            'need_arbitration': 0,
            'processed_arbitration': int(value or 0)
        })['processed_arbitration'] = int(value or 0)

    # 计算未处理仲裁数量（类型6 - 类型7）
    group_data = []
    for ques_group_id, values in data_map.items():
        group_data.append({
            'group_id': ques_group_id,
            'group_name': values['group_name'],
            'need_arbitration': values['need_arbitration'],
            'processed_arbitration': values['processed_arbitration'],
            'unprocessed_arbitration': max(0, values['need_arbitration'] - values['processed_arbitration'])
        })

    # 按已处理仲裁数量降序排序
    group_data.sort(key=lambda x: x['processed_arbitration'], reverse=True)

    # 构建workload_data结构
    x_data = [item['group_name'] for item in group_data]
    y1_data = [item['processed_arbitration'] for item in group_data]  # 已处理仲裁
    y2_data = [item['unprocessed_arbitration'] for item in group_data]  # 未处理仲裁

    # 构建响应数据
    response_data = {
        "workload_data": {
            "legend": ["已处理仲裁", "未处理仲裁"],
            "x_data": x_data,
            "y1_data": y1_data,
            "y2_data": y2_data
        },
        "processed_arbitration": sum(item['processed_arbitration'] for item in group_data),
        "unprocessed_arbitration": sum(item['unprocessed_arbitration'] for item in group_data)
    }

    logger.info(f"题组页面：计算仲裁情况，结果: {response_data}")
    return BaseResponse(data=response_data, msg="仲裁情况统计结果")


@q_score_analysis_router.post(path="/ques_group_progress",response_model=BaseResponse,summary="总监控页面：获取所有题组评分进度")
async def ques_group_progress_api(
        query: TGroupProgressReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 总监控页面：获取所有题组评分进度")

    try:
        # 获取所有题组
        all_groups = new_session.query(HumanStatisticsSmallGroup.ques_group_id,
        func.coalesce(HumanMarkGroup.group_name, "未知小组").label('group_name')
    ).outerjoin(
        HumanMarkGroup,
        HumanStatisticsSmallGroup.ques_group_id == HumanMarkGroup.group_id
    ).distinct(HumanStatisticsSmallGroup.ques_group_id)
        
        # 存储结果
        progress_results = []
        
        # 遍历所有题组，调用现有接口获取进度
        for group in all_groups:
            # 构造请求参数（保持与calculate_mark_total_api相同的参数结构）
            group_query = QCalculateMarkTotalReq(
                round_count=query.round_count,
                task_type=query.task_type,
                ques_group_id=group.ques_group_id,
                subject_id=query.subject_id,
                project_id=query.project_id,
                date_range=query.date_range  # 传递日期范围
            )
            
            # 调用现有接口逻辑
            response = await calculate_mark_total_api(
                query=group_query,
                user=user,
                new_session=new_session
            )
            # 调用接口获取速度
            speed_response = await calculate_average_speed_api(
                query=group_query,
                user=user,
                new_session=new_session
            )

                
            progress_results.append({
                "group_name": group.group_name,
                "progress": response.data.get("progress", 0.0),
                "speed": speed_response.data.get("average_speed", 0.0)
            })
        
        # 按进度从高到低排序
        sorted_results = sorted(
            progress_results, 
            key=lambda x: x['progress'], 
            reverse=True
        )
        
        # 构建响应数据（采用新格式）
        response_data = {
            "total_count": len(sorted_results),
            "group_data": sorted_results
        }

        logger.info(f"成功获取 {len(sorted_results)} 个题组评分进度和速度，时间范围: {query.date_range}")
        return BaseResponse(data=response_data, msg="题组评分进度和速度统计成功")

    except Exception as e:
        logger.error(f"获取题组评分进度和速度失败：{e}")
        traceback.print_exc()
        return BaseResponse(data={},msg="获取题组评分进度和速度失败")


@q_score_analysis_router.post("/exception_statistics", response_model=BaseResponse, summary="题组界面:各类型问题卷数量")
async def exception_statistics(query: ExceptionStatisticsReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 题组界面:各类型问题卷数量")
    """
    统计每个task_id的各类型问题卷数量
    """

    try:
        # 参数校验：ques_group_id和task_id必须二选一
        has_group_id = bool(query.ques_group_id)
        has_task_id = bool(query.task_id)
        if not (has_group_id ^ has_task_id):
            return BaseResponse(data=[], msg="必须且只能传入ques_group_id或task_id")

        # 如果传入ques_group_id，需要获取对应的task_id
        task_ids = []
        if query.ques_group_id:
            # 通过ques_group_id获取试题编号
            ques_codes = new_session.query(HumanMarkGroup.ques_code).filter(
                HumanMarkGroup.group_id == query.ques_group_id
            ).all()

            if not ques_codes:
                return BaseResponse(data=[], msg="未找到关联试题编号")

            ques_code_list = [q.ques_code for q in ques_codes]
            
            # 通过试题编号和任务类型获取task_id
            tasks = new_session.query(HumanReadTask).filter(
                HumanReadTask.ques_code.in_(ques_code_list),
                HumanReadTask.task_type == query.task_type
            ).all()

            if not tasks:
                return BaseResponse(data=[], msg="未找到对应任务")

            task_ids = [task.task_id for task in tasks]
        else:
            task_ids = [query.task_id]

        # 构建查询
        db_query = new_session.query(
            HumanAnswerException.task_id,
            HumanAnswerException.exception_type,
            func.count(case((HumanAnswerException.exception_type != 0, HumanAnswerException.answer_exception_id)))
        ).group_by(HumanAnswerException.task_id, HumanAnswerException.exception_type)
        
        # 添加task_id过滤条件
        db_query = db_query.filter(HumanAnswerException.task_id.in_(task_ids))
        
        # 执行查询
        results = db_query.all()
        
        # 构造图表数据格式
        # 按异常类型分组统计总数
        exception_stats = {}
        for result in results:
            exception_type = result.exception_type
            count = result.count
            
            if exception_type not in exception_stats:
                exception_stats[exception_type] = 0
            exception_stats[exception_type] += count
        
        # 构造返回数据
        data = {
            "legend": ["数量"],
            "x_data": sorted(list(exception_stats.keys())),  # 按字母顺序排序异常类型
            "y_data": [exception_stats[exception_type] for exception_type in sorted(list(exception_stats.keys()))]
        }

        logger.info(f"题组页面：问题卷，结果: {data}")
        return BaseResponse(data=data, msg="题组页面：问题卷统计结果")

    except Exception as e:
        logger.error(f"统计问题卷数量失败：{e}")
        traceback.print_exc()
        return BaseResponse(data={},msg="统计问题卷数量失败")
    


@q_score_analysis_router.post(path="/get_group_id_by_ques_code", response_model=BaseResponse, summary="根据试题编号获取题组ID")
async def get_group_id_by_ques_code_api(
        query: GetGroupIdByQuesCodeReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 根据试题编号获取题组ID: {query.ques_code}")
    
    # 查询对应的题组ID
    group_data = new_session.query(HumanMarkGroup.group_id).filter(
        HumanMarkGroup.ques_code == query.ques_code
    ).first()
    
    if not group_data:
        return BaseResponse(data={}, msg="未找到对应的题组ID")
    
    # 返回结果
    result_data = {
        "group_id": group_data.group_id
    }
    
    logger.info(f"根据试题编号 {query.ques_code} 获取题组ID成功: {result_data}")
    return BaseResponse(data=result_data, msg="获取题组ID成功")


@q_score_analysis_router.post(path="/get_user_tasks", response_model=BaseResponse, summary="获取用户对应的任务")
async def get_user_tasks_api(
        query: GetTasksReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 获取用户对应的任务: {query.user_id}, role_id: {query.role_id}")

    # 获取用户角色
    role_id = int(query.role_id)

    # 只处理角色7（资格组长）和角色5（题组长）
    if role_id == 7:  # 资格组长
        # 查询用户绑定的所有subject_id
        subject_ids = new_session.query(
            distinct(UserDataPermission.subject_id)
        ).filter(
            UserDataPermission.user_id == query.user_id,
            UserDataPermission.subject_id.isnot(None)
        ).all()

        # 获取对应的subject_name和project_id、project_name
        result_data = []
        for subject_id in subject_ids:
            # 获取subject信息
            subject = new_session.query(Subject).filter(
                Subject.subject_id == subject_id[0]
            ).first()
            
            if subject:
                # 获取project信息
                project = new_session.query(Project).filter(
                    Project.project_id == subject.project_id
                ).first()
                
                result_data.append({
                    "subject_id": subject.subject_id,
                    "subject_name": subject.subject_name,
                    "project_id": subject.project_id,
                    "project_name": project.project_name if project else ""
                })

        logger.info(f"资格组长 {query.user_id} 绑定的科目: {result_data}")
        return BaseResponse(data=result_data, msg="获取资格组长绑定的科目成功")

    elif role_id == 5:  # 题组长
        # 查询用户绑定的所有ques_code
        ques_codes = new_session.query(
            distinct(UserDataPermission.ques_code)
        ).filter(
            UserDataPermission.user_id == query.user_id,
            UserDataPermission.ques_code.isnot(None)
        ).all()

        # 获取对应的ques_group_id、group_name、project_id、project_name、subject_id、subject_name
        result_data = []
        for ques_code in ques_codes:
            # 通过ques_code获取group_id（ques_group_id）
            group_data = new_session.query(HumanMarkGroup).filter(
                HumanMarkGroup.ques_code == ques_code[0]
            ).first()

            if group_data:
                group_id = group_data.group_id
                # 通过group_id获取group_name
                group = new_session.query(HumanMarkGroup.group_name).filter(
                    HumanMarkGroup.group_id == group_id
                ).first()

                # 获取subject_id和project_id
                subject_id = group_data.subject_id
                project_id = group_data.project_id
                
                # 获取subject和project信息
                subject = new_session.query(Subject).filter(
                    Subject.subject_id == subject_id
                ).first()
                
                project = new_session.query(Project).filter(
                    Project.project_id == project_id
                ).first()
                
                if group and subject and project:
                    result_data.append({
                        "ques_group_id": group_id,
                        "group_name": group.group_name,
                        "subject_id": subject_id,
                        "subject_name": subject.subject_name,
                        "project_id": project_id,
                        "project_name": project.project_name
                    })

        logger.info(f"题组长 {query.user_id} 绑定的题组: {result_data}")
        return BaseResponse(data=result_data, msg="获取题组长绑定的题组成功")

    else:
        # 其他角色不返回数据
        logger.info(f"用户 {query.user_id} 角色 {role_id} 不返回数据")
        return BaseResponse(data=[], msg="其他角色不返回数据")
