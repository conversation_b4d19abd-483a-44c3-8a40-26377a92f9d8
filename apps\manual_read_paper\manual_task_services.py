import traceback
from decimal import Decimal

from settings import logger
from sqlalchemy import select, and_

from apps.base.global_cache import get_small_ques_count, get_group_id_list_by_task_id
from apps.manual_read_paper.manual_read_services import save_distri_answer_data
from apps.models.models import ExamStudent, StuAnswer, ExamQuestion, ExamPaper, ManualDistributeAnswer, ManualReadPaperGroup, \
    ManualArbitrateQuality, ManualMark
from settings import configs
from utils.utils import round_half_up


def get_batch_stu_answer_by_ratio(ques_code: str, allow_exam_num_list: list, ratio: float, allow_exam_num_length: int,
                                  group_num: int, is_first: bool = False):
    """
    根据比例获取考生信息，如果按比例获取的考生数量小于小组数量，则增大比例，执行递归
    :param ques_code:
    :param allow_exam_num_list:
    :param ratio:
    :param allow_exam_num_length:
    :param group_num:
    :param is_first: 是否是第一次发起任务
    :return:
    """
    if is_first:
        if allow_exam_num_length < group_num:
            logger.error("考生数量少于小组数量，无法发起阅卷任务")
            msg = "考生数量少于小组数量，无法发起阅卷任务"
            return [], msg

    end_index = int(allow_exam_num_length * ratio)
    one_batch_allow_exam_num = list(set(allow_exam_num_list[: end_index]))
    one_batch_num_length = len(one_batch_allow_exam_num)

    if one_batch_num_length < group_num and ratio < 1:
        ratio += 0.1
        return get_batch_stu_answer_by_ratio(ques_code, allow_exam_num_list, ratio, allow_exam_num_length, group_num)
    else:
        return one_batch_allow_exam_num, None


def divide_students_into_groups(group_id_list, allow_exam_num):
    """
    按小组分配考生
    :param group_id_list:
    :param allow_exam_num:
    :return:
    """
    # 创建一个包含空列表的小组列表
    num_groups = len(group_id_list)
    groups_exam_num = [[] for _ in range(num_groups)]

    # 计算每组应该分配的考生数目
    for i, exam_num in enumerate(allow_exam_num):
        # 使用余数循环分配考生
        group_index = i % num_groups
        groups_exam_num[group_index].append(exam_num)

    groups_exam_num_dict = {}
    for group_id, exam_num_list in zip(group_id_list, groups_exam_num):
        groups_exam_num_dict[group_id] = exam_num_list

    return groups_exam_num_dict


def get_distri_stu_answer(new_session, exam_num_group_dict, paper_id, ques_code, ques_id, ques_type_code):
    """
    获取分配的作答信息
    """
    distri_info = []
    stu_secret_num_list = []
    paper_condition = StuAnswer.paper_id == paper_id if paper_id else True
    for group_id, exam_num_list in exam_num_group_dict.items():
        distri_stmt = select(StuAnswer.answer_id, ExamStudent.stu_secret_num, StuAnswer.stu_answer, StuAnswer.op_file) \
            .outerjoin(ExamPaper, ExamPaper.paper_id == StuAnswer.paper_id) \
            .join(ExamQuestion, ExamQuestion.ques_id == StuAnswer.ques_id) \
            .join(ExamStudent, ExamStudent.allow_exam_num == StuAnswer.allow_exam_num) \
            .where(and_(StuAnswer.ques_id == ques_id, paper_condition, StuAnswer.allow_exam_num.in_(exam_num_list)))
        try:
            result = new_session.execute(distri_stmt)
            for row in result:
                distri_item = {
                    "group_id": group_id,
                    "answer_id": row.answer_id,
                    "stu_secret_num": row.stu_secret_num,
                    "paper_id": paper_id,
                    "ques_code": ques_code,
                    "stu_answer": row.stu_answer,
                    "ques_type_code": ques_type_code,
                    "op_file": row.op_file
                }
                stu_secret_num_list.append(row.stu_secret_num)
                distri_info.append(distri_item)
        except Exception as e:
            logger.error(f"获取分配的阅卷任务的具体信息失败，{e}")
            logger.error(traceback.format_exc())
            return False, None, []
        # logger.info("获取分配的阅卷任务的具体信息成功")
    return True, distri_info, stu_secret_num_list


def launch_manual_task_main(new_session, m_read_task_id, project_id, subject_id, paper_id, ques_code, ques_type_code,
                            group_id_list, ques_id, parent_ques_id, is_first=False):
    """
    发起人工阅卷主程序
    """
    # 根据比例获取待分配作答信息
    paper_condition = StuAnswer.paper_id == paper_id if paper_id else True
    exam_stu_info = new_session.query(StuAnswer.allow_exam_num.distinct()).filter(
        and_(StuAnswer.ques_id == ques_id, StuAnswer.project_id == project_id, StuAnswer.subject_id == subject_id,
             paper_condition, StuAnswer.manual_distri_state == 1)).all()
    if not exam_stu_info:
        if is_first:
            return False, "该试题暂无考生信息或者考生作答信息，请先导入相关信息"
        else:
            return True, "该小组已无新分配待评分数据"

    try:
        # 获取未分配考生的所有准考证号
        allow_exam_num_list = [i[0] for i in exam_stu_info]

        expert_ai_count = new_session.query(ManualReadPaperGroup.expert_ai_num).filter(
            ManualReadPaperGroup.manual_group_id == group_id_list[0]).scalar()

        allow_exam_num_length = len(allow_exam_num_list)
        task_group_id_list = get_group_id_list_by_task_id(new_session, m_read_task_id)
        all_group_num = len(task_group_id_list)
        curr_group_num = len(group_id_list)
        if is_first:
            if expert_ai_count:
                group_divide_ratio = 1
            else:
                if curr_group_num == 1:
                    # 发起任务时如果只有 1组 直接分完
                    group_divide_ratio = 1
                else:
                    group_divide_ratio = configs.GROUP_DIVIDE_RATIO
        else:
            group_divide_ratio = configs.GROUP_DIVIDE_RATIO / all_group_num * curr_group_num
        one_batch_allow_exam_num, msg = get_batch_stu_answer_by_ratio(ques_code, allow_exam_num_list,
                                                                      group_divide_ratio, allow_exam_num_length,
                                                                      curr_group_num, is_first)
        if msg:
            return False, msg
        # 根据小组平均分配考生的准考证号
        groups_exam_num_dict = divide_students_into_groups(group_id_list, one_batch_allow_exam_num)

        # 获取分配的作答信息
        result, distri_info, stu_secret_num_list = get_distri_stu_answer(new_session, groups_exam_num_dict, paper_id,
                                                                         ques_code, ques_id, ques_type_code)
        if not result:
            return False, "发起阅卷任务失败"
        # 将分配的作答信息、试题信息、小组信息、考生的准考证号信息插入数据库
        result = save_distri_answer_data(new_session, m_read_task_id, distri_info, ques_id, parent_ques_id)
        if not result:
            return False, "发起阅卷任务失败"
    except Exception as e:
        traceback.print_exc()
        new_session.rollback()
        logger.error(f"保存分配信息失败，{e}")
        return False, "发起阅卷任务失败"

    try:
        # 将作答信息状态标记为已分配
        if paper_id:
            new_session.query(StuAnswer) \
                .filter(and_(StuAnswer.ques_id == ques_id, StuAnswer.paper_id == paper_id,
                             StuAnswer.allow_exam_num.in_(one_batch_allow_exam_num))) \
                .update({StuAnswer.manual_distri_state: 2})
        else:
            new_session.query(StuAnswer) \
                .filter(and_(StuAnswer.ques_id == ques_id, StuAnswer.project_id == project_id,
                             StuAnswer.subject_id == subject_id,
                             StuAnswer.allow_exam_num.in_(one_batch_allow_exam_num))) \
                .update({StuAnswer.manual_distri_state: 2})
    except Exception as e:
        traceback.print_exc()
        new_session.rollback()
        logger.error(f"标记作答信息状态为已分配失败，{e}")
        return False, "发起阅卷任务失败"
    return True, None


def get_task_type(role_id: str):
    """
    获取任务类型
    """
    if role_id == "3":
        task_type = "阅卷"
    elif role_id == "4":
        task_type = "仲裁"
    elif role_id == "5":
        task_type = "质检"
    else:
        task_type = "普通任务"
    return task_type


def get_manual_total_percentage(new_session, role_id, m_read_task_id, curr_user_id, group_id):
    group_id_condition = ManualDistributeAnswer.manual_group_id == group_id
    if role_id == "3":
        # 根据任务id和分组id获取考生分配数量
        distri_stu_count = new_session.query(ManualDistributeAnswer.distri_answer_id) \
            .filter(and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id,
                         group_id_condition)).count()
        # 所有质检返评数量
        # all_quality_return_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
        #     .join(ManualDistributeAnswer,
        #           ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
        #     .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id,
        #                  ManualArbitrateQuality.aq_type == 2, group_id_condition)).count()
        # print("all_quality_return_count", all_quality_return_count)
        # 已质检返评数量
        yet_quality_return_user_id = new_session.query(ManualArbitrateQuality.yet_return_user_id) \
            .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id,
                         ManualArbitrateQuality.aq_type == 2)).all()
        quality_return_count = 0
        for i in yet_quality_return_user_id:
            return_user_id_item = i[0]
            for return_user_id in return_user_id_item:
                if return_user_id == curr_user_id:
                    quality_return_count += 1
        # 已评分个数
        # 该专家已评分数量
        expert_marked_count = new_session.query(ManualMark.manual_mark_id) \
            .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualMark.distri_answer_id) \
            .filter(and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id,
                         ManualDistributeAnswer.manual_group_id == group_id)).count()
        # # 组合题要除以小题数量
        # small_ques_count = get_small_ques_count(ques_code)
        # small_ques_count = small_ques_count if small_ques_count != 0 else 1
        # marked_count = round_half_up(expert_marked_count / small_ques_count, 4)
        percentage = round_half_up((expert_marked_count + quality_return_count) / (distri_stu_count + quality_return_count), 4) if distri_stu_count else 0
    elif role_id == "4":
        # 需仲裁数量
        all_arbitrate_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
            .join(ManualDistributeAnswer,
                  ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
            .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id,
                         ManualArbitrateQuality.aq_type == 1, group_id_condition)).count()
        # 已仲裁数量
        yet_arbitrate_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
            .join(ManualDistributeAnswer,
                  ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
            .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, group_id_condition,
                         ManualArbitrateQuality.aq_type == 1, ManualArbitrateQuality.aq_state == 2)).count()
        percentage = round_half_up(yet_arbitrate_count / all_arbitrate_count, 4) if all_arbitrate_count else 0
    elif role_id == "5":
        # 需质检数量
        all_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
            .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
            .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id,
                         ManualArbitrateQuality.aq_type == 2, group_id_condition)).count()
        # 已质检数量
        yet_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
            .join(ManualDistributeAnswer,
                  ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
            .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id,
                         ManualArbitrateQuality.aq_type == 2, ManualArbitrateQuality.aq_state == 4,
                         group_id_condition)).count()
        percentage = round_half_up(yet_quality_count / all_quality_count, 4) if all_quality_count else 0
    else:
        percentage = 0
    percentage = round_half_up(Decimal(str(percentage)) * Decimal(str(100)), 2)
    return percentage


if __name__ == '__main__':
    group_id_list = [i for i in range(499)]
    allow_exam_num = [i for i in range(5138)]
    groups_exam_num_dict = divide_students_into_groups(group_id_list, allow_exam_num)
    for group, stu_list in groups_exam_num_dict.items():
        print(group, len(stu_list))
