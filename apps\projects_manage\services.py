from sqlalchemy import and_
from apps.models.models import Subject,Project,SubjectSession


def subject_query_condition(project_id, subject_id):
    project_query = Subject.project_id == project_id if project_id else True
    subject_query = Subject.subject_id == subject_id if subject_id else True

    condition = and_(project_query, subject_query)
    return condition


def get_project_tree(new_session):
    """
    获取项目树结构数据，格式为：项目 -> 科目 -> 场次
    返回格式：
    [
        {
            "id": "project_id",
            "name": "项目名称",
            "type": "project",
            "children": [
                {
                    "id": "subject_id",
                    "name": "科目名称",
                    "type": "subject",
                    "children": [
                        {
                            "id": "session_id",
                            "name": "场次名称",
                            "type": "session"
                        }
                    ]
                }
            ]
        }
    ]
    """
    # 查询所有激活的项目
    projects = new_session.query(Project).filter(Project.is_active == True).all()

    # 查询所有激活的科目
    subjects = new_session.query(Subject).filter(Subject.is_active == True).all()

    # 查询所有场次
    sessions = new_session.query(SubjectSession).all()

    # 构建项目字典，便于快速查找
    project_dict = {project.project_id: {
        "id": project.project_id,
        "name": project.project_name,
        "type": "project",
        "children": []
    } for project in projects}

    # 构建科目字典，便于快速查找
    subject_dict = {subject.subject_id: {
        "id": subject.subject_id,
        "name": subject.subject_name,
        "type": "subject",
        "children": []
    } for subject in subjects}

    # 构建场次字典，便于快速查找
    session_dict = {}
    for session in sessions:
        session_dict[session.session_id] = {
            "id": session.session_id,
            "name": f"场次{session.exam_session}",
            "type": "session"
        }

    # 将场次添加到对应的科目下
    for session in sessions:
        if session.subject_id in subject_dict:
            subject_dict[session.subject_id]["children"].append(session_dict[session.session_id])

    # 将科目添加到对应的项目下
    for subject in subjects:
        if subject.project_id in project_dict:
            project_dict[subject.project_id]["children"].append(subject_dict[subject.subject_id])

    # 返回项目树结构
    return list(project_dict.values())