import traceback

from fastapi import APIRouter, Depends
from settings import logger
from typing import Any

from sqlalchemy.orm import Session

from apps.models.models import EmptyModuleUser
from apps.permission.schemas import GetRoleModuleReq, UpdateRoleModuleReq, UpdateUserModuleReq, GetModuleReq
from apps.permission.services import get_selected_module_flag, get_user_role, update_permission, filter_modules, \
    get_role_show_module
from apps.sys_manage.services import get_all_sys_info
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from factory_apps import session_depend
from helper import response_utils

user_module_router = APIRouter()


@user_module_router.post(path="/get_user_module", response_model=BaseResponse, summary="获取用户功能权限")
def get_user_module(query: GetModuleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    user_id, login_role_id_list = query.user_id, query.login_role_id_list
    username = user['username']
    logger.info(f"{username} 获取 user_id 为 {user_id} 的用户的功能权限")
    if not login_role_id_list:
        role_id_list = get_user_role(new_session, user_id)
    else:
        role_id_list = login_role_id_list
    all_module_data = get_all_sys_info(new_session)

    # 所选用户的角色可以展示的模块
    selected_module_flag = get_selected_module_flag(new_session, role_id=role_id_list)
    show_module = filter_modules(all_module_data, selected_module_flag, is_delete=True)

    # 所选用户拥有的模块
    selected_module_flag = get_selected_module_flag(new_session, user_id=user_id)
    empty_module_user = new_session.query(EmptyModuleUser.user_id).filter(EmptyModuleUser.user_id == user_id).scalar()
    if selected_module_flag or empty_module_user:
        logger.info(f"{username} 用户有特定的功能权限")
    else:
        logger.info(f"{username} 用户无特定功能权限，按照角色 {'和'.join(role_id_list)} 功能权限分配")
        selected_module_flag = get_selected_module_flag(new_session, role_id=role_id_list)

    permission_data = filter_modules(show_module, selected_module_flag, is_delete=False)

    return BaseResponse(data=permission_data)


@user_module_router.post(path="/get_role_module", response_model=BaseResponse, summary="获取角色功能权限")
def get_role_module(query: GetRoleModuleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    role_id = query.role_id_list[0]
    logger.info(f"{user['username']} 获取 role_id 为 {role_id} 的角色功能权限")
    curr_role_id = user["role"][0]

    # 当前角色可以展示的模块
    curr_permission_data = get_role_show_module(new_session, curr_role_id)

    # 获取要修改的角色拥有的功能权限
    dest_selected_module_flag = get_selected_module_flag(new_session, role_id=role_id)
    if curr_role_id in ["1", "2"]:
        permission_data = filter_modules(curr_permission_data, dest_selected_module_flag, is_delete=False)
    else:
        permission_data = filter_modules(curr_permission_data, dest_selected_module_flag, is_delete=True)

    return BaseResponse(data=permission_data)


@user_module_router.post(path="/update_role_module", response_model=BaseResponse, summary="更新角色功能权限")
def update_role_module(query: UpdateRoleModuleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    role_id, update_module_flag = query.role_id, query.module_flag
    logger.info(f"{user['username']} 更新 role_id 为 {role_id} 的角色功能权限")

    # if role_id in ["1", "2"]:
    #     return BaseResponse(code=response_utils.permission_deny, msg="管理员的功能权限不允许修改")

    try:
        result, msg = update_permission(new_session, update_module_flag, role_id=role_id, c_user_id=user.get("user_id"))
        if result:
            new_session.commit()
            logger.info(msg)
            return BaseResponse(msg=msg)
        else:
            logger.warning(msg)
            return BaseResponse(code=response_utils.has_no_update_data, msg=msg)
    except Exception as e:
        logger.error(f"更新角色功能权限失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg="更新角色功能权限失败")


@user_module_router.post(path="/update_user_module", response_model=BaseResponse, summary="更新用户功能权限")
def update_user_module(query: UpdateUserModuleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    user_id, update_module_flag = query.user_id, query.module_flag
    logger.info(f"{user['username']} 更新 user_id 为 {user_id} 的用户功能权限")
    role_id_list = get_user_role(new_session, user_id)

    # if "1" in role_id_list or "2" in role_id_list:
    #     return BaseResponse(code=response_utils.permission_deny, msg="管理员的功能权限不允许修改")

    try:
        result, msg = update_permission(new_session, update_module_flag, user_id=user_id, c_user_id=user.get("user_id"))
        if result:
            new_session.commit()
            logger.info(msg)
            return BaseResponse(msg=msg)
        else:
            logger.warning(msg)
            return BaseResponse(code=response_utils.has_no_update_data, msg=msg)
    except Exception as e:
        logger.error(f"更新用户功能权限失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg=f"更新用户功能权限失败")
