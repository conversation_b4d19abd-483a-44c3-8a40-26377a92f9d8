import io
import time
import traceback

from fastapi import APIRouter, UploadFile, File, Depends, Form
from sqlalchemy import select, and_
import openpyxl
from settings import logger
from typing import Any, List

from sqlalchemy.orm import Session

from apps.base.global_cache import set_stu_num_by_task_id
from apps.users.services import get_current_user
from apps.models.models import StuAnswer, ExamQuestion, ExamPaper, ExamStudent, SameStuAnswerGroup, ImportDataRecord, \
    Project, Subject, StuTraj, PaperDetail
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from apps.read_paper import GetStuAnswerReq, ImportProgressReq
from helper import response_utils
from settings import configs
from apps.read_paper.common_services import get_user_data_flag, del_excel_empty_row, get_history_answer, \
    delete_same_answer_group, stu_answer_display_condition, toggle_partition
from utils.utils import round_half_up

stu_answer_router = APIRouter()


@stu_answer_router.post(path="/check_stu_answer_exist", response_model=BaseResponse, summary="检查考生作答信息是否存在")
async def check_stu_answer_exist(files: List[UploadFile] = File(...), user: Any = Depends(get_current_user),
                                 new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 检查考生作答信息是否存在")

    # 检查作答信息的考生准考证号是否存在
    exists_exam_num = new_session.query(ExamStudent.allow_exam_num).all()
    all_exists_exam_num_list = list(set([i[0] for i in exists_exam_num])) if exists_exam_num else []
    answer_exam_num_set = set()
    paper_name_and_ques_code_info = {}
    record_id_list = []
    paper_name_list = []

    for file in files:
        filename = file.filename
        if not filename.endswith(".xlsx"):
            return BaseResponse(code=response_utils.no_field, msg="请上传 .xlsx 格式的文件")

        content = await file.read()
        excel_data = io.BytesIO(content)
        try:
            wb = openpyxl.load_workbook(filename=excel_data, read_only=True)
        except ValueError:
            logger.warning("格式错误，请检查文件是否设置了筛选等其他操作，如是请清空格式")
            return BaseResponse(code=response_utils.no_field,
                                msg="格式错误，请检查文件是否设置了筛选等其他操作，如是请清空格式")
        sheet = wb.worksheets[0]

        count = 0
        paper_name_set = set()
        for row in sheet.iter_rows(min_col=0, max_col=3, values_only=True):
            count += 1
            if count == 1:
                continue
            answer_exam_num, paper_name, ques_code = row[0], row[1], row[2]
            if any(row):
                paper_name_set.add(paper_name)
                answer_exam_num_set.add(str(answer_exam_num))
                if paper_name:
                    if paper_name not in paper_name_and_ques_code_info:
                        paper_name_and_ques_code_info[paper_name] = set()
                        paper_name_and_ques_code_info[paper_name].add(ques_code)
                    else:
                        if ques_code:
                            paper_name_and_ques_code_info[paper_name].add(ques_code)
        paper_name_list = list(paper_name_set)

        # 检查试卷是否已导入系统
        for paper_name in paper_name_list:
            paper_info = new_session.query(ExamPaper.paper_id).filter(ExamPaper.paper_name == paper_name).first()
            if not paper_info:
                logger.warning(f"{paper_name} 该作答信息的试卷未导入系统，请先导入对应的试卷")
                return BaseResponse(code=response_utils.no_field,
                                    msg=f"{paper_name} 该试卷未导入系统，请先导入对应的试卷")

        not_exist_num = list(answer_exam_num_set - set(all_exists_exam_num_list))
        if not_exist_num:
            logger.warning(f"准考证号为 {', '.join(not_exist_num)} 的考生的信息不存在，请先导入考生信息")
            return BaseResponse(code=response_utils.no_field,
                                msg=f"准考证号为 {', '.join(not_exist_num)} 的考生的信息不存在，请先导入考生信息")

        # 创建导入考生作答数据记录id
        record_id_list.append(configs.snow_worker.get_id())

    data = {
        "data": {
            "record_id_list": record_id_list,
            "paper_name_list": paper_name_list
        }
    }
    new_session.commit()

    if new_session.query(StuAnswer.answer_id).filter(StuAnswer.allow_exam_num.in_(list(answer_exam_num_set))).first():
        return BaseResponse(code=response_utils.fields_exist, data=data, msg="作答信息已存在，是否覆盖？")

    return BaseResponse(data=data)


@stu_answer_router.post(path="/import_stu_answer", response_model=BaseResponse, summary="导入考生作答信息")
async def import_stu_answer(record_id_list: str = Form(...,
                                                       title="进度条记录字符串，formData 会将列表转化为字符串，需要作字符串转列表的操作"),
                            paper_name_list: str = Form(..., title="模板包含的试卷名称，formData 会将列表转化为字符串，需要作字符串转列表的操作"),
                            files: List[UploadFile] = File(...),
                            user: Any = Depends(get_current_user),
                            new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 导入考生作答信息")
    start = time.time()
    curr_user_id = user["user_id"]
    record_id_list = record_id_list.split(",")
    paper_name_list = paper_name_list.split(",")

    paper_info = {}
    # paper_info = {
    #     paper_name: {
    #         "paper_id": "xxx",
    #         "project_id": "xxx",
    #         "subject_id": "xxx"
    #     }
    # }

    ques_info = {}
    # ques_info = {
    #     ques_code: {
    #         "ques_id": "xxx",
    #         "parent_ques_id": "xxx",
    #         "ques_score_list": ["1.00", "2.00"],
    #         "d_out_of_order_group": [[1, 2]]
    #     }
    # }

    paper_id_list = []

    ques_id_par_list = []

    for file_index, file in enumerate(files):
        new_record = ImportDataRecord(record_id=record_id_list[file_index], record_type=3, progress=0,
                                      c_user_id=user.get("user_id"))
        new_session.add(new_record)
        new_session.commit()

        content = await file.read()
        wb = openpyxl.load_workbook(filename=io.BytesIO(content))
        sheet = wb.worksheets[0]
        new_sheet, rows_count = del_excel_empty_row(sheet)

        if rows_count <= 1:
            logger.warning("导入内容不能为空")
            return BaseResponse(code=response_utils.params_error, msg="导入内容不能为空")

        for paper_name in paper_name_list:
            if paper_name not in paper_info:
                paper_query_info = new_session.query(ExamPaper.paper_id, ExamPaper.paper_code, ExamPaper.project_id,
                                                     ExamPaper.subject_id).filter(
                    ExamPaper.paper_name == paper_name).first()
                if paper_query_info:
                    paper_id, paper_code, project_id, subject_id = paper_query_info
                    paper_info[paper_name] = {
                        "paper_id": paper_id,
                        "paper_code": paper_code,
                        "project_id": project_id,
                        "subject_id": subject_id
                    }
                    paper_id_list.append(paper_id)

        history_answer_dict = get_history_answer(new_session, paper_id_list)
        # history_answer_dict = {
        #     allow_exam_num: [paper_id]
        # }

        insert_data = []
        count = 0
        saved_batch_count = 0
        a_batch_num = configs.STU_ANSWER_BATCH_NUM
        batch_count = (int(rows_count / a_batch_num) + 1) if (rows_count % a_batch_num != 0) else (
                rows_count / a_batch_num)  # 总共分成多少批
        try:
            stu_answer_display_data_set = set()
            stu_answer_display_insert_data = []
            allow_exam_num_set = set()
            for row in new_sheet.iter_rows(values_only=True):
                count += 1
                if count == 1:
                    continue
                if not any(row):
                    continue
                allow_exam_num, paper_name, ques_code, f_small_ques_order, ques_type_code, stu_answer = str(row[0]), \
                    row[1], row[2], row[3], row[4], str(row[5]) if row[5] is not None else row[5]

                if stu_answer and configs.NEW_SPLIT_FLAG in stu_answer and ques_type_code not in ["C", "D"]:
                    logger.warning(f"第 {count + 1} 行试题格式 {ques_type_code} 与考生答案格式不匹配")
                    return BaseResponse(code=response_utils.params_error,
                                        msg=f"第 {count + 1} 行试题格式 {ques_type_code} 与考生答案格式不匹配")

                paper_id, paper_code = paper_info[paper_name]["paper_id"], paper_info[paper_name]["paper_code"]
                project_id, subject_id = paper_info[paper_name]["project_id"], paper_info[paper_name]["subject_id"]

                if paper_code in ques_info:
                    ques_id = ques_info[paper_code]["ques_id"]
                    parent_ques_id = ques_info[paper_code]["parent_ques_id"]
                    ques_score_list = ques_info[paper_code]["ques_score_list"]
                    d_out_of_order_group = ques_info[paper_code]["d_out_of_order_group"]
                else:
                    ques_order_query = PaperDetail.ques_order.ilike(
                        f"%（{f_small_ques_order}）%") if f_small_ques_order else True
                    ques_id, parent_ques_id, ques_score_list, d_out_of_order_group = new_session.query(
                        ExamQuestion.ques_id, PaperDetail.parent_ques_id, PaperDetail.ques_score_list,
                        ExamQuestion.d_out_of_order_group) \
                        .join(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
                        .filter(and_(PaperDetail.paper_code == paper_code, ExamQuestion.ques_code == ques_code,
                                     ques_order_query)).first()

                    ques_info[ques_code] = {
                        "ques_id": ques_id,
                        "parent_ques_id": parent_ques_id,
                        "ques_score_list": ques_score_list,
                        "d_out_of_order_group": d_out_of_order_group
                    }

                if ques_id not in ques_id_par_list:
                    ques_id_par_list.append(ques_id)
                    toggle_partition(["t_stu_answer", "t_same_stu_answer_group", "t_stu_answer_detail"], ques_id)

                # 重复导入时需根据导入考生准考证号和试卷信息删除已有数据，以最新导入数据为准
                if allow_exam_num not in allow_exam_num_set:
                    allow_exam_num_set.add(allow_exam_num)
                    stu_history_paper_id_list = history_answer_dict.get(allow_exam_num, [])
                    if paper_id in stu_history_paper_id_list:
                        new_session.query(StuAnswer).filter(and_(StuAnswer.allow_exam_num == allow_exam_num,
                                                                 StuAnswer.paper_id == paper_id)).delete()
                        new_session.query(StuTraj).filter(
                            and_(StuTraj.allow_exam_num == allow_exam_num,
                                 StuTraj.paper_id == paper_id)).delete()
                        new_session.commit()

                small_ques_order = 0
                if ques_type_code != "D":
                    if parent_ques_id:
                        small_ques_order = f_small_ques_order
                    else:
                        small_ques_order += 1
                    score = ques_score_list[0]
                    if stu_answer:
                        stu_answer = stu_answer.strip()
                        answer = stu_answer.replace(configs.NEW_SPLIT_FLAG, "").strip()
                    else:
                        answer = None

                    # 判断答案分组表里是否有该条数据
                    answer_group_info = new_session.query(SameStuAnswerGroup.same_answer_group_id) \
                        .filter(
                        and_(SameStuAnswerGroup.ques_id == ques_id, SameStuAnswerGroup.stu_answer == answer)).first()
                    if answer_group_info:
                        same_answer_group_id = answer_group_info[0]
                    else:
                        same_answer_group_id = configs.snow_worker.get_id()
                        same_answer_group_item = SameStuAnswerGroup(same_answer_group_id=same_answer_group_id,
                                                                    paper_id=paper_id,
                                                                    ques_id=ques_id, ques_code=ques_code,
                                                                    parent_ques_id=parent_ques_id,
                                                                    small_ques_order=str(small_ques_order),
                                                                    ques_type_code=ques_type_code,
                                                                    stu_answer=answer, score=score, mark_state=1,
                                                                    mark_result=4, c_user_id=curr_user_id)
                        new_session.add(same_answer_group_item)

                    stu_answer_data = StuAnswer(answer_id=configs.snow_worker.get_id(), allow_exam_num=allow_exam_num,
                                                paper_id=paper_id, ques_id=ques_id, ques_code=ques_code,
                                                small_ques_order=int(small_ques_order) if small_ques_order else None,
                                                parent_ques_id=parent_ques_id, ques_type_code=ques_type_code,
                                                stu_answer=answer, score=score, stu_score=None, mark_state=1,
                                                mark_result=4, c_user_id=curr_user_id,
                                                same_answer_group_id=same_answer_group_id)
                    insert_data.append(stu_answer_data)
                else:
                    # 针对填空题，分数列表有多个分数说明有多个空，将每个空当作一行数据进行处理，并将答案为空的数据的分数置为0
                    # 填空题可乱序答案组合列表，eg: [[1, 2]]
                    for index, score in enumerate(ques_score_list):
                        ooo_group_index = 0
                        small_ques_order += 1
                        if ques_type_code == "D" and d_out_of_order_group:
                            # 填空题答案可乱序时，标志该空答案属于哪个组
                            for group_index, group_order_list in enumerate(d_out_of_order_group):
                                if small_ques_order in group_order_list:
                                    ooo_group_index = group_index + 1
                                    break
                        answer = None
                        if stu_answer:
                            stu_answer_list = stu_answer.split(configs.NEW_SPLIT_FLAG)
                            try:
                                answer = stu_answer_list[index]
                            except IndexError:
                                logger.warning(f"第 {count + 1} 行的考生作答有缺失")
                                return BaseResponse(code=response_utils.params_error,
                                                    msg=f"第 {count + 1} 行的考生作答有缺失")

                        compare_answer = stu_answer
                        same_answer_score = f"{configs.NEW_SPLIT_FLAG}".join(ques_score_list)
                        same_answer_small_ques_order = f"{configs.NEW_SPLIT_FLAG}".join(
                            [str(i + 1) for i in range(len(ques_score_list))])

                        # 判断答案分组表里是否有该条数据
                        answer_group_info = new_session.query(SameStuAnswerGroup.same_answer_group_id).filter(and_(
                            SameStuAnswerGroup.project_id == project_id, SameStuAnswerGroup.subject_id == subject_id,
                            SameStuAnswerGroup.paper_code == paper_code, SameStuAnswerGroup.ques_code == ques_code,
                            SameStuAnswerGroup.small_ques_order == same_answer_small_ques_order,
                            SameStuAnswerGroup.parent_ques_id == parent_ques_id,
                            SameStuAnswerGroup.stu_answer == compare_answer)).first()
                        if answer_group_info:
                            same_answer_group_id = answer_group_info[0]
                        else:
                            same_answer_group_id = configs.snow_worker.get_id()
                            same_answer_group_item = SameStuAnswerGroup(same_answer_group_id=same_answer_group_id,
                                                                        paper_id=paper_id, paper_code=paper_code,
                                                                        project_id=project_id, subject_id=subject_id,
                                                                        ques_id=ques_id, ques_code=ques_code,
                                                                        small_ques_order=same_answer_small_ques_order,
                                                                        parent_ques_id=parent_ques_id,
                                                                        ques_type_code=ques_type_code,
                                                                        stu_answer=compare_answer,
                                                                        score=same_answer_score,
                                                                        mark_state=1, mark_result=4,
                                                                        c_user_id=curr_user_id)
                            new_session.add(same_answer_group_item)

                        stu_answer_data = StuAnswer(answer_id=configs.snow_worker.get_id(),
                                                    allow_exam_num=allow_exam_num,
                                                    paper_id=paper_id, ques_id=ques_id, ques_code=ques_code,
                                                    small_ques_order=small_ques_order, parent_ques_id=parent_ques_id,
                                                    ques_type_code=ques_type_code, stu_answer=answer,
                                                    ooo_group_index=ooo_group_index, score=score,
                                                    stu_score=None, mark_state=1, mark_result=4,
                                                    c_user_id=curr_user_id, same_answer_group_id=same_answer_group_id)
                        insert_data.append(stu_answer_data)

                single_display_str = ",".join([allow_exam_num, paper_id])
                if single_display_str not in stu_answer_display_data_set:
                    stu_answer_display_data_set.add(single_display_str)
                    stu_answer_display_data = StuTraj(display_id=configs.snow_worker.get_id(),
                                                      allow_exam_num=allow_exam_num, project_id=project_id,
                                                      subject_id=subject_id, paper_id=paper_id,
                                                      c_user_id=curr_user_id)
                    stu_answer_display_insert_data.append(stu_answer_display_data)

                if count % a_batch_num == 0 or count == rows_count:
                    new_session.add_all(insert_data)
                    delete_same_answer_group(new_session)
                    new_session.add_all(stu_answer_display_insert_data)
                    new_session.commit()
                    new_session.flush()
                    saved_batch_count += 1
                    percent = round_half_up(saved_batch_count / batch_count * 100, 2)
                    logger.info(f"考生作答信息导入进度 {percent}%")
                    # 更新数据库记录的进度
                    record_id = record_id_list[file_index]
                    new_session.query(ImportDataRecord).filter(ImportDataRecord.record_id == record_id).update({
                        ImportDataRecord.progress: percent,
                        ImportDataRecord.u_user_id: curr_user_id
                    })
                    new_session.commit()
                    insert_data, stu_answer_display_insert_data = [], []
        except Exception as e:
            logger.error(f"第 {count + 1} 行数据格式不符合模板要求，导入考生作答信息失败，{e}")
            traceback.print_exc()
            return BaseResponse(code=response_utils.params_error,
                                msg=f"第 {count + 1} 行数据格式不符合模板要求，导入考生作答信息失败")
        set_stu_num_by_task_id()
    logger.info(f"导入考生作答信息成功，导入耗时：{round_half_up(float(time.time() - start), 2)} 秒")
    return BaseResponse(msg=f"导入考生作答信息成功")


@stu_answer_router.post(path="/get_import_stu_answer_progress", response_model=BaseResponse,
                        summary="获取导入考生作答信息进度")
async def get_import_stu_answer_progress(query: ImportProgressReq, user: Any = Depends(get_current_user),
                                         new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取导入考生作答信息进度")
    # 获取进度
    record_id = query.record_id
    progress = new_session.query(ImportDataRecord.progress).filter(ImportDataRecord.record_id == record_id).scalar()
    data = {"data": {"progress": progress if progress is not None else 0}}
    return BaseResponse(msg="获取导入考生作答信息进度成功", data=data)


@stu_answer_router.post(path="/get_stu_answer", response_model=BaseResponse, summary="获取作答信息列表")
async def get_stu_answer(query: GetStuAnswerReq, user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend)):
    current_page, page_size, project_id, subject_id, paper_id, allow_exam_num, stu_secret_num = query.model_dump().values()
    logger.info(f"{user['username']} 获取作答信息列表")
    project_condition, subject_condition = get_user_data_flag(new_session, user)

    stu_answer_data = []
    limit = current_page - 1
    offset = limit * page_size

    select_fields = [StuTraj.allow_exam_num, Project.project_name, Subject.subject_name, ExamPaper.paper_name,
                     StuTraj.created_time]

    # 拼凑查询条件
    condition = stu_answer_display_condition(new_session, project_id, subject_id, paper_id, allow_exam_num,
                                             stu_secret_num)

    stu_answer_stmt = select(*select_fields) \
        .join(Project, Project.project_id == StuTraj.project_id) \
        .join(Subject, Subject.subject_id == StuTraj.subject_id) \
        .outerjoin(ExamPaper, ExamPaper.paper_id == StuTraj.paper_id) \
        .filter(and_(condition, project_condition, subject_condition)) \
        .order_by(StuTraj.created_time.desc(), StuTraj.traj_id.desc()) \
        .limit(page_size).offset(offset)

    total = new_session.query(StuTraj) \
        .join(Project, Project.project_id == StuTraj.project_id) \
        .join(Subject, Subject.subject_id == StuTraj.subject_id) \
        .outerjoin(ExamPaper, ExamPaper.paper_id == StuTraj.paper_id) \
        .filter(and_(condition, project_condition, subject_condition)).count()

    try:
        result = list(new_session.execute(stu_answer_stmt))
        allow_exam_num_list = [i.allow_exam_num for i in result]
        stu_num_dict = {}
        stu_num_info = new_session.query(ExamStudent.allow_exam_num, ExamStudent.stu_secret_num).filter(
            ExamStudent.allow_exam_num.in_(allow_exam_num_list)).all()
        for allow_exam_num, stu_secret_num in stu_num_info:
            stu_num_dict[allow_exam_num] = stu_secret_num

        for row in result:
            answer_item = {
                "allow_exam_num": row.allow_exam_num,
                "stu_secret_num": stu_num_dict[row.allow_exam_num],
                "project_name": row.project_name,
                "subject_name": row.subject_name,
                "paper_name": row.paper_name,
                "created_time": row.created_time and str(row.created_time).replace("T", " ")
            }
            stu_answer_data.append(answer_item)
    except Exception as e:
        logger.error(f"获取作答信息列表失败，{e}")
        logger.error(traceback.print_exc())
        return BaseResponse(code=response_utils.server_error, msg=f"获取作答信息列表失败")
    logger.info("获取作答信息列表成功")
    data = {
        "total": total,
        "data": stu_answer_data
    }
    return BaseResponse(msg="获取作答信息列表成功", data=data)
