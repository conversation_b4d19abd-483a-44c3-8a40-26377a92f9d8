import concurrent.futures
import re
import threading
import traceback
from collections import deque
from typing import Optional, Union

from sqlalchemy import and_, select, insert

from apps.ai_set_std.ai_interface import ai_set_std_req, check_ai_mark, ai_small_set_std_req
from apps.base.global_cache import get_redis_ques_info_dict, set_redis_ques_set_std_state
from apps.base.services import request_api
from apps.models.models import SameStuAnswerGroup, QuesSetStdState, SetStdBackup
from apps.ques_manage.services import combine_image_desc
from apps.read_paper.common_services import is_all_punctuation, clean_ques_text
from factory_apps import session_depend
from helper.response_utils import ai_score_ambiguity
from settings import logger, configs
from utils.time_func import format_now_time
from utils.utils import sum_with_precision


def is_set_std_task_exit(ques_id, log=True, skip_check_state=False):
    """
    检测定标任务状态是否为 暂停评分 或 取消评分，是则退出任务
    """
    if skip_check_state:
        return False
    new_session = next(session_depend())
    set_std_state = new_session.query(QuesSetStdState.set_std_state).filter(QuesSetStdState.ques_id == ques_id).scalar()
    if set_std_state == 4:
        if log:
            logger.info(f"{ques_id} 定标任务暂停")
        return True
    elif set_std_state == 5:
        if log:
            logger.info(f"{ques_id} 定标任务取消")
        return True
    return False


def fill_ques_blank_answer(desc: str, std_answer: list, space_num: list):
    """
    试题除了space_num所在空，其他空用标准答案补全
    :param desc: 试题描述
    :param std_answer: 标准答案列表
    :param space_num: 空号列表
    :return: 格式化的试题描述
    """
    # 使用正则表达式匹配两种类型的空格：下划线和中文数字空格（例如（1））
    pattern1 = r'_{4,}'
    pattern2 = r'（\d+）'

    blanks = []
    tmp_blanks = []
    tmp_blanks1 = re.findall(pattern1, desc)
    tmp_blanks2 = re.findall(pattern2, desc)

    if tmp_blanks1:
        tmp_blanks = tmp_blanks1
        pattern = pattern1
    elif tmp_blanks2:
        tmp_blanks = tmp_blanks2
        pattern = pattern2

    count = 0
    for _ in tmp_blanks:
        count += 1
        blank = f"-*-{count}-*-"
        blanks.append(blank)
        desc = re.sub(pattern, blank, desc, count=1)

    # 检查标准答案数量是否符合
    if len(std_answer) != len(blanks):
        msg = f"标准答案数量{len(std_answer)}与空格数量{len(blanks)}不匹配，标准答案：{std_answer}"
        logger.error(msg)
        return False, None, None, msg

    # 替换除了指定空格之外的其他空格
    formatted_desc = desc
    # print("formatted_desc", formatted_desc)
    # print("blanks", blanks)
    for i, blank in enumerate(blanks):
        if str(i + 1) not in space_num:
            formatted_desc = formatted_desc.replace(blank, std_answer[i], 1)  # 逐个替换，避免重复替换
        else:
            formatted_desc = formatted_desc.replace(blank, "______", 1)  # 应 AI 要求，将空替换为下划线

    space_std_answer = []
    for num in space_num:
        space_std_answer.append(std_answer[int(num) - 1])

    logger.info(f"格式化后的试题描述：{formatted_desc} | 标准答案：{space_std_answer}")
    return True, formatted_desc, space_std_answer, None


def save_score_to_db(new_session, ques_id, group_id_list, stu_score, reason, delete_priority_flag=False):
    """
    将分数和评析保存至数据库
    """
    update_data = {
        SameStuAnswerGroup.running_state: 3,
        SameStuAnswerGroup.mark_state: 2,
        SameStuAnswerGroup.ai_score: stu_score,
        SameStuAnswerGroup.ai_answer_parse: [reason],
    }
    if delete_priority_flag:
        update_data[SameStuAnswerGroup.mark_priority] = None

    new_session.query(SameStuAnswerGroup).filter(and_(SameStuAnswerGroup.ques_id == ques_id,
                                                      SameStuAnswerGroup.same_answer_group_id.in_(group_id_list))) \
        .update(update_data)
    new_session.commit()
    logger.info(f"试题 {ques_id} 数据标记完成")


def remark_init_stu_answer_group(new_session, ques_id, is_priority: bool = False,
                                 priority_group_id_list: Optional[list] = None, is_backup: bool = False):
    """
    标记待定标数据
    """
    update_dict = {
        SameStuAnswerGroup.set_std_state: 1,
        SameStuAnswerGroup.running_state: 2,
        SameStuAnswerGroup.mark_state: 1,
        SameStuAnswerGroup.mark_fail_reason: None,
        SameStuAnswerGroup.ai_score: None,
        SameStuAnswerGroup.ai_answer_parse: [],
        SameStuAnswerGroup.manual_ai_diff_score: None,
        SameStuAnswerGroup.set_std_score_type: None,
        SameStuAnswerGroup.set_std_score: None
    }
    if is_priority:
        update_dict[SameStuAnswerGroup.mark_priority] = 1
        condition = and_(SameStuAnswerGroup.ques_id == ques_id,
                         SameStuAnswerGroup.same_answer_group_id.in_(priority_group_id_list))
    else:
        condition = SameStuAnswerGroup.ques_id == ques_id
    if is_backup:
        # 备份原来的 评分、评析、状态、失败原因 等数据
        # 查询
        source_query = new_session.query(SameStuAnswerGroup.ques_id, SameStuAnswerGroup.same_answer_group_id,
                                         SameStuAnswerGroup.mark_state, SameStuAnswerGroup.running_state,
                                         SameStuAnswerGroup.set_std_state, SameStuAnswerGroup.mark_fail_reason,
                                         SameStuAnswerGroup.ai_score, SameStuAnswerGroup.ai_answer_parse,
                                         SameStuAnswerGroup.manual_ai_diff_score, SameStuAnswerGroup.set_std_score_type,
                                         SameStuAnswerGroup.set_std_score,
                                         SameStuAnswerGroup.set_std_time).filter(condition).all()

        destination_objects = [
            SetStdBackup(
                backup_id=configs.snow_worker.get_id(), ques_id=item.ques_id,
                same_answer_group_id=item.same_answer_group_id, mark_state=item.mark_state,
                running_state=item.running_state, set_std_state=item.set_std_state,
                mark_fail_reason=item.mark_fail_reason, ai_score=item.ai_score,
                ai_answer_parse=item.ai_answer_parse, manual_ai_diff_score=item.manual_ai_diff_score,
                set_std_score_type=item.set_std_score_type, set_std_score=item.set_std_score,
                set_std_time=item.set_std_time
            )
            for item in source_query
        ]

        # 分批插入
        batch_size = 2000
        for i in range(0, len(destination_objects), batch_size):
            new_session.bulk_save_objects(destination_objects[i:i + batch_size])
            new_session.commit()

    new_session.query(SameStuAnswerGroup).filter(condition).update(update_dict)
    new_session.commit()
    logger.info(f"试题 {ques_id} 数据标记完成")


def restore_backup_set_std_data(new_session, ques_id):
    """
    将备份还原并取消标记
    """
    backup_query = new_session.query(SetStdBackup.ques_id, SetStdBackup.same_answer_group_id, SetStdBackup.mark_state,
                                     SetStdBackup.running_state, SetStdBackup.set_std_state,
                                     SetStdBackup.mark_fail_reason, SetStdBackup.ai_score, SetStdBackup.ai_answer_parse,
                                     SetStdBackup.manual_ai_diff_score, SetStdBackup.set_std_score_type,
                                     SetStdBackup.set_std_score, SetStdBackup.set_std_time) \
        .filter(SetStdBackup.ques_id == ques_id).all()
    update_data = [
        {
            SameStuAnswerGroup.ques_id: item.ques_id,
            SameStuAnswerGroup.same_answer_group_id: item.same_answer_group_id,
            SameStuAnswerGroup.mark_state: item.mark_state,
            SameStuAnswerGroup.running_state: item.running_state,
            SameStuAnswerGroup.set_std_state: item.set_std_state,
            SameStuAnswerGroup.mark_fail_reason: item.mark_fail_reason,
            SameStuAnswerGroup.ai_score: item.ai_score,
            SameStuAnswerGroup.ai_answer_parse: item.ai_answer_parse,
            SameStuAnswerGroup.manual_ai_diff_score: item.manual_ai_diff_score,
            SameStuAnswerGroup.set_std_score_type: item.set_std_score_type,
            SameStuAnswerGroup.set_std_score: item.set_std_score,
            SameStuAnswerGroup.set_std_time: item.set_std_time,
            SameStuAnswerGroup.mark_priority: None
        } for item in backup_query
    ]
    # 分批插入
    if update_data:
        batch_size = 2000
        for i in range(0, len(update_data), batch_size):
            new_session.bulk_update_mappings(SameStuAnswerGroup, update_data[i:i + batch_size])
            new_session.commit()
    # 删除备份数据
    new_session.query(SetStdBackup).filter(SetStdBackup.ques_id == ques_id).delete()
    new_session.commit()


def cancel_remark_stu_answer_group(new_session, ques_id):
    """
    取消待定标数据标记
    """
    try:
        new_session.query(SameStuAnswerGroup).filter(SameStuAnswerGroup.ques_id == ques_id).update({
            SameStuAnswerGroup.running_state: 1,
            SameStuAnswerGroup.set_std_state: 1,
            SameStuAnswerGroup.mark_state: 1,
            SameStuAnswerGroup.mark_priority: None,
            SameStuAnswerGroup.mark_fail_reason: None,
            SameStuAnswerGroup.ai_score: None,
            SameStuAnswerGroup.ai_answer_parse: [],
            SameStuAnswerGroup.manual_ai_diff_score: None,
            SameStuAnswerGroup.set_std_score_type: None,
            SameStuAnswerGroup.set_std_score: None
        })
        new_session.commit()
        logger.info(f"试题 {ques_id} 取消待定标数据标记完成")
        return True
    except:
        logger.error(f"试题 {ques_id} 取消待定标数据标记失败")
        logger.error(traceback.format_exc())
        return False


def update_ques_set_std_state(new_session, ques_id: Union[str, list], running_state, set_std_state, pause_state=0):
    """
    更新试题定标任务的状态
    """
    update_data = {
        QuesSetStdState.running_state: running_state,
        QuesSetStdState.set_std_state: set_std_state
    }

    if set_std_state == 2:
        update_data[QuesSetStdState.launch_ip] = configs.BACKEND_IP
        update_data[QuesSetStdState.set_std_time] = format_now_time()
        update_data[QuesSetStdState.set_std_end_time] = None

    if set_std_state == 3 and running_state == 7:
        update_data[QuesSetStdState.set_std_end_time] = format_now_time()

    if pause_state != 0:
        update_data[QuesSetStdState.pause_state] = pause_state

    if type(ques_id) == list:
        new_session.query(QuesSetStdState).filter(QuesSetStdState.ques_id.in_(ques_id)).update(update_data)
        set_redis_ques_set_std_state(ques_id, running_state)
    else:
        new_session.query(QuesSetStdState).filter(QuesSetStdState.ques_id == ques_id).update(update_data)
        set_redis_ques_set_std_state([ques_id], running_state)
    new_session.commit()


def filter_answer_group_info(new_session, ques_id, ques_info, again_mark_group_id_list=[], delete_priority_flag=False):
    """
    过滤作答信息
    过滤条件：
    1.答案为空
    2.答案全是标点符号
    3.答案与标准答案一样
    """
    if again_mark_group_id_list:
        same_group_info = new_session.query(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.score,
                                            SameStuAnswerGroup.small_ques_order, SameStuAnswerGroup.stu_answer) \
            .filter(and_(SameStuAnswerGroup.ques_id == ques_id,
                         SameStuAnswerGroup.same_answer_group_id.in_(again_mark_group_id_list))).all()
    else:
        same_group_info = new_session.query(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.score,
                                            SameStuAnswerGroup.small_ques_order, SameStuAnswerGroup.stu_answer) \
            .filter(and_(SameStuAnswerGroup.ques_id == ques_id, SameStuAnswerGroup.running_state == 2)).all()
    answer_group_list, no_answer_group_id_list, all_punc_group_id_list, perfect_score_group_id_list = [], [], [], []

    if same_group_info:
        stu_score = None
        ques_std_answer, ques_type_code, d_out_of_order_group = ques_info["standard_answer"], ques_info[
            "ques_type_code"], ques_info["d_out_of_order_group"]
        for group_id, ques_score, small_ques_order, stu_answer in same_group_info:
            if stu_answer is None or stu_answer == "" or stu_answer.replace(configs.NEW_SPLIT_FLAG, "") == "":
                # 无答案数据
                no_answer_group_id_list.append(group_id)
                continue
            else:
                if is_all_punctuation(stu_answer.replace(configs.NEW_SPLIT_FLAG, "").replace("\n", "")):
                    all_punc_group_id_list.append(group_id)
                    continue
                elif ques_type_code == "D":
                    stu_answer = stu_answer.replace("\n", "")
                    if d_out_of_order_group and configs.NEW_SPLIT_FLAG in small_ques_order:
                        # 填空题可乱序
                        sort_stu_answer = sorted(stu_answer.split(configs.NEW_SPLIT_FLAG))
                        index_list = [(int(i) - 1) for i in small_ques_order.split(configs.NEW_SPLIT_FLAG)]
                        space_std_answer = [ques_std_answer[j] for j in index_list]
                        sort_std_answer = sorted(space_std_answer)
                        if sort_stu_answer == sort_std_answer:
                            stu_score = sum_with_precision(ques_score.split(configs.NEW_SPLIT_FLAG))
                            perfect_score_group_id_list.append(group_id)
                            continue

                        ques_score = [float(i) for i in ques_score.split(configs.NEW_SPLIT_FLAG)]
                    else:
                        # 填空题不可乱序
                        space_std_answer = ques_std_answer[int(small_ques_order) - 1]
                        if stu_answer == space_std_answer:
                            stu_score = float(ques_score)
                            perfect_score_group_id_list.append(group_id)

                if isinstance(ques_score, str) and configs.NEW_SPLIT_FLAG not in ques_score:
                    ques_score = float(ques_score)

                elif isinstance(ques_score, str) and configs.NEW_SPLIT_FLAG in ques_score:
                    ques_score = ques_score.split(configs.NEW_SPLIT_FLAG)

                answer_group_list.append({
                    "same_answer_group_id": group_id,
                    "ques_score": ques_score,
                    "small_ques_order": small_ques_order,
                    "stu_answer": stu_answer
                })

        if no_answer_group_id_list:
            # 答案为空的判为 0 分
            save_score_to_db(new_session, ques_id, no_answer_group_id_list, 0, "考生未作答，得0分", delete_priority_flag)
        if all_punc_group_id_list:
            # 答案全是标点符号的判为 0 分
            save_score_to_db(new_session, ques_id, all_punc_group_id_list, 0, "考生作答不符合规范，得0分",
                             delete_priority_flag)

        if perfect_score_group_id_list:
            save_score_to_db(new_session, ques_id, perfect_score_group_id_list, stu_score,
                             "考生答案与参考答案一致，得满分", delete_priority_flag)

        return answer_group_list


def filter_answer_group_info_small(new_session, ques_id, ques_info, again_mark_group_id_list=[], delete_priority_flag=False):
    """
    过滤作答信息（兼容小题且保留原始逻辑）
    过滤条件：
    1.答案为空
    2.答案全是标点符号
    3.答案与标准答案一样
    """
    if again_mark_group_id_list:
        same_group_info = new_session.query(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.score,
                                            SameStuAnswerGroup.small_ques_order, SameStuAnswerGroup.stu_answer) \
            .filter(and_(SameStuAnswerGroup.ques_id == ques_id,
                         SameStuAnswerGroup.same_answer_group_id.in_(again_mark_group_id_list))).all()
    else:
        same_group_info = new_session.query(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.score,
                                            SameStuAnswerGroup.small_ques_order, SameStuAnswerGroup.stu_answer) \
            .filter(and_(SameStuAnswerGroup.ques_id == ques_id, SameStuAnswerGroup.running_state == 2)).all()

    answer_group_list, no_answer_group_id_list, all_punc_group_id_list, perfect_score_group_id_list = [], [], [], []
    perfect_score = None  # 用于保存完美匹配的分数

    if same_group_info:
        stu_score = None
        ques_std_answer, ques_type_code, d_out_of_order_group = ques_info["standard_answer"], ques_info[
            "ques_type_code"], ques_info["d_out_of_order_group"]
        sub_ques_list = ques_info.get("sub_ques_list", [])

        for group_id, ques_score, small_ques_order, stu_answer in same_group_info:
            if stu_answer is None or stu_answer == "" or stu_answer.replace(configs.NEW_SPLIT_FLAG, "") == "":
                no_answer_group_id_list.append(group_id)
                continue

            if is_all_punctuation(stu_answer.replace(configs.NEW_SPLIT_FLAG, "").replace("\n", "")):
                all_punc_group_id_list.append(group_id)
                continue

            # 处理小题
            if sub_ques_list:
                # 处理每个小题
                answer_segments = stu_answer.split(configs.NEW_SPLIT_FLAG)

                for sub_idx, sub_ques in enumerate(sub_ques_list):
                    print(sub_ques)
                    sub_ques_id = sub_ques["sub_ques_id"]
                    sub_std_answer = sub_ques.get("sub_ques_std_answer", [])

                    if sub_idx < len(answer_segments):
                        sub_stu_answer = answer_segments[sub_idx]

                        # 检查完美匹配（保留原始逻辑）
                        is_perfect, perfect_score = is_perfect_match_with_score(
                            sub_stu_answer, sub_std_answer,
                            ques_type_code, d_out_of_order_group,
                            ques_score, small_ques_order
                        )

                        if is_perfect:
                            perfect_score_group_id_list.append(group_id)
                            continue

                    # 添加需要AI评分的答案
                    answer_group_list.append({
                        "same_answer_group_id": group_id,
                        "ques_score": ques_score,
                        "small_ques_order": small_ques_order,
                        "stu_answer": sub_stu_answer,
                        "sub_ques_id": sub_ques_id
                    })
            else:
                # 处理主问题（保持原始逻辑）
                is_perfect, perfect_score = is_perfect_match_with_score(
                    stu_answer, ques_std_answer,
                    ques_type_code, d_out_of_order_group,
                    ques_score, small_ques_order
                )

                if is_perfect:
                    perfect_score_group_id_list.append(group_id)
                    continue

                answer_group_list.append({
                    "same_answer_group_id": group_id,
                    "ques_score": ques_score,
                    "small_ques_order": small_ques_order,
                    "stu_answer": stu_answer
                })

        # 保存过滤结果
        if no_answer_group_id_list:
            save_score_to_db(new_session, ques_id, no_answer_group_id_list, 0, "考生未作答，得0分", delete_priority_flag)

        if all_punc_group_id_list:
            save_score_to_db(new_session, ques_id, all_punc_group_id_list, 0, "考生作答不符合规范，得0分",
                             delete_priority_flag)

        if perfect_score_group_id_list and perfect_score is not None:
            save_score_to_db(new_session, ques_id, perfect_score_group_id_list, perfect_score,
                             "考生答案与参考答案一致，得满分", delete_priority_flag)

    return answer_group_list


def is_perfect_match_with_score(stu_answer, std_answer, ques_type_code, d_out_of_order_group, ques_score,
                                small_ques_order):
    """
    带分数计算的完美匹配判断
    保留原始逻辑并增强功能
    """
    if not stu_answer or not std_answer:
        return False, None

    if ques_type_code == "D":  # 填空题
        if isinstance(ques_score, str) and configs.NEW_SPLIT_FLAG in ques_score:
            ques_score = ques_score.split(configs.NEW_SPLIT_FLAG)

        if d_out_of_order_group and configs.NEW_SPLIT_FLAG in small_ques_order:
            # 乱序处理逻辑
            sort_stu_answer = sorted(stu_answer.split(configs.NEW_SPLIT_FLAG))
            index_list = [(int(i) - 1) for i in small_ques_order.split(configs.NEW_SPLIT_FLAG)]
            space_std_answer = [std_answer[j] for j in index_list]
            sort_std_answer = sorted(space_std_answer)

            if sort_stu_answer == sort_std_answer:
                return True, sum_with_precision(ques_score)

        else:
            # 非乱序处理
            if isinstance(ques_score, str) and configs.NEW_SPLIT_FLAG not in ques_score:
                ques_score = float(ques_score)

            if isinstance(std_answer, list):
                print(small_ques_order)
                print(std_answer)
                space_std_answer = std_answer[0]
                if stu_answer == space_std_answer:
                    return True, float(ques_score)
            else:
                if stu_answer == std_answer:
                    return True, float(ques_score)

    elif ques_type_code == "E":  # 选择题
        if stu_answer == std_answer:
            return True, float(ques_score)

    return False, None


def auto_pause_ai_set_std(ques_id_list, user):
    # 批量自动暂停任务
    req_data = {
        "ques_id_list": ques_id_list,
        "is_auto_trigger": True
    }
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/set_std/pause_set_std"
    res, msg = request_api(url, "POST", req_data, token, "批量自动暂停任务")
    if res == 0:
        logger.error("批量自动暂停任务失败")
        return False, msg
    logger.info("批量自动暂停任务成功")
    return True, None


def auto_continue_ai_set_std(ques_id_list, token, is_auto_launch=False):
    # 批量自动继续启动任务
    req_data = {
        "ques_id_list": ques_id_list,
        "is_auto_trigger": True,
        "is_auto_launch": is_auto_launch
    }
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/set_std/continue_set_std"
    res, msg = request_api(url, "POST", req_data, token, "批量自动继续启动任务")
    if res == 0:
        logger.error("批量自动继续启动任务失败")
        return False, msg
    logger.info("批量自动继续启动任务成功")
    return True, None


def launch_next_set_std_task(new_session):
    # 查询是否有在运行中的任务
    running_task = new_session.query(QuesSetStdState.state_id).filter(QuesSetStdState.set_std_state == 2).first()
    if not running_task:
        # 没有就执行下一个待定标的任务
        next_info = new_session.query(QuesSetStdState.ques_id, QuesSetStdState.running_state) \
            .filter(QuesSetStdState.set_std_state == 6) \
            .order_by(QuesSetStdState.updated_time.desc(), QuesSetStdState.ques_id).first()
        if next_info:
            ques_id, running_state = next_info
            ques_id_list = [ques_id]
            if running_state == 4:
                # 继续任务
                auto_continue_ai_set_std([ques_id], configs.GLOBAL_TOKEN)
            else:
                # 开始任务
                result, launch_ques_id_list = check_task_execute_state(new_session, ques_id_list)
                threading.Thread(target=start_set_std_main,
                                 args=([ques_id], False, False, [], [], configs.GLOBAL_TOKEN, launch_ques_id_list)).start()


def check_task_execute_state(new_session, ques_id_list, verify_ip=True):
    launch_ques_id_list = []
    try:
        for index, ques_id in enumerate(ques_id_list):
            # 查询是否有在运行中的任务
            if index == 0:
                if configs.IS_SEVERAL_SERVER and verify_ip:
                    condition = and_(QuesSetStdState.set_std_state == 2, QuesSetStdState.ques_id != ques_id, QuesSetStdState.launch_ip == configs.BACKEND_IP)
                else:
                    condition = and_(QuesSetStdState.set_std_state == 2, QuesSetStdState.ques_id != ques_id)
                running_task = new_session.query(QuesSetStdState.state_id).filter(condition).first()
                if not running_task:
                    update_ques_set_std_state(new_session, ques_id, 3, 2)
                    launch_ques_id_list.append(ques_id)
                else:
                    update_ques_set_std_state(new_session, ques_id, 8, 6)
            else:
                update_ques_set_std_state(new_session, ques_id, 8, 6)
    except Exception as e:
        logger.error(e, traceback.format_exc())
        return False, launch_ques_id_list
    return True, launch_ques_id_list


def start_set_std_main(ques_id_list, skip_remark=False, skip_check_state=False, again_mark_group_id_list=[],
                       auto_start_ques_id_list=[], token=None, launch_ques_id_list=[]):
    """
    定标主流程函数
    skip_remark 为 False，skip_check_state 为 False 表示开始 AI 定标
    skip_remark 为 True，skip_check_state 为 False 表示继续 AI 定标
    skip_remark 为 True，skip_check_state 为 True 表示重新 AI 定标，again_mark_group_id_list 为重新定标的 group_id 列表，auto_start_ques_id_list 表示重新定标时被暂停的任务
    """
    new_session = next(session_depend())

    # 标记数据, 用试题id分批标记，防止大批量更新数据库失败
    for index, ques_id in enumerate(ques_id_list):
        if is_set_std_task_exit(ques_id):
            return
        if not skip_remark:
            remark_init_stu_answer_group(new_session, ques_id)
            logger.info(f"试题 {ques_id} 数据标记完成")
            if is_set_std_task_exit(ques_id):
                return

        # # 查询是否有在运行中的任务
        # if index == 0:
        #     running_task = new_session.query(QuesSetStdState.state_id).filter(and_(QuesSetStdState.set_std_state == 2, QuesSetStdState.ques_id != ques_id)).first()
        #     if not running_task:
        #         update_ques_set_std_state(new_session, ques_id, 3, 2)
        #         launch_ques_id_list.append(ques_id)
        #     else:
        #         update_ques_set_std_state(new_session, ques_id, 8, 6)
        # else:
        #     update_ques_set_std_state(new_session, ques_id, 8, 6)
        # if is_set_std_task_exit(ques_id):
        #     return

    if again_mark_group_id_list:
        launch_ques_id_list = ques_id_list
    launch_ques_num = len(launch_ques_id_list)
    all_ques_info = get_redis_ques_info_dict(new_session) if launch_ques_id_list else {}
    for index, ques_id in enumerate(launch_ques_id_list):
        if is_set_std_task_exit(ques_id, skip_check_state=skip_check_state):
            return
        ques_info = all_ques_info[ques_id]
        # 按 ques_id 进行评分
        # 获取该题信息

        if ques_info.get("sub_ques_list"):
            answer_group_list = filter_answer_group_info_small(new_session, ques_id, ques_info, again_mark_group_id_list,
                                                         skip_check_state)
        else:
            # 获取作答信息
            answer_group_list = filter_answer_group_info(new_session, ques_id, ques_info, again_mark_group_id_list,
                                                         skip_check_state)
        if not answer_group_list:
            logger.info(f"修改试题 {ques_id} 状态为已定标")
            update_ques_set_std_state(new_session, ques_id, 7, 3)
            if skip_check_state:
                if index + 1 == launch_ques_num:
                    # 自动启用被暂停的任务
                    auto_continue_ai_set_std(auto_start_ques_id_list, token)
            else:
                # 检测当前是否有正在 AI 定标的任务，有就跳过，没有就启动一个待定标的任务
                launch_next_set_std_task(new_session)
            continue

        if is_set_std_task_exit(ques_id, skip_check_state=skip_check_state):
            return

        # 实现可暂停的任务队列
        finish_count = 0
        max_workers = configs.AI_REQ_THREAD_COUNT
        subtasks = deque(answer_group_list)  # 使用双端队列存储任务
        subtasks_length = len(answer_group_list)
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = set()
            running_count = 0
            while subtasks or futures:
                while len(futures) < max_workers and subtasks:
                    logger.info(f"{ques_id} 开启线程数：{len(futures)}, 队列剩余任务：{len(subtasks)}")
                    if is_set_std_task_exit(ques_id, skip_check_state=skip_check_state):
                        break

                    # 提交任务
                    answer_group = subtasks.popleft()
                    future = executor.submit(ai_set_std_thread, ques_id, ques_info, answer_group, skip_check_state)
                    futures.add(future)
                    running_count += 1

                # 等待至少一个任务完成或状态变更
                done, _ = concurrent.futures.wait(
                    futures,
                    timeout=1,  # 每秒检查一次状态
                    return_when=concurrent.futures.FIRST_COMPLETED
                )

                # 处理已完成的任务
                for future in done:
                    futures.remove(future)
                    try:
                        result = future.result()
                        logger.info(f"智能定标线程结果：{result}")
                        finish_count += 1
                    except:
                        logger.error("智能定标线程结果：failed")
                        logger.error(traceback.print_exc())

                    if finish_count == subtasks_length:
                        if skip_check_state:
                            # 自动启用被暂停的任务
                            auto_continue_ai_set_std(auto_start_ques_id_list, token)
                        else:
                            logger.info(f"修改试题 {ques_id} 状态为已定标")
                            update_ques_set_std_state(new_session, ques_id, 7, 3)
                            if index + 1 == launch_ques_num:
                                # 检测当前是否有正在 AI 定标的任务，有就跳过，没有就启动一个待定标的任务
                                launch_next_set_std_task(new_session)

                if is_set_std_task_exit(ques_id, skip_check_state=skip_check_state):
                    for future in futures:
                        future.cancel()
                    # 清空未执行任务队列
                    subtasks.clear()
                    return


def save_fail_reason(new_session, ques_id, same_answer_group_id, mark_state, fail_reason, delete_priority_flag=False):
    """
    保存定标失败原因
    """
    update_data = {
        SameStuAnswerGroup.running_state: 3,
        SameStuAnswerGroup.mark_state: mark_state,
        SameStuAnswerGroup.mark_fail_reason: fail_reason,
        SameStuAnswerGroup.ai_mark_time: format_now_time()
    }
    if delete_priority_flag:
        update_data[SameStuAnswerGroup.mark_priority] = None

    new_session.query(SameStuAnswerGroup) \
        .filter(and_(SameStuAnswerGroup.ques_id == ques_id,
                     SameStuAnswerGroup.same_answer_group_id == same_answer_group_id)).update(update_data)
    new_session.commit()


def save_success_result(new_session, ques_id, same_answer_group_id, ai_score, ai_parse, ai_error_analysis, delete_priority_flag=False):
    """
    保存定标成功结果
    """
    try:
        update_data = {
            SameStuAnswerGroup.running_state: 3,
            SameStuAnswerGroup.mark_state: 2,
            SameStuAnswerGroup.ai_score: ai_score,
            SameStuAnswerGroup.ai_answer_parse: ai_parse,
            SameStuAnswerGroup.ai_error_analysis: ai_error_analysis,
            SameStuAnswerGroup.ai_mark_time: format_now_time()
        }
        if delete_priority_flag:
            update_data[SameStuAnswerGroup.mark_priority] = None
        print(ques_id)
        new_session.query(SameStuAnswerGroup) \
            .filter(and_(SameStuAnswerGroup.ques_id == ques_id,
                         SameStuAnswerGroup.same_answer_group_id == same_answer_group_id)).update(update_data)
        new_session.commit()
        logger.info("ai定标信息已存入数据库")
    except Exception as e:
        logger.error(f"ai定标信息存入数据库失败: {str(e)}")
        new_session.rollback()


def ai_set_std_thread(ques_id, ques_info, answer_group, delete_priority_flag=False):
    """
    发送给 AI 进行评分
    返回结果：1 表示成功，2 表示失败
    """
    new_session = next(session_depend())
    same_answer_group_id = answer_group["same_answer_group_id"]
    try:
        (ques_score, small_ques_order, stu_answer) = (answer_group["ques_score"], answer_group["small_ques_order"],
                                                      answer_group["stu_answer"])

        (subject_name, subject_info, ques_desc, ques_material, ques_std_answer, ques_type_code, weight,
         d_out_of_order_group, e_mark_rule, mark_point, material_desc, parent_material_desc) = (
            ques_info["subject_name"], ques_info["subject_info"], ques_info["ques_desc"], ques_info["ques_material"],
            ques_info["standard_answer"], ques_info["ques_type_code"], ques_info["weight"],
            ques_info["d_out_of_order_group"], ques_info["e_mark_rule"], ques_info["ques_mark_point"],
            ques_info["material_desc"], ques_info["parent_material_desc"]
        )
        print(1,stu_answer)
        # 合并试题图片描述
        parent_images_desc, sub_images_desc = combine_image_desc(parent_material_desc, material_desc)

        # 清洗试题描述，以便 AI 理解
        ques_desc_text = clean_ques_text(ques_desc.get("text", "")) if ques_desc else ""

        is_multiple = 0
        if ques_type_code == "D":
            if d_out_of_order_group and configs.NEW_SPLIT_FLAG in small_ques_order:
                is_multiple = 1
                # 可乱序
                index_list = small_ques_order.split(configs.NEW_SPLIT_FLAG)
                # 拼接试题描述
                result, ques_desc_text, ques_std_answer, msg = fill_ques_blank_answer(ques_desc_text, ques_std_answer,
                                                                                      index_list)
                # 修改试题评分规则
                # e_mark_rule = "所有空不区分顺序"
                stu_answer = ("".join(stu_answer)).split(configs.NEW_SPLIT_FLAG)
                print(2,stu_answer)
            else:
                ques_score = [ques_score]
                # 不可乱序
                if len(ques_std_answer) >= 2:
                    # 补全答案
                    result, ques_desc_text, ques_std_answer, msg = fill_ques_blank_answer(ques_desc_text,
                                                                                          ques_std_answer,
                                                                                          small_ques_order)
                    is_multiple = 0
                    if not result:
                        logger.error(f"分组id：{same_answer_group_id}，{msg}")
                        save_fail_reason(new_session, ques_id, same_answer_group_id, 3, msg, delete_priority_flag)
                        return "failed"
                    # else:
                    #     e_mark_rule = "空区分顺序"
        ques_material_text = clean_ques_text(ques_material.get("text", "")) if ques_material else ""
        if not isinstance(stu_answer, list):
            stu_answer = [stu_answer]
        if not isinstance(ques_score, list):
            ques_score = [ques_score]

        # 给试题添加图片描述
        ques_desc_text += sub_images_desc
        if ques_material_text:
            ques_material_text += parent_images_desc
        else:
            ques_material_text = parent_images_desc
        # 判断是否需要调用小题接口
        if ques_info.get("sub_ques_list"):  # 如果包含小题信息
            sub_question_list = ques_info["sub_ques_list"]
            sub_ques_list = []
            for sub_question in sub_question_list:
                sub_ques_id, sub_ques_mark_point, sub_ques_std_score, sub_ques_mark_rule, sub_ques_std_answer = \
                sub_question["sub_ques_id"], sub_question["sub_ques_mark_point"], sub_question["sub_ques_std_score"], \
                sub_question["sub_ques_mark_rule"], sub_question["sub_ques_std_answer"]
                sub_ques_list.append({
                    "sub_ques_id": sub_ques_id,
                    "sub_ques_mark_point": sub_ques_mark_point,
                    "sub_ques_std_score": [sub_ques_std_score],
                    "sub_ques_mark_rule": sub_ques_mark_rule,
                    "is_multiple": 1,
                    "sub_ques_stu_answer":stu_answer,
                    "sub_ques_std_answer": sub_ques_std_answer
                })
            code, ai_res = ai_small_set_std_req(
                same_answer_group_id=same_answer_group_id,
                ques_id=ques_id,
                ques_desc=ques_desc_text,
                subject=subject_name if subject_name else "软件设计师",
                e_mark_rule=e_mark_rule,
                std_score=ques_score,
                sub_ques_list= sub_ques_list,
                ques_material=ques_material_text
            )
        else:
            code, ai_res = ai_set_std_req(
                same_answer_group_id=same_answer_group_id,
                ques_id=ques_id,
                subject=subject_name,
                subject_info=subject_info,
                ques_type=ques_type_code,
                ques_desc=ques_desc_text,
                std_answer=ques_std_answer,
                stu_answer=stu_answer,
                std_score=ques_score,
                e_mark_rule=e_mark_rule,
                mark_point=mark_point,
                ques_material=ques_material_text,
                is_multiple=is_multiple
            )

        if code == 200:
            # 保存结果
            if ques_info.get("sub_ques_list"):
                sub_ques_data_list,same_answer_group_id = ai_res["sub_ques_data_list"], ai_res["same_answer_group_id"]
                for sub_ques_data in sub_ques_data_list:
                    ques_id, small_ques_id, ai_score, ai_score_list, ai_reason, ai_error_analysis = ques_id, sub_ques_data["sub_ques_id"], \
                    sub_ques_data["sub_ques_ai_score"], sub_ques_data["sub_ques_score_list"], sub_ques_data[
                        "sub_ques_ai_reason"], sub_ques_data["sub_ques_ai_error_analysis"]
                    if ai_score == -1:
                        logger.warning(f"分组id：{same_answer_group_id}，{ai_score_ambiguity}")
                        save_fail_reason(new_session, ques_id, same_answer_group_id, 4, ai_score_ambiguity,
                                         delete_priority_flag)
                        return "ambiguity"
                    else:
                        # 检查 AI 评分的分数
                        ai_score = check_ai_mark(ques_score, ai_score)
                        save_success_result(new_session, ques_id, same_answer_group_id, ai_score, ai_reason, ai_error_analysis,
                                            delete_priority_flag)
                        return "success"
            else:
                ai_score, ai_parse, ai_score_list,ai_error_analysis = ai_res["ai_score"], ai_res["ai_parse"], ai_res["ai_score_list"], ai_res["ai_error_analysis"]
                if ai_score == -1:
                    logger.warning(f"分组id：{same_answer_group_id}，{ai_score_ambiguity}")
                    save_fail_reason(new_session, ques_id, same_answer_group_id, 4, ai_score_ambiguity,
                                     delete_priority_flag)
                    return "ambiguity"
                else:
                    # 检查 AI 评分的分数
                    ai_score = check_ai_mark(ques_score, ai_score)
                    save_success_result(new_session, ques_id, same_answer_group_id, ai_score, ai_parse,ai_error_analysis,
                                        delete_priority_flag)
                    return "success"
        else:
            # 保存失败原因
            logger.error(f"分组id：{same_answer_group_id}，{ai_res}")
            save_fail_reason(new_session, ques_id, same_answer_group_id, 3, ai_res, delete_priority_flag)
            return "failed"
    except:
        logger.error(traceback.format_exc())
        save_fail_reason(new_session, ques_id, same_answer_group_id, 3, "程序异常")
        return 2


def check_can_pause(state):
    msg = None
    if state == 0:
        msg = "所选任务中包含 未开始定标 的任务"
    elif state == 4:
        msg = "所选任务中包含 已暂停 的任务，请勿重复操作"
    elif state in [5, 6]:
        msg = "所选任务中包含 取消 的任务，无法暂停"
    elif state == 7:
        msg = "所选任务中包含 已完成定标 的任务，无法暂停"
    return msg


def check_can_cancel(state):
    msg = None
    if state == 0:
        msg = "任务未开始"
    elif state in [5, 6]:
        msg = "任务已取消，请勿重复操作"
    # elif state == 7:
    #     msg = "任务已完成，无法取消"
    return msg


if __name__ == '__main__':
    # ques_desc = "某同学想要用木板手工制作如下图所示木质粉笔盒，正确的加工工序为（1）、（1）、刨削、钉接、表面处理（填写正确加工工序）。需要用到的工具有铅笔、（1）、木工锯、刨子、（1）、钉子、（1）等（填序号，每空只能选一个，不可重复）。A.划规    B.砂纸     C.锤子     D.划针    E.钢直尺   "
    ques_desc = "某同学想要用木板手工制作如下图所示木质粉笔盒，正确的加工工序为______、______、刨削、钉接、表面处理（填写正确加工工序）。需要用到的工具有铅笔、______、木工锯、刨子、______、钉子、______等（填序号，每空只能选一个，不可重复）。A.划规    B.砂纸     C.锤子     D.划针    E.钢直尺   "
    std_answer = ["等于(画线)", "等于(锯割)", "B或C或E", "B或C或E", "B或C或E"]
    space_num = ["2"]

    fill_ques_blank_answer(ques_desc, std_answer, space_num)
