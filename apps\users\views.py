import json
import os
import traceback

from fastapi import APIRouter, Depends, BackgroundTasks,Request

from apps.human_mark_group.models import HumanGroupMember,HumanMarkGroup
from settings import logger
from sqlalchemy import exists, select, and_, func
from sqlalchemy.orm import aliased, Session
from typing import Any

from apps.manual_read_paper.services import get_all_username_and_id_card, verify_manual_people
from apps.permission.services import get_user_role, add_user_data_permission, update_user_data_permission
from apps.users.services import get_current_user, generate_user_login_data, verify_login, get_default_password, \
    user_query_condition, verify_unlock_screen_pwd
from apps.base.schemas import BaseResponse
from apps.users.schemas import UserBaseReq, GetUserReq, RegisterReq, ChangePasswordReq, UpdateUserReq, UserIdReq, \
    UserLoginReq, ChangeRoleReq, UpdateUserStateReq, FirstChangePassword
from apps.models.models import User<PERSON>n<PERSON>, User<PERSON><PERSON>, <PERSON>, ManualGroupUser, AdministrativeRegion, UserDataPer<PERSON>, \
    Project, Subject,ExamQuestion
from factory_apps import session_depend
from helper import response_utils
from helper.encrypto import decrypt
from helper.token import get_hash_password, verify_hash_password
from helper.excel_export_utils import ExcelExportService
from settings import configs
user_router = APIRouter()


@user_router.post(path="/register", response_model=BaseResponse, summary="创建用户")
async def register(query: RegisterReq, user: Any = Depends(get_current_user),
                   new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建用户")
    curr_user_id = user.get("user_id")
    username, role_id_list, system_user_type, project_id_list, subject_id_list, round_count, name, phone, id_card, province_code, city_code, district_code, work_unit, is_ai = query.model_dump().values()
    # 验证轮次范围（虽然Pydantic已经验证，但额外验证确保安全）
    # if round_count < 1 or round_count > 6:
    #     return BaseResponse(code=response_utils.params_error, msg="轮次必须在1-6范围内")
    is_exist = new_session.query(exists().where(UserInfo.username == username)).scalar()
    # 判断是否已经注册
    if is_exist:
        return BaseResponse(code=response_utils.fields_exist, msg="该账号已存在")

    if system_user_type == 2:
        if not project_id_list or not subject_id_list:
            return BaseResponse(code=response_utils.params_error, msg="请选择项目和科目")

    all_username, all_id_card, _, all_phone = get_all_username_and_id_card()
    result, msg = verify_manual_people(username, phone, id_card, all_username, all_id_card,all_phone)
    # 新增手机号重复校验逻辑
    if phone:
        # 检查手机号是否已存在（排除当前用户）
        existing_user = new_session.query(UserInfo.user_id).filter(
            UserInfo.phone == phone
        ).first()
        if existing_user:
            return BaseResponse(code=response_utils.fields_exist, msg="手机号已存在，请使用其他手机号")

    if not result:
        return BaseResponse(code=response_utils.params_error, msg=msg)

    # 获取默认密码
    result, msg, de_default_password = get_default_password()
    if not result:
        return BaseResponse(code=response_utils.permission_deny, msg=msg)

    # 密码哈希加密
    private_password = get_hash_password(de_default_password)

    try:
        # 数据插入数据库
        user_id = configs.snow_worker.get_id()
        new_user = UserInfo(user_id=user_id, username=username, password=private_password, name=name, phone=phone, round_count=round_count,
                            id_card=id_card, province_code=province_code, city_code=city_code, system_user_type=system_user_type,
                            district_code=district_code, work_unit=work_unit, user_type=is_ai, c_user_id=curr_user_id)
        new_session.add(new_user)
        new_user_role_list = []
        for single_role_id in role_id_list:
            new_user_role = UserRole(user_role_id=configs.snow_worker.get_id(), user_id=user_id, role_id=single_role_id,
                                     c_user_id=curr_user_id)
            new_user_role_list.append(new_user_role)

        new_session.add_all(new_user_role_list)

        if system_user_type == 2:
            add_user_data_permission(new_session, user_id, project_id_list, subject_id_list)

        new_session.commit()
        logger.info(f"{username} 创建用户成功")
        return BaseResponse(msg=f'{username} 创建用户成功')
    except Exception as e:
        new_session.rollback()
        logger.error(f"{username} 创建用户失败，{e}")
    return BaseResponse(code=response_utils.server_error, msg=f'{username} 创建用户失败')


@user_router.post(path="/is_has_several_role", response_model=BaseResponse, summary="检查该用户是否有多个角色")
async def is_has_several_role(query: UserBaseReq, new_session: Session = Depends(session_depend)):
    username, input_password = query.username, query.password
    logger.info(f"检查 {username} 该用户是否有多个角色")

    user_id, name, role_id_list, user_type, already_login, system_user_type, phone, id_card, msg = verify_login(
        new_session, username, input_password)

    if msg:
        return BaseResponse(code=response_utils.permission_deny, msg=msg)

    if len(role_id_list) > 1:
        # 选择角色
        role_info = new_session.query(Role.role_id, Role.role_name).filter(Role.role_id.in_(role_id_list)).all()
        role_data = [{"role_id": role[0], "role_name": role[1]} for role in role_info]

        data = generate_user_login_data(new_session, user_id, username, name, ["6"], user_type, already_login,
                                        system_user_type, phone, id_card)
        data["role_data"] = role_data
        return BaseResponse(data=data, msg="请选择登录角色")
    else:
        logger.info(f"{username} 用户无多个角色，直接登录")
        data = generate_user_login_data(new_session, user_id, username, name, role_id_list, user_type, already_login,
                                        system_user_type, phone, id_card)
        logger.info(f"{username} 登录成功")
        return BaseResponse(data=data)


@user_router.post(path="/login", response_model=BaseResponse, summary="登录")
async def login(query: UserLoginReq, new_session: Session = Depends(session_depend)):
    username, input_password, role_id_list = query.model_dump().values()
    logger.info(f"{username} 用户登录")

    user_id, name, _, user_type, already_login, system_user_type, phone, id_card, msg = verify_login(new_session,
                                                                                                     username,
                                                                                                     input_password)

    if msg:
        return BaseResponse(code=response_utils.permission_deny, msg=msg)

    data = generate_user_login_data(new_session, user_id, username, name, role_id_list, user_type, already_login,
                                    system_user_type, phone, id_card)

    logger.info(f"{username} 登录成功")
    return BaseResponse(data=data)


@user_router.post(path="/verify_unlock_screen", response_model=BaseResponse, summary="解锁屏幕")
async def verify_unlock_screen(query: UserBaseReq, new_session: Session = Depends(session_depend)):
    username, input_password = query.model_dump().values()
    logger.info(f"{username} 解锁屏幕")

    result, msg = verify_unlock_screen_pwd(new_session, username, input_password)

    if not result:
        return BaseResponse(code=response_utils.permission_deny, msg=msg)

    logger.info(f"{username} 解锁屏幕成功")
    return BaseResponse(msg="解锁屏幕成功")


@user_router.post(path="/change_role", response_model=BaseResponse, summary="切换角色")
async def login(query: ChangeRoleReq, user: Any = Depends(get_current_user),
                new_session: Session = Depends(session_depend)):
    role_id_list = query.role_id_list
    user_id, username, raw_role_list = user["user_id"], user["username"], user["role"]
    logger.info(f"{username} 切换角色")

    if len(role_id_list) != 1:
        return BaseResponse(code=response_utils.params_error, msg="请选择一个角色")

    if raw_role_list == role_id_list:
        return BaseResponse(code=response_utils.params_error, msg="角色无更新")

    role_id = role_id_list[0]
    role_info = new_session.query(UserRole.role_id).filter(
        and_(UserRole.user_id == user_id, UserRole.role_id == role_id)).first()

    if not role_info:
        logger.error(f"用户 {user_id} 无 {role_id} 该角色")
        return BaseResponse(code=response_utils.params_error, msg="该用户无该角色，请清空浏览器缓存后重试")

    user_info = new_session.query(UserInfo.user_type, UserInfo.name, UserInfo.already_login, UserInfo.is_active,
                                  UserInfo.system_user_type).filter(UserInfo.user_id == user_id).first()
    user_type, name, already_login, is_active, system_user_type = user_info

    if not is_active:
        return BaseResponse(code=response_utils.permission_deny, msg="该用户已被禁用")

    data = generate_user_login_data(new_session, user_id, username, name, role_id_list, user_type, already_login,
                                    system_user_type)

    logger.info(f"{username} 切换角色成功")
    return BaseResponse(data=data)


# @user_router.post(path="/login", response_model=BaseResponse, summary="登录")
# async def login(query: UserBaseReq, new_session: Session = Depends(session_depend)):
#     username = query.username
#     logger.info(f"{username} 用户登录")
#     user_info = new_session.query(UserInfo.user_id, UserInfo.username, UserInfo.password).filter(
#         UserInfo.username == username).first()
#     # 判断是否已经注册
#     if not user_info:
#         return BaseResponse(code=response_utils.no_field, msg="账号或密码有误，请重新登录")
#
#     user_id = user_info.user_id
#     username = user_info.username
#     password = user_info.password
#     user_role_info = new_session.query(UserRole.role_id).filter(UserRole.user_id == user_id).all()
#     if not user_role_info:
#         role_id_list = ["6"]
#     else:
#         role_id_list = [i[0] for i in user_role_info]
#
#     # 校验密码，做哈希校验
#     private_password = password
#     real_password = decrypt(query.password)
#     verify_result = verify_hash_password(real_password, private_password)
#     if not verify_result:
#         logger.warning(f"{username} 账号或密码有误，请重新登录")
#         return BaseResponse(code=response_utils.verify_error, msg="账号或密码有误，请重新登录")
#
#     user_dict = {
#         "user_id": user_id,
#         "username": username,
#         "role": role_id_list
#     }
#
#     # 获取用户的按钮权限列表
#     if "1" in role_id_list:
#         role_id = 1
#     else:
#         role_id = role_id_list[0]
#
#     func_point_value = get_user_func_flag_value(user_id, role_id)
#     user_dict["func_point_value"] = func_point_value
#
#     # 创建 token
#     token, expires = create_access_token(user_dict)
#
#     data = {
#         "accessToken": token,
#         "expires": expires.strftime("%Y/%m/%d %H:%M:%S"),
#         "user_info": user_dict
#     }
#
#     logger.info(f"{username} 登录成功")
#     return BaseResponse(data=data)


@user_router.post(path="/get_user", response_model=BaseResponse, summary="获取用户信息列表")
async def get_user(query: GetUserReq, user: Any = Depends(get_current_user),
                   new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取用户信息列表")
    (current_page, page_size, username, name, role_id, province_code_list, city_code_list,
     district_code_list, work_unit, user_type, is_active, system_user_type, round_count_list, is_real_name, role_id_list, project_id, subject_id) = query.model_dump().values()
    limit = current_page - 1
    page_size = page_size
    offset = limit * page_size
    user_data = []

    condition = user_query_condition(username, name, role_id, province_code_list, city_code_list, district_code_list,
                                     work_unit, user_type, is_active, system_user_type, round_count_list, is_real_name, role_id_list, project_id, subject_id)

    user_info_alias = aliased(UserInfo, name="user_info_alias")
    user_info_u_alias = aliased(UserInfo, name="user_info_u_alias")
    city_info_alias = aliased(AdministrativeRegion, name="city_info_alias")
    district_info_alias = aliased(AdministrativeRegion, name="district_info_alias")

    total = new_session.query(UserInfo.user_id.distinct()) \
        .join(UserRole, UserRole.user_id == UserInfo.user_id) \
        .join(Role, Role.role_id == UserRole.role_id) \
        .outerjoin(user_info_alias, UserInfo.c_user_id == user_info_alias.user_id) \
        .outerjoin(AdministrativeRegion, AdministrativeRegion.region_code == UserInfo.province_code) \
        .outerjoin(city_info_alias, city_info_alias.region_code == UserInfo.city_code) \
        .outerjoin(district_info_alias, district_info_alias.region_code == UserInfo.district_code) \
        .outerjoin(UserDataPermission, UserDataPermission.user_id == UserInfo.user_id) \
        .where(condition).count()

    user_stmt = (select(UserInfo.user_id, UserInfo.username, func.group_concat(UserRole.role_id.distinct()), UserInfo.name, UserInfo.created_time,
                        UserInfo.updated_time, user_info_alias.username, func.group_concat(Role.role_name.distinct()), UserInfo.phone, UserInfo.id_card,
                        UserInfo.province_code, UserInfo.city_code, UserInfo.district_code, AdministrativeRegion.region_name,
                        city_info_alias.region_name, district_info_alias.region_name, UserInfo.work_unit, UserInfo.user_type, UserInfo.is_active,
                        UserInfo.already_login, UserInfo.system_user_type, func.group_concat(Project.project_id.distinct()), func.group_concat(Subject.subject_id.distinct()),
                        func.group_concat(Project.project_name.distinct()), func.group_concat(Subject.subject_name.distinct()), user_info_u_alias.username, UserInfo.round_count, func.group_concat(ExamQuestion.knowledge_show.distinct()))
                 .join(UserRole, UserRole.user_id == UserInfo.user_id)
                 .join(Role, Role.role_id == UserRole.role_id)
                 .outerjoin(user_info_alias, UserInfo.c_user_id == user_info_alias.user_id)
                 .outerjoin(user_info_u_alias, UserInfo.u_user_id == user_info_u_alias.user_id)
                 .outerjoin(AdministrativeRegion, AdministrativeRegion.region_code == UserInfo.province_code)
                 .outerjoin(city_info_alias, city_info_alias.region_code == UserInfo.city_code)
                 .outerjoin(district_info_alias, district_info_alias.region_code == UserInfo.district_code)
                 .outerjoin(UserDataPermission, UserDataPermission.user_id == UserInfo.user_id)
                 .outerjoin(Project, Project.project_id == UserDataPermission.project_id)
                 .outerjoin(Subject, Subject.subject_id == UserDataPermission.subject_id)
                 .outerjoin(HumanGroupMember, UserInfo.user_id == HumanGroupMember.user_id)
                 .outerjoin(HumanMarkGroup, HumanGroupMember.group_id == HumanMarkGroup.group_id and HumanMarkGroup.group_level==3)
                 .outerjoin(ExamQuestion, HumanMarkGroup.ques_code == ExamQuestion.ques_code and ExamQuestion.parent_ques_id==None)
                 .where(condition)
                 .group_by(UserInfo.user_id, UserInfo.username, UserInfo.name, UserInfo.created_time, UserInfo.updated_time,
                           user_info_alias.username, UserInfo.phone, UserInfo.id_card, UserInfo.province_code, UserInfo.city_code,
                           UserInfo.district_code, AdministrativeRegion.region_name, city_info_alias.region_name, district_info_alias.region_name,
                           UserInfo.work_unit, UserInfo.user_type, UserInfo.is_active, user_info_u_alias.username, UserInfo.round_count)
                 .order_by(UserInfo.created_time.desc(), UserInfo.user_id.desc())
                 .limit(page_size).offset(offset))
    try:
        result = new_session.execute(user_stmt)
        for row in result:
            updated_time_str = None
            u_user_name_str = None
            if row[5] is not None:  # updated_time不为null
                updated_time_str = row[5] and str(row[5]).replace("T", " ")
                u_user_name_str  = row[25]
            else:  # updated_time为null，使用created_time
                if row[4] is not None:
                    updated_time_str = row[4] and str(row[4]).replace("T", " ")
                    u_user_name_str = row[6]
            user_item = {
                "user_id": row[0],
                "username": row[1],
                "role_id": row[2] and row[2].split(","),
                "role_name": row[7] and row[7].split(","),
                "name": row[3],
                "created_time": row[4] and str(row[4]).replace("T", " "),
                "updated_time": updated_time_str,
                "c_user_name": row[6],
                "phone": row[8],
                "id_card": row[9],
                "province_code": row[10],
                "city_code": row[11],
                "district_code": row[12],
                "province_name": row[13],
                "city_name": row[14],
                "district_name": row[15],
                "work_unit": row[16],
                "user_type": row[17],
                "is_active": row[18],
                "is_used": row[19],
                "system_user_type": row[20],
                "project_id_list": row[21] and row[21].split(","),
                "subject_id_list": row[22] and row[22].split(","),
                "project_name_list": row[23] and row[23].split(","),
                "subject_name_list": row[24] and row[24].split(","),
                "u_user_name": u_user_name_str,
                "round_count": row[26],
                "knowledge_show": row[27] and row[27].split(",")
            }
            user_data.append(user_item)
    except Exception as e:
        logger.error(f"获取用户信息列表失败，{e}")
        logger.error(traceback.format_exc())
        return BaseResponse(code=response_utils.server_error, msg=f"获取用户信息列表失败")
    logger.info("获取用户信息列表成功")
    data = {
        "data": user_data,
        "total": total
    }
    return BaseResponse(msg="获取用户信息列表成功", data=data)



# 新增用户导出接口
@user_router.post(path="/export_get_user_list", response_model=BaseResponse, summary="导出用户信息列表")
async def export_get_user_list(query: GetUserReq, user: Any = Depends(get_current_user),
                   new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 导出用户信息列表")
    (current_page, page_size, username, name, role_id, province_code_list, city_code_list,
     district_code_list, work_unit, user_type, is_active, system_user_type, round_count_list, is_real_name, role_id_list, project_id, subject_id) = query.model_dump().values()
    # limit = current_page - 1
    # page_size = page_size
    # offset = limit * page_size
    excel_export_service = ExcelExportService()
    user_data = []

    condition = user_query_condition(username, name, role_id, province_code_list, city_code_list, district_code_list,
                                     work_unit, user_type, is_active, system_user_type, round_count_list, is_real_name, role_id_list, project_id, subject_id)

    user_info_alias = aliased(UserInfo, name="user_info_alias")
    user_info_u_alias = aliased(UserInfo, name="user_info_u_alias")
    city_info_alias = aliased(AdministrativeRegion, name="city_info_alias")
    district_info_alias = aliased(AdministrativeRegion, name="district_info_alias")

    # total = new_session.query(UserInfo.user_id.distinct()) \
    #     .join(UserRole, UserRole.user_id == UserInfo.user_id) \
    #     .join(Role, Role.role_id == UserRole.role_id) \
    #     .outerjoin(user_info_alias, UserInfo.c_user_id == user_info_alias.user_id) \
    #     .outerjoin(AdministrativeRegion, AdministrativeRegion.region_code == UserInfo.province_code) \
    #     .outerjoin(city_info_alias, city_info_alias.region_code == UserInfo.city_code) \
    #     .outerjoin(district_info_alias, district_info_alias.region_code == UserInfo.district_code) \
    #     .outerjoin(UserDataPermission, UserDataPermission.user_id == UserInfo.user_id) \
    #     .where(condition).count()

    user_stmt = (select(UserInfo.user_id, UserInfo.username, func.group_concat(UserRole.role_id.distinct()), UserInfo.name, UserInfo.created_time,
                        UserInfo.updated_time, user_info_alias.username, func.group_concat(Role.role_name.distinct()), UserInfo.phone, UserInfo.id_card,
                        UserInfo.province_code, UserInfo.city_code, UserInfo.district_code, AdministrativeRegion.region_name,
                        city_info_alias.region_name, district_info_alias.region_name, UserInfo.work_unit, UserInfo.user_type, UserInfo.is_active,
                        UserInfo.already_login, UserInfo.system_user_type, func.group_concat(Project.project_id.distinct()), func.group_concat(Subject.subject_id.distinct()),
                        func.group_concat(Project.project_name.distinct()), func.group_concat(Subject.subject_name.distinct()), user_info_u_alias.username, UserInfo.round_count, func.group_concat(ExamQuestion.knowledge_show.distinct()))
                 .join(UserRole, UserRole.user_id == UserInfo.user_id)
                 .join(Role, Role.role_id == UserRole.role_id)
                 .outerjoin(user_info_alias, UserInfo.c_user_id == user_info_alias.user_id)
                 .outerjoin(user_info_u_alias, UserInfo.u_user_id == user_info_u_alias.user_id)
                 .outerjoin(AdministrativeRegion, AdministrativeRegion.region_code == UserInfo.province_code)
                 .outerjoin(city_info_alias, city_info_alias.region_code == UserInfo.city_code)
                 .outerjoin(district_info_alias, district_info_alias.region_code == UserInfo.district_code)
                 .outerjoin(UserDataPermission, UserDataPermission.user_id == UserInfo.user_id)
                 .outerjoin(Project, Project.project_id == UserDataPermission.project_id)
                 .outerjoin(Subject, Subject.subject_id == UserDataPermission.subject_id)
                 .outerjoin(HumanGroupMember, UserInfo.user_id == HumanGroupMember.user_id)
                 .outerjoin(HumanMarkGroup, HumanGroupMember.group_id == HumanMarkGroup.group_id and HumanMarkGroup.group_level==3)
                 .outerjoin(ExamQuestion, HumanMarkGroup.ques_code == ExamQuestion.ques_code and ExamQuestion.parent_ques_id==None)
                 .where(condition)
                 .group_by(UserInfo.user_id, UserInfo.username, UserInfo.name, UserInfo.created_time, UserInfo.updated_time,
                           user_info_alias.username, UserInfo.phone, UserInfo.id_card, UserInfo.province_code, UserInfo.city_code,
                           UserInfo.district_code, AdministrativeRegion.region_name, city_info_alias.region_name, district_info_alias.region_name,
                           UserInfo.work_unit, UserInfo.user_type, UserInfo.is_active, user_info_u_alias.username, UserInfo.round_count)
                 .order_by(UserInfo.created_time.desc(), UserInfo.user_id.desc()))
                 # .limit(page_size).offset(offset))
    try:
        result = new_session.execute(user_stmt)
        for row in result:
            user_item = {
                # "用户id": row[0],
                "用户账号": row[1],
                # "角色id": row[2] and row[2].split(","),
                "所属角色": ','.join(row[7].split(',')) if row[7] else '',
                "用户名": row[3],
                # "创建时间": row[4] and str(row[4]).replace("T", " "),
                # "更新时间": row[5] and str(row[5]).replace("T", " "),
                # "创建人": row[6],
                "手机号码": row[8],
                "身份证号": row[9],
                # "省份编码": row[10],
                # "城市编码": row[11],
                # "地区编码": row[12],
                "省份": row[13],
                "城市": row[14],
                "地区": row[15],
                "所在单位": row[16],
                # "用户类型": row[17],
                "启用状态": "启用" if row[18] == 1 else "禁用",
                # "使用状态": row[19],
                # "系统与业务类型": row[20],
                # "所属资格id": row[21] and row[21].split(","),
                # "所属科目id": row[22] and row[22].split(","),
                # "所属资格": row[23] and row[23].split(","),
                "所属资格": ','.join(row[23].split(',')) if row[23] else '',
                "所属科目": ','.join(row[24].split(',')) if row[24] else '',
                # "更新人": row[25],
                "所属轮次": row[26],
                "所属试题": ','.join(row[27].split(',')) if row[27] else '',
            }
            user_data.append(user_item)
        if not user_data:
            return BaseResponse(code=response_utils.no_field, msg=f"导出用户信息列表为空")
        return await excel_export_service.export_to_excel_stream(
            data=user_data,
            file_name="用户信息列表.xlsx"
        )
    except Exception as e:
        logger.error(f"导出用户信息列表失败，{e}")
        logger.error(traceback.format_exc())
        return BaseResponse(code=response_utils.server_error, msg=f"导出用户信息列表失败")



@user_router.post(path="/first_change_password", response_model=BaseResponse, summary="首次修改用户信息")
async def first_change_password(query: FirstChangePassword, new_session: Session = Depends(session_depend)):

    user_id, new_password, user_phone_number, user_ID_number, user_real_name, _ = query.model_dump().values()  # 读取前端传入的信息

    # 查询用户
    user_info = new_session.query(UserInfo.user_id, UserInfo.password, UserInfo.already_login) \
        .filter(UserInfo.user_id == user_id).first()

    # 判断是否已经注册
    if not user_info:
        return BaseResponse(code=response_utils.no_field, msg="该用户不存在")

    user_id, origin_private_password, already_login = user_info
    
    de_new_password = decrypt(new_password)
    
    # # 验证新密码与原密码是否相同
    # # 使用密码验证函数来比较解密后的密码是否与原密码相同
    # if verify_hash_password(de_new_password, origin_private_password):
    #     return BaseResponse(code=response_utils.params_error, msg="新密码不能与原密码相同")
    
    private_password = get_hash_password(de_new_password)
    
    update_dict = {UserInfo.password: private_password, UserInfo.phone: user_phone_number,
                   UserInfo.id_card: user_ID_number, UserInfo.already_login: True, UserInfo.name: user_real_name}

    try:
        new_session.query(UserInfo).filter(UserInfo.user_id == user_id).update(update_dict)
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"修改失败: {e}")
        return BaseResponse(code=response_utils.server_error, msg="修改失败")
    logger.info("修改成功")
    return BaseResponse(msg="修改成功")


@user_router.post(path="/change_password", response_model=BaseResponse, summary="修改密码")
async def change_password(query: ChangePasswordReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    role_id_list = user['role']
    curr_user_id = user["user_id"]
    user_id, raw_password, new_password = query.model_dump().values()
    logger.info(f"{user['username']} 修改 id 为 {user_id} 的密码")

    # 查询用户
    user_info = new_session.query(UserInfo.user_id, UserInfo.password, UserInfo.already_login) \
        .filter(UserInfo.user_id == user_id).first()

    # 判断是否已经注册
    if not user_info:
        return BaseResponse(code=response_utils.no_field, msg="该用户不存在")

    user_id, private_password, already_login = user_info

    if curr_user_id == user_id:
        logger.info(f"{user['username']} 修改自己的密码")
    else:
        logger.info(f"{user['username']} 修改其他人的密码")
        if "1" not in role_id_list and "2" not in role_id_list:
            return BaseResponse(code=response_utils.permission_deny, msg="权限不足，无法修改密码")
    # print("new_password", new_password)
    de_new_password = decrypt(new_password)

    # 已经登陆过，需要校验原始密码
    if already_login:
        if not raw_password:
            return BaseResponse(code=response_utils.params_error, msg="请输入原始密码")

        de_raw_password = decrypt(raw_password)

        # 校验密码，做哈希校验
        verify_result = verify_hash_password(de_raw_password, private_password)
        if not verify_result:
            return BaseResponse(code=response_utils.verify_error, msg="原密码不正确")

        # 新密码哈希加密
        private_password = get_hash_password(de_new_password)
        update_dict = {UserInfo.password: private_password}
    else:
        # 未登陆过，直接进行新密码哈希加密
        private_password = get_hash_password(de_new_password)
        update_dict = {UserInfo.password: private_password, UserInfo.already_login: True}

    try:
        new_session.query(UserInfo).filter(UserInfo.user_id == user_id).update(update_dict)
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"修改密码失败: {e}")
        return BaseResponse(code=response_utils.server_error, msg="修改密码失败")
    logger.info("修改密码成功")
    return BaseResponse(msg="修改密码成功")


@user_router.post(path="/update_user", response_model=BaseResponse, summary="编辑用户信息")
async def update_user(query: UpdateUserReq, user: Any = Depends(get_current_user),
                      new_session: Session = Depends(session_depend)):
    user_id, name, new_role_id_list, phone, id_card, round_count, province_code, city_code, district_code, work_unit, user_type, project_id_list, subject_id_list = query.model_dump().values()
    curr_user_id = user["user_id"]
    role_id_list = user["role"]
    logger.info(f"{user['username']} 编辑用户信息，id 为 {user_id}")
    user_data = new_session.query(UserInfo.user_type, UserInfo.is_builtin, UserInfo.c_user_id,
                                  UserInfo.system_user_type).filter(UserInfo.user_id == user_id).first()
    if not user_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该用户")

    raw_user_type, is_builtin, c_user_id, system_user_type = user_data
    if is_builtin == 1:
        return BaseResponse(code=response_utils.permission_deny, msg="该用户为内置数据，无法编辑该用户")
    # 新增手机号重复校验逻辑
    if phone:
        # 检查手机号是否已存在（排除当前用户）
        existing_user = new_session.query(UserInfo.user_id).filter(
            UserInfo.phone == phone,
            UserInfo.user_id != user_id
        ).first()
        if existing_user:
            return BaseResponse(code=response_utils.fields_exist, msg="手机号已存在，请使用其他手机号")

    # 获取用户角色
    raw_role_id_list = get_user_role(new_session, user_id)

    used_user_count = new_session.query(ManualGroupUser.manual_group_user_id).filter(
        ManualGroupUser.user_id == user_id).count()
    if used_user_count > 0:
        if raw_user_type != user_type or set(raw_role_id_list) != set(new_role_id_list):
            return BaseResponse(code=response_utils.permission_deny,
                                msg="该用户已被使用，无法编辑该用户的角色和用户类型")

    if curr_user_id != user_id and curr_user_id != c_user_id:
        if "1" not in role_id_list and "2" not in role_id_list:
            return BaseResponse(code=response_utils.permission_deny, msg="权限不足，无法编辑该用户")

    try:
        new_session.query(UserInfo).filter(UserInfo.user_id == user_id).update({
            UserInfo.name: name,
            UserInfo.phone: phone,
            UserInfo.id_card: id_card,
            UserInfo.province_code: province_code,
            UserInfo.city_code: city_code,
            UserInfo.district_code: district_code,
            UserInfo.work_unit: work_unit,
            UserInfo.user_type: user_type,
            UserInfo.u_user_id: curr_user_id,
            UserInfo.round_count: round_count
        })

        insert_role_data = []
        for new_role_id in new_role_id_list:
            if new_role_id not in raw_role_id_list:
                new_role = UserRole(user_role_id=configs.snow_worker.get_id(), user_id=user_id, role_id=new_role_id,
                                    c_user_id=curr_user_id)
                insert_role_data.append(new_role)
        if insert_role_data:
            new_session.add_all(insert_role_data)

        delete_role_data = []
        for raw_role_id in raw_role_id_list:
            if raw_role_id not in new_role_id_list:
                delete_role_data.append(raw_role_id)
        new_session.query(UserRole).filter(
            and_(UserRole.user_id == user_id, UserRole.role_id.in_(delete_role_data))).delete()

        # if system_user_type and (project_id_list or subject_id_list):
        #     add_user_data_permission(new_session, user_id, project_id_list, subject_id_list)

        # 调整为使用 update_user_data_permission 方法
        if system_user_type and (project_id_list or subject_id_list):
            # 构造 path_list
            path_list = []
            for i in range(len(project_id_list)):
                path_list.append([project_id_list[i], subject_id_list[i]])
            update_user_data_permission(new_session, path_list, user_id, curr_user_id)

        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"编辑用户信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"编辑用户信息失败")
    logger.info("编辑用户信息成功")
    return BaseResponse(msg="编辑用户信息成功")


@user_router.post(path="/delete_user", response_model=BaseResponse, summary="删除用户信息")
async def delete_user(query: UserIdReq, user: Any = Depends(get_current_user),
                      new_session: Session = Depends(session_depend)):
    user_id = query.user_id
    logger.info(f"{user['username']} 删除用户信息，id 为 {user_id}")
    current_user_id = user.get("user_id")
    if current_user_id == user_id:
        return BaseResponse(code=response_utils.params_error, msg="不能删除自己")

    user_data = new_session.query(UserInfo.is_builtin, UserInfo.lock_state, UserInfo.already_login).filter(
        UserInfo.user_id == user_id).first()
    if not user_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该用户")

    is_builtin, lock_state, already_login = user_data
    if is_builtin == 1:
        return BaseResponse(code=response_utils.permission_deny, msg="该用户为内置数据，无法删除该用户")
    if lock_state == 2:
        return BaseResponse(code=response_utils.permission_deny, msg="该用户已被使用，无法删除该用户")
    if already_login:
        return BaseResponse(code=response_utils.permission_deny, msg="该用户已被使用，无法删除该用户")

    used_user = new_session.query(HumanGroupMember.member_id).filter(HumanGroupMember.user_id == user_id).first()
    if used_user:
        return BaseResponse(code=response_utils.permission_deny, msg="该用户已被使用，无法删除该用户")

    try:
        # 删除用户及其对应的数据权限
        new_session.query(UserInfo).filter(UserInfo.user_id == user_id).delete()
        new_session.query(UserDataPermission).filter(UserDataPermission.user_id == user_id).delete()
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除用户信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除用户信息失败")
    logger.info("删除用户信息成功")
    return BaseResponse(msg="删除用户信息成功")


@user_router.post(path="/reset_password", response_model=BaseResponse, summary="重置密码")
async def reset_password(query: UserIdReq, user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend)):
    role_id_list = user['role']
    curr_user_id = user["user_id"]
    user_id = query.user_id

    if curr_user_id == user_id:
        logger.info(f"{user['username']} 重置自己的密码")
    else:
        logger.info(f"{user['username']} 重置其他人的密码")
        if "1" not in role_id_list and "2" not in role_id_list:
            return BaseResponse(code=response_utils.permission_deny, msg="权限不足，无法重置密码")

    # 查询默认密码
    result, msg, de_default_password = get_default_password()
    if not result:
        return BaseResponse(code=response_utils.permission_deny, msg=msg)

    # 重置的密码哈希加密
    private_password = get_hash_password(de_default_password)
    try:
        new_session.query(UserInfo).filter(UserInfo.user_id == user_id).update({
            UserInfo.password: private_password,
            UserInfo.u_user_id: curr_user_id
        })
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"重置密码失败: {e}")
        return BaseResponse(code=response_utils.server_error, msg="重置密码失败")
    logger.info(f"重置密码成功，密码已重置为 {de_default_password}")
    return BaseResponse(msg=f"重置密码成功，密码已重置为 {de_default_password}")


@user_router.post(path="/update_user_state", response_model=BaseResponse, summary="修改用户状态")
async def update_user_state(query: UpdateUserStateReq, user: Any = Depends(get_current_user),
                            new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 修改用户状态")
    role_id_list = user['role']
    curr_user_id = user["user_id"]
    user_id, is_active = query.user_id, query.is_active

    if "1" not in role_id_list and "2" not in role_id_list:
        return BaseResponse(code=response_utils.permission_deny, msg="只有管理员才能修改用户状态")

    if curr_user_id == user_id:
        return BaseResponse(code=response_utils.permission_deny, msg="无法修改自己的状态")

    try:
        new_session.query(UserInfo).filter(UserInfo.user_id == user_id).update({
            UserInfo.is_active: is_active,
            UserInfo.u_user_id: curr_user_id
        })
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"修改用户状态失败: {e}")
        return BaseResponse(code=response_utils.server_error, msg="修改用户状态失败")
    logger.info(f"修改用户状态成功")
    return BaseResponse(msg=f"修改用户状态成功")


@user_router.get(path="/get_default_pwd", response_model=BaseResponse, summary="获取默认密码")
async def get_default_pwd(user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 获取默认密码")
    result, msg, de_default_password = get_default_password()
    if not result:
        return BaseResponse(code=response_utils.permission_deny, msg=msg)
    data = {"data": de_default_password}
    return BaseResponse(data=data)


@user_router.get(path="/get_region", response_model=BaseResponse, summary="获取行政区域信息")
async def get_region(user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 获取行政区域信息")
    region_path = os.path.join(configs.PROJECT_PATH, "server_static\\resource\\administrative_region.json")
    with open(region_path, "r", encoding="utf-8") as f:
        content = f.read()
    data = {
        "data": json.loads(content)
    }
    return BaseResponse(data=data)


@user_router.get(path="/save_region", response_model=BaseResponse, summary="插入行政区域信息")
async def save_region(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 插入行政区域信息")
    count = new_session.query(AdministrativeRegion.region_id).count()
    if count >= 3445:
        return BaseResponse(code=response_utils.fields_exist, msg="行政区域信息已存在，请勿重复导入")
    region_path = os.path.join(configs.PROJECT_PATH, "server_static\\resource\\administrative_region.json")
    with open(region_path, "r", encoding="utf-8") as f:
        content = f.read()
    data = json.loads(content)
    insert_data = []
    for prov in data:
        prov_item = AdministrativeRegion(region_id=configs.snow_worker.get_id(), region_level=1,
                                         parent_region_code=None, region_code=prov["code"],
                                         region_name=prov["name"])
        insert_data.append(prov_item)
        city_info = prov.get("children")
        if city_info:
            for city in city_info:
                cify_item = AdministrativeRegion(region_id=configs.snow_worker.get_id(), region_level=2,
                                                 parent_region_code=prov["code"], region_code=city["code"],
                                                 region_name=city["name"])
                insert_data.append(cify_item)

                district_info = city.get("children")
                if district_info:
                    for district in district_info:
                        district_item = AdministrativeRegion(region_id=configs.snow_worker.get_id(), region_level=3,
                                                             parent_region_code=city["code"],
                                                             region_code=district["code"], region_name=district["name"])
                        insert_data.append(district_item)
    new_session.add_all(insert_data)
    new_session.commit()
    return BaseResponse(msg="插入行政区域信息成功")
