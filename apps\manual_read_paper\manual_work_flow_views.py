from fastapi import APIRouter, Depends
from sqlalchemy import exists, select, and_, func
from settings import logger
from typing import Any

from sqlalchemy.orm import Session

from apps.manual_read_paper.services import parse_process_instance
from apps.models.models import WorkFlowMainProcess, WorkFlowMainProcessInstance, UserInfo
from apps.users.services import get_current_user
from apps.read_paper import CreateReadWorkFlowProcessReq, GetReadWorkFlowProcessReq, ReadWorkFlowProcessIdReq, \
    UpdateReadWorkFlowProcessReq
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from settings import configs

work_flow_router = APIRouter()


@work_flow_router.post(path="/create_read_work_flow_process", response_model=BaseResponse, summary="创建人工阅卷流程")
async def create_read_work_flow_process(query: CreateReadWorkFlowProcessReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建人工阅卷流程")

    curr_user_id = user.get("user_id")

    process_name, process_json = query.model_dump().values()

    is_exist = new_session.query(exists().where(WorkFlowMainProcess.process_name == process_name)).scalar()
    if is_exist:
        return BaseResponse(code=response_utils.fields_exist, msg=f"人工阅卷流程名 {process_name} 已存在")

    new_process_id = configs.snow_worker.get_id()
    try:
        new_process = WorkFlowMainProcess(process_id=new_process_id, process_name=process_name, process_json=process_json,
                                          c_user_id=curr_user_id)
        new_session.add(new_process)
    except Exception as e:
        logger.error(f"创建人工阅卷流程失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"创建人工阅卷流程失败")

    result, msg = parse_process_instance(new_session, new_process_id, process_json, curr_user_id)
    if not result:
        return BaseResponse(code=response_utils.server_error, msg="创建人工阅卷流程失败")
    new_session.commit()
    logger.info("创建人工阅卷流程成功")
    return BaseResponse(msg=f"创建人工阅卷流程成功")


@work_flow_router.post(path="/get_read_work_flow_process", response_model=BaseResponse, summary="获取人工阅卷流程列表")
async def create_manual_group(query: GetReadWorkFlowProcessReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取人工阅卷流程列表")
    page_size = query.page_size
    manual_group_data = []

    select_fields = [WorkFlowMainProcess.process_id, func.max(WorkFlowMainProcess.process_name),
                     func.max(WorkFlowMainProcess.lock_state),
                     func.max(WorkFlowMainProcess.created_time), func.max(WorkFlowMainProcess.updated_time),
                     func.max(UserInfo.username), func.group_concat(WorkFlowMainProcessInstance.instance_ele_type),
                     func.group_concat(func.ifnull(WorkFlowMainProcessInstance.instance_people_num, 0)),
                     func.max(WorkFlowMainProcessInstance.instance_target_percentage), func.max(UserInfo.name)]

    if page_size == -1:
        total = new_session.query(WorkFlowMainProcess.process_id).count()
        process_stmt = select(*select_fields) \
            .join(UserInfo, UserInfo.user_id == WorkFlowMainProcess.c_user_id) \
            .join(WorkFlowMainProcessInstance,
                  WorkFlowMainProcessInstance.parent_process_id == WorkFlowMainProcess.process_id) \
            .group_by(WorkFlowMainProcess.process_id)
    else:
        limit = query.current_page - 1
        offset = limit * query.page_size
        # 拼凑查询条件
        if query.process_name:
            condition = and_(WorkFlowMainProcess.process_name.ilike(f"%{query.process_name}%"))
        else:
            condition = True

        if query.c_name:
            user_condition = UserInfo.name.ilike(f"%{query.c_name}%")
        else:
            user_condition = True
        total = new_session.query(WorkFlowMainProcess.process_id.distinct()) \
            .join(UserInfo, UserInfo.user_id == WorkFlowMainProcess.c_user_id) \
            .join(WorkFlowMainProcessInstance,
                  WorkFlowMainProcessInstance.parent_process_id == WorkFlowMainProcess.process_id) \
            .where(and_(condition, user_condition)).count()
        process_stmt = select(*select_fields) \
            .join(UserInfo, WorkFlowMainProcess.c_user_id == UserInfo.user_id) \
            .join(WorkFlowMainProcessInstance,
                  WorkFlowMainProcessInstance.parent_process_id == WorkFlowMainProcess.process_id) \
            .where(and_(condition, user_condition)) \
            .group_by(WorkFlowMainProcess.process_id) \
            .order_by(WorkFlowMainProcess.created_time.desc(), WorkFlowMainProcess.process_id.desc()) \
            .limit(page_size).offset(offset)

    try:
        result = new_session.execute(process_stmt)
        for row in result:
            expert_num, arbitrator_num, quality_num, arbitrate_threshold = 0, 0, 0, None
            ele_type_list = row[6].split(",")
            instance_num_list = row[7].split(",")
            for index, value in enumerate(ele_type_list):
                # 获取专家个数
                if value == "bpmn:userTask":
                    expert_num += int(instance_num_list[index])
                # 获取仲裁个数
                elif value == "bpmn:arbitratorNode":
                    arbitrator_num += int(instance_num_list[index])
                # 检查是否有质检人员
                elif value == "bpmn:qualityNode":
                    quality_num += int(instance_num_list[index])

            process_item = {
                "process_id": row[0],
                "process_name": row[1],
                "lock_state": row[2],
                "created_time": row[3] and str(row[3]).replace("T", " "),
                "updated_time": row[4] and str(row[4]).replace("T", " "),
                "c_user_name": row[5],
                "has_quality": True if quality_num > 0 else False,
                "has_fetch_score": True if expert_num > 1 else False,
                "expert_num": expert_num,
                "arbitrator_num": arbitrator_num,
                "quality_num": quality_num,
                "arbitrate_threshold": row[8],
                "c_name": row[9]
            }
            manual_group_data.append(process_item)
    except Exception as e:
        logger.error(f"获取人工阅卷流程列表失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取人工阅卷流程列表失败")
    logger.info("获取人工阅卷流程列表成功")
    manual_group_data.sort(key=lambda x: x["process_id"], reverse=True)
    data = {
        "data": manual_group_data,
        "total": total
    }
    return BaseResponse(msg="获取人工阅卷流程列表成功", data=data)


@work_flow_router.post(path="/update_read_work_flow_process", response_model=BaseResponse, summary="编辑人工阅卷流程")
async def create_manual_group(query: UpdateReadWorkFlowProcessReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 编辑人工阅卷流程列表")
    curr_user_id = user.get("user_id")

    process_id, process_name, process_json = query.model_dump().values()

    process_info = new_session.query(WorkFlowMainProcess.process_name, WorkFlowMainProcess.lock_state, WorkFlowMainProcess.is_builtin).filter(WorkFlowMainProcess.process_id == process_id).first()
    raw_process_name, lock_state, is_builtin = process_info
    if is_builtin == 1:
        return BaseResponse(code=response_utils.permission_deny, msg="该阅卷模式为内置模式，不允许编辑")

    if lock_state == 2:
        return BaseResponse(code=response_utils.permission_deny, msg="该阅卷模式已被使用，不允许编辑")

    if process_name != raw_process_name:
        if new_session.query(WorkFlowMainProcess.process_id).filter(WorkFlowMainProcess.process_name == process_name).count() > 0:
            return BaseResponse(code=response_utils.params_error, msg="该阅卷模式名称已存在")

    new_session.query(WorkFlowMainProcessInstance).filter(
        WorkFlowMainProcessInstance.parent_process_id == process_id).delete()
    new_session.query(WorkFlowMainProcess).filter(WorkFlowMainProcess.process_id == process_id).update({
        WorkFlowMainProcess.process_name: process_name,
        WorkFlowMainProcess.process_json: process_json
    })
    result, msg = parse_process_instance(new_session, process_id, process_json, curr_user_id)
    if not result:
        return BaseResponse(code=response_utils.server_error, msg="编辑人工阅卷流程列表失败")
    new_session.commit()
    logger.info("编辑人工阅卷流程列表成功")
    return BaseResponse(msg=f"编辑人工阅卷流程列表成功")


@work_flow_router.get(path="/get_single_process", response_model=BaseResponse, summary="通过工作流id获取阅卷工作流信息")
async def get_ques_info(query: ReadWorkFlowProcessIdReq = Depends(), user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    process_id = query.process_id
    logger.info(f"{user['username']} 通过工作流id {process_id} 获取阅卷工作流信息")
    process_data = {}
    try:
        for row in new_session.query(WorkFlowMainProcess.process_id, WorkFlowMainProcess.process_json) \
                .filter(WorkFlowMainProcess.process_id.__eq__(process_id)):
            process_data = {
                "process_id": row[0],
                "process_json": row[1]
            }
    except Exception as e:
        logger.error(f"获取阅卷工作流信息列表失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取阅卷工作流信息失败")
    logger.info(f"通过工作流id {process_id} 获取阅卷工作流信息成功")
    # 按照阅卷工作流类型格式化数据
    data = {
        "data": process_data
    }
    return BaseResponse(msg=f"通过工作流id {process_id} 获取阅卷工作流信息成功", data=data)


@work_flow_router.post(path="/delete_read_work_flow_process", response_model=BaseResponse, summary="删除人工阅卷流程信息")
async def delete_read_work_flow_process(query: ReadWorkFlowProcessIdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    process_id = query.process_id
    logger.info(f"{user['username']} 删除人工阅卷流程信息，id 为 {process_id}")
    process_info = new_session.query(WorkFlowMainProcess.process_id, WorkFlowMainProcess.lock_state, WorkFlowMainProcess.is_builtin).filter(
        WorkFlowMainProcess.process_id == process_id).first()
    _, lock_state, is_builtin = process_info

    if is_builtin == 1:
        return BaseResponse(code=response_utils.permission_deny, msg="该阅卷模式为内置模式，不允许编辑")

    if lock_state == 2:
        return BaseResponse(code=response_utils.permission_deny, msg="该人工阅卷流程已被使用，不允许删除")

    try:
        new_session.query(WorkFlowMainProcess).filter(WorkFlowMainProcess.process_id == process_id).delete()
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除人工阅卷流程信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除人工阅卷流程信息失败")
    logger.info("删除人工阅卷流程信息成功")
    return BaseResponse(msg="删除人工阅卷流程信息成功")
