import io
import traceback

from fastapi import APIRouter, UploadFile, File, Form, Depends
from sqlalchemy import exists, select, and_, func
from sqlalchemy.orm import Session
from settings import logger
from typing import Optional, Any
import openpyxl

from apps.read_paper import GetPaperReq, UpdatePaperReq, DeletePaperReq, GetBusinessQuesTypeReq
from apps.models.models import ExamPaper, UserInfo, Project, Subject, MarkRule, ExamQuestion, QuesType, StuAnswer, PaperDetail, \
    BusinessQuesType
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from apps.read_paper.common_services import parse_paper_template, paper_query_condition, get_user_data_flag, \
    check_rule_ques_type_code, get_order_max_length, create_paper_detail
from apps.users.services import get_current_user
from utils.utils import round_half_up

paper_router = APIRouter()


@paper_router.post(path="/create_paper", response_model=BaseResponse, summary="创建试卷并导入试题")
async def create_paper_info(
        project_id: str = Form(..., title="项目id"),
        subject_id: str = Form(..., title="科目id"),
        mark_rule_id: str = Form(..., title="评分规则id"),
        remark: Optional[str] = Form(None, title="备注"),
        file: UploadFile = File(...),
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)):
    """
    1.校验模板数据
    2.生成试题
    3.生成试卷
    4.生成试卷详情
    """
    logger.info(f"{user['username']} 创建试卷")
    curr_user_id = user.get("user_id")

    if not file.filename.endswith(".xlsx"):
        return BaseResponse(code=response_utils.params_error, msg="请上传 .xlsx 的文件")

    content = await file.read()
    wb = openpyxl.load_workbook(filename=io.BytesIO(content), read_only=True)
    sheet = wb.worksheets[0]
    all_rows = list(sheet.iter_rows(values_only=True))
    if len(all_rows) == 0:
        return BaseResponse(code=response_utils.params_error, msg="导入内容不能为空")

    try:
        select_subject_name = new_session.query(Subject.subject_name).filter(Subject.subject_id == subject_id).scalar()

        # 获取试卷名称、科目名称、试卷的题型
        paper_name_list, subject_name_list, paper_ques_type_code = [], [], []

        # 获取每套试卷所有题号，以便获得最大的题号位数，将位数不够的试题序号在左边补零补全位数，解决排序紊乱问题
        ques_order_dict = {}
        # ques_order_dict = {
        #     paper_name: ["1", "2", "15"]
        # }

        # 获取组合题总分
        f_ques_score = {}
        # f_ques_score = {
        #     ques_code: score
        # }

        for i in all_rows[1:]:
            if not any(i):
                continue
            paper_name, ques_code, subject_name, ques_order, type_code, ques_score = i[0], str(i[1]), i[2], str(i[3]), \
                i[4], float(i[7]) if i[7] is not None else 0
            if select_subject_name != subject_name:
                return BaseResponse(code=response_utils.params_error,
                                    msg=f"导入的科目 {subject_name} 与所选科目 {select_subject_name} 不一致")

            if "（" in ques_order:
                ques_order_dict[paper_name] = []
                if ques_code in f_ques_score:
                    f_ques_score[ques_code] += ques_score
                else:
                    f_ques_score[ques_code] = ques_score

            if paper_name and paper_name not in paper_name_list:
                paper_name_list.append(paper_name)
                if "（" in ques_order:
                    ques_order_dict[paper_name] = []
                else:
                    ques_order_dict[paper_name] = [ques_order]
            else:
                if "（" not in ques_order:
                    ques_order_dict[paper_name].append(ques_order)

            if subject_name and subject_name not in subject_name_list:
                subject_name_list.append(subject_name)

            # 组合题（F）没有评分规则
            if type_code and type_code != "F" and type_code not in paper_ques_type_code:
                paper_ques_type_code.append(type_code)

        # 获得最长的题号位数
        max_order_length_dict = get_order_max_length(ques_order_dict)

        result, msg = check_rule_ques_type_code(new_session, mark_rule_id, paper_ques_type_code)
        if not result:
            return BaseResponse(code=response_utils.params_error, msg=msg)

        if not paper_name_list:
            return BaseResponse(code=response_utils.params_error, msg="试卷名不允许为空")

        if len(subject_name_list) > 1:
            return BaseResponse(code=response_utils.params_error, msg="只能同时导入同一科目的试卷")

        for paper_name in paper_name_list:
            paper_name_exist = new_session.query(exists().where(ExamPaper.paper_name == paper_name)).scalar()
            if paper_name_exist:
                return BaseResponse(code=response_utils.params_error, msg=f"试卷 {paper_name} 已存在，请勿重复导入")

        # 通过 ques_code 获取 ques_id
        ques_code_id_dict = {}
        all_ques_code_info = new_session.query(ExamQuestion.ques_code, ExamQuestion.ques_id,
                                               func.coalesce(ExamQuestion.small_ques_num, 0)).all()

        if all_ques_code_info:
            for ques_code, ques_id, small_ques_num in all_ques_code_info:
                if ques_code in ques_code_id_dict:
                    ques_code_id_dict[ques_code][small_ques_num] = ques_id
                else:
                    ques_code_id_dict[ques_code] = {small_ques_num: ques_id}

        logger.info("解析试卷模板...")
        flag, msg, insert_data, paper_score_dict, paper_code_dict, paper_detail_list = \
            parse_paper_template(all_rows, project_id, subject_id, ques_code_id_dict, max_order_length_dict,
                                 mark_rule_id, remark, f_ques_score, curr_user_id)
        if not flag:
            return BaseResponse(code=response_utils.params_error, msg=msg)
    except Exception as e:
        new_session.rollback()
        logger.error(f"系统错误，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg="系统错误")

    try:
        insert_detail_data_list = create_paper_detail(paper_detail_list)
        insert_data.extend(insert_detail_data_list)
        new_session.add_all(insert_data)

        # 将项目、科目和绑定的评分规则锁定
        new_session.query(Project).filter(Project.project_id == project_id).update({Project.lock_state: 2})
        new_session.query(Subject).filter(Subject.subject_id == subject_id).update({Subject.lock_state: 2})
        new_session.query(MarkRule).filter(MarkRule.rule_id == mark_rule_id).update({MarkRule.lock_state: 2})
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.error(f"保存试题信息失败，{e}")
        return BaseResponse(code=response_utils.params_error, msg=f"保存试题信息失败")
    logger.info("保存试题信息成功")
    return BaseResponse(msg="创建试卷信息成功")


@paper_router.post(path="/get_paper", response_model=BaseResponse, summary="获取试卷信息列表")
async def get_paper(query: GetPaperReq, user: Any = Depends(get_current_user),
                    new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取试卷信息列表")
    current_page, page_size, project_id, subject_id, paper_name, is_create_task, c_name = query.model_dump().values()

    paper_data = []
    limit = current_page - 1
    offset = limit * page_size

    if page_size == -1 and is_create_task:
        if not project_id or not subject_id:
            return BaseResponse(code=response_utils.params_error, msg="请选择项目和科目")

        paper_count = new_session.query(ExamPaper).filter(and_(ExamPaper.project_id == project_id, ExamPaper.subject_id == subject_id)).count()
        if paper_count == 0:
            # 抽参
            not_duplicate_code_list, not_duplicate_name_list, business_id_list = [], [], []
            ques_type_info = new_session.query(BusinessQuesType.business_ques_type_id, BusinessQuesType.ques_type_code,
                                               BusinessQuesType.ques_type_name, BusinessQuesType.parent_ques_type_id) \
                .filter(and_(BusinessQuesType.project_id == project_id, BusinessQuesType.subject_id == subject_id)).all()
            if ques_type_info:
                for business_id, ques_type_code, ques_type_name, parent_ques_type_id in ques_type_info:
                    if not parent_ques_type_id:
                        if ques_type_code == "F":
                            business_id_list.append(business_id)
                            if "F" not in not_duplicate_code_list:
                                not_duplicate_code_list.append("F")
                                not_duplicate_name_list.append("组合题")
                        else:
                            not_duplicate_code_list.append(ques_type_code)
                            not_duplicate_name_list.append(ques_type_name)
                paper_item = {
                    "ques_type_code_list": not_duplicate_code_list,
                    "ques_type_name_list": not_duplicate_name_list,
                    "business_id_list": business_id_list,
                }
                paper_data.append(paper_item)
            data = {"data": paper_data, "is_paper": False}
            return BaseResponse(msg="获取试卷信息列表成功", data=data)

        else:
            # 抽卷
            paper_info = new_session.query(ExamPaper.paper_id, ExamPaper.paper_code, ExamPaper.paper_name,
                                           func.group_concat(ExamQuestion.ques_type_code),
                                           func.group_concat(QuesType.ques_type_name),
                                           func.group_concat(func.coalesce(PaperDetail.parent_ques_id, 0))) \
                .outerjoin(PaperDetail, PaperDetail.paper_id == ExamPaper.paper_id) \
                .outerjoin(ExamQuestion, ExamQuestion.ques_id == PaperDetail.ques_id) \
                .join(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
                .filter(and_(ExamPaper.project_id == project_id, ExamPaper.subject_id == subject_id)) \
                .group_by(ExamPaper.paper_id, ExamPaper.paper_code, ExamPaper.paper_name).all()
            if not paper_info:
                data = {"data": paper_data, "is_paper": True}
                return BaseResponse(data=data)
            for paper in paper_info:
                ques_type_code_list = paper[3].split(",")
                ques_type_name_list = paper[4].split(",")
                parent_ques_id_list = paper[5].split(",")
                # 去重且要保证 code 和 name 一一对应
                not_duplicate_code_list = []
                not_duplicate_name_list = []
                for i, j, k in zip(ques_type_code_list, ques_type_name_list, parent_ques_id_list):
                    # 不列出组合题的小题试题类型
                    if not k or k == "0":
                        if i not in not_duplicate_code_list:
                            not_duplicate_code_list.append(i)
                            not_duplicate_name_list.append(j)

                paper_item = {
                    "paper_id": paper[0],
                    "paper_code": paper[1],
                    "paper_name": paper[2],
                    "ques_type_code_list": not_duplicate_code_list,
                    "ques_type_name_list": not_duplicate_name_list
                }
                paper_data.append(paper_item)
            data = {"data": paper_data, "is_paper": True}
            return BaseResponse(msg="获取试卷信息列表成功", data=data)

    # 拼凑查询条件
    project_condition, subject_condition = get_user_data_flag(new_session, user)
    condition = paper_query_condition(project_id, subject_id, paper_name, c_name)

    total = new_session.query(ExamPaper.paper_id) \
        .join(Project, ExamPaper.project_id == Project.project_id) \
        .join(Subject, ExamPaper.subject_id == Subject.subject_id) \
        .join(UserInfo, ExamPaper.c_user_id == UserInfo.user_id).where(
        and_(condition, project_condition, subject_condition)).count()

    paper_stmt = select(ExamPaper.paper_id, ExamPaper.paper_code, ExamPaper.paper_name, ExamPaper.total_score,
                        ExamPaper.remark, ExamPaper.mark_rule_id, ExamPaper.lock_state, ExamPaper.created_time,
                        ExamPaper.updated_time, Project.project_id, Project.project_name, Subject.subject_id,
                        Subject.subject_name, MarkRule.rule_name, UserInfo.username, UserInfo.name) \
        .join(Project, ExamPaper.project_id == Project.project_id) \
        .join(Subject, ExamPaper.subject_id == Subject.subject_id) \
        .join(UserInfo, ExamPaper.c_user_id == UserInfo.user_id) \
        .outerjoin(MarkRule, ExamPaper.mark_rule_id == MarkRule.rule_id) \
        .where(and_(condition, project_condition, subject_condition)) \
        .order_by(ExamPaper.created_time.desc(), ExamPaper.paper_id.desc())

    if page_size != -1:
        paper_stmt = paper_stmt.limit(page_size).offset(offset)
    try:
        result = new_session.execute(paper_stmt)
        for row in result:
            paper_id = row[0]
            # stu_answer_paper = new_session.query(StuAnswer.answer_id).filter(StuAnswer.paper_id == paper_id).first()
            # is_stu_answer_user = True if stu_answer_paper else False
            paper_item = {
                "paper_id": paper_id,
                "paper_code": row[1],
                "paper_name": row[2],
                "total_score": row[3],
                "remark": row[4],
                "mark_rule_id": row[5],
                "lock_state": row[6],
                "created_time": row[7] and str(row[7]).replace("T", " "),
                "updated_time": row[8] and str(row[8]).replace("T", " "),
                "project_id": row[9],
                "project_name": row[10],
                "subject_id": row[11],
                "subject_name": row[12],
                "mark_rule_name": row[13],
                "c_user_name": row[14],
                "c_name": row[15],
                # "is_stu_answer_user": user_paper_dict[paper_id]
                "is_stu_answer_user": True
            }
            paper_data.append(paper_item)
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.error(f"获取试卷信息列表失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取试卷信息列表失败")
    logger.info("获取试卷信息列表成功")
    data = {
        "data": paper_data,
        "total": total
    }
    return BaseResponse(msg="获取试卷信息列表成功", data=data)


@paper_router.post(path="/update_paper", response_model=BaseResponse, summary="编辑试卷信息")
async def update_paper(query: UpdatePaperReq, user: Any = Depends(get_current_user),
                       new_session: Session = Depends(session_depend)):
    paper_id, project_id, subject_id, paper_name, mark_rule_id, remark = query.model_dump().values()
    logger.info(f"{user['username']} 编辑试卷信息，id 为 {paper_id}")
    paper_data = new_session.query(ExamPaper.project_id, ExamPaper.subject_id, ExamPaper.paper_name,
                                   ExamPaper.mark_rule_id, ExamPaper.lock_state).filter(
        ExamPaper.paper_id == paper_id).first()
    if not paper_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该试卷")

    raw_project_id, raw_subject_id, raw_paper_name, raw_mark_rule_id, lock_state = paper_data
    try:
        if lock_state == 2:
            if project_id != raw_project_id or raw_subject_id != subject_id or raw_paper_name != paper_name:
                return BaseResponse(code=response_utils.params_error, msg="该试卷已使用，无法修改项目、科目和试卷名")
            ques_type_info = new_session.query(ExamQuestion.ques_type_code.distinct()).filter(
                and_(ExamQuestion.paper_id == paper_id, ExamQuestion.ques_type_code != "F")).all()
            paper_ques_type_code = [i[0] for i in ques_type_info]
            result, msg = check_rule_ques_type_code(new_session, mark_rule_id, paper_ques_type_code)
            if not result:
                return BaseResponse(code=response_utils.params_error, msg=msg)
            new_session.query(ExamPaper).filter(ExamPaper.paper_id == paper_id).update({
                ExamPaper.mark_rule_id: mark_rule_id,
                ExamPaper.remark: remark,
                ExamPaper.u_user_id: user["user_id"]
            })
        else:
            new_session.query(ExamPaper).filter(ExamPaper.paper_id == paper_id).update({
                ExamPaper.paper_name: paper_name,
                ExamPaper.project_id: project_id,
                ExamPaper.subject_id: subject_id,
                ExamPaper.mark_rule_id: mark_rule_id,
                ExamPaper.remark: remark,
                ExamPaper.u_user_id: user["user_id"]
            })
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.error(f"编辑试卷信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"编辑试卷信息失败")
    logger.info("编辑试卷信息成功")
    return BaseResponse(msg="编辑试卷信息成功")


@paper_router.post(path="/delete_paper", response_model=BaseResponse, summary="删除试卷信息")
async def delete_paper(query: DeletePaperReq, user: Any = Depends(get_current_user),
                       new_session: Session = Depends(session_depend)):
    paper_id = query.paper_id
    logger.info(f"{user['username']} 删除试卷信息，id 为 {paper_id}")
    paper_data = new_session.query(ExamPaper.lock_state).filter(ExamPaper.paper_id == paper_id).first()
    if not paper_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该试卷")

    if new_session.query(StuAnswer.answer_id).filter(StuAnswer.paper_id == paper_id).first():
        return BaseResponse(code=response_utils.permission_deny, msg="该试卷已绑定作答信息，无法删除")

    if paper_data.lock_state == 2:
        return BaseResponse(code=response_utils.permission_deny, msg="该试卷已被使用，无法删除")

    try:
        new_session.query(ExamPaper).filter(ExamPaper.paper_id == paper_id).delete()
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.error(f"删除试卷信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除试卷信息失败")
    logger.info("删除试卷信息成功")
    return BaseResponse(msg="删除试卷信息成功")


@paper_router.post(path="/get_business_ques", response_model=BaseResponse, summary="获取抽参业务题型名称成功")
async def get_f_business_ques(query: GetBusinessQuesTypeReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取抽参业务题型名称成功")
    project_id, subject_id, business_id_list = query.model_dump().values()

    row_business_id_list, business_name_list = [], []

    if business_id_list:
        condition = and_(BusinessQuesType.project_id == project_id, BusinessQuesType.subject_id == subject_id,
                         BusinessQuesType.parent_ques_type_id.in_(business_id_list))
    else:
        condition = and_(BusinessQuesType.project_id == project_id, BusinessQuesType.subject_id == subject_id,
                         BusinessQuesType.ques_type_name != "F")

    business_info = new_session.query(BusinessQuesType.business_ques_type_id, BusinessQuesType.ques_type_name) \
        .filter(condition).all()
    if business_info:
        for business_id, ques_type_name in business_info:
            row_business_id_list.append(business_id)
            business_name_list.append(ques_type_name)

    data = {
        "business_id_list": row_business_id_list,
        "business_name_list": business_name_list
    }
    return BaseResponse(data=data, msg="获取抽参业务题型名称成功")
