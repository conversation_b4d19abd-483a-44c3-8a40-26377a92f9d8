# import datetime
# from fastapi import APIRouter, Depends
# from apps.base.global_cache import get_redis_ques_info_dict
# from settings import logger
# from sqlalchemy import select, exists, and_, func
# from sqlalchemy.orm import Session
# from typing import Any
# from pathlib import Path
# from fastapi.responses import FileResponse

# from apps.grade_manage.models import HumanStudentSubjectGrade
# from apps.users.services import get_current_user
# from apps.base.schemas import BaseResponse
# from apps.human_repeat_mark.schemas import GetStuReq, CreateRepeatMark
# from apps.human_repeat_mark.models import HumanRepeatTaskRound, HumanRepeatTask
# from apps.models.models import TaskExecuteRecord, StuAnswer, ExamStudent, ExamQuestion, BusinessQuesType, Subject, UserInfo, ExamPaper, PaperDetail, QuesType, Project
# from helper import response_utils
# from factory_apps import session_depend
# from settings import configs
# from apps.human_repeat_mark.models import HumanRepeatRoundDetail

# repeat_router = APIRouter()


# @repeat_router.post(path="/get_stu_list", response_model=BaseResponse, summary="获取考生列表")
# async def get_stu_list(query: GetStuReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
#     logger.info(f"{user['username']} 获取考生作答列表")
#     ids_list, score_list = query.model_dump().values()
#     stu_ids = []

#     for ids in ids_list:
#         exam_stus = []
#         for score in score_list:
#             # 1 根据条件查询考生
#             exam_stu_sql = select(
#                 HumanStudentSubjectGrade.stu_secret_num,
#                 HumanStudentSubjectGrade.student_subject_grade_id,
#                 HumanStudentSubjectGrade.project_id,
#                 HumanStudentSubjectGrade.subject_id,
#                 HumanStudentSubjectGrade.exam_session,
#             ).where(
#                 and_(
#                     (HumanStudentSubjectGrade.subject_id == ids[1]),
#                     (HumanStudentSubjectGrade.exam_session == ids[2]),
#                     (HumanStudentSubjectGrade.score >= score[0]),
#                     (HumanStudentSubjectGrade.score <= score[1]),
#                 )
#             )
#             exam_stu = new_session.execute(exam_stu_sql).all()
#             exam_stu = [dict(row._mapping) for row in exam_stu]
#             exam_stus.extend(exam_stu)
#         stu_ids.append({"condition": ids, "exam_stus": exam_stus})

#     projects = new_session.query(Project.project_id, Project.project_name).all()
#     subjects = new_session.query(Subject.subject_id, Subject.subject_name).all()

#     stus_data = []
#     for stu_id in stu_ids:
#         for id in stu_id["exam_stus"]:
#             p_name = next(p for p in projects if p.project_id == stu_id["condition"][0])[1]
#             s_name = next(p for p in subjects if p.subject_id == stu_id["condition"][1])[1]
#             stus_data.append({"stu_id": id["stu_secret_num"], "project_name": p_name, "subject_name": s_name, "session": stu_id["condition"][2]})
#     data = {"stus_data": stus_data, "stu_ids": stu_ids}
#     return BaseResponse(msg="获取考生作答列表", data=data)


# @repeat_router.post(path="/create_repeat_mark", response_model=BaseResponse, summary="创建复评")
# async def create_sample(query: CreateRepeatMark, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
#     logger.info(f"{user['username']} 创建复评")
#     c_user_id = user.get("user_id")
#     stu_list = query.stu_list

#     try:
#         repeat_task_round = new_session.query(HumanRepeatTaskRound).first()
#         if repeat_task_round is None:
#             task_id = configs.snow_worker.get_id()
#             repeat_task_round = HumanRepeatTaskRound(
#                 repeat_task_round_id=task_id,
#                 task_name="复评任务轮次",
#                 round_count=1,  # 复评通常为第一轮
#                 score_type=0,  # 单轮次
#                 select_round_count=1,
#                 select_score_type=0,  # 最高分
#                 c_user_id=c_user_id,
#                 u_user_id=c_user_id,
#             )
#             new_session.add(repeat_task_round)

#         # 创建 HumanRepeatTask
#         task_round_id = repeat_task_round.repeat_task_round_id
#         for stu in stu_list:
#             # stu_ids.append({"condition": ids, "exam_stus": exam_stus})
#             condition = stu["condition"]
#             exam_stus = stu["exam_stus"]
#             repeat_task_round = HumanRepeatTask(
#                 repeat_task_id=configs.snow_worker.get_id(),
#                 repeat_task_round_id=task_round_id,
#                 project_id=condition[0],
#                 subject_id=condition[1],
#                 exam_session=condition[2],
#                 repeat_task_count=0,
#                 c_user_id=c_user_id,
#                 u_user_id=c_user_id,
#             )
#             new_session.add(repeat_task_round)
#             for exam_stu in exam_stus:
#                 # 创建 HumanRepeatRoundDetail
#                 detail = HumanRepeatRoundDetail(
#                     repeat_round_detail_id=configs.snow_worker.get_id(),
#                     round_id=repeat_task_round.repeat_task_round_id,
#                     stu_secret_num=exam_stu["stu_secret_num"],
#                     c_user_id=c_user_id,
#                     u_user_id=c_user_id,
#                 )
#                 new_session.add(detail)
#         new_session.commit()
#         return BaseResponse(msg=f"创建复评成功", data={"id": ""})
#     except Exception as e:
#         logger.error(f"创建复评任务失败，{e}")
#         new_session.rollback()
#         return BaseResponse(code=response_utils.server_error, msg="创建复评任务失败")


# # @repeat_router.post(path="/delete_repeat_mark", response_model=BaseResponse, summary="删除复评")
# # async def delete_repeat_mark(query: GetPrepareStuAnswerBySampleIdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
# #     logger.info(f"{user['username']} 删除复评")
# #     try:
# #         # 根据sample_id获取复评任务轮次ID
# #         # 首先查询HumanRepeatTask表，获取对应的repeat_task_round_id
# #         task_query = select(HumanRepeatTask.repeat_task_round_id).where(HumanRepeatTask.repeat_task_id == query.sample_id)
# #         result = new_session.execute(task_query).scalars().first()
        
# #         if not result:
# #             return BaseResponse(code=response_utils.server_error, msg="未找到对应的复评任务")
        
# #         repeat_task_round_id = result
        
# #         # 删除复评轮次详情
# #         detail_stmt = select(HumanRepeatRoundDetail).where(HumanRepeatRoundDetail.round_id == repeat_task_round_id)
# #         details = new_session.execute(detail_stmt).scalars().all()
# #         for d in details:
# #             new_session.delete(d)
            
# #         # 删除复评任务
# #         task_stmt = select(HumanRepeatTask).where(HumanRepeatTask.repeat_task_round_id == repeat_task_round_id)
# #         tasks = new_session.execute(task_stmt).scalars().all()
# #         for t in tasks:
# #             new_session.delete(t)
            
# #         # 删除复评任务轮次
# #         round_stmt = select(HumanRepeatTaskRound).where(HumanRepeatTaskRound.repeat_task_round_id == repeat_task_round_id)
# #         rounds = new_session.execute(round_stmt).scalars().all()
# #         for r in rounds:
# #             new_session.delete(r)
            
# #         new_session.commit()
# #         return BaseResponse(msg="删除复评成功")
# #     except Exception as e:
# #         logger.error(f"删除复评失败，{e}")
# #         new_session.rollback()
# #         return BaseResponse(code=response_utils.server_error, msg="删除复评失败")
