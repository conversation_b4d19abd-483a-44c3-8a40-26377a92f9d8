from typing import Optional, Literal, List

from pydantic import BaseModel, Field

from apps.base.schemas import PaginationModel


class GetHumanGroupReq(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    round_id: Optional[str] = Field(None, description="轮次id")
    task_id: Optional[str] = Field(None, description="题组/任务id")


class CreateHumanGroupReq(BaseModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    group_level: Literal[1, 2, 3] = Field(..., description="小组层级")
    group_code: str = Field(..., description="小组编号")
    group_name: str = Field(..., description="小组名称")
    parent_group_id: Optional[str] = Field(None, description="题组级和评卷小组关联的父级id")
    paper_id: Optional[str] = Field(None, description="题组级关联的试卷id")
    ques_code: Optional[str] = Field(None, description="题组级关联的试题编号")
    ques_order: Optional[str] = Field(None, description="题组级关联的试题序号")


class GetParentHumanGroupReq(BaseModel):
    group_level: Literal[2, 3] = Field(..., description="小组层级")


class GetRecommendGroupCodeReq(BaseModel):
    parent_group_id: str = Field(..., description="父小组id")
    parent_group_code: str = Field(..., description="父小组编号")


class GetGroupUnionQuesReq(BaseModel):
    parent_group_id: str = Field(..., description="父小组id")
    exam_mode: Optional[int] = Field(None, description="考试模式")    # 改成非必项


class FilterHumanGroupMember(BaseModel):
    parent_group_id: Optional[str] = Field(None, description="父小组id")
    group_level: Literal[1, 2, 3] = Field(..., description="小组层级")
    team_leader: Optional[bool] = Field(False, description="是否为小组组长，group_level 为 3 时必填")


class SetHumanGroupMemberReq(BaseModel):
    group_id_list: list = Field(..., description="小组id列表")
    user_id_list: List[str] = Field([], description="评阅小组组员 id 列表")
    leader_user_id_list: List[str] = Field([], description="组长用户 id 列表")


class GetHumanGroupMemberReq(BaseModel):
    group_id: str = Field(..., description="小组id列表")


class BaseHumanGroupReq(BaseModel):
    group_id: str = Field(..., description="小组id")


class UpdateHumanGroupReq(BaseModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    group_level: Literal[1, 2, 3] = Field(..., description="小组层级")
    group_id: str = Field(..., description="小组id")
    group_code: str = Field(..., description="小组编号")
    group_name: str = Field(..., description="小组名称")
    parent_group_id: Optional[str] = Field(None, description="题组级和评卷小组关联的父级id")
    paper_id: Optional[str] = Field(None, description="题组级关联的试卷id")
    ques_code: Optional[str] = Field(None, description="题组级关联的试题编号")
    ques_order: Optional[str] = Field(None, description="题组级关联的试题序号")
    user_id_list: List[str] = Field([], description="用户 id 列表")
    leader_user_id_list: List[str] = Field([], description="组长用户 id 列表")


class IsActiveHumanGroupReq(BaseModel):
    group_id: str = Field(..., description="小组id")
    is_used: Literal[0, 1] = Field(..., description="使用状态，0 表示不使用，1 表示使用")


class GetHumanSmallGroupReq(BaseModel):
    project_id: str = Field(..., description="项目id")
    subject_id: str = Field(..., description="科目id")
    ques_code: str = Field(..., description="试题编号")
