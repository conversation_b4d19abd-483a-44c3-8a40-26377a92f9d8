import copy
import traceback
from typing import Optional

from sqlalchemy import func, and_

from apps.grade_manage.models import HumanStudentSubjectGrade, HumanStudentSubjectGradeDetail
from settings import logger

from apps.base.services import request_api
from apps.models.models import StuAnswer, ExamPaper, StuTotalGrade, Project, Subject, ExamStudent, SameStuAnswerGroup, StuTotalGradeDetail, ManualDistributeAnswer, CreateGradeRecord
from apps.read_paper.ai_services import judge_mark_result
from apps.read_paper.common_services import format_info_by_ques_type
from settings import configs
from utils.utils import round_half_up
from factory_apps import session_depend
from apps.human_repeat_mark.models import HumanRepeatTask, HumanRepeatRoundDetail


def get_paper_ques_info(paper_code, user, format_by_ques_type, is_get_all_info):
    """
    获取试卷的试题信息
    """
    data = {"paper_code": paper_code, "format_by_ques_type": format_by_ques_type, "is_get_all_info": is_get_all_info}
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/ques/get_ques"
    res, msg = request_api(url, "POST", data, token, "试卷的试题信息")
    if res == 0:
        return False, {}
    paper_data = res["data"]
    # 转化为以 ques_id 为键的字典形式
    paper_data_dict = {}
    for ques_item in paper_data:
        paper_data_dict[ques_item["ques_id"]] = ques_item
    return True, paper_data_dict


def instantiation_stu_grade(grade_type, grade_id, allow_exam_num, project_id, subject_id, paper_code, round_ai_grade, c_user_id):
    """
    实例化学生成绩
    """
    if grade_type == 1:
        insert_grade_data = StuTotalGrade(
            grade_id=grade_id, allow_exam_num=allow_exam_num, project_id=project_id, subject_id=subject_id, paper_code=paper_code, ai_grade=round_ai_grade, c_user_id=c_user_id
        )
    else:
        insert_grade_data = StuTotalGrade(
            grade_id=grade_id, allow_exam_num=allow_exam_num, project_id=project_id, subject_id=subject_id, paper_code=paper_code, manual_grade=round_ai_grade, c_user_id=c_user_id
        )
    return insert_grade_data


def update_stu_grade(new_session, grade_type, grade_id, round_grade, c_user_id):
    """
    更新学生成绩
    """
    try:
        if grade_type == 1:
            new_session.query(StuTotalGrade).filter(StuTotalGrade.grade_id == grade_id).update({StuTotalGrade.ai_grade: round_grade, StuTotalGrade.u_user_id: c_user_id})
        else:
            new_session.query(StuTotalGrade).filter(StuTotalGrade.grade_id == grade_id).update({StuTotalGrade.manual_grade: round_grade, StuTotalGrade.u_user_id: c_user_id})
        new_session.query(StuTotalGradeDetail).filter(and_(StuTotalGradeDetail.grade_id == grade_id, StuTotalGradeDetail.grade_type == grade_type)).delete()
        return True
    except Exception as e:
        logger.info(f"更新学生成绩失败：{e}")
        traceback.print_exc()
        return False


def create_ai_manual_grade_detail(new_session, allow_exam_num, stu_secret_num, f_ques_data_dict, ai_ques_data_dict, manual_ques_data_dict, manual_grade_dict, paper_code):
    """
    创建 AI 和人工评分混合成绩详情
    """
    # AI 评分
    ai_ques_score_info = (
        new_session.query(SameStuAnswerGroup.ques_id, SameStuAnswerGroup.stu_score, SameStuAnswerGroup.stu_answer)
        .join(StuAnswer, StuAnswer.same_answer_group_id == SameStuAnswerGroup.same_answer_group_id)
        .filter(and_(StuAnswer.allow_exam_num == allow_exam_num, StuAnswer.paper_code == paper_code))
        .all()
    )

    ai_ques_id_score_dict = {}
    for ques_id, stu_score, stu_answer in ai_ques_score_info:
        ai_ques_id_score_dict[ques_id] = {"stu_score": stu_score, "stu_answer": stu_answer.split(configs.NEW_SPLIT_FLAG) if stu_answer is not None else []}
    # print("ai_ques_id_score_dict", ai_ques_id_score_dict)
    for ques_id, ques in ai_ques_data_dict.items():
        ques["stu_score"] = ai_ques_id_score_dict.get(ques_id, {}).get("stu_score", None)
        ques["ques_score"] = ques["total_score"]
        ques["mark_result"] = judge_mark_result(ques["stu_score"], ques["ques_score"])
        ques["stu_answer"] = ai_ques_id_score_dict.get(ques_id, {}).get("stu_answer", None)

    # 人工主观题评分
    manual_ques_id_score_dict = {}
    stu_manual_grade_info = manual_grade_dict.get(stu_secret_num, {})
    for ques_id, ques_info in stu_manual_grade_info.items():
        manual_ques_id_score_dict[ques_id] = ques_info
    for ques_id, ques in manual_ques_data_dict.items():
        ques["stu_score"] = manual_ques_id_score_dict.get(ques_id).get("stu_score") if manual_ques_id_score_dict.get(ques_id) else None
        ques["ques_score"] = ques["total_score"]
        ques["mark_result"] = judge_mark_result(ques["stu_score"], ques["ques_score"])
        if manual_ques_id_score_dict.get(ques_id):
            ques["stu_answer"] = manual_ques_id_score_dict.get(ques_id).get("stu_answer")
        else:
            stu_answer_info = (
                new_session.query(func.max(SameStuAnswerGroup.stu_answer))
                .join(StuAnswer, StuAnswer.same_answer_group_id == SameStuAnswerGroup.same_answer_group_id)
                .filter(SameStuAnswerGroup.ques_id == ques_id, StuAnswer.allow_exam_num == allow_exam_num)
                .scalar()
            )
            ques["stu_answer"] = stu_answer_info.split(configs.NEW_SPLIT_FLAG) if stu_answer_info else []

    merge_grade_detail = {**ai_ques_data_dict, **manual_ques_data_dict, **f_ques_data_dict}

    merge_grade_detail_list = list(merge_grade_detail.values())
    sorted_merge_grade_detail = sorted(merge_grade_detail_list, key=lambda x: x["ques_order"])
    format_ques_data = format_info_by_ques_type(sorted_merge_grade_detail)
    statistics_data, total_grade = statistics_grade_data(format_ques_data)
    return True, statistics_data, total_grade


def get_all_same_group_score(new_session, ai_ques_data_dict, paper_code):
    stu_ai_score_dict = {}
    if ai_ques_data_dict:
        stu_ai_score = (
            new_session.query(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.ques_id, func.coalesce(SameStuAnswerGroup.stu_score, 0))
            .filter(SameStuAnswerGroup.paper_code == paper_code)
            .all()
        )
        if stu_ai_score:
            for same_answer_group_id, ques_id, stu_score in stu_ai_score:
                if same_answer_group_id not in stu_ai_score_dict:
                    stu_ai_score_dict[same_answer_group_id] = {ques_id: stu_score}
                else:
                    stu_ai_score_dict[same_answer_group_id][ques_id] = stu_score
    return stu_ai_score_dict


def get_all_stu_manual_grade(new_session, paper_code, manual_ques_id_list):
    """
    获取所有考生的人工阅卷成绩
    """
    all_manual_grade_dict = {}
    # all_manual_grade_dict = {
    #     stu_secret_num: {
    #         ques_id : {
    #             "final_mark_score": final_mark_score,
    #             "stu_answer": stu_answer
    #         }
    #     }
    # }
    condition = and_(ManualDistributeAnswer.paper_code == paper_code, ManualDistributeAnswer.ques_id.in_(manual_ques_id_list))
    manual_grade_info = new_session.query(ManualDistributeAnswer.stu_secret_num, ManualDistributeAnswer.ques_id, ManualDistributeAnswer.final_mark_score, ManualDistributeAnswer.stu_answer).filter(
        condition
    )
    if manual_grade_info:
        for stu_secret_num, ques_id, final_mark_score, stu_answer in manual_grade_info:
            if stu_secret_num in all_manual_grade_dict:
                if ques_id not in all_manual_grade_dict[stu_secret_num]:
                    all_manual_grade_dict[stu_secret_num][ques_id] = {"final_mark_score": final_mark_score, "stu_answer": stu_answer}
                else:
                    logger.warning(f"{stu_secret_num}, {ques_id} 重复")
            else:
                ques_item = {ques_id: {"final_mark_score": final_mark_score, "stu_answer": stu_answer}}
                all_manual_grade_dict[stu_secret_num] = ques_item
    return all_manual_grade_dict


def create_ai_manual_grade(
    new_session,
    stu_info,
    project_id,
    subject_id,
    paper_code,
    c_user_id,
    grade_type,
    f_ques_data_dict,
    ai_ques_data_dict,
    manual_ques_data_dict,
    history_grade_data,
    record_id,
    total_count,
    finish_count,
):
    """
    生成成绩
    """
    insert_grade_data_list = []
    insert_grade_detail_list = []
    # manual_sum_grade_dict, stu_manual_score_dict = {}, {}
    manual_ques_id_list = list(manual_ques_data_dict.keys())
    # condition = and_(ManualDistributeAnswer.paper_code == paper_code,
    #                  ManualDistributeAnswer.ques_id.in_(manual_ques_id_list))
    # manual_grade_info = new_session.query(ManualDistributeAnswer.stu_secret_num,
    #                                       func.coalesce(func.sum(ManualDistributeAnswer.final_mark_score), 0)) \
    #     .filter(condition) \
    #     .group_by(ManualDistributeAnswer.stu_secret_num).all()
    #
    # for stu_secret_num, final_total_score in manual_grade_info:
    #     manual_sum_grade_dict[stu_secret_num] = final_total_score

    stu_manual_score_dict = get_all_stu_manual_grade(new_session, paper_code, manual_ques_id_list)

    # stu_ai_score_dict = get_all_same_group_score(new_session, ai_ques_data_dict, paper_code)

    stu_count = len(stu_info)
    update_grade_count = 0

    for allow_exam_num, same_answer_group_id_str, stu_secret_num in stu_info:
        # same_answer_group_id_list = list(set(same_answer_group_id_str.split(",")))
        # ques_id_list = list(ai_ques_data_dict.keys())
        # ai_grade = 0
        # for same_answer_group_id in same_answer_group_id_list:
        #     for ques_id in ques_id_list:
        #         ai_grade += stu_ai_score_dict.get(same_answer_group_id, {}).get(ques_id, 0)
        # round_ai_grade = ai_grade if ai_grade is not None else 0
        # manual_grade = manual_sum_grade_dict.get(stu_secret_num, 0)
        # merge_grade = round_half_up((round_ai_grade + manual_grade), 1)

        # 生成成绩详情
        # 人工主观题评分
        manual_grade_dict = {}
        if manual_ques_data_dict:
            manual_grade_dict = stu_manual_score_dict.get(stu_secret_num, {})

        result, grade_detail, total_grade = create_ai_manual_grade_detail(
            new_session, allow_exam_num, stu_secret_num, f_ques_data_dict, ai_ques_data_dict, manual_ques_data_dict, manual_grade_dict, paper_code
        )
        merge_grade = round_half_up(total_grade, 1)
        is_exist, grade_id = check_grade_exist(history_grade_data, allow_exam_num, paper_code)

        if is_exist:
            update_grade_count += 1
            result = update_stu_grade(new_session, grade_type, grade_id, merge_grade, c_user_id)
            if not result:
                logger.error(f"更新 {grade_id} 学生成绩失败，准考证号为 {allow_exam_num}")
                return False, "更新学生成绩失败", update_grade_count
        else:
            grade_id = configs.snow_worker.get_id()
            insert_grade_data = instantiation_stu_grade(grade_type, grade_id, allow_exam_num, project_id, subject_id, paper_code, merge_grade, c_user_id)
            insert_grade_data_list.append(insert_grade_data)

        insert_grade_detail = StuTotalGradeDetail(detail_id=configs.snow_worker.get_id(), grade_id=grade_id, grade_type=grade_type, detail_data=copy.deepcopy(grade_detail))
        insert_grade_detail_list.append(insert_grade_detail)
        finish_count += 1

        # 分批生成成绩
        create_grade_num = len(insert_grade_data_list) + update_grade_count
        if create_grade_num % 2000 == 0 or create_grade_num == stu_count:
            new_session.add_all(insert_grade_data_list)
            new_session.add_all(insert_grade_detail_list)
            new_session.commit()
            insert_grade_data_list, insert_grade_detail = [], []
            update_create_grade_process(new_session, record_id, total_count, finish_count)

    return True, None, finish_count


def load_history_grade(new_session, paper_code_list: list, allow_exam_num):
    """
    加载历史成绩到内存中
    生成格式
    history_grade_data = {
        allow_exam_num: {
            paper_code: grade_id
        }
    }
    """
    history_grade_data = {}
    stu_query_condition = StuTotalGrade.allow_exam_num == allow_exam_num if allow_exam_num else True
    grade_info = (
        new_session.query(StuTotalGrade.grade_id, StuTotalGrade.paper_code, StuTotalGrade.allow_exam_num).filter(and_(StuTotalGrade.paper_code.in_(paper_code_list), stu_query_condition)).all()
    )
    # print("grade_info", grade_info)

    for grade_id, paper_code, allow_exam_num in grade_info:
        if allow_exam_num in history_grade_data:
            if paper_code not in history_grade_data[allow_exam_num]:
                history_grade_data[allow_exam_num][paper_code] = grade_id
        else:
            history_grade_data[allow_exam_num] = {paper_code: grade_id}
    return history_grade_data


def check_grade_exist(history_grade_data: dict, allow_exam_num: str, paper_code: str):
    """
    检查成绩是否存在
    """
    try:
        grade_id = history_grade_data[allow_exam_num][paper_code]
        return True, grade_id
    except KeyError:
        return False, None


def update_create_grade_process(new_session, record_id, total_count, finish_count):
    """
    更新生成成绩进度
    """
    progress = round_half_up(finish_count / total_count * 100, 2)
    new_session.query(CreateGradeRecord).filter(CreateGradeRecord.record_id == record_id).update(
        {CreateGradeRecord.total_count: total_count, CreateGradeRecord.success_count: finish_count, CreateGradeRecord.progress: progress}
    )
    new_session.commit()


def create_grade(
    new_session, objective_grade, subjective_grade, op_grade, project_id, subject_id, paper_code, stu_secret_num, project_condition, subject_condition, user, c_user_id, grade_type, record_id
):
    """
    按照不同成绩类型分发不同参数给统计成绩的函数
    """
    try:
        condition = paper_grade_query_condition(project_id, subject_id, paper_code, project_condition, subject_condition)
        need_grade_paper_list = (
            new_session.query(ExamPaper.paper_id, ExamPaper.project_id, ExamPaper.subject_id, ExamPaper.paper_code, ExamPaper.paper_name)
            .outerjoin(Project, Project.project_id == ExamPaper.project_id)
            .outerjoin(Subject, Subject.subject_id == ExamPaper.subject_id)
            .filter(condition)
            .all()
        )

        stu_query_condition = True
        if stu_secret_num:
            allow_exam_num = new_session.query(ExamStudent.allow_exam_num).filter(ExamStudent.stu_secret_num == stu_secret_num).scalar()
            if not allow_exam_num:
                return False, "找不到该考生"

            stu_query_condition = StuAnswer.allow_exam_num == allow_exam_num
        else:
            allow_exam_num = None

        paper_code_list = [i.paper_code for i in need_grade_paper_list] if need_grade_paper_list else []

        finish_count = 0
        total_count = (
            new_session.query(StuAnswer.allow_exam_num, ExamStudent.stu_secret_num)
            .join(ExamStudent, ExamStudent.allow_exam_num == StuAnswer.allow_exam_num)
            .filter(and_(StuAnswer.paper_code.in_(paper_code_list), stu_query_condition))
            .group_by(StuAnswer.allow_exam_num, ExamStudent.stu_secret_num)
            .count()
        )

        # 加载历史成绩到内存中，方便后面判断成绩是更新还是插入
        history_grade_data = load_history_grade(new_session, paper_code_list, allow_exam_num)

        objective_ques_type_code = ["A", "B", "C"]
        subjective_ques_type_code = ["D", "E"]
        op_ques_type_code = ["G"]

        result, msg = True, None
        for paper in need_grade_paper_list:
            paper_id, project_id, subject_id, paper_code, paper_name = paper

            # 获取所有考生密号及其对应的准考证号、密号
            stu_info = (
                new_session.query(StuAnswer.allow_exam_num, func.group_concat(StuAnswer.same_answer_group_id), ExamStudent.stu_secret_num)
                .join(ExamStudent, ExamStudent.allow_exam_num == StuAnswer.allow_exam_num)
                .filter(and_(StuAnswer.paper_id == paper_id, stu_query_condition))
                .group_by(StuAnswer.allow_exam_num, ExamStudent.stu_secret_num)
                .all()
            )
            if not stu_info:
                continue

            _, paper_data_dict = get_paper_ques_info(paper_code, user, False, False)
            f_ques_data_dict, ai_ques_data_dict, manual_ques_data_dict, op_ques_data_dict = {}, {}, {}, {}

            if objective_grade == 1 and subjective_grade == 1:
                logger.info("客观题和主观题都取 AI 成绩")
                for ques_id, ques in paper_data_dict.items():
                    ques_type_code = ques["ques_type_code"]
                    if ques_type_code in [*objective_ques_type_code, *subjective_ques_type_code]:
                        ai_ques_data_dict[ques_id] = ques
                    else:
                        f_ques_data_dict[ques_id] = ques

            elif objective_grade == 1 and subjective_grade == 2:
                logger.info("客观题取 AI 成绩，主观题取人工成绩")
                for ques_id, ques in paper_data_dict.items():
                    ques_type_code = ques["ques_type_code"]
                    if ques_type_code in objective_ques_type_code:
                        ai_ques_data_dict[ques_id] = ques
                    elif ques_type_code in subjective_ques_type_code:
                        manual_ques_data_dict[ques_id] = ques
                    else:
                        f_ques_data_dict[ques_id] = ques

            elif objective_grade == 2 and subjective_grade == 1:
                logger.info("客观题取人工成绩，主观题取 AI 成绩")
                for ques_id, ques in paper_data_dict.items():
                    ques_type_code = ques["ques_type_code"]
                    if ques_type_code in subjective_ques_type_code:
                        ai_ques_data_dict[ques_id] = ques
                    elif ques_type_code in objective_ques_type_code:
                        manual_ques_data_dict[ques_id] = ques
                    else:
                        f_ques_data_dict[ques_id] = ques

            elif objective_grade == 2 and subjective_grade == 2:
                logger.info("客观题和主观题都取人工成绩")
                for ques_id, ques in paper_data_dict.items():
                    ques_type_code = ques["ques_type_code"]
                    if ques_type_code in [*objective_ques_type_code, *subjective_ques_type_code]:
                        manual_ques_data_dict[ques_id] = ques
                    else:
                        f_ques_data_dict[ques_id] = ques

            result, msg, finish_count = create_ai_manual_grade(
                new_session,
                stu_info,
                project_id,
                subject_id,
                paper_code,
                c_user_id,
                grade_type,
                f_ques_data_dict,
                ai_ques_data_dict,
                manual_ques_data_dict,
                history_grade_data,
                record_id,
                total_count,
                finish_count,
            )

        return result, msg
    except Exception as e:
        traceback.print_exc()
        logger.info(f"生成考生成绩失败，{e}")
        return False, "生成考生成绩失败"


def grade_query_condition(project_id: Optional[str], subject_id: Optional[str], paper_code: Optional[str], stu_secret_num: Optional[str], grade_type: Optional[int], stu_score_range: Optional[list]):
    project_query = StuTotalGrade.project_id == project_id if project_id else True
    subject_query = StuTotalGrade.subject_id == subject_id if subject_id else True
    paper_query = StuTotalGrade.paper_code == paper_code if paper_code else True
    stu_secret_num_query = ExamStudent.stu_secret_num.ilike(f"%{stu_secret_num}%") if stu_secret_num else True
    stu_score_range_query = True
    if grade_type == 1:
        stu_score_range_query = StuTotalGrade.ai_grade.between(*stu_score_range)
    elif grade_type == 2:
        stu_score_range_query = StuTotalGrade.manual_grade.between(*stu_score_range)
    condition = and_(project_query, subject_query, paper_query, stu_secret_num_query, stu_score_range_query)
    return condition


def paper_grade_query_condition(project_id: Optional[str], subject_id: Optional[str], paper_code: Optional[str], project_condition, subject_condition):
    project_query = ExamPaper.project_id == project_id if project_id else True
    subject_query = ExamPaper.subject_id == subject_id if subject_id else True
    paper_query = ExamPaper.paper_code == paper_code if paper_code else True

    condition = and_(project_query, subject_query, paper_query, project_condition, subject_condition)
    return condition


def statistics_grade_data(ques_data):
    total_grade = 0
    for ques_type_item in ques_data:
        ques_type_data = ques_type_item["data"]
        ques_type_item["ques_count"] = len(ques_type_data)
        type_ques_score, type_stu_score, correct_count, wrong_count, part_correct_count, not_marked_count = 0, 0, 0, 0, 0, 0
        for single_item in ques_type_data:
            # print("single_item", single_item)
            ques_type_code = single_item["ques_type_code"]
            if ques_type_code != "F":
                # 非组合题
                ques_score = single_item.get("total_score")
                type_ques_score += ques_score
                type_stu_score += single_item.get("stu_score") if single_item.get("stu_score") is not None else 0
                mark_result = single_item.get("mark_result")
                if mark_result == 1:
                    correct_count += 1
                elif mark_result == 2:
                    wrong_count += 1
                elif mark_result == 3:
                    part_correct_count += 1
                elif mark_result == 4:
                    not_marked_count += 1
            else:
                # 组合题
                f_mark_result, f_stu_score, f_ques_score = 3, 0, single_item["total_score"]
                small_correct_count, small_wrong_count, small_part_correct_count, small_not_marked_count = 0, 0, 0, 0
                f_ques_children = single_item.get("children", {})
                for f_child in f_ques_children:
                    ques_score = f_child.get("total_score")
                    type_ques_score += ques_score
                    stu_score = f_child.get("stu_score") if f_child.get("stu_score") is not None else 0
                    f_stu_score += stu_score
                    type_stu_score += stu_score
                    mark_result = f_child.get("mark_result")
                    if mark_result == 1:
                        small_correct_count += 1
                    elif mark_result == 2:
                        small_wrong_count += 1
                    elif mark_result == 3:
                        small_part_correct_count += 1
                    elif mark_result == 4:
                        small_not_marked_count += 1

                children_length = len(f_ques_children)
                if small_correct_count == children_length:
                    correct_count += 1
                elif small_wrong_count == children_length:
                    wrong_count += 1
                elif small_part_correct_count == children_length:
                    part_correct_count += 1
                elif small_not_marked_count == children_length:
                    not_marked_count += 1

                single_item["stu_score"] = f_stu_score
                single_item["ques_score"] = f_ques_score
                single_item["mark_result"] = judge_mark_result(f_stu_score, f_ques_score)

        type_stu_score = round_half_up(type_stu_score, 2)

        (
            ques_type_item["type_ques_score"],
            ques_type_item["type_stu_score"],
            ques_type_item["correct_count"],
            ques_type_item["wrong_count"],
            ques_type_item["part_correct_count"],
            ques_type_item["not_marked_count"],
        ) = (type_ques_score, type_stu_score, correct_count, wrong_count, part_correct_count, not_marked_count)

        total_grade += type_stu_score
    return ques_data, total_grade


def calculate_stu_subject_grade():
    new_session = next(session_depend())
    stu_subject_infos = new_session.query(HumanStudentSubjectGrade.student_subject_grade_id, HumanStudentSubjectGrade.subject_id, HumanStudentSubjectGrade.stu_secret_num).all()
    for stu_subject in stu_subject_infos:
        student_info = (
            new_session.query(HumanStudentSubjectGradeDetail.answer_id, HumanStudentSubjectGradeDetail.stu_score)
            .filter(HumanStudentSubjectGradeDetail.student_subject_grade_id == stu_subject.student_subject_grade_id)
            .all()
        )
        # answer_info = new_session.query(StuAnswer.answer_id).filter(
        #     and_(StuAnswer.stu_secret_num == stu_subject.stu_secret_num,
        #          StuAnswer.subject_id == stu_subject.subject_id)).all()
        #
        #
        # list1 = [item.answer_id for item in student_info]
        # list2 = [item.answer_id for item in answer_info]
        # if set(list1) == set(list2):
        #     logger.info("两个列表元素相同")
        total_score = sum(item.stu_score for item in student_info if item.stu_score is not None)
        (
            new_session.query(HumanStudentSubjectGrade)
            .filter(HumanStudentSubjectGrade.student_subject_grade_id == stu_subject.student_subject_grade_id)
            .update({HumanStudentSubjectGrade.score: total_score})
        )
        new_session.commit()


# 根据HumanRepeatRoundDetail中last_score和stu_score的差值是否在HumanRepeatTask的score_threshold范围内，更新 HumanRepeatTask 中的pass_count


def update_human_repeat_task_pass_count():
    """
    根据HumanRepeatRoundDetail中last_score和stu_score的差值是否在HumanRepeatTask的score_threshold范围内，
    更新HumanRepeatTask中的pass_count
    """
    try:
        new_session = next(session_depend())
        # 查询所有未完成的复评任务
        repeat_tasks = new_session.query(HumanRepeatTask).filter(HumanRepeatTask.task_type.in_([4, 5]), HumanRepeatTask.task_state.in_([3, 4])).all()  # 进行中或已完成状态

        for task in repeat_tasks:
            # 获取该任务的所有复评轮次详情
            round_details = new_session.query(HumanRepeatRoundDetail).filter(HumanRepeatRoundDetail.repeat_task_id == task.repeat_task_id).all()

            pass_count = 0

            # 统计通过的复评记录数
            for detail in round_details:
                if detail.last_score is not None and detail.stu_score is not None:
                    # 计算差值
                    diff = abs(float(detail.last_score) - float(detail.stu_score))
                    # 判断差值是否在阈值范围内
                    if diff <= task.score_threshold:
                        pass_count += 1

            # 更新任务的通过量
            if task.pass_count != pass_count:
                task.pass_count = pass_count
                new_session.commit()

        return True
    except Exception as e:
        logger.error(f"更新复评任务通过量失败: {e}")
        traceback.print_exc()
        return False
