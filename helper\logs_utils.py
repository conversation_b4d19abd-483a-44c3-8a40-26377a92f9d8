from datetime import datetime
from typing import Union

from fastapi import Request, WebSocket
from settings import logger


def loger_chat(request: Union[Request, WebSocket], user_info: dict, **kwargs) -> None:
    logging_info = {
        "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "user_id": user_info.get("Id"),
        "username": user_info.get("username"),
        "user_ip": request.client.host,
        "user_agent": request.headers.get("User-Agent"),
        **kwargs,
    }
    logger_str = "--".join(f"{k}:{v}" for k, v in logging_info.items())
    logger.info(logger_str)
