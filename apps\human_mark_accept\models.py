from sqlalchemy import Column, String, Integer, Float, JSON, Text, DateTime, DECIMAL, PrimaryKeyConstraint, Boolean
from sqlalchemy.orm import declarative_base

from apps.models.models import DateTimeBaseMixin

# 声明基类
Base = declarative_base()


class HumanMarkAccept(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_mark_accept"
    __table_args__ = {"comment": "阅卷验收表"}
    mark_accept_id = Column(String(50), primary_key=True, comment="阅卷验收id")
    project_id = Column(String(50), comment="项目id")
    subject_id = Column(String(50), comment="科目id")
    paper_id = Column(String(30), comment="试卷id")
    extract_num = Column(Integer, comment="抽卷数量")
    pass_num = Column(Integer, comment="通过数量")
    score_threshold = Column(Integer, comment="验收分差阈值")
    accept_state = Column(Integer, comment="验收状态：0 未抽取；1 待验收；2 验收中；3 已完成")
    accept_result = Column(Integer, comment="验收结果：0 通过；1 不通过")
    paper_threshold = Column(Integer, comment="阅卷差错允许份数")
    accept_rate = Column(DECIMAL(10, 2), comment="通过率阈值")


class HumanMarkAcceptDetail(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_mark_accept_detail"
    __table_args__ = {"comment": "阅卷验收详情表"}
    mark_accept_detail_id = Column(String(50), primary_key=True, comment="阅卷验收详情id")
    mark_accept_id = Column(String(50), comment="阅卷验收id")
    # subjective_ques_grade_id = Column(String(50), comment="主观题评分id")
    # objective_ques_grade_id = Column(String(50), comment="客观题评分id")
    stu_secret_num = Column(String(30), comment="考生密号")
    accept_score = Column(DECIMAL(10, 2), comment="验收分数")
    mark_score = Column(DECIMAL(10, 2), comment="评阅分数")
    is_stu_marked = Column(Integer, default=0, comment="该考生是否已验收，验了分才会更改为 1")
    accept_result = Column(Integer, comment="验收结果：0 合格；1 不合格")
    accept_user_id = Column(Integer, comment="验收人id")
