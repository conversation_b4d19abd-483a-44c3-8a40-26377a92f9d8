import traceback

from fastapi import APIRouter, Depends
from sqlalchemy import exists, select, and_
from settings import logger
from typing import Any

from sqlalchemy.orm import Session

from apps.users.services import get_current_user
from apps.read_paper import CreateMarkRuleReq, GetMarkRuleReq, GetMarkRuleDetailReq, UpdateMarkRuleReq
from apps.models.models import MarkRule, MarkRuleDetail, UserInfo, Project, Subject, ExamPaper, ExamQuestion, \
    SameStuAnswerGroup, QuesAnswerMark, PaperDetail
from apps.read_paper.common_services import mark_rule_query_condition, get_user_data_flag
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from settings import configs

mark_rule_router = APIRouter()


@mark_rule_router.post(path="/create_mark_rule", response_model=BaseResponse, summary="创建评分规则信息")
async def create_mark_rule(query: CreateMarkRuleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建评分规则信息")
    project_id, subject_id, rule_name, rule_year, is_params, remark, rule_dict = query.model_dump().values()
    # print("rule_dict", rule_dict)
    is_exist = new_session.query(exists().where(MarkRule.rule_name == rule_name)).scalar()
    if is_exist:
        return BaseResponse(code=response_utils.fields_exist, msg=f"评分规则名 {rule_name} 已存在")

    include_ques_type_code = list(rule_dict.keys())
    try:
        new_rule_id = configs.snow_worker.get_id()
        new_rule = MarkRule(rule_id=new_rule_id, project_id=project_id, subject_id=subject_id,
                            rule_name=rule_name, rule_year=rule_year, remark=remark,
                            include_ques_type_code=include_ques_type_code, lock_state=1, c_user_id=user["user_id"])
        new_session.add(new_rule)
    except Exception as e:
        logger.error(f"创建评分规则信息失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"创建评分规则信息失败")
    new_detail_list = []
    # 抽卷逻辑
    for i in rule_dict:
        rule_detail_id = configs.snow_worker.get_id()
        new_detail = MarkRuleDetail(rule_detail_id=rule_detail_id, parent_rule_id=new_rule_id, ques_type_code=i,
                                    mark_rule_detail=rule_dict[i])
        new_detail_list.append(new_detail)
    # todo：抽参逻辑

    try:
        new_session.add_all(new_detail_list)
        new_session.query(Subject).filter(Subject.subject_id == subject_id).update({Subject.lock_state: 2})
        new_session.commit()
    except Exception as e:
        logger.error(f"创建评分规则信息失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"创建评分规则信息失败")
    logger.info("创建评分规则信息成功")
    return BaseResponse(msg=f"创建评分规则信息成功")


@mark_rule_router.post(path="/get_mark_rule", response_model=BaseResponse, summary="获取评分规则列表")
async def get_mark_rule(query: GetMarkRuleReq, user: Any = Depends(get_current_user),
                        new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取评分规则列表")
    current_page, page_size, project_id, subject_id, rule_year, c_name = query.model_dump().values()
    project_condition, subject_condition = get_user_data_flag(new_session, user)

    rule_data = []
    limit = current_page - 1
    offset = limit * page_size
    # 拼凑查询条件
    condition = mark_rule_query_condition(project_id, subject_id, rule_year)
    if c_name:
        user_condition = UserInfo.name.ilike(f"%{c_name}%")
    else:
        user_condition = True

    total = new_session.query(MarkRule) \
        .join(UserInfo, MarkRule.c_user_id == UserInfo.user_id) \
        .join(Project, MarkRule.project_id == Project.project_id) \
        .join(Subject, MarkRule.subject_id == Subject.subject_id) \
        .where(and_(condition, user_condition, project_condition, subject_condition)).count()

    if page_size == -1:
        mark_rule_stmt = select(MarkRule.rule_id, MarkRule.rule_name, MarkRule.rule_year,
                                MarkRule.include_ques_type_code, MarkRule.remark, MarkRule.lock_state,
                                MarkRule.created_time, MarkRule.updated_time, UserInfo.username, Project.project_id,
                                Project.project_name, Subject.subject_id, Subject.subject_name, UserInfo.name) \
            .join(Project, MarkRule.project_id == Project.project_id) \
            .join(Subject, MarkRule.subject_id == Subject.subject_id) \
            .join(UserInfo, MarkRule.c_user_id == UserInfo.user_id) \
            .where(and_(condition, user_condition, project_condition, subject_condition))
    else:
        mark_rule_stmt = select(MarkRule.rule_id, MarkRule.rule_name, MarkRule.rule_year,
                                MarkRule.include_ques_type_code, MarkRule.remark, MarkRule.lock_state,
                                MarkRule.created_time, MarkRule.updated_time, UserInfo.username, Project.project_id,
                                Project.project_name, Subject.subject_id, Subject.subject_name, UserInfo.name) \
            .join(Project, MarkRule.project_id == Project.project_id) \
            .join(Subject, MarkRule.subject_id == Subject.subject_id) \
            .join(UserInfo, MarkRule.c_user_id == UserInfo.user_id) \
            .where(and_(condition, user_condition, project_condition, subject_condition)) \
            .order_by(MarkRule.created_time.desc(), MarkRule.rule_id.desc()).limit(page_size).offset(offset)
    try:
        result = new_session.execute(mark_rule_stmt)
        for row in result:
            rule_item = {
                "rule_id": row[0],
                "rule_name": row[1],
                "rule_year": row[2],
                "include_ques_type_code": row[3],
                "remark": row[4],
                "lock_state": row[5],
                "created_time": row[6] and str(row[6]).replace("T", " "),
                "updated_time": row[7] and str(row[7]).replace("T", " "),
                "c_user_name": row[8],
                "project_id": row[9],
                "project_name": row[10],
                "subject_id": row[11],
                "subject_name": row[12],
                "c_name": row[13]
            }
            rule_data.append(rule_item)
    except Exception as e:
        logger.error(f"获取评分规则列表失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg=f"获取评分规则列表失败")
    logger.info("获取评分规则列表成功")
    data = {
        "data": rule_data,
        "total": total
    }
    return BaseResponse(msg="获取评分规则列表成功", data=data)


@mark_rule_router.get(path="/get_mark_rule_detail", response_model=BaseResponse, summary="通过规则 id 获取评分规则详情")
async def get_mark_rule_detail(query: GetMarkRuleDetailReq = Depends(), user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取评分规则详情：规则id为 {query.rule_id}")
    detail_stmt = select(MarkRuleDetail.rule_detail_id, MarkRuleDetail.parent_rule_id, MarkRuleDetail.ques_type_code,
                         MarkRuleDetail.mark_rule_detail).where(
        MarkRuleDetail.parent_rule_id == query.rule_id).order_by(
        MarkRuleDetail.ques_type_code)
    detail_data = []
    try:
        result = new_session.execute(detail_stmt)
        for row in result:
            detail_item = {
                "rule_detail_id": row[0],
                "parent_rule_id": row[1],
                "ques_type_code": row[2],
                "mark_rule_detail": row[3]
            }
            detail_data.append(detail_item)
    except Exception as e:
        logger.error(f"获取评分规则详情失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取评分规则详情失败")
    logger.info("获取评分规则列表成功")
    return BaseResponse(msg="获取评分规则详情成功", data=detail_data)


@mark_rule_router.post(path="/update_mark_rule", response_model=BaseResponse, summary="编辑评分规则信息")
async def update_mark_rule(query: UpdateMarkRuleReq, user: Any = Depends(get_current_user),
                           new_session: Session = Depends(session_depend)):
    rule_id, project_id, subject_id, rule_name, rule_year, remark, rule_dict = query.model_dump().values()
    logger.info(f"{user['username']} 编辑评分规则信息，id 为 {rule_id}")
    try:
        raw_rule_detail_dict = {}
        update_ques_type_code = list(rule_dict.keys())

        lock_state = new_session.query(MarkRule.lock_state).filter(MarkRule.rule_id == rule_id).scalar()
        # 查询出原来的判分规则
        raw_rule_detail = new_session.query(MarkRuleDetail.ques_type_code, MarkRuleDetail.mark_rule_detail).filter(
            MarkRuleDetail.parent_rule_id == rule_id).all()
        for ques_type_code, mark_rule_detail in raw_rule_detail:
            raw_rule_detail_dict[ques_type_code] = mark_rule_detail
        raw_ques_type_code = list(raw_rule_detail_dict.keys())

        if lock_state == 2:
            can_not_delete_detail = [i for i in raw_ques_type_code if i not in update_ques_type_code]
            if can_not_delete_detail:
                return BaseResponse(code=response_utils.params_error, msg="被使用过的评分规则不允许删除题型")

        new_session.query(MarkRule).filter(MarkRule.rule_id == rule_id).update({
            MarkRule.rule_name: rule_name,
            MarkRule.project_id: project_id,
            MarkRule.subject_id: subject_id,
            MarkRule.rule_year: rule_year,
            MarkRule.remark: remark,
            MarkRule.include_ques_type_code: update_ques_type_code,
            MarkRule.u_user_id: user.get("user_id")
        })
        new_detail_list = []
        detail_data = new_session.query(MarkRuleDetail.rule_detail_id).filter(
            MarkRuleDetail.parent_rule_id == rule_id).all()
        all_detail_id_list = [i[0] for i in detail_data]

        update_detail_id_list = []
        need_update_ques_type = []
        for i in rule_dict:
            detail_data = new_session.query(MarkRuleDetail.rule_detail_id).filter(
                and_(MarkRuleDetail.parent_rule_id == rule_id, MarkRuleDetail.ques_type_code == i)).first()

            if detail_data:
                rule_detail_id = detail_data.rule_detail_id
                # 不一样的规则进行更新并记录下来
                if raw_rule_detail_dict[i] != rule_dict[i]:
                    new_session.query(MarkRuleDetail).filter(MarkRuleDetail.rule_detail_id == rule_detail_id).update({
                        MarkRuleDetail.mark_rule_detail: rule_dict[i]
                    })
                    need_update_ques_type.append(i)
                update_detail_id_list.append(rule_detail_id)
            else:
                new_detail = MarkRuleDetail(rule_detail_id=configs.snow_worker.get_id(), parent_rule_id=rule_id,
                                            ques_type_code=i, mark_rule_detail=rule_dict[i])
                new_detail_list.append(new_detail)
        if new_detail_list:
            new_session.add_all(new_detail_list)

        delete_detail_id = []
        for detail_id in all_detail_id_list:
            if detail_id not in update_detail_id_list:
                delete_detail_id.append(detail_id)
        if delete_detail_id:
            new_session.query(MarkRuleDetail).filter(MarkRuleDetail.rule_detail_id.in_(delete_detail_id)).delete()

        # 如果该题型的评分规则已被使用，清空智能阅卷的相关评分信息
        # if need_update_ques_type:
        #     need_update_mark_ques = new_session.query(ExamQuestion.ques_id) \
        #         .outerjoin(PaperDetail, PaperDetail.ques_id == PaperDetail.ques_id) \
        #         .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
        #         .filter(and_(ExamPaper.mark_rule_id == rule_id,
        #                      ExamQuestion.ques_type_code.in_(need_update_ques_type))).all()
        #     if need_update_mark_ques:
        #         need_update_mark_ques_id = [i[0] for i in need_update_mark_ques]
        #         new_session.query(QuesAnswerMark).filter(QuesAnswerMark.ques_id.in_(need_update_mark_ques_id)).delete()
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.error(f"编辑评分规则信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"编辑评分规则信息失败")
    logger.info("编辑评分规则信息成功")
    return BaseResponse(msg="编辑评分规则信息成功")


@mark_rule_router.post(path="/delete_mark_rule", response_model=BaseResponse, summary="删除评分规则信息")
async def delete_paper(query: GetMarkRuleDetailReq, user: Any = Depends(get_current_user),
                       new_session: Session = Depends(session_depend)):
    rule_id = query.rule_id
    logger.info(f"{user['username']} 删除评分规则信息，id 为 {rule_id}")
    rule_data = new_session.query(MarkRule.lock_state).filter(MarkRule.rule_id == rule_id).first()
    if not rule_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该评分规则")

    if rule_data.lock_state == 2:
        return BaseResponse(code=response_utils.permission_deny, msg="该评分规则已被使用，无法删除")

    try:
        new_session.query(MarkRule).filter(MarkRule.rule_id == rule_id).delete()
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除评分规则信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除评分规则信息失败")
    logger.info("删除评分规则信息成功")
    return BaseResponse(msg="删除评分规则信息成功")
