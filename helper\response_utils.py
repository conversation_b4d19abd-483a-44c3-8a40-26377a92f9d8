success = 10010  # 成功
fields_exist = 10012  # 数据库存在该字段
no_field = 10013  # 数据库不存在该字段
verify_error = 10014  # 数据库字段校验错误
server_error = 10015  # 服务器异常
end_page = 10016  # 加载到最后一页了
params_error = 10017  # 请求参数校验异常
running = 10018  # 任务正在进行
operation_repeat = 10019  # 操作重复
permission_deny = 10020  # 权限不足
has_no_update_data = 10021  # 数据无更新
task_not_finished = 10022  # 任务未完成

"""定义异常码"""
timeout = 40001  # 连接第三方API超时
ai_model_error = 40002  # 大模型异常
terminal_error = 40003  # 终端服务异常
auth_failed = 401  # 认证失败

"""定义 AI 阅卷异常提示信息"""
ai_score_ambiguity_code = 50001
ai_score_ambiguity = "AI 评分分数存在歧义，需要人工进行评分。"
ai_can_not_connect_code = 50002
ai_can_not_connect = "无法与 AI 建立连接"
ai_not_response_code = 50003
ai_not_response = "AI 未及时响应"


"""定义 fastapi 参数异常 type 对应中文异常提示信息"""
trans_type_dict = {
    "string_too_long": "该字段长度过长",
    "missing": "该字段缺失",
    "greater_than": "该值不合法",
    "less_than": "该值不合法"
}


