import json


def modify_path(full_ques_save_path, full_answer_save_path):
    with open(full_answer_save_path, "r", encoding="utf-8") as f:
        content = json.load(f)
    content["qtfile"] = full_ques_save_path
    result_save_path = full_answer_save_path.replace(".jdet", "_result")
    content["vbusefilepath"] = result_save_path
    with open(full_answer_save_path, "w", encoding="utf-8") as f1:
        json.dump(content, f1, ensure_ascii=False)
    return result_save_path
