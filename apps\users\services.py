from copy import deepcopy

from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2P<PERSON>wordBearer
from settings import logger, configs
from sqlalchemy import and_

from apps.models.models import UserInfo, UserRole, Role, SysDefaultPwd, Project, Subject
from apps.permission.services import get_user_func_flag_value
from factory_apps import session_depend
from factory_apps.redis_db.redis_cahe import r
from helper.encrypto import decrypt
from helper.token import verify_access_token, create_access_token, verify_hash_password

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/token")


def get_current_user(token: str = Depends(oauth2_scheme)):
    payload = verify_access_token(token)
    user_id = payload.get("user_id", None)
    payload["token"] = token
    if not user_id:
        logger.info(f"当前用户认证错误 : user_id：{user_id} - {status.HTTP_401_UNAUTHORIZED}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="未授权的用户")
    return payload


def user_query_condition(username, name, role_id, province_code_list, city_code_list, district_code_list, work_unit,
                         user_type, is_active, system_user_type, round_count_list, is_real_name, role_id_list, project_id, subject_id):
    username_query = UserInfo.username.ilike(f"%{username}%") if username else True
    name_query = UserInfo.name.ilike(f"%{name}%") if name else True
    province_query = UserInfo.province_code.in_(province_code_list) if province_code_list else True
    city_query = UserInfo.city_code.in_(city_code_list) if city_code_list else True
    district_query = UserInfo.district_code.in_(district_code_list) if district_code_list else True
    work_unit_query = UserInfo.work_unit.ilike(f"%{work_unit}%") if work_unit else True
    user_type_query = UserInfo.user_type == user_type if user_type else True
    is_active_query = UserInfo.is_active == is_active if is_active is not None else True
    system_user_type_query = UserInfo.system_user_type == system_user_type if system_user_type else True
    role_query = UserRole.role_id == role_id if role_id else True
    role_query_list = UserRole.role_id.in_(role_id_list) if role_id_list else True
    round_count = UserInfo.round_count.in_(round_count_list) if round_count_list else True
    is_real_name = UserInfo.already_login == is_real_name if is_real_name is not None else True
    project_id_query = Project.project_id == project_id if project_id else True
    subject_id_query = Subject.subject_id == subject_id if subject_id else True

    condition = and_(username_query, name_query, province_query, city_query, district_query,
                     work_unit_query, user_type_query, is_active_query, system_user_type_query, role_query, role_query_list, round_count, is_real_name, project_id_query, subject_id_query)
    return condition


def verify_login(new_session, username, input_password):
    """
    登录校验
    """
    # 获取配置参数
    max_attempts = configs.MAX_LOGIN_ATTEMPTS
    lock_time = configs.LOGIN_LOCK_TIME
    
    # 检查用户是否被锁定
    lock_key = f"login_locked:{username}"
    if r.exists(lock_key):
        # 获取剩余锁定时间
        remaining_time = r.ttl(lock_key)
        return None, None, [], 0, False, None, None, None, f"登录失败，账号已被锁定，请{remaining_time}秒后重试。"
    
    ## 新增电话号码，身份信息返回 25.8.24
    user_info = new_session.query(UserInfo.user_id, UserInfo.name, UserInfo.password, UserInfo.user_type,
                                  UserInfo.already_login, UserInfo.is_active, UserInfo.system_user_type,UserInfo.phone,UserInfo.id_card).filter(UserInfo.username == username).first()
    # 判断是否已经注册
    if not user_info:
        # 登录失败，记录失败次数
        fail_key = f"login_fail_count:{username}"
        fail_count = r.incr(fail_key)
        r.expire(fail_key, lock_time)  # 使用配置的锁定时间过期
        if fail_count >= max_attempts:
            # 达到最大失败次数，锁定账户
            r.setex(lock_key, lock_time, "locked")
            remaining_time = r.ttl(lock_key)
            return None, None, [], 0, False, None, None, None, f"登录失败，账号已被锁定，请{remaining_time}秒后重试。"
        return None, None, [], 0, False, None, None, None, "登录失败，账号或密码不正确。"

    user_id, name, password, user_type, already_login, is_active, system_user_type, phone, id_card = user_info 

    if not is_active:
        # 登录失败，记录失败次数
        fail_key = f"login_fail_count:{username}"
        fail_count = r.incr(fail_key)
        r.expire(fail_key, lock_time)  # 使用配置的锁定时间过期
        if fail_count >= max_attempts:
            # 达到最大失败次数，锁定账户
            r.setex(lock_key, lock_time, "locked")
            remaining_time = r.ttl(lock_key)
            return None, None, [], 0, False, None, None, None, f"登录失败，账号已被锁定，请{remaining_time}秒后重试。"
        return None, None, [], 0, False, None, None, None, "登录失败，该账号已被禁用。"

    # 校验密码，做哈希校验
    private_password = password
    real_password = decrypt(input_password)
    verify_result = verify_hash_password(real_password, private_password)
    if not verify_result:
        logger.warning(f"{username} 登录失败，账号或密码不正确。")
        # 登录失败，记录失败次数
        fail_key = f"login_fail_count:{username}"
        fail_count = r.incr(fail_key)
        r.expire(fail_key, lock_time)  # 使用配置的锁定时间过期
        if fail_count >= max_attempts:
            # 达到最大失败次数，锁定账户
            r.setex(lock_key, lock_time, "locked")
            remaining_time = r.ttl(lock_key)
            return None, name, [], user_type, already_login, None, phone, id_card, f"登录失败，账号已被锁定，请{remaining_time}秒后重试。"
        return None, name, [], user_type, already_login, None, phone, id_card, "登录失败，账号或密码不正确。"

    # 登录成功，重置失败次数
    fail_key = f"login_fail_count:{username}"
    r.delete(fail_key)
    
    user_role_info = new_session.query(UserRole.role_id).filter(UserRole.user_id == user_id).all()
    if not user_role_info:
        role_id_list = ["6"]
    else:
        role_id_list = [i[0] for i in user_role_info]
    return user_id, name, role_id_list, user_type, already_login, system_user_type,phone, id_card, None


def verify_unlock_screen_pwd(new_session, username, input_password):
    """
    登录校验
    """
    user_info = new_session.query(UserInfo.user_id, UserInfo.password).filter(UserInfo.username == username).first()
    # 判断是否已经注册
    if not user_info:
        return False, "密码不正确，解锁失败"

    user_id, password = user_info

    # 校验密码，做哈希校验
    private_password = password
    real_password = decrypt(input_password)
    verify_result = verify_hash_password(real_password, private_password)
    if not verify_result:
        logger.warning("密码不正确，解锁失败")
        return False, "密码不正确"
    return True, None


def generate_user_login_data(new_session, user_id, username, name, role_id_list, user_type, already_login, system_user_type,phone,id_card):
    """
    生成登录需要的数据
    """
    # 25.8.24 添加了手机号  身份证号入参
    user_dict = {
        "user_id": user_id,
        "username": username,
        "name": name,
        "role": role_id_list,
        "user_type": user_type,
        "system_user_type": system_user_type,
        "phone_number":phone,
        "id_card":id_card
    }

    role_id = role_id_list[0]

    role_info = new_session.query(Role.role_name).filter(Role.role_id == role_id).first()
    if not role_info:
        role_name = ["普通用户"]
    else:
        role_name = [role_info[0]]
    user_dict["role_name"] = role_name

    # 创建 token
    token, expires = create_access_token(user_dict)

    func_point_value = get_user_func_flag_value(new_session, user_id, role_id)
    user_dict["func_point_value"] = func_point_value

    data = {
        "accessToken": token,
        "expires": expires.strftime("%Y/%m/%d %H:%M:%S"),
        "user_info": user_dict,
        "already_login": already_login
    }
    return data


def get_default_password():
    """查询默认密码"""
    new_session = next(session_depend())
    pwd_info = new_session.query(SysDefaultPwd.pwd_value).filter(SysDefaultPwd.pwd_type == 1).first()
    if not pwd_info:
        return False, "请联系管理员设置默认密码", None
    default_pwd = pwd_info[0]
    de_default_password = decrypt(default_pwd)
    return True, None, de_default_password
