import os
import traceback
import json
from typing import Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy import select, and_, func

from apps.human_official_mark.services import get_stu_answer_info
from apps.human_repeat_mark.models import HumanRepeatTask, HumanRepeatRoundDetail, HumanRepeatTaskRound
from apps.human_statistics.services import get_human_statistics_condition
from helper.excel_export_utils import ExcelExportService
from settings import logger,configs
from pydantic import BaseModel
from sqlalchemy.orm import Session

from apps.users.services import get_current_user
from apps.human_statistics.models import HumanStatisticsPerson
from apps.human_statistics.schemas import PersonSurveyMonitorReq, GetHumanStatisticsListReq, \
    ExportHumanStatisticsListReq
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from apps.models.models import UserInfo, ManualReadTask, Role, UserRole
from apps.human_mark_group.models import HumanMarkGroup, HumanGroupMember
from apps.models.models import ExamPaper, Project, Subject, UserInfo, BusinessQuesType, ExamQuestion, WorkFlowMainProcess
from apps.human_task_manage.models import HumanPersonDistriAnswer, HumanReadRoundGroup, HumanReadTask, HumanReadTaskRound
from factory_apps import session_depend

# 创建APIRouter
human_statistics_router = APIRouter()

@human_statistics_router.post(path="/get_human_statistics_list", response_model=BaseResponse, summary="获取人员工作量数据统计列表")
async def get_human_statistics_list(query: GetHumanStatisticsListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取人员数据统计列表")
    current_page, page_size, role_ids, project_ids, subject_ids, user_name = query.model_dump().values()
    limit = current_page - 1
    offset = limit * page_size
    user_query = UserInfo.username.ilike(f"%{user_name}%") if user_name else True
    role_query = UserRole.role_id.in_(role_ids) if role_ids else True
    condition = get_human_statistics_condition(project_ids,subject_ids)
    #复评 t_human_repeat_task 正评 t_human_statistics_person
    total = new_session.query(UserInfo.user_id).filter(user_query).count()
    user_info = new_session.query(UserInfo.username, UserInfo.id_card, UserInfo.user_id,UserInfo.name).filter(user_query) \
        .filter(UserInfo.already_login == 1)\
        .offset(offset) \
        .limit(page_size) \
        .all()
    if not user_info:
        return BaseResponse(data=[], msg="获取人员工作量数据统计列表成功")
    result = []
    #对于每个用户根据他的id去不同的表里找数据
    for user in user_info:
        role_info = new_session.query(UserRole.role_id,Role.role_name)\
            .join(UserInfo, UserInfo.user_id == UserRole.user_id)\
            .join(Role, Role.role_id == UserRole.role_id)\
            .filter(and_(role_query,UserInfo.user_id == user.user_id)).all()
        if role_info and (role_info[0].role_id == '1' or  role_info[0].role_id == '2'):
            continue
        # 重新设计查询以包含需要的字段，但要确保GROUP BY正确
        if role_info:
            official_mark_info = (
                new_session.query(func.sum(HumanStatisticsPerson.statistics_result_1).label('count'),
                                  Project.project_name, Subject.subject_name)
                .join(Project, Project.project_id == HumanStatisticsPerson.project_id)
                .join(Subject, Subject.subject_id == HumanStatisticsPerson.subject_id)

                .filter(condition, HumanStatisticsPerson.user_id == user.user_id,HumanStatisticsPerson.task_type == 1,
                        HumanStatisticsPerson.statistics_type == 4)
                .group_by(Project.project_id, Subject.subject_id)
                .first())
            # session_info =new_session.query(HumanPersonDistriAnswer.answer_id, HumanPersonDistriAnswer.ques_code).filter(HumanPersonDistriAnswer.user_id == user.user_id).first()

            # official_exam_session = None
            # if session_info and session_info.ques_code:
            #     stu_answer_dict = get_stu_answer_info(new_session, session_info.ques_code)
            #     official_exam_session = stu_answer_dict[session_info.answer_id]["exam_session"]

            # 处理official_mark_info，确保返回正确的字段
            official_mark_data = None
            if official_mark_info:
                official_mark_data = {
                    # "exam_session": official_exam_session,  # 无法从当前查询获取
                    "project_name": official_mark_info.project_name,
                    "subject_name": official_mark_info.subject_name,
                    "count": official_mark_info.count,
                }

            repeat_mark_info = (new_session.query(func.max(HumanRepeatTask.repeat_task_id), func.count(HumanRepeatRoundDetail.repeat_round_detail_id).label('count'),Project.project_name,Subject.subject_name)
                                .join(Project,Project.project_id == HumanRepeatTask.project_id)
                                .join(Subject,Subject.subject_id == HumanRepeatTask.subject_id)
                                .join(HumanRepeatRoundDetail, HumanRepeatRoundDetail.repeat_task_id == HumanRepeatTask.repeat_task_id)
                                .join(HumanRepeatTaskRound, HumanRepeatTaskRound.repeat_task_round_id == HumanRepeatTask.repeat_task_round_id)
                                .filter(condition, HumanRepeatRoundDetail.repeat_user_id == user.user_id,HumanRepeatTaskRound.task_type == 3)
                                .group_by(HumanRepeatRoundDetail.repeat_user_id, Project.project_id, Subject.subject_id)
                                .first())
            repeat_mark_data = None
            if repeat_mark_info:
                repeat_mark_data = {
                    # "exam_session": repeat_mark_info.exam_session,
                    "project_name": repeat_mark_info.project_name,
                    "subject_name": repeat_mark_info.subject_name,
                    "count": repeat_mark_info.count
                }
            mark_accept_data = None
            mark_accept_info = (new_session.query(HumanRepeatTask.repeat_task_id, func.count(HumanRepeatRoundDetail.repeat_round_detail_id).label('count'),Project.project_name,Subject.subject_name)
                                .join(Project,Project.project_id == HumanRepeatTask.project_id)
                                .join(Subject,Subject.subject_id == HumanRepeatTask.subject_id)
                                .join(HumanRepeatRoundDetail, HumanRepeatRoundDetail.repeat_task_id == HumanRepeatTask.repeat_task_id)
                                .join(HumanRepeatTaskRound, HumanRepeatTaskRound.repeat_task_round_id == HumanRepeatTask.repeat_task_round_id)
                                .filter(condition, HumanRepeatRoundDetail.repeat_user_id == user.user_id,HumanRepeatTaskRound.task_type == (4 or 5))
                                .group_by(HumanRepeatTask.repeat_task_id, Project.project_id, Subject.subject_id)
                                .first())
            if mark_accept_info:
                mark_accept_data = {
                    # "exam_session": repeat_mark_info.exam_session,
                    "project_name": mark_accept_info.project_name,
                    "subject_name": mark_accept_info.subject_name,
                    "count": mark_accept_info.count
                }
            # 处理role_info，将其转换为字典格式
            role_info_list = []
            for role in role_info:
                role_info_list.append({
                    "role_id": role.role_id,
                    "role_name": role.role_name
                })

            # 处理repeat_mark_info，确保返回正确的字段
            if subject_ids or project_ids:
                if not (mark_accept_data or repeat_mark_data or official_mark_data):
                    continue
            result_item = {
                "user_id": user.user_id,
                "name": user.name,
                "user_name": user.username,
                "id_card": user.id_card,
                "role_info": role_info_list,
                "official_mark_info": official_mark_data,
                "repeat_mark_info": repeat_mark_data,
                "mark_accept_info": mark_accept_data,
            }
            result.append(result_item)
    data ={
        "total":len(result),
        "data":result
    }
    return BaseResponse(data=data, msg="获取人员工作量数据统计列表成功")

@human_statistics_router.post(path="/export_human_statistics_list", response_model=BaseResponse, summary="导出人员工作量数据")
async def export_human_statistics_list(
        query: ExportHumanStatisticsListReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 导出人员工作量数据")
    role_ids, project_ids, subject_ids, user_name, user_id = query.model_dump().values()
    user_query = UserInfo.username.ilike(f"%{user_name}%") if user_name else True
    role_query = UserRole.role_id.in_(role_ids) if role_ids else True
    condition = get_human_statistics_condition(project_ids, subject_ids)
    excel_export_service = ExcelExportService()
    if user_id:
        user_info = new_session.query(UserInfo.username, UserInfo.id_card, UserInfo.user_id).filter(and_(UserInfo.user_id.in_(user_id),UserInfo.already_login == 1)).all()
    else:
        user_info = new_session.query(UserInfo.username, UserInfo.id_card, UserInfo.user_id).filter(and_(user_query,UserInfo.already_login == 1)) \
            .all()
    result = []
    try:
        for user in user_info:
            role_info = new_session.query(UserRole.role_id, Role.role_name) \
                .join(UserInfo, UserInfo.user_id == UserRole.user_id) \
                .join(Role, Role.role_id == UserRole.role_id) \
                .filter(and_(role_query, UserInfo.user_id == user.user_id)).all()
            if role_info and (role_info[0].role_id == '1' or role_info[0].role_id == '2'):
                continue
            # 重新设计查询以包含需要的字段，但要确保GROUP BY正确
            if role_info:
                project_name =""
                subject_name = ""
                official_count = 0
                repeat_count = 0
                mark_accept_count = 0
                official_mark_info = (
                    new_session.query(func.sum(HumanStatisticsPerson.statistics_result_1).label('count'),
                                      Project.project_name, Subject.subject_name)
                    .join(Project, Project.project_id == HumanStatisticsPerson.project_id)
                    .join(Subject, Subject.subject_id == HumanStatisticsPerson.subject_id)

                    .filter(condition, HumanStatisticsPerson.user_id == user.user_id,HumanStatisticsPerson.task_type ==1,
                            HumanStatisticsPerson.statistics_type == 4)
                    .group_by(Project.project_id, Subject.subject_id)
                    .first())
                # session_info =new_session.query(HumanPersonDistriAnswer.answer_id, HumanPersonDistriAnswer.ques_code).filter(HumanPersonDistriAnswer.user_id == user.user_id).first()

                # official_exam_session = None
                # if session_info and session_info.ques_code:
                #     stu_answer_dict = get_stu_answer_info(new_session, session_info.ques_code)
                #     official_exam_session = stu_answer_dict[session_info.answer_id]["exam_session"]

                # 处理official_mark_info，确保返回正确的字段

                if official_mark_info:

                        # "exam_session": official_exam_session,  # 无法从当前查询获取
                    project_name =  official_mark_info.project_name,
                    subject_name = official_mark_info.subject_name,
                    official_count = official_mark_info.count,


                repeat_mark_info = (new_session.query(func.max(HumanRepeatTask.repeat_task_id),
                                                      func.count(HumanRepeatRoundDetail.repeat_round_detail_id).label(
                                                          'count'), Project.project_name, Subject.subject_name)
                                    .join(Project, Project.project_id == HumanRepeatTask.project_id)
                                    .join(Subject, Subject.subject_id == HumanRepeatTask.subject_id)
                                    .join(HumanRepeatRoundDetail,
                                          HumanRepeatRoundDetail.repeat_task_id == HumanRepeatTask.repeat_task_id)
                                    .join(HumanRepeatTaskRound,
                                          HumanRepeatTaskRound.repeat_task_round_id == HumanRepeatTask.repeat_task_round_id)
                                    .filter(condition, HumanRepeatRoundDetail.repeat_user_id == user.user_id,
                                            HumanRepeatTaskRound.task_type == 3)
                                    .group_by(HumanRepeatRoundDetail.repeat_user_id,Project.project_id, Subject.subject_id)
                                    .first())

                if repeat_mark_info:
                        # "exam_session": repeat_mark_info.exam_session,
                    project_name =  repeat_mark_info.project_name,
                    subject_name = repeat_mark_info.subject_name,
                    repeat_count = repeat_mark_info.count

                mark_accept_info = (new_session.query(HumanRepeatTask.repeat_task_id,
                                                      func.count(HumanRepeatRoundDetail.repeat_round_detail_id).label(
                                                          'count'), Project.project_name, Subject.subject_name)
                                    .join(Project, Project.project_id == HumanRepeatTask.project_id)
                                    .join(Subject, Subject.subject_id == HumanRepeatTask.subject_id)
                                    .join(HumanRepeatRoundDetail,
                                          HumanRepeatRoundDetail.repeat_task_id == HumanRepeatTask.repeat_task_id)
                                    .join(HumanRepeatTaskRound,
                                          HumanRepeatTaskRound.repeat_task_round_id == HumanRepeatTask.repeat_task_round_id)
                                    .filter(condition, HumanRepeatRoundDetail.repeat_user_id == user.user_id,
                                            HumanRepeatTaskRound.task_type == (4 or 5))
                                    .group_by(HumanRepeatTask.repeat_task_id,Project.project_id, Subject.subject_id)
                                    .first())
                if mark_accept_info:

                    project_name = mark_accept_info.project_name,
                    subject_name = mark_accept_info.subject_name,
                    mark_accept_count = mark_accept_info.count

                # 处理role_info，将其转换为字典格式
                role_info_list = []
                for role in role_info:
                    role_info_list.append(role.role_name)

                # 处理repeat_mark_info，确保返回正确的字段

                result_item = {
                    "用户名": user.username,
                    "证件号码": user.id_card,
                    "所属资格": project_name[0] if project_name else "",
                    "所属科目": subject_name[0] if subject_name else "",
                    "所属角色": ",".join(role_info_list),
                    "正评阅卷量(题)": official_count[0] if official_count else "",
                    "复评量(卷)": repeat_count[0] if repeat_count else "",
                    "验收量(卷)": mark_accept_count[0] if mark_accept_count else "",
                }
                result.append(result_item)
    except Exception as e:
        print(e)
    return await excel_export_service.export_to_excel_stream(
        data=result,
        file_name="人员工作量.xlsx"
    )
