from datetime import datetime, timedelta


def format_now_time(format_str="%Y-%m-%d %H:%M:%S"):
    now_time = datetime.now().strftime(format_str)
    return now_time


def add_days_to_date(input_date: str, days: int) -> str:
    """
    将指定日期增加指定天数
    """
    # 解析输入的日期字符串为 datetime 对象，时间默认为 00:00:00
    date_obj = datetime.strptime(input_date, "%Y-%m-%d")

    # 增加指定的天数
    new_date = date_obj + timedelta(days=days)

    # 返回新的日期字符串
    return new_date.strftime("%Y-%m-%d")


if __name__ == '__main__':
    input_date = "2024-09-06"
    days_to_add = 1
    new_date_str = add_days_to_date(input_date, days_to_add)
    print(new_date_str)


