import json

from sqlalchemy import select, func, and_
from sqlalchemy import select, and_
from redis import asyncio as redis

from apps.grade_manage.models import HumanStudentSubjectGradeDetail
from apps.human_official_mark.schemas import QuesCodeItem, AnswerIdListItem
from apps.human_repeat_mark.models import HumanRepeatTask, HumanRepeatTaskRound, HumanRepeatAnswerScore, \
    HumanRepeatRoundDetail
from apps.human_task_manage.models import HumanReadTask, HumanReadTaskRound, HumanPersonDistriAnswer, HumanRoundDistriAnswer
from apps.models.models import StuAnswer, Subject, ExamQuestion
from apps.ques_manage.services import get_ques_id_list
from factory_apps import redis_session
# from factory_apps.redis_db.redis_cahe import get_redis_connection
from settings import configs, logger
from datetime import datetime

def get_stu_answer_info(new_session, ques_code, is_reload=False):
    """
    将作答按照 ques_code 加载到 redis
    """
    # # 使用安全的连接方式
    # r = get_redis_connection()
    # if r is None:  # 检查Redis连接是否成功
    #     # 如果Redis连接失败，返回默认值而不是抛出异常
    #     return {}
    r = next(redis_session())
    stu_answer_dict = None if is_reload else r.get(f"stu_answer_dict_{ques_code}")

    if stu_answer_dict:
        stu_answer_dict = json.loads(stu_answer_dict)
    else:
        ques_id_list = get_ques_id_list(new_session, ques_code)
        stu_answer_info = new_session.execute(select(StuAnswer.answer_id, StuAnswer.stu_secret_num, StuAnswer.ques_id, StuAnswer.answer_image_path, StuAnswer.word_count, StuAnswer.is_do, StuAnswer.ques_order).where(StuAnswer.ques_id.in_(ques_id_list))).all()
        stu_answer_dict = {}
        for row in stu_answer_info:
            answer_image_path = f'server_static/{row.answer_image_path.replace("\\", "/")}' if row.answer_image_path else None
            stu_answer_dict[row.answer_id] = {
                "stu_secret_num": row.stu_secret_num,
                "ques_id": row.ques_id,
                "answer_image_path": answer_image_path,
                "word_count": row.word_count,
                "is_do": row.is_do,
                "ques_order": row.ques_order
            }
        r.set(f"stu_answer_dict_{ques_code}", json.dumps(stu_answer_dict))
    return stu_answer_dict


def add_mark_to_redis_queue(r: redis, round_id: str, mark_data: dict):
    """将评分数据添加到 redis 队列"""
    r.rpush(f"{configs.R_QUEUE_KEY}_{round_id}", json.dumps(mark_data))


def get_batch_mark_data(r: redis, batch_size: int = 10):
    """从 redis 队列中获取一批数据"""
    pipeline = r.pipeline()
    pipeline.lrange(configs.R_QUEUE_KEY, 0, batch_size - 1)
    pipeline.ltrim(configs.R_QUEUE_KEY, batch_size, -1)
    result = pipeline.excute()
    return result


def mark_record_condition(subject_id, task_type, round_count, ques_code, user_id, stu_secret_num, start_time, end_time, mark_score_range):
    subject_query = Subject.subject_id == subject_id if subject_id else True
    task_type_query = HumanReadTask.task_type == task_type if task_type else True
    round_count_query = HumanReadTaskRound.round_count == round_count if round_count else True
    ques_code_query = HumanReadTask.ques_code == ques_code if ques_code else True
    user_query = HumanPersonDistriAnswer.user_id == user_id if user_id else True
    secret_num_query = HumanPersonDistriAnswer.stu_secret_num == stu_secret_num if stu_secret_num else True
    is_answer_query = HumanPersonDistriAnswer.is_answer_marked == 1
    # 时间过滤
    mark_time_query = True
    mark_score_query = True
    if mark_score_range:
        mark_score_query = HumanPersonDistriAnswer.mark_score.between(*mark_score_range)
    try:
        if start_time and end_time:
            # 假设时间格式为 "YYYY-MM-DD HH:MM:SS"

                start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                mark_time_query = HumanPersonDistriAnswer.mark_time.between(start_dt, end_dt)

        elif start_time:

                start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                mark_time_query = HumanPersonDistriAnswer.mark_time >= start_dt
        elif end_time:

                end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                mark_time_query = HumanPersonDistriAnswer.mark_time <= end_dt

    except Exception as e:
        logger.error("查询评分记录起始或者结束时间有误")

    condition = and_(subject_query,task_type_query,round_count_query,ques_code_query,user_query,secret_num_query,is_answer_query,mark_time_query)
    return condition

def repeat_record_condition(subject_id, task_type, round_count, ques_code, user_id, stu_secret_num, start_time, end_time, mark_score_range):
    subject_query = Subject.subject_id == subject_id if subject_id else True
    round_count_query = HumanRepeatTaskRound.round_count == round_count if round_count else True
    ques_code_query = HumanRepeatAnswerScore.ques_code == ques_code if ques_code else True
    user_query = HumanRepeatRoundDetail.repeat_user_id == user_id if user_id else True
    secret_num_query = HumanRepeatRoundDetail.stu_secret_num == stu_secret_num if stu_secret_num else True

    # 时间过滤
    mark_time_query = True

    try:
        if start_time and end_time:
            # 假设时间格式为 "YYYY-MM-DD HH:MM:SS"

            start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            mark_time_query = HumanRepeatAnswerScore.mark_time.between(start_dt, end_dt)

        elif start_time:

            start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            mark_time_query = HumanRepeatAnswerScore.mark_time >= start_dt
        elif end_time:

            end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            mark_time_query = HumanRepeatAnswerScore.mark_time <= end_dt

    except Exception as e:
        logger.error("查询评分记录起始或者结束时间有误")

    condition = and_(subject_query, round_count_query, ques_code_query, user_query, secret_num_query,
                     mark_time_query)
    return condition


def get_official_task_process(new_session, round_id_list: list, only_marked=False):
    """
    获取管理端任务的进度
    only_marked 为 True 表示只获取已阅量
    """
    process_data = {}
    total_condition = HumanRoundDistriAnswer.round_id.in_(round_id_list)
    marked_condition = and_(total_condition, HumanRoundDistriAnswer.reviewer_id.isnot(None))

    ques_num_info = new_session.query(HumanReadTaskRound.round_id, func.count(ExamQuestion.ques_id).label("ques_id_length")) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .join(ExamQuestion, ExamQuestion.ques_code == HumanReadTask.ques_code) \
        .filter(HumanReadTaskRound.round_id.in_(round_id_list), ExamQuestion.parent_ques_id.isnot(None)) \
        .group_by(HumanReadTaskRound.round_id).all()

    ques_num_dict = {i.round_id: i.ques_id_length for i in ques_num_info}

    # 已阅量
    marked_count_info = new_session.query(
        HumanRoundDistriAnswer.round_id,
        func.count(HumanRoundDistriAnswer.stu_secret_num).label("stu_secret_num_count")
    ) \
        .filter(marked_condition).group_by(HumanRoundDistriAnswer.round_id).all()

    marked_count_dict = {}
    for row in marked_count_info:
        small_ques_num = ques_num_dict.get(row.round_id, 1)
        marked_count_dict[row.round_id] = row.stu_secret_num_count / small_ques_num

    # 阅卷总量
    if not only_marked:
        total_count_info = new_session.query(HumanRoundDistriAnswer.round_id, func.count(HumanRoundDistriAnswer.stu_secret_num.distinct()).label("total_count")) \
            .filter(total_condition).group_by(HumanRoundDistriAnswer.round_id).all()
        total_count_dict = {}
        for row in total_count_info:
            total_count_dict[row.round_id] = row.total_count

        for round_id in round_id_list:
            marked_count, total_count = marked_count_dict.get(round_id, 0), total_count_dict.get(round_id, 0)
            item = {
                "marked_count": marked_count,
                "total_count": total_count,
                "process": round((marked_count / total_count * 100), 2) if total_count else 0
            }
            process_data[round_id] = item
    else:
        for round_id in round_id_list:
            item = {
                "marked_count": marked_count_dict.get(round_id, 0),
            }
            process_data[round_id] = item

    return process_data


def get_my_mark_round_process(new_session, round_id_list: list, only_marked=False):
    """
    获取评阅员任务的进度
    only_marked 为 True 表示只获取已阅量
    """
    process_data = {}
    total_condition = HumanPersonDistriAnswer.round_id.in_(round_id_list)
    marked_condition = and_(total_condition, HumanPersonDistriAnswer.is_answer_marked == 1)

    ques_num_info = new_session.query(HumanReadTaskRound.round_id, func.count(ExamQuestion.ques_id).label("ques_id_length")) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .join(ExamQuestion, ExamQuestion.ques_code == HumanReadTask.ques_code) \
        .filter(HumanReadTaskRound.round_id.in_(round_id_list), ExamQuestion.parent_ques_id.isnot(None)) \
        .group_by(HumanReadTaskRound.round_id).all()

    ques_num_dict = {i.round_id: i.ques_id_length for i in ques_num_info}

    # 已阅量
    marked_count_info = new_session.query(
        HumanPersonDistriAnswer.round_id,
        func.count(HumanPersonDistriAnswer.stu_secret_num).label("stu_secret_num_count")
    ) \
        .filter(marked_condition).group_by(HumanPersonDistriAnswer.round_id).all()

    marked_count_dict = {}
    for row in marked_count_info:
        small_ques_num = ques_num_dict.get(row.round_id, 1)
        marked_count_dict[row.round_id] = row.stu_secret_num_count / small_ques_num

    # 阅卷总量
    if not only_marked:
        total_count_info = new_session.query(HumanRoundDistriAnswer.round_id, func.count(func.distinct(HumanRoundDistriAnswer.stu_secret_num).distinct()).label("total_count")) \
            .filter(total_condition).group_by(HumanRoundDistriAnswer.round_id).all()
        total_count_dict = {}
        for row in total_count_info:
            total_count_dict[row.round_id] = row.total_count

        for round_id in round_id_list:
            marked_count, total_count = marked_count_dict.get(round_id, 0), total_count_dict.get(round_id, 0)
            item = {
                "marked_count": marked_count,
                "total_count": total_count,
                "process": round((marked_count / total_count * 100), 2) if total_count else 0
            }
            process_data[round_id] = item
    else:
        for round_id in round_id_list:
            item = {
                "marked_count": marked_count_dict.get(round_id, 0),
            }
            process_data[round_id] = item

    return process_data


def get_official_task_score(new_session, ques_answer_data: dict[QuesCodeItem, AnswerIdListItem]) -> dict:
    """
    获取正评任务分数
    """
    ques_code_list = list(ques_answer_data.keys())
    ques_round_id_info = new_session.query(HumanReadTask.ques_code, HumanReadTaskRound.round_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .filter(and_(HumanReadTask.task_type == 1, HumanReadTask.ques_code.in_(ques_code_list), HumanReadTaskRound.round_count == 1)) \
        .all()

    if not ques_round_id_info:
        return {}

    result = {}
    for ques_code, round_id in ques_round_id_info:
        answer_id_list = ques_answer_data[ques_code]

        score_info = new_session.query(HumanStudentSubjectGradeDetail.answer_id, HumanStudentSubjectGradeDetail.stu_score, HumanStudentSubjectGradeDetail.stu_score_list) \
            .filter(and_(HumanStudentSubjectGradeDetail.answer_id.in_(answer_id_list))).all()
        if not score_info:
            continue

        answer_id_score = {}
        for answer_id, stu_score, stu_score_list in score_info:
            answer_id_score[answer_id] = {
                "stu_score": stu_score,
                "stu_score_list": stu_score_list
            }

        result[ques_code] = answer_id_score
    return result
