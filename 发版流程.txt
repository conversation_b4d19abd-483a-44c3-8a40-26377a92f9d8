发版流程：
1.修改 settings/settings.py 里的 SYS_VERSION
2.添加项目根目录下的 发版说明.txt，写明该版本更新或者添加了什么功能
3.使用 cmd 激活虚拟环境后切换到项目根目录
4.运行 `pyinstaller 卓帆智能定标阅卷系统V1.0.spec`
5.上面的命令运行完成后会在 dist 文件夹下生成一个 卓帆电子化考试阅卷管理系统V1.0.exe
6.运行 `pyinstaller 定时任务V1.0.spec`
7.上面的命令运行完成后会在 dist 文件夹下生成一个 定时任务V1.0.exe
8.将前端打包的包解压后替换dist/web_root里的前端文件（前端如果没有更新就不需要）
9.将 dist 文件夹复制一份到 back_dist 文件夹进行备份
10.复制到 back_dist 里的dist 文件夹按照 human_read_paper_250901 的格式进行重命名
11.将重命名后的文件夹压缩后发给测试