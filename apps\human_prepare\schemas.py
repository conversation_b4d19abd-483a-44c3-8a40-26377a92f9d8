from typing import Optional, Literal, List, Union
from pydantic import BaseModel, Field, constr, conint
from datetime import datetime

from apps.base.schemas import PaginationModel


class CreateSampleReq(BaseModel):
    # subject_id: str = Field(..., max_length=50, description="科目id")
    # ques_code: str = Field(..., max_length=50, description="试题编号")
    query_conditions: str = Field(..., description="查询条件")
    stu_ids: list[object] = Field(..., description="考生列表")


class GetPrepareStuAnswerByIdReq(BaseModel):
    answer_id_list: list[str] = Field(None, description="作答id列表")

class GetPrepareStuByIdReq(BaseModel):
    stu_id_list: list[str] = Field(None, description="考生id列表")

class GetPrepareStuAnswerBySampleIdReq(BaseModel):
    sample_id: str = Field(..., description="抽样id")


class GetSampleReq(PaginationModel):
    subject_id_list: Optional[list[str]] = Field(None, max_length=50, description="科目id列表")
    exam_mode: Optional[int] = Field(None, description="考试模式，0 表示抽参，1 表示抽卷")


class GetPrepareStuAnswerReq(PaginationModel):
    subject_id: str = Field(..., max_length=50, description="科目id")
    exam_session_list: Optional[list[int]] = Field(None, description="场次列表")
    exam_area_code_list: Optional[list[str]] = Field(None, description="考区编码列表")
    exam_point_code_list: Optional[list[str]] = Field(None, description="考点编码列表")
    exam_room_code_list: Optional[list[str]] = Field(None, description="考场编号列表")
    ques_type_code_list: Optional[list[str]] = Field(None, description="题型编码列表")
    ques_code_list: Optional[list[str]] = Field(None, description="试题编号列表")
    score_list: Optional[list[list[int]]] = Field(None, description="分数条件列表:[[关系，分值]]；关系：0")
    number_list: Optional[list[int]] = Field(None, description="抽取分数")


class GetPrepareStuReq(BaseModel):
    ids_list: Optional[list[list[Union[str,int]]]] = Field(..., description="[id列表,id列表]")
    number: Optional[int] = Field(..., description="抽取分数")

# -------------------------------------------------
# HumanAnswerSample 接口请求模型
# -------------------------------------------------
from pydantic import Field


class GetAnswerSampleListReq(PaginationModel):
    """获取作答样卷列表请求（支持分页并可按科目、题型、题号过滤）"""

    subject_id: Optional[str] = Field(None, description="科目id")
    business_ques_type_id: Optional[str] = Field(None, description="业务题型id")
    ques_code: Optional[str] = Field(None, description="题号")


class CreateAnswerSampleReq(BaseModel):
    """新增作答样卷请求"""

    answer_id: str = Field(..., max_length=50, description="作答id")


class DeleteAnswerSampleReq(BaseModel):
    """删除作答样卷请求"""

    answer_sample_id: Optional[str] = Field(None, max_length=50, description="作答样卷id")
    answer_id: Optional[str] = Field(None, max_length=50, description="作答id")


class CreateExceptionItem(BaseModel):
    answer_id: Optional[str] = Field(None, max_length=50, description="作答id")
    exception_type: int = Field(..., description="异常类型：0为无问题，1 为图像错误，2 为作答位置错误，3 为作答合并，4 为其他")


class DeleteAnswerReq(BaseModel):
    task_id: str = Field(..., max_length=50, description="任务id")
    round_id: str = Field(..., max_length=50, description="轮次id")
    exception_data: List[CreateExceptionItem] = Field(..., description="错误信息列表")
    is_delete_all: Literal[0, 1] = Field(..., description="0 为否，1 为是")


class CreateExceptionReq(BaseModel):
    task_id: str = Field(..., max_length=50, description="任务id")
    round_id: str = Field(..., max_length=50, description="轮次id")
    exception_data: List[CreateExceptionItem] = Field(..., description="错误信息列表")


class AddPrepareProcessTestDataOneReq(BaseModel):
    """为指定流程类型添加一条测试流程"""

    process_type: int = Field(..., description="流程类型，取值 1-5")


class CreatePrepareProcessReq(BaseModel):
    """创建准备流程请求"""

    process_name: str = Field(..., max_length=100, description="流程名称")
    process_type: int = Field(..., description="流程类型：1 考生导入；2 作答信息导入；3 作答抄袭识别；4 雷同卷识别；5 抄袭题干识别")
    parent_process_type: int = Field(..., description="父流程类型")
    fail_reason: str | None = Field(None, max_length=255, description="流程失败原因")
    progress: int = Field(0, description="流程进行中的进度（0-100）")
