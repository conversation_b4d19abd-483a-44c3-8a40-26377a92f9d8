from fastapi import APIRouter, Depends

from apps.base.schemas import BaseResponse
from bs4 import BeautifulSoup
from collections import defaultdict
from sqlalchemy.orm import Session

from apps.human_mark_accept.schemas import GetMarkAcceptReq, EditScoreThresholdReq
from apps.human_task_manage.models import HumanRoundDistriAnswer
from apps.models.models import StuAnswer, PaperDetail, ExamPaper
from apps.ques_manage.schemas import GetAdjoinQuesDetailReq
from apps.users.services import get_current_user
from helper.quest_retrieve import text_same_two
from factory_apps import session_depend
from typing import Any

from settings import logger

mark_accept_router = APIRouter()

@mark_accept_router.post(path="/get_answered_no_score", response_model=BaseResponse, summary="获取有作答没分数的列表")
async def get_answered_no_score(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    try:
        # 联表查询有作答且未评分的记录
        logger.info(f"{user['username']}获取有作答没分数的列表")
        query = new_session.query(HumanRoundDistriAnswer.round_score, StuAnswer.is_do,StuAnswer.ques_id,ExamPaper.paper_name,HumanRoundDistriAnswer.stu_secret_num).join(
            StuAnswer, HumanRoundDistriAnswer.answer_id == StuAnswer.answer_id,ExamPaper.paper_id == StuAnswer.paper_id
        ).filter(
            StuAnswer.is_do == 1,
            HumanRoundDistriAnswer.round_score.is_(None),
        )
        
        # 执行查询并提取结果
        results = query.all()
        answered_list = [{
            "stu_secret_num": item.stu_secret_num,
            "paper_name": item.paper_name,
            "ques_id": item.ques_id,
            "is_do": item.is_do,
            "round_score": item.rount_score
        } for item in results]
        logger.info(f"{user['username']}获取有作答没分数的列表成功")
        return BaseResponse(data=answered_list)
    except Exception as e:
        # 记录异常日志
        logger.info(f"{user['username']}获取有作答没分数的列表失败",e)
        return BaseResponse(code=500, msg=f"获取数据异常: {str(e)}")


@mark_accept_router.post(path="/get_mark_accept_list", response_model=BaseResponse, summary="获取阅卷验收列表")
async def get_mark_accept_list(query: GetMarkAcceptReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']}获取阅卷验收的列表")
    #获取参考人数，判断抽取数量


@mark_accept_router.post(path="/edit_score_threshold", response_model=BaseResponse, summary="编辑分差阈值")
async def edit_score_threshold(query: EditScoreThresholdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']}编辑分差阈值")


@mark_accept_router.post(path="/extract_accept_paper", response_model=BaseResponse, summary="抽取验收试卷")
async def extract_accept_paper(query: EditScoreThresholdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']}抽取验收试卷")
