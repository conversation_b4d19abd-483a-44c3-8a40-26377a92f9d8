from pydantic import BaseModel, Field
from typing import Optional, Literal
from apps.base.schemas import BaseResponse
from typing import Any, List, Dict
from apps.base.schemas import PaginationModel


class GetAnswerPlagiarismListReq(PaginationModel):
    """请求参数：查询作答抄袭列表"""

    subject_id: Optional[str] = Field(None, description="科目ID")
    paper_id: Optional[str] = Field(None, description="试卷ID")
    ques_number: Optional[int] = Field(None, description="题号（题序）")
    ques_code: Optional[str] = Field(None, description="试题编号")
    stu_secret_num: Optional[str] = Field(None, description="考生密号")
    judgement_status: Optional[int] = Field(None, description="判定状态")
    op: Optional[int] = Field(None, description="相似度比较：0 等于；1 大于等于；2 大于；3 小于；4 小于等于；5 不等于；6 区间")
    similarity: Optional[float] = Field(None, description="相似度阈值")
    judgement_result: Optional[int] = Field(None, description="判定结果（是否抄袭）")

class GetExceptionListReq(PaginationModel):
    """请求参数：查询作答抄袭列表"""
    project_id: Optional[str] = Field(None, description="资格ID")
    subject_id: Optional[str] = Field(None, description="科目ID")
    ques_code: Optional[str] = Field(None, description="试题编号")
    task_type: Optional[int] = Field(None, description="任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务")
    handler_state: Optional[int] = Field(None, description="处理状态：0 为处理；1 已处理")
    is_show_self: Literal[0, 1] = Field(1, description="是否只展示自己处理的问题卷的数据，0 否，1 是")


class UpdateJudgementResult(BaseModel):
    answer_quest_similarity_id: str = Field(..., description="作答题干相似度id")
    judgement_result: int = Field(..., description="判定结果（是否抄袭）:1 抄袭；2 未抄袭")

class UpdateExceptionResult(BaseModel):
    answer_exception_id: str = Field(..., description="作答题干相似度id")
    handler_state: int = Field(..., description="处理状态：0 为处理；1 已处理")

class SimilarityResult(BaseModel):
    answer_id: str
    question_text: str
    answer_text: str
    similarity_score: float


class SimilarityResponse(BaseResponse):
    data: List[SimilarityResult]


class GetNoHandleExceptionListReq(BaseModel):
    stu_secret_num: Optional[str] = Field(None, description="考生密号")


class ReviewerMarkReq(BaseModel):
    answer_exception_id: str = Field(..., description="问题卷作答id")
    answer_id: str = Field(..., description="作答id")
    mark_point_score_list: list = Field([], description="评分标准得分")
    mark_score: float = Field(..., description="评分分数")


class HandleExceptionAnswerReq(BaseModel):
    stu_secret_num: str = Field(..., description="考生密号")
    task_id: str = Field(..., description="任务id")
    round_id: str = Field(..., description="轮次id")
    ques_code: str = Field(..., description="试题编号")
    mark_info: List[ReviewerMarkReq] = Field(..., description="评分数据")
