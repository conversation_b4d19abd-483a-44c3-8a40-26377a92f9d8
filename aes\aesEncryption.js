const CryptoJS = require("crypto-js");
const aseKey = 'cw28e80g57re4gs3'  // 秘钥必须为：8/16/32位


// AES/DES加解密方式
//加密
const encryptPassword = (message) => {
    const encrypt = CryptoJS.AES.encrypt(message, CryptoJS.enc.Utf8.parse(aseKey), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    }).toString();
    console.log(`${message} encrypt is ${encrypt}`);
    return encrypt;
}

//解密
const decryptPassword = (encrypt) => {
    const decrypt = CryptoJS.AES.decrypt(encrypt, CryptoJS.enc.Utf8.parse(aseKey), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    }).toString(CryptoJS.enc.Utf8);
    console.log('decrypt', decrypt);
    console.log(`${encrypt} decrypt is ${decrypt}`);
    return decrypt;
}


encryptPassword('123456')
// decryptPassword('cr2NLNxwvb3KAF37jkAf1A==')
