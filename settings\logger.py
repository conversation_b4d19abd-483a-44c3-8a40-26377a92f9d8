import datetime
import logging
import os

import colorama
from concurrent_log_handler import ConcurrentRotatingFileHandler


# 定义颜色
COLORS = {
    'DEBUG': colorama.Fore.CYAN,
    'INFO': colorama.Fore.GREEN,
    'WARNING': colorama.Fore.YELLOW,
    'ERROR': colorama.Fore.RED,
    'CRITICAL': colorama.Fore.RED + colorama.Style.BRIGHT,
}


# 自定义带颜色的日志格式化器
class ColoredFormatter(logging.Formatter):
    def format(self, record):
        log_message = super().format(record)
        log_level = record.levelname
        if log_level in COLORS:
            return f"{COLORS[log_level]}{log_message}{colorama.Style.RESET_ALL}"
        return log_message


def get_logger():
    # 初始化 colorama
    colorama.init(strip=False)

    # 配置日志信息
    project_path = os.path.abspath(".")
    log_path = os.path.join(project_path, "logs")
    os.makedirs(log_path, exist_ok=True)

    logger = logging.getLogger("my_logger")
    logger.setLevel(logging.DEBUG)

    console_formatter = ColoredFormatter(
        fmt='%(asctime)s.%(msecs)03d | %(levelname)s | %(module)s.%(funcName)s:%(lineno)d - %(message)s',
        datefmt="%Y-%m-%d %H:%M:%S"
    )

    # 设置控制台输出日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(console_formatter)

    # 设置日志文件处理器，使用 ConcurrentRotatingFileHandler 可以实现多进程控制日志文件大小
    logs_file_path = os.path.join(log_path, f"{datetime.datetime.now().strftime('%Y-%m-%d')}.log")
    file_handler = ConcurrentRotatingFileHandler(
        filename=logs_file_path,
        mode="a",  # 追加模式
        maxBytes=200 * 1024 * 1024,  # 日志文件最大 200 MB，超过将备份日志文件并新建日志文件进行存储
        backupCount=50,  # 保留备份文件数
        encoding="utf-8"
    )

    file_formatter = logging.Formatter(
        fmt='%(asctime)s.%(msecs)03d | %(levelname)s | %(module)s.%(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    file_handler.setFormatter(file_formatter)
    console_handler.setLevel(logging.DEBUG)

    # 将处理器添加到日志记录器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger
