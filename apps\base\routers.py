from fastapi import APIRouter

from apps.data_transfer.export_views import export_router
# from apps.data_transfer.export_views import export_router
from apps.human_mark_quality.views import human_quality_router
from apps.human_statistics.views import human_statistics_router
from apps.operation_ques.slave_op_engine_mark_views import salve_op_mark_router

from apps.users.views import user_router
from apps.read_paper.views import common_router
from apps.read_paper.mark_rule_views import mark_rule_router
from apps.read_paper.exam_paper_views import paper_router
from apps.read_paper.exam_ques_views import ques_router
from apps.read_paper.stu_answer_views import stu_answer_router
from apps.projects_manage.views import project_router
from apps.read_paper.ques_type_views import ques_type_router
from apps.read_paper.read_paper_views import read_paper_router
from apps.grade_manage.stu_grade_views import grade_manage_router
from apps.read_paper.exam_stu_views import exam_stu_router
from apps.permission.role_views import role_router
from apps.sys_manage.views import sys_module_router
from apps.permission.user_module_views import user_module_router
from apps.manual_read_paper.manage_people_views import manual_people_router
from apps.manual_read_paper.manual_group_views import manual_group_router
from apps.permission.user_data_views import user_data_router
from apps.manual_read_paper.manual_work_flow_views import work_flow_router
from apps.manual_read_paper.manual_read_task_views import manual_task_router
from apps.manual_read_paper.manual_read_views import manual_read_router
from apps.manual_read_paper.munual_ai_read_views import manual_ai_read_router
from apps.data_statistics.expert_data_views import expert_data_router
from apps.data_statistics.arbitrate_data_views import arbitrate_data_router
from apps.data_statistics.quality_data_views import quality_data_router
from apps.human_statistics.score_analysis_views import score_analysis_router
from apps.human_statistics.g_score_analysis_views import g_score_analysis_router
from apps.human_statistics.q_score_analysis_views import q_score_analysis_router
from apps.human_statistics.p_score_analysis_views import p_score_analysis_router
from apps.human_statistics.survey_monitor_views import survey_monitor_router
from apps.operation_ques.op_manual_mark_views import op_manual_mark_router
from apps.operation_ques.op_engine_mark_views import op_engine_mark_router
from apps.ai_set_std.set_std_views import set_std_router
from apps.set_std_check.views import set_std_check_router
from apps.ai_mark.views import ai_mark_router
from apps.ques_manage.views import ques_manage_router
from apps.base.views import base_router
from apps.data_transfer.views import data_transfer_router
from apps.data_transfer.standard_views import standard_transfer_router
from apps.human_mark_group.views import human_group_router
from apps.human_task_manage.views import human_task_router
from apps.logs.views import logs_router
from apps.human_prepare.views import prepare_router
from apps.human_official_mark.views import human_mark_router
from apps.human_mark_exception.views import exception_router
from apps.human_mark_accept.views import mark_accept_router
from apps.human_try_mark.views import human_try_mark_router
from apps.human_repeat_mark.views import repeat_router
from apps.service_monitor.views import service_monitor_router
from apps.paper_verification.views import paper_verification_router


version_router = APIRouter()
version_router.include_router(router=salve_op_mark_router, prefix="/salve_op")
version_router.include_router(router=base_router, prefix="/base")
version_router.include_router(router=user_router, prefix="/user")
version_router.include_router(router=common_router, prefix="/common")
version_router.include_router(router=mark_rule_router, prefix="/mark_rule")
version_router.include_router(router=paper_router, prefix="/paper")
version_router.include_router(router=ques_router, prefix="/ques")
version_router.include_router(router=stu_answer_router, prefix="/stu_answer")
version_router.include_router(router=project_router, prefix="/project")
version_router.include_router(router=ques_type_router, prefix="/ques_type")
version_router.include_router(router=read_paper_router, prefix="/read_paper")
version_router.include_router(router=grade_manage_router, prefix="/grade_manage")
version_router.include_router(router=exam_stu_router, prefix="/exam_stu")
version_router.include_router(router=role_router, prefix="/role")
version_router.include_router(router=sys_module_router, prefix="/sys_module")
version_router.include_router(router=user_module_router, prefix="/user_module")
version_router.include_router(router=manual_people_router, prefix="/manual_people")
version_router.include_router(router=manual_group_router, prefix="/manual_group")
version_router.include_router(router=user_data_router, prefix="/user_data")
version_router.include_router(router=work_flow_router, prefix="/work_flow")
version_router.include_router(router=manual_task_router, prefix="/manual_task")
version_router.include_router(router=manual_read_router, prefix="/manual_read")
version_router.include_router(router=manual_ai_read_router, prefix="/manual_ai_read")
version_router.include_router(router=expert_data_router, prefix="/expert_data")
version_router.include_router(router=arbitrate_data_router, prefix="/arbitrate_data")
version_router.include_router(router=quality_data_router, prefix="/quality_data")
version_router.include_router(router=score_analysis_router, prefix="/score_analysis")
version_router.include_router(router=g_score_analysis_router, prefix="/g_score_analysis")
version_router.include_router(router=q_score_analysis_router, prefix="/q_score_analysis")
version_router.include_router(router=p_score_analysis_router, prefix="/p_score_analysis")
version_router.include_router(router=survey_monitor_router, prefix="/survey_monitor")
version_router.include_router(router=op_manual_mark_router, prefix="/op")
version_router.include_router(router=op_engine_mark_router, prefix="/op_mark")
version_router.include_router(router=set_std_router, prefix="/set_std")
version_router.include_router(router=set_std_check_router, prefix="/std_check")
version_router.include_router(router=ai_mark_router, prefix="/ai_mark")
version_router.include_router(router=ques_manage_router, prefix="/ques_manage")
version_router.include_router(router=data_transfer_router, prefix="/transfer")
version_router.include_router(router=standard_transfer_router, prefix="/std_transfer")
version_router.include_router(router=human_group_router, prefix="/human_group")
version_router.include_router(router=human_task_router, prefix="/official_mark")  # official_mark，任务管理路由，前端优化路由管理时再改名
version_router.include_router(router=logs_router, prefix="/logs")
version_router.include_router(router=prepare_router, prefix="/prepare")
version_router.include_router(router=human_mark_router, prefix="/human_mark")
version_router.include_router(router=exception_router, prefix="/exception")
version_router.include_router(router=mark_accept_router, prefix="/mark_accept")
version_router.include_router(router=human_try_mark_router, prefix="/try_mark")
version_router.include_router(router=repeat_router, prefix="/repeat_mark")
version_router.include_router(router=human_statistics_router, prefix="/human_statistics")
version_router.include_router(router=service_monitor_router, prefix="/service_monitor")
version_router.include_router(router=human_quality_router, prefix="/human_quality")
version_router.include_router(router=paper_verification_router, prefix="/paper_verification")
version_router.include_router(router=export_router, prefix="/export")