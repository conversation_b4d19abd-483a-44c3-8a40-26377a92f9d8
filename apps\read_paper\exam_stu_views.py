import io
import time
import traceback

from fastapi import APIRouter, UploadFile, File, Depends, Form
from sqlalchemy import exists, select, and_, func, or_
from sqlalchemy.orm import Session
import openpyxl
from settings import logger
from typing import Any
from redis import asyncio as redis

from apps.base.services import delete_keys_with_prefix
from apps.read_paper import GetStuReq, UpdateStuReq, CreateStuReq, ImportProgressReq, GetStuWithAllSubjectReq, GetExamPlaceReq
from apps.read_paper.common_services import student_query_condition, get_project_subject_info, check_project_subject_exist, update_stu_subject, get_saved_stu_info, del_excel_empty_row, get_user_data_flag
from apps.users.services import get_current_user
from apps.models.models import ExamStudent, UserInfo, Subject, Project, StudentSubject, ImportDataRecord, SchoolInfo, Room, SysOrganizationCode
from factory_apps import session_depend, redis_session
from apps.base.schemas import BaseResponse
from helper import response_utils
from settings import configs
from utils.utils import round_half_up

exam_stu_router = APIRouter()


@exam_stu_router.post(path="/check_stu_exist", response_model=BaseResponse, summary="检查考生信息是否存在")
async def check_stu_exist(file: UploadFile = File(...), user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 检查考生信息是否存在")
    if not file.filename.endswith(".xlsx"):
        return BaseResponse(code=response_utils.params_error, msg="请上传 .xlsx 格式的文件")

    content = await file.read()
    wb = openpyxl.load_workbook(filename=io.BytesIO(content), read_only=True)
    sheet = wb.worksheets[0]

    all_rows = sheet.iter_rows(min_col=0, max_col=0, values_only=True)

    allow_exam_num_list = [row[0] for row in all_rows][1:]

    if len(allow_exam_num_list) <= 1:
        return BaseResponse(code=response_utils.params_error, msg="导入内容不能为空")

    # 创建导入考生数据
    record_id = configs.snow_worker.get_id()
    new_record = ImportDataRecord(record_id=record_id, record_type=2, progress=0, c_user_id=user.get("user_id"))
    new_session.add(new_record)
    new_session.commit()

    if new_session.query(exists().where(ExamStudent.allow_exam_num.in_(allow_exam_num_list))).scalar():
        return BaseResponse(code=response_utils.fields_exist, msg="考生信息已存在，确定继续导入吗？", data={"data": {"record_id": record_id}})

    return BaseResponse(msg="考生信息不存在", data={"data": {"record_id": record_id}})


@exam_stu_router.post(path="/import_stu", response_model=BaseResponse, summary="导入考生信息")
async def import_stu(record_id: str = Form(..., title="进度条记录id"), file: UploadFile = File(...), user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend), redis: redis = Depends(redis_session)):
    start = time.time()
    logger.info(f"{user['username']} 导入考生信息")
    curr_user_id = user.get("user_id")

    if not file.filename.endswith(".xlsx"):
        return BaseResponse(code=response_utils.params_error, msg="请上传 .xlsx 格式的文件")

    content = await file.read()
    wb = openpyxl.load_workbook(filename=io.BytesIO(content), read_only=True)
    sheet = wb.worksheets[0]

    new_sheet, all_rows_length = del_excel_empty_row(sheet)
    a_batch_num = configs.EXAM_STU_BATCH_NUM
    batch_count = (int(all_rows_length / a_batch_num) + 1) if (all_rows_length % a_batch_num != 0) else (all_rows_length / a_batch_num)  # 总共分成多少批

    pro_sub_dict = get_project_subject_info(new_session)

    if not pro_sub_dict:
        return BaseResponse(code=response_utils.params_error, msg="系统暂无项目和科目，请新建项目和科目")
    msg, project_id_list = check_project_subject_exist(new_sheet, pro_sub_dict)
    if msg:
        return BaseResponse(code=response_utils.params_error, msg=msg)

    # 获取数据库相同项目的考生信息
    result, stu_info_dict = get_saved_stu_info(project_id_list, user)
    if not result:
        return BaseResponse(code=response_utils.params_error, msg=msg)

    insert_data = []
    try:
        count = 0
        saved_batch_count = 0
        for row in new_sheet.iter_rows(values_only=True):
            count += 1
            if count == 1:
                continue
            if not any(row):
                continue

            allow_exam_num, project_name, subject_name_str, exam_province_code, exam_province_name, exam_point_code, exam_point_name, exam_room_code, exam_room_name = (row[0], row[1], row[2], row[3], row[4], row[5], row[6], row[7], row[8])

            if not allow_exam_num:
                return BaseResponse(code=response_utils.params_error, msg="考生准考证号不能为空")
            allow_exam_num = str(allow_exam_num)

            subject_name_list = subject_name_str.split(configs.NEW_SPLIT_FLAG)

            project_id = pro_sub_dict[project_name]["project_id"]
            if stu_info_dict.get(project_id):
                stu_info_item = stu_info_dict[project_id].get(allow_exam_num)
            else:
                stu_info_dict[project_id] = {}
                stu_info_item = None
            if not stu_info_item:
                stu_id = configs.snow_worker.get_id()
                stu_item = ExamStudent(
                    stu_id=stu_id,
                    allow_exam_num=allow_exam_num,
                    project_id=project_id,
                    exam_province_code=exam_province_code,
                    exam_province_name=exam_province_name,
                    exam_point_code=exam_point_code,
                    exam_point_name=exam_point_name,
                    exam_room_code=exam_room_code,
                    exam_room_name=exam_room_name,
                    stu_secret_num=stu_id,
                    c_user_id=curr_user_id,
                )
                insert_data.append(stu_item)
                stu_info_dict[project_id][allow_exam_num] = {
                    "stu_id": stu_id,
                    "allow_exam_num": allow_exam_num,
                    "project_id": project_id,
                    "exam_province_code": exam_province_code,
                    "exam_province_name": exam_province_name,
                    "exam_point_code": exam_point_code,
                    "exam_point_name": exam_point_name,
                    "exam_room_code": exam_room_code,
                    "exam_room_name": exam_room_name,
                    "stu_secret_num": stu_id,
                    "c_user_id": curr_user_id,
                }
            else:
                stu_id = stu_info_item["stu_id"]
                if (
                    stu_info_item["exam_province_code"] != exam_province_code
                    or stu_info_item["exam_province_name"] != exam_province_name
                    or stu_info_item["exam_point_code"] != exam_point_code
                    or stu_info_item["exam_point_name"] != exam_point_name
                    or stu_info_item["exam_room_code"] != exam_room_code
                    or stu_info_item["exam_room_name"] != exam_room_name
                ):
                    new_session.query(ExamStudent).filter(ExamStudent.stu_id == stu_id).update(
                        {
                            ExamStudent.exam_province_code: exam_province_code,
                            ExamStudent.exam_province_name: exam_province_name,
                            ExamStudent.exam_point_code: exam_point_code,
                            ExamStudent.exam_point_name: exam_point_name,
                            ExamStudent.exam_room_code: exam_room_code,
                            ExamStudent.exam_room_name: exam_room_name,
                            ExamStudent.u_user_id: curr_user_id,
                        }
                    )

            for subject_name in subject_name_list:
                subject_id = pro_sub_dict[project_name]["subject"][subject_name]
                saved_subject_id = stu_info_dict[project_id][allow_exam_num].get("subject_id")
                stu_subject = True if (saved_subject_id and subject_id in saved_subject_id) else False
                if not stu_subject:
                    stu_subject_item = StudentSubject(stu_subject_id=configs.snow_worker.get_id(), allow_exam_num=allow_exam_num, subject_id=subject_id, c_user_id=curr_user_id)
                    insert_data.append(stu_subject_item)
                    if saved_subject_id:
                        stu_info_dict[project_id][allow_exam_num]["subject_id"].append(subject_id)
                    else:
                        stu_info_dict[project_id][allow_exam_num]["subject_id"] = [subject_id]
            if count % a_batch_num == 0 or count == all_rows_length:
                new_session.add_all(insert_data)
                new_session.commit()
                new_session.flush()
                saved_batch_count += 1
                percent = round_half_up(saved_batch_count / batch_count * 100, 2)
                logger.info(f"考生信息导入进度 {percent}%")
                # 更新数据库记录的进度
                new_session.query(ImportDataRecord).filter(and_(ImportDataRecord.record_id == record_id, ImportDataRecord.record_type == 2)).update({ImportDataRecord.progress: percent, ImportDataRecord.u_user_id: curr_user_id})
                new_session.commit()
                insert_data = []
    except Exception as e:
        new_session.rollback()
        logger.error(f"导入考生信息失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.params_error, msg=f"导入考生信息失败")
    # redis 删除以 stu_count 开头的键
    delete_keys_with_prefix(redis, "stu_count")
    logger.info(f"导入考生信息成功，导入耗时：{round_half_up(float(time.time() - start), 2)} 秒")
    return BaseResponse(msg=f"导入考生信息成功")


@exam_stu_router.post(path="/get_import_stu_progress", response_model=BaseResponse, summary="获取导入考生信息进度")
async def get_import_stu_progress(query: ImportProgressReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取导入考生信息进度")
    record_id = query.record_id
    progress = new_session.query(ImportDataRecord.progress).filter(and_(ImportDataRecord.record_id == record_id, ImportDataRecord.record_type == 2)).scalar()
    data = {"data": {"progress": progress}}
    return BaseResponse(msg="获取导入考生信息进度成功", data=data)


@exam_stu_router.post(path="/create_stu", response_model=BaseResponse, summary="创建考生")
async def create_stu(query: CreateStuReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建考生")

    (
        allow_exam_num,
        project_id,
        subject_id_list,
        exam_province_code,
        exam_province_name,
        exam_city_code,
        exam_city_name,
        exam_point_code,
        exam_point_name,
        exam_room_code,
        exam_room_name,
        stu_name,
        cert_number,
        project_code,
        subject_id,
        subject_code,
        exam_session,
        is_violation,
        is_exam,
    ) = query.model_dump().values()
    is_exist = new_session.query(exists().where(ExamStudent.project_id == project_id, ExamStudent.allow_exam_num == allow_exam_num)).scalar()

    if is_exist:
        return BaseResponse(code=response_utils.fields_exist, msg=f"该项目下该准考证号 {allow_exam_num} 已存在")

    try:
        new_stu = ExamStudent(
            stu_id=configs.snow_worker.get_id(),
            stu_name=stu_name,
            allow_exam_num=allow_exam_num,
            stu_secret_num=configs.snow_worker.get_id(),
            cert_number=cert_number,
            project_id=project_id,
            project_code=project_code,
            subject_id=subject_id if subject_id else (subject_id_list[0] if subject_id_list else None),
            subject_code=subject_code,
            exam_province_code=exam_province_code,
            exam_province_name=exam_province_name,
            exam_city_code=exam_city_code,
            exam_city_name=exam_city_name,
            exam_session=exam_session,
            exam_point_code=exam_point_code,
            exam_point_name=exam_point_name,
            exam_room_code=exam_room_code,
            exam_room_name=exam_room_name,
            is_violation=is_violation,
            is_exam=is_exam,
            c_user_id=user["user_id"],
            u_user_id=user["user_id"],
        )
        new_session.add(new_stu)

        new_stu_subject_list = []
        if not subject_id_list:
            return BaseResponse(code=response_utils.params_error, msg=f"科目不能为空")
        for subject_id in subject_id_list:
            new_stu_subject = StudentSubject(stu_subject_id=configs.snow_worker.get_id(), allow_exam_num=allow_exam_num, subject_id=subject_id, c_user_id=user["user_id"])
            new_stu_subject_list.append(new_stu_subject)
        new_session.add_all(new_stu_subject_list)
        new_session.commit()
        logger.info("创建考生成功")
        # redis 删除以 stu_count 开头的键
        delete_keys_with_prefix(redis, "stu_count")
        return BaseResponse(msg=f"创建考生成功")
    except Exception as e:
        logger.error(f"创建考生失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"创建考生失败")


@exam_stu_router.post(path="/get_exam_info", response_model=BaseResponse, summary="获取考生的考区，考点，考场")
async def get_exam_info(query: GetExamPlaceReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考生的考区，考点，考场")
    project_id, subject_id, place_type, code = query.model_dump().values()

    condition = and_(ExamStudent.project_id == project_id, ExamStudent.subject_id == subject_id)
    province_condition = ExamStudent.exam_province_code == code if code else True
    city_condition = ExamStudent.exam_city_code == code if code else True
    point_condition = ExamStudent.exam_point_code == code if code else True

    if place_type == 1:
        exam_info = new_session.query(ExamStudent.exam_province_code, ExamStudent.exam_province_name, ExamStudent.exam_city_code, ExamStudent.exam_city_name) \
            .filter(condition).distinct().all()
        data = []
        province_map = {}
        for p_code, p_name, c_code, c_name in exam_info:
            if p_code not in province_map:
                province_node = {"code": p_code, "name": p_name or p_code, "children": []}
                province_map[p_code] = province_node
                data.append(province_node)
            children = province_map[p_code]["children"]
            if not any(child["code"] == c_code for child in children):
                children.append({"code": c_code, "name": c_name or c_code})

    elif place_type == 2:
        exam_info = new_session.query(ExamStudent.exam_point_code, ExamStudent.exam_point_name).filter(and_(condition, or_(province_condition, city_condition))).group_by(ExamStudent.exam_point_code, ExamStudent.exam_point_name).all()
        data = [{"exam_point_code": exam_point_code, "exam_point_name": exam_point_name if exam_point_name else exam_point_code} for exam_point_code, exam_point_name in exam_info] if exam_info else []
    else:
        exam_info = new_session.query(ExamStudent.exam_room_code, ExamStudent.exam_room_name).filter(and_(condition, or_(province_condition, city_condition, point_condition))).group_by(ExamStudent.exam_room_code, ExamStudent.exam_room_name).all()
        data = [{"exam_room_code": exam_room_code, "exam_room_name": exam_room_name if exam_room_name else exam_room_code} for exam_room_code, exam_room_name in exam_info] if exam_info else []

    return BaseResponse(data=data, msg="获取成功")


@exam_stu_router.post(path="/get_stu", response_model=BaseResponse, summary="获取考生信息列表")
async def get_stu(query: GetStuReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考生信息列表")
    current_page, page_size, allow_exam_num, project_id, subject_id, exam_province_code, exam_city_code, exam_point_code, exam_room_code = query.model_dump().values()
    project_condition, subject_condition = get_user_data_flag(new_session, user)
    stu_data = []
    limit = current_page - 1
    offset = limit * page_size
    try:
        select_fields = [
            ExamStudent.stu_id,
            ExamStudent.allow_exam_num,
            ExamStudent.exam_province_code,
            ExamStudent.exam_province_name,
            ExamStudent.exam_city_code,
            ExamStudent.exam_city_name,
            ExamStudent.exam_point_code,
            ExamStudent.exam_point_name,
            ExamStudent.exam_room_code,
            ExamStudent.exam_room_name,
            ExamStudent.project_id,
            Project.project_name,
            StudentSubject.subject_id,
            Subject.subject_name,
            ExamStudent.created_time,
            ExamStudent.updated_time,
            UserInfo.username,
            ExamStudent.lock_state,
            ExamStudent.from_tool,
        ]

        # 拼凑查询条件
        condition = and_(student_query_condition(project_id, subject_id, allow_exam_num, exam_province_code, exam_city_code, exam_point_code, exam_room_code), project_condition, subject_condition)

        total = new_session.query(ExamStudent.stu_id).join(Project, ExamStudent.project_id == Project.project_id).join(StudentSubject, StudentSubject.allow_exam_num == ExamStudent.allow_exam_num).join(Subject, StudentSubject.subject_id == Subject.subject_id).where(condition).count()

        stu_stmt = (
            select(*select_fields)
            .join(UserInfo, ExamStudent.c_user_id == UserInfo.user_id)
            .join(Project, ExamStudent.project_id == Project.project_id)
            .join(StudentSubject, StudentSubject.allow_exam_num == ExamStudent.allow_exam_num)
            .join(Subject, StudentSubject.subject_id == Subject.subject_id)
            .where(condition)
            .order_by(ExamStudent.created_time.desc(), ExamStudent.stu_id)
            .limit(page_size)
            .offset(offset)
        )

        result = new_session.execute(stu_stmt)
        stu_data = [dict(row._mapping) for row in result]

        # for row in result:
        #     stu_item = {
        #         "stu_id": row.stu_id,
        #         "allow_exam_num": row.allow_exam_num,
        #         "exam_province_code": row.exam_province_code,
        #         "exam_province_name": row.exam_province_name,
        #         "exam_point_code": row.exam_point_code,
        #         "exam_point_name": row.exam_point_name,
        #         "exam_room_code": row.exam_room_code,
        #         "exam_room_name": row.exam_room_name,
        #         "project_id": row.project_id,
        #         "project_name": row.project_name,
        #         "subject_id": row.subject_id,
        #         "subject_name": row.subject_name,
        #         "created_time": row.created_time and str(row.created_time).replace("T", " "),
        #         "updated_time": row.updated_time and str(row.updated_time).replace("T", " "),
        #         "c_user_name": row.username,
        #         "lock_state": row.lock_state,
        #         "from_tool": row.from_tool,
        #     }
        #     stu_data.append(stu_item)
    except Exception as e:
        logger.error(f"获取考生信息列表失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取考生信息列表失败")
    logger.info("获取考生信息列表成功")
    data = {"data": stu_data, "total": total}
    return BaseResponse(msg="获取考生信息列表成功", data=data)


@exam_stu_router.post(path="/update_stu", response_model=BaseResponse, summary="编辑考生信息")
async def update_stu(query: UpdateStuReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    (stu_id, allow_exam_num, project_id, subject_id_list, exam_province_code, exam_province_name, exam_point_code, exam_point_name, exam_room_code, exam_room_name) = query.model_dump().values()
    logger.info(f"{user['username']} 编辑考生信息，考生 id 为 {stu_id}")
    from_tool = new_session.query(ExamStudent.from_tool).filter(ExamStudent.stu_id == stu_id).scalar()
    if from_tool is None:
        return BaseResponse(code=response_utils.no_field, msg="没有该考生")
    else:
        if from_tool == 1:
            return BaseResponse(code=response_utils.permission_deny, msg="该考生数据由导入工具导入，不允许编辑")

    try:
        new_session.query(ExamStudent).filter(ExamStudent.stu_id == stu_id).update(
            {
                ExamStudent.exam_province_code: exam_province_code,
                ExamStudent.exam_province_name: exam_province_name,
                ExamStudent.exam_point_code: exam_point_code,
                ExamStudent.exam_point_name: exam_point_name,
                ExamStudent.exam_room_code: exam_room_code,
                ExamStudent.exam_room_name: exam_room_name,
                ExamStudent.u_user_id: user.get("user_id"),
            }
        )
        subject_info = new_session.query(StudentSubject.subject_id).join(ExamStudent, ExamStudent.allow_exam_num == StudentSubject.allow_exam_num).filter(and_(StudentSubject.allow_exam_num == allow_exam_num, ExamStudent.project_id == project_id)).all()
        raw_subject_id_list = [i[0] for i in subject_info]
        result, msg = update_stu_subject(new_session, raw_subject_id_list, subject_id_list, allow_exam_num, user.get("user_id"))
        if not result:
            logger.error(msg)
            return BaseResponse(code=response_utils.server_error, msg=msg)
        logger.info(msg)
    except Exception as e:
        new_session.rollback()
        logger.error(f"编辑考生信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"编辑考生信息失败")
    new_session.commit()
    logger.info("编辑考生信息成功")
    return BaseResponse(msg="编辑考生信息成功")


@exam_stu_router.post(path="/get_stu_with_all_subject", response_model=BaseResponse, summary="获取带所有科目的考生信息列表")
async def get_stu(query: GetStuWithAllSubjectReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    project_id_list = query.project_id_list
    logger.info(f"{user['username']} 获取带所有科目的考生信息列表")
    stu_data = []
    try:
        select_fields = [
            ExamStudent.stu_id,
            ExamStudent.allow_exam_num,
            ExamStudent.exam_province_code,
            ExamStudent.exam_province_name,
            SysOrganizationCode.organizationName,
            ExamStudent.exam_point_code,
            ExamStudent.exam_point_name,
            SchoolInfo.schooName,
            ExamStudent.exam_room_code,
            ExamStudent.exam_room_name,
            Room.roomName,
            ExamStudent.project_id,
            Project.project_name,
            func.group_concat(StudentSubject.subject_id),
            func.group_concat(Subject.subject_name),
            ExamStudent.created_time,
            ExamStudent.updated_time,
            UserInfo.username,
            ExamStudent.lock_state,
            ExamStudent.from_tool,
        ]

        group_by_fields = [
            ExamStudent.stu_id,
            ExamStudent.allow_exam_num,
            ExamStudent.exam_province_code,
            ExamStudent.exam_province_name,
            SysOrganizationCode.organizationName,
            ExamStudent.exam_point_code,
            ExamStudent.exam_point_name,
            SchoolInfo.schooName,
            ExamStudent.exam_room_code,
            ExamStudent.exam_room_name,
            Room.roomName,
            ExamStudent.project_id,
            Project.project_name,
            ExamStudent.created_time,
            ExamStudent.updated_time,
            UserInfo.username,
            ExamStudent.lock_state,
            ExamStudent.from_tool,
        ]

        stu_info = (
            new_session.query(ExamStudent.stu_id.distinct())
            .join(Project, ExamStudent.project_id == Project.project_id)
            .join(StudentSubject, StudentSubject.allow_exam_num == ExamStudent.allow_exam_num)
            .join(Subject, StudentSubject.subject_id == Subject.subject_id)
            .filter(ExamStudent.project_id.in_(project_id_list))
        )

        stu_id_list = [i[0] for i in stu_info]

        stu_stmt = (
            select(*select_fields)
            .join(UserInfo, ExamStudent.c_user_id == UserInfo.user_id)
            .join(Project, ExamStudent.project_id == Project.project_id)
            .join(StudentSubject, StudentSubject.allow_exam_num == ExamStudent.allow_exam_num)
            .join(Subject, StudentSubject.subject_id == Subject.subject_id)
            .outerjoin(SysOrganizationCode, SysOrganizationCode.organizationNo == ExamStudent.exam_province_code)
            .outerjoin(SchoolInfo, SchoolInfo.schooNo == ExamStudent.exam_point_code)
            .outerjoin(Room, Room.roomNo == ExamStudent.exam_room_code)
            .where(ExamStudent.stu_id.in_(stu_id_list))
            .group_by(*group_by_fields)
            .order_by(ExamStudent.created_time.desc())
        )

        result = new_session.execute(stu_stmt)
        for row in result:
            stu_item = {
                "stu_id": row[0],
                "allow_exam_num": row[1],
                "exam_province_code": row[2],
                "exam_province_name": row[3] if row[3] else row[4],  # 先找考生表，考生表里没有就去导入工具的表里找
                "exam_point_code": row[5],
                "exam_point_name": row[6] if row[6] else row[7],
                "exam_room_code": row[8],
                "exam_room_name": row[9] if row[9] else row[10],
                "project_id": row[11],
                "project_name": row[12],
                "subject_id": [i for i in row[13].split(",")],
                "subject_name": row[14].split(","),
                "created_time": row[15] and str(row[15]).replace("T", " "),
                "updated_time": row[16] and str(row[16]).replace("T", " "),
                "c_user_name": row[17],
                "lock_state": row[18],
                "from_tool": row[19],
            }
            stu_data.append(stu_item)
    except Exception as e:
        logger.error(f"获取带所有科目的考生信息列表失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取带所有科目的考生信息列表失败")
    logger.info("获取带所有科目的考生信息列表成功")
    data = {"data": stu_data}
    return BaseResponse(msg="获取带所有科目的考生信息列表成功", data=data)
