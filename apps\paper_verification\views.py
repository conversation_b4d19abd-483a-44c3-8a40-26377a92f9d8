from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from apps.users.services import get_current_user
from . import services
from apps.paper_verification.schemas import (GetVerificationStataisitcs,
                                             GetVerificationResult,
                                             GetVerificationResultsByCategory,
                                             UpdateVerificationStatusRequest,
                                             GetVerificationCount, GetVerificationBase,
                                             VerificationResultsTableData)
from settings import logger
from apps.base.schemas import BaseResponse
from apps.base.schemas import PaginationModel
import traceback
from factory_apps import session_depend
from helper.excel_export_utils import excel_export_service
from utils.time_func import format_now_time


paper_verification_router = APIRouter()
@paper_verification_router.get("/statistics", response_model=BaseResponse)
async def get_verification_statistics(
        query: GetVerificationStataisitcs,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    """
    获取核验统计信息
    """
    logger.info(f'{user["user_id"]}获取核验统计信息')
    project_id, subject_id, exam_session, task_type = query.model_dump()
    try:
        stats = services.get_verification_statistics(
            db=new_session,
            project_id=project_id,
            subject_id=subject_id,
            exam_session=exam_session,
            task_type=task_type
        )
        return BaseResponse(data=stats, msg="获取核验统计信息成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@paper_verification_router.get("/results", response_model=BaseResponse)
async def get_verification_results(
        query: GetVerificationResult,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    """
    获取核验结果列表
    """
    logger.info(f'{user["user_id"]}获取核验结果列表')
    current_page, page_size, project_id, subject_id, exam_session, task_type = query.model_dump()
    try:
        results = services.get_verification_results(
            db=new_session,
            project_id=project_id,
            subject_id=subject_id,
            exam_session=exam_session,
            task_type=task_type,
            page=current_page,
            page_size=page_size
        )
        return BaseResponse(data=results, msg="获取核验结果列表成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取核验结果失败: {str(e)}")





@paper_verification_router.post("/verification_results", response_model=BaseResponse)
async def get_verification_results_by_category(
        query: GetVerificationResultsByCategory,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):          
    """
    按核验分类获取核验结果列表
    """
    logger.info(f'{user["user_id"]}按核验分类获取核验结果列表')
    subject_id = query.subject_id
    try:
        results = services.get_verification_results_by_category(
            db=new_session,
            subject_id=subject_id,
        )
        return BaseResponse(data=results, msg="按核验分类获取核验结果列表成功")
    except Exception as e:
        print(traceback.print_exc())
        raise HTTPException(status_code=500, detail=f"获取核验结果失败: {str(e)}")



@paper_verification_router.post("/get_verify_results_file_response", response_model=BaseResponse)
async def get_verify_results_file_response(query:VerificationResultsTableData):
    data, business_class = query.model_dump().values()
    data_map = {
        "stu_secret_num": "考生密号",
        "subject_name": "科目名称",
        "is_answer":"是否作答",
        "subject_score":"科目总分",
        "stu_score":"考生得分",
        "error": "异常信息",
        "ques_id":"试题编号",
    }
    business_class_map = {
        "no_answer_no_score": {
            "name": "考生试题有作答无成绩",
            "description": "有作答但没有成绩记录的题目"
        },
        "answer_no_score": {
            "name": "考生试题有成绩无作答",
            "description": "有成绩记录但没有作答记录的题目"
        },
        "score_exceeds_max": {
            "name": "得分超过试题满分或出现负分",
            "description": "单题得分超过该题满分或出现负分的记录"
        },
        "total_score_exceeds_max": {
            "name": "总分超过科目满分或负分",
            "description": "考生的科目总得分超过科目满分或出现负分的记录"
        }
    }
    file_name = f"{business_class_map[business_class]["name"]}_{format_now_time().replace(":","-")}.xlsx"
    mapped_data = [
        {data_map[k]: v for k, v in data_dict.items() if k in data_map.keys()}
        for data_dict in data
    ]
    # mapped_data = [{data_map[k]:v} for data_dict in data  for k,v in data_dict.items() if k in data_map.keys()]
    return await excel_export_service.export_to_excel_stream(mapped_data,file_name)


@paper_verification_router.post("/verify_result_list", response_model=BaseResponse)
async def get_verify_result_list(
        query: GetVerificationBase,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    """
    获取资格科目核验状态列表
    """
    logger.info(f'{user["user_id"]}获取资格科目核验状态列表')
    query_dict = query.model_dump()
    current_page = query_dict.get("current_page", 1)
    page_size = query_dict.get("page_size", 10)
    project_id = query_dict.get("project_id")
    subject_id = query_dict.get("subject_id")
    verification_status = query_dict.get("verification_status", -1)
    try:
        results = services.get_verify_result_list(
            db=new_session,
            project_id=project_id,
            subject_id=subject_id,
            verification_status=verification_status,
            page=current_page,
            page_size=page_size
        )
        return BaseResponse(data=results, msg="获取资格科目核验状态列表成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取核验状态列表失败: {str(e)}")
