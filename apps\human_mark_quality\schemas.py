from typing import Literal, List, Optional
from pydantic import Field, BaseModel

from apps.base.schemas import PaginationModel


class QualitySampleReq(BaseModel):
    project_id: str = Field(..., description="项目id")
    subject_id: str = Field(..., description="科目id")
    ques_code: str = Field(..., description="试题编号")
    score_list: List[float] = Field([], description="考生得分区间")
    stu_secret_num: Optional[str] = Field(None, description="考生密号")
    sample_num: Optional[int] = Field(None, description="抽样份数")


class StartQualityReq(BaseModel):
    round_id: str = Field(..., description="轮次id")
    stu_secret_num_list: list = Field(..., description="考生密号列表")


class QualityStuListReq(PaginationModel):
    round_id: str = Field(..., description="轮次id")
    ques_code: str = Field(..., description="试题编号")
    stu_secret_num_list: list = Field([], description="质检抽取的考生密号")


class YetQualityStuListReq(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    stu_secret_num: Optional[str] = Field(None, description="考生密号")
    is_show_self: Literal[0, 1] = Field(1, description="是否只展示自己质检过的数据，0 否，1 是")


class QualityMarkItem(BaseModel):
    quality_id: Optional[str] = Field(None, description="质检id，第二次质检的时候该字段才有值")
    distri_id: str = Field(..., description="轮次分配id")
    person_distri_id: str = Field(..., description="评阅员分配id")
    answer_id: str = Field(..., description="作答id")
    ques_id: str = Field(..., description="小题id")
    mark_point_score_list: list = Field([], description="评分标准得分，选择了质检通过和退回重评不需要传")
    mark_score: Optional[float] = Field(None, description="评分分数，选择了质检通过和退回重评不需要传")


class QualityMarkReq(BaseModel):
    project_id: str = Field(..., description="项目id")
    subject_id: str = Field(..., description="科目id")
    round_id: str = Field(..., description="轮次id")
    stu_secret_num:  str = Field(..., description="考生密号")
    ques_code: str = Field(..., description="试题编号")
    quality_type: Literal[1, 2, 3] = Field(1, description="质检类型，1 表示质检通过，2 表示修改提交，3 表示退回重评")
    mark_info: List[QualityMarkItem] = Field(..., description="评分数据")
    reviewer_id: str = Field(..., description="评阅员id")
    quality_mark_type: Literal[1, 2] = Field(1, description="质检评分类型，1 表示正常质检，2 表示重新质检，重新质检不允许进行退回重评")
