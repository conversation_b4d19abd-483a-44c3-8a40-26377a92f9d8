import random
from typing import Optional, Union

import requests
from settings import logger
from sqlalchemy import and_, func

from factory_apps import session_depend
from apps.models.models import UserInfo, ManualGroupUser, ManualReadPaperGroup, UserRole, WorkFlowMainProcessInstance, \
    ManualReadTask, ManualReadTaskGroup, Project, Subject
from settings import configs


def get_all_username_and_id_card():
    """
    获取所有用户账号、角色、身份证号
    """
    new_session = next(session_depend())
    all_user_info = new_session.query(UserInfo.username, UserInfo.id_card, UserInfo.phone, func.group_concat(UserRole.role_id)) \
        .join(UserRole, UserRole.user_id == UserInfo.user_id) \
        .group_by(UserInfo.username, UserInfo.id_card, UserInfo.phone).all()
    all_username = []
    all_username_role = {}
    all_id_card = []
    all_phone = []
    if all_user_info:
        for username, id_card, phone, role_id_str in all_user_info:
            all_username.append(username)
            role_id_list = role_id_str.split(",")
            if username in all_username_role:
                all_username_role[username].extend(role_id_list)
            else:
                all_username_role[username] = role_id_list
            if id_card:
                all_id_card.append(id_card)
            if phone:
                all_phone.append(phone)
    return all_username, all_id_card, all_username_role, all_phone


def verify_pro_sub(new_session, project_name, subject_name, pro_sub_id_dict):
    """
    校验项目科目信息
    """
    # 先获取项目ID
    project_id = new_session.query(Project.project_id).filter(Project.project_name == project_name).scalar()

    # 通过项目ID和科目名称来唯一确定科目ID
    subject = new_session.query(Subject).filter(
        Subject.project_id == project_id,
        Subject.subject_name == subject_name
    ).first()

    subject_id = subject.subject_id if subject else None

    print("pro_sub_id_dict", pro_sub_id_dict)
    print("project_id", project_id)
    if subject_id not in pro_sub_id_dict[project_id]:
        return False, f"{project_name} 项目下暂无 {subject_name} 该科目", None, None

    return True, None, project_id, subject_id


def verify_manual_people(username: str, phone: Optional[str], id_card: Optional[str], all_username: list, all_id_card: list,all_phone:list, check_user: bool = True):
    phone = str(phone)
    if check_user:
        if username in all_username:
            return False, f"{username} 账号已存在"
        if id_card and id_card in all_id_card:
            return False, f"{id_card} 身份证号已存在"
        if phone and len(phone) != 11:
            return False, f"{phone} 手机号非法"
        if phone and phone in all_phone:
            return False, f"{phone} 手机号已存在"
        if id_card and len(id_card) != 18:
            return False, f"{id_card} 身份证号非法"
    return True, None


def check_ai_num_when_create_group(new_session, expert_ai_num, arbitrator_ai_num, quality_ai_num):
    """
    创建小组时检查 ai 用户的数量
    """
    msg = None
    if expert_ai_num or arbitrator_ai_num or quality_ai_num:
        ai_user_dict = {
            "3": 0,
            "4": 0,
            "5": 0
        }
        ai_user_info = new_session.query(UserInfo.user_id, UserRole.role_id) \
            .join(UserRole, UserRole.user_id == UserInfo.user_id) \
            .filter(and_(UserInfo.is_active.is_(True), UserInfo.user_type == 2, UserRole.role_id.in_(["3", "4", "5"]))) \
            .all()
        if ai_user_info:
            for _, role_id in ai_user_info:
                ai_user_dict[role_id] += 1

        msg_list = []
        if ai_user_dict["3"] < expert_ai_num:
            msg_list.append("AI 专家")
        if ai_user_dict["4"] < arbitrator_ai_num:
            msg_list.append("AI 组长")
        if ai_user_dict["5"] < quality_ai_num:
            msg_list.append("AI 质检")
        if msg_list:
            msg = "，".join(msg_list) + " 用户数量不足。"
    return msg



def check_expert_state(group_user_id_list):
    logger.info("检查选择的专家是否已加入阅卷小组")
    new_session = next(session_depend())
    expert_info = new_session.query(ManualGroupUser.manual_group_user_id, ManualReadPaperGroup.manual_group_name,
                                    UserInfo.name) \
        .join(ManualReadPaperGroup, ManualReadPaperGroup.manual_group_id == ManualGroupUser.manual_group_id) \
        .join(UserInfo, UserInfo.user_id == ManualGroupUser.user_id) \
        .join(UserRole, UserRole.user_id == ManualGroupUser.user_id) \
        .where(and_(ManualGroupUser.user_id.in_(group_user_id_list), UserRole.role_id == 3)).all()
    if expert_info:
        msg = ""
        for expert_item in expert_info:
            msg += f"{expert_item[2]} 该专家已加入 {expert_item[1]} 该小组，"
        msg = msg[:-1] + "。"
        return msg
    else:
        return None


def are_lists_of_dicts_equal(list1, list2):
    """
    检查两个以字典为元素的列表里的元素是否一样
    """
    # 首先检查长度是否相同
    if len(list1) != len(list2):
        return False

    # 将每个字典转换为frozenset，以便它们可以放入集合中进行比较
    set1 = {frozenset(d.items()) for d in list1}
    set2 = {frozenset(d.items()) for d in list2}

    # 比较两个集合是否相同
    return set1 == set2


def update_group_user_info(group_user_info_list: list, manual_group_id: str, c_user_id: str, new_session) -> (
        bool, str):
    """
    更新阅卷小组人员
    """
    manual_group_info = new_session.query(ManualGroupUser.user_id, ManualGroupUser.role_id).filter(
        ManualGroupUser.manual_group_id == manual_group_id).all()

    raw_group_user_info_list = []
    if manual_group_info:
        for info in manual_group_info:
            info_dict = {
                "user_id": info[0],
                "role_id": info[1]
            }
            raw_group_user_info_list.append(info_dict)

    if are_lists_of_dicts_equal(raw_group_user_info_list, group_user_info_list):
        return True, "小组人员无更新"

    # 新增的小组人员插入
    insert_data = []
    for user_info in group_user_info_list:
        if user_info not in raw_group_user_info_list:
            new_group_user = ManualGroupUser(manual_group_user_id=configs.snow_worker.get_id(),
                                             manual_group_id=manual_group_id, user_id=user_info["user_id"],
                                             role_id=user_info["role_id"], c_user_id=c_user_id)
            insert_data.append(new_group_user)

    if insert_data:
        new_session.add_all(insert_data)

    # 减少的小组人员删除
    delete_data = []
    for user_info in raw_group_user_info_list:
        if user_info not in group_user_info_list:
            new_session.query(ManualGroupUser) \
                .filter(and_(ManualGroupUser.manual_group_id == manual_group_id,
                             ManualGroupUser.user_id == user_info["user_id"],
                             ManualGroupUser.role_id == user_info["role_id"])).delete()

    return True, "编辑小组人员成功"


def get_manual_user_info():
    """
    获取阅卷人员信息
    """
    new_session = next(session_depend())
    # 已入组的专家可以再次入组
    # used_user_id = new_session.query(ManualGroupUser.user_id.distinct()).all()
    # used_user_id_list = [i[0] for i in used_user_id]
    user_info = new_session.query(UserInfo.user_id, UserInfo.username, UserInfo.name, UserInfo.user_type,
                                  UserRole.role_id) \
        .join(UserRole, UserRole.user_id == UserInfo.user_id) \
        .filter(and_(UserInfo.is_active.is_(True), UserRole.role_id.in_(["3", "4", "5"]))).all()

    if not user_info:
        return [], [], []

    # AI助手信息
    # ai_user_info = new_session.query(UserInfo.user_id, UserInfo.username, UserInfo.name, UserRole.role_id) \
    #     .join(UserRole, UserRole.user_id == UserInfo.user_id) \
    #     .filter(UserInfo.user_type == 2, UserRole.role_id.in_(["3", "4", "5"])).all()

    expert_info, arbitrator_info, quality_info, ai_expert_info, ai_arbitrator_info, ai_quality_info = [], [], [], [], [], []
    for user in user_info:
        user_info = {
            "user_id": user.user_id,
            "username": user.username,
            "name": user.name,
            "role_id": user.role_id,
        }
        if user.role_id == "3":
            expert_info.append(user_info) if user.user_type == 1 else ai_expert_info.append(user_info)
        elif user.role_id == "4":
            arbitrator_info.append(user_info) if user.user_type == 1 else ai_arbitrator_info.append(user_info)
        elif user.role_id == "5":
            quality_info.append(user_info) if user.user_type == 1 else ai_quality_info.append(user_info)

    return expert_info, arbitrator_info, quality_info, ai_expert_info, ai_arbitrator_info, ai_quality_info


def check_manual_user_num(new_group_id_num: int, expert_num: int, arbitrator_num: int, quality_num: int,
                          free_expert_num: int, free_arbitrator_num: int, free_quality_num: int):
    """
    随机分配小组人员时检验阅卷人员数量是否符合分配要求
    """
    not_enough_user_role = []
    if free_expert_num < expert_num * new_group_id_num:
        not_enough_user_role.append("【阅卷专家】")
    if arbitrator_num and not free_arbitrator_num:
        not_enough_user_role.append("【阅卷组长】")
    if quality_num and not free_quality_num:
        not_enough_user_role.append("【质检】")
    if not_enough_user_role:
        return f"{'、'.join(not_enough_user_role)} 人员不足，请导入相关人员信息"
    return None


def random_assign_manual_user_to_group(new_group_id_list, new_group_name_list, expert_num, expert_ai_num,
                                       arbitrator_num, arbitrator_ai_num, quality_num, quality_ai_num, expert_info,
                                       arbitrator_info, quality_info, ai_expert_info, ai_arbitrator_info,
                                       ai_quality_info):
    """
    随机分配小组人员
    """
    # 随机打乱所有用户列表，确保分配的随机性
    random.shuffle(expert_info)
    random.shuffle(arbitrator_info)
    random.shuffle(quality_info)
    random.shuffle(ai_expert_info)
    random.shuffle(ai_arbitrator_info)
    random.shuffle(ai_quality_info)

    # 创建一个空的字典用于存储分配结果
    groups = []
    not_enough_user_role = []
    msg = None

    # 遍历每个小组ID
    for index, group_id in enumerate(new_group_id_list):
        # 从专家、组长和领导列表中随机抽取指定数量的成员
        try:
            if expert_num - expert_ai_num == 0:
                group_experts = []
            else:
                group_experts = random.sample(expert_info, expert_num - expert_ai_num)
            if expert_ai_num > 0:
                group_experts.extend(random.sample(ai_expert_info, expert_ai_num))
        except ValueError:
            not_enough_user_role.append("【阅卷专家】")
        try:
            if arbitrator_num - arbitrator_ai_num <= 0:
                group_arbitrators = []
            else:
                group_arbitrators = random.sample(arbitrator_info, arbitrator_num - arbitrator_ai_num)
            if arbitrator_ai_num > 0:
                group_arbitrators.extend(random.sample(ai_arbitrator_info, arbitrator_ai_num))
        except ValueError:
            not_enough_user_role.append("【阅卷组长】")
        try:
            if quality_num - quality_ai_num <= 0:
                group_quality_men = []
            else:
                group_quality_men = random.sample(quality_info, quality_num - quality_ai_num)
            if quality_ai_num > 0:
                group_quality_men.extend(random.sample(ai_quality_info, quality_ai_num))
        except ValueError:
            not_enough_user_role.append("【质检】")

        if not_enough_user_role:
            msg = f"{'、'.join(not_enough_user_role)} 人员不足，请导入相关人员信息"
            return msg, groups

        # 从原列表中删除已分配的成员，防止重复分配
        # expert_info = [expert for expert in expert_info if expert not in group_experts]
        # arbitrator_info = [arbitrator for arbitrator in arbitrator_info if
        #                         arbitrator not in group_arbitrators]
        # quality_info = [quality for quality in quality_info if quality not in group_quality_men]

        # 将分配结果存储到字典中
        group = {
            "group_id": group_id,
            "group_name": new_group_name_list[index],
            "expert": [i["user_id"] for i in group_experts],
            "expert_name": [i["name"] for i in group_experts],
            "arbitrator": [i["user_id"] for i in group_arbitrators],
            "arbitrator_name": [i["name"] for i in group_arbitrators],
            "quality": [i["user_id"] for i in group_quality_men],
            "quality_name": [i["name"] for i in group_quality_men]
        }
        groups.append(group)

    return msg, groups


def get_every_manual_role_user(group_user_id, role_id, group_user_name):
    """
    获取每个阅卷角色的信息列表
    """
    expert, arbitrator, quality, expert_name, arbitrator_name, quality_name = [], [], [], [], [], []
    if group_user_id:
        for i in zip(group_user_id, role_id, group_user_name):
            if i[1] == "3":
                expert.append(i[0])
                expert_name.append(i[2])
            elif i[1] == "4":
                arbitrator.append(i[0])
                arbitrator_name.append(i[2])
            elif i[1] == "5":
                quality.append(i[0])
                quality_name.append(i[2])
            else:
                continue
    return expert, arbitrator, quality, expert_name, arbitrator_name, quality_name


def get_every_manual_role_num(process_id):
    """
    通过阅卷主流程id获取每个阅卷角色数量
    """
    new_session = next(session_depend())
    process_instance = new_session.query(WorkFlowMainProcessInstance.instance_ele_type,
                                         WorkFlowMainProcessInstance.instance_people_num) \
        .filter(WorkFlowMainProcessInstance.parent_process_id == process_id).all()
    expert_num, arbitrator_num, quality_num = 0, 0, 0
    if process_instance:
        for i in process_instance:
            if i[0] == "bpmn:userTask":
                expert_num = i[1]
            if i[0] == "bpmn:arbitratorNode":
                arbitrator_num = i[1]
            if i[0] == "bpmn:qualityNode":
                quality_num = i[1]
    return expert_num, arbitrator_num, quality_num


def manual_group_query_condition(project_id: Optional[str], subject_id: Optional[str], manual_group_name: Optional[str]) \
        -> (Union[str, bool], Union[str, bool]):
    project_query = ManualReadPaperGroup.manual_project_id == project_id if project_id else True
    subject_query = ManualReadPaperGroup.manual_subject_id == subject_id if subject_id else True
    manual_group_name_query = ManualReadPaperGroup.manual_group_name.ilike(
        f"%{manual_group_name}%") if manual_group_name else True

    condition = and_(project_query, subject_query, manual_group_name_query)
    return condition


def manual_task_query_condition(m_read_task_name: Optional[str], project_id: Optional[str], subject_id: Optional[str]) \
        -> (Union[str, bool], Union[str, bool]):
    project_query = ManualReadTask.project_id == project_id if project_id else True
    subject_query = ManualReadTask.subject_id == subject_id if subject_id else True
    m_read_task_name_query = ManualReadTask.m_read_task_name.ilike(
        f"%{m_read_task_name}%") if m_read_task_name else True

    condition = and_(project_query, subject_query, m_read_task_name_query)
    return condition


def update_task_group_info(m_read_task_id, update_group_id_list, new_session):
    """
    更新阅卷任务对应的小组信息
    """
    raw_task_group = new_session.query(ManualReadTaskGroup.manual_group_id).filter(
        ManualReadTaskGroup.m_read_task_id == m_read_task_id).all()
    raw_task_group_id_list = [i[0] for i in raw_task_group]
    add_data = []
    for update_group_id in update_group_id_list:
        if update_group_id not in raw_task_group_id_list:
            add_item = ManualReadTaskGroup(m_read_task_group_id=configs.snow_worker.get_id(),
                                           m_read_task_id=m_read_task_id, manual_group_id=update_group_id)
            add_data.append(add_item)
    if add_data:
        new_session.add_all(add_data)
    delete_data = []
    for raw_group_id in raw_task_group_id_list:
        if raw_group_id not in update_group_id_list:
            delete_data.append(raw_group_id)
    if delete_data:
        new_session.query(ManualReadTaskGroup).filter(ManualReadTaskGroup.m_read_task_id == m_read_task_id,
                                                      ManualReadTaskGroup.manual_group_id.in_(delete_data)).delete()
    return True, "成功"


def get_manual_task_by_conditions(m_read_task_id_list: list, real_group_id_list: list, token: str,
                                  current_page: int = 1, page_size: int = 10,
                                  m_read_task_name: Optional[str] = None, project_id: Optional[str] = None,
                                  subject_id: Optional[str] = None, paper_id: Optional[str] = None,
                                  paper_name: Optional[str] = None, is_distri_task: bool = False):
    """
    通过 id 获取阅卷任务
    """
    headers = {"Authorization": token}

    data = {
        "current_page": current_page,
        "page_size": page_size,
        "m_read_task_id_list": m_read_task_id_list,
        "real_group_id_list": real_group_id_list,
        "m_read_task_name": m_read_task_name,
        "project_id": project_id,
        "subject_id": subject_id,
        "paper_id": paper_id,
        "paper_name": paper_name,
        "is_distri_task": is_distri_task
    }

    res = requests.post(f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/manual_task/get_manual_task", json=data,
                        headers=headers)
    if res.status_code != 200:
        return 0, [], "服务出错", 500
    res_data = res.json()
    if res_data["code"] != 200:
        return 0, [], "服务出错", 500
    task_data = res_data["data"]["data"]
    total = res_data["data"]["total"]
    if not task_data:
        return 0, [], "该任务不存在", 200
    else:
        return total, task_data, None, 200


def get_task_state_name(task_state: int):
    """
    获取任务状态中文名字
    1 为未开始，2 为正在进行中，3 为已完成
    """
    if task_state == 1:
        task_state_name = "未发起"
    elif task_state == 2:
        task_state_name = "进行中"
    elif task_state == 3:
        task_state_name = "已完成"
    elif task_state == 4:
        task_state_name = "已作废"
    elif task_state == 5:
        task_state_name = "已结束"
    else:
        task_state_name = None
    return task_state_name


def create_single_user(username, name, phone, id_card, bank_card_num, project_id, subject_id, c_user_id, role_id_list, private_password,round_count, user_count, system_user_type,user_type=1, create_user=True):
    """
    创建单个用户
    """
    insert_data = []

    if create_user:
        # 插入用户信息
        user_id = configs.snow_worker.get_id()
        new_user = UserInfo(user_id=user_id, username=username, password=private_password, name=name, phone=phone, id_card=id_card,round_count=round_count,
                            bank_card_num=bank_card_num, c_user_id=c_user_id, user_type=user_type, system_user_type=system_user_type)
        insert_data.append(new_user)
        user_count += 1
    else:
        new_session = next(session_depend())
        user_id = new_session.query(UserInfo.user_id).filter(UserInfo.username).scalar()

    # 插入用户角色信息
    for role_id in role_id_list:
        new_role = UserRole(user_role_id=configs.snow_worker.get_id(), user_id=user_id, role_id=role_id, c_user_id=c_user_id)
        insert_data.append(new_role)

    return insert_data, user_count, user_id


def parse_process_instance(new_session, new_process_id, process_json, curr_user_id):
    """
    解析工作流流程
    """
    new_instance_list = []
    try:
        nodes = process_json["nodes"]
        edges = process_json["edges"]
        for node in nodes:
            instance_self_id = node["id"]
            instance_ele_type = node["type"]
            instance_ele_name_text = node.get("text")
            if instance_ele_name_text:
                instance_ele_name = instance_ele_name_text["value"]
            else:
                instance_ele_name = None
            if instance_ele_type == "bpmn:endEvent":
                new_instance = WorkFlowMainProcessInstance(instance_id=configs.snow_worker.get_id(),
                                                           instance_self_id=instance_self_id,
                                                           parent_process_id=new_process_id,
                                                           instance_ele_type="bpmn:endEvent",
                                                           instance_ele_name=instance_ele_name,
                                                           instance_people_num=node["properties"].get("number"),
                                                           instance_target_id=None,
                                                           instance_target_text=None,
                                                           instance_target_percentage=None,
                                                           instance_target_logic=None,
                                                           c_user_id=curr_user_id)
                new_instance_list.append(new_instance)
            else:
                for edge in edges:
                    source_node_id = edge["sourceNodeId"]
                    instance_target_text = None
                    if instance_self_id == source_node_id:
                        instance_target_id = edge["targetNodeId"]
                        if edge.get("text"):
                            instance_target_text = edge["text"].get("value")
                        instance_target_percentage = edge["properties"].get("percentage")
                        instance_target_logic = edge["properties"].get("logic")
                        instance_ele_name_text = node.get("text")
                        if instance_ele_name_text:
                            instance_ele_name = instance_ele_name_text["value"]
                        else:
                            instance_ele_name = None
                        new_instance = WorkFlowMainProcessInstance(instance_id=configs.snow_worker.get_id(),
                                                                   instance_self_id=instance_self_id,
                                                                   parent_process_id=new_process_id,
                                                                   instance_ele_type=instance_ele_type,
                                                                   instance_ele_name=instance_ele_name,
                                                                   instance_people_num=node["properties"].get("number"),
                                                                   instance_target_id=instance_target_id,
                                                                   instance_target_text=instance_target_text,
                                                                   instance_target_percentage=instance_target_percentage,
                                                                   instance_target_logic=instance_target_logic,
                                                                   c_user_id=curr_user_id)
                        new_instance_list.append(new_instance)

        new_session.add_all(new_instance_list)
        return True, None
    except Exception as e:
        logger.error(f"创建人工阅卷流程实例失败，{e}")
        new_session.rollback()
        return False, "创建人工阅卷流程实例失败"


def get_ai_user_by_role_id(new_session):
    """
    获取不同角色的AI用户列表
    """
    all_expert_ai_list, all_arbitrator_ai_list, all_quality_ai_list = [], [], []

    ai_user_info = new_session.query(UserRole.role_id, func.group_concat(UserInfo.user_id)) \
        .join(UserInfo, UserInfo.user_id == UserRole.user_id) \
        .filter(and_(UserInfo.user_type == 2, UserRole.role_id.in_(["3", "4", "5"]))) \
        .group_by(UserRole.role_id).all()
    for role_id, user_id_str in ai_user_info:
        if role_id == "3":
            all_expert_ai_list = user_id_str.split(",")
        if role_id == "4":
            all_arbitrator_ai_list = user_id_str.split(",")
        if role_id == "5":
            all_quality_ai_list = user_id_str.split(",")
    return all_expert_ai_list, all_arbitrator_ai_list, all_quality_ai_list


def get_ai_user_num_by_group_id(new_session, group_id_list):
    """
    获取小组的ai阅卷、仲裁、质检人员数量
    """
    group_ai_user_dict = {}
    group_ai_user_info = new_session.query(ManualReadPaperGroup.manual_group_id,
                                           ManualReadPaperGroup.expert_ai_num,
                                           ManualReadPaperGroup.arbitrator_ai_num,
                                           ManualReadPaperGroup.quality_ai_num,
                                           ManualReadPaperGroup.manual_group_name).filter(
        ManualReadPaperGroup.manual_group_id.in_(group_id_list)).all()
    for manual_group_id, expert_ai_num, arbitrator_ai_num, quality_ai_num, manual_group_name in group_ai_user_info:
        group_ai_user_dict[manual_group_id] = {
            "expert_ai_num": expert_ai_num,
            "arbitrator_ai_num": arbitrator_ai_num,
            "quality_ai_num": quality_ai_num,
            "manual_group_name": manual_group_name
        }
    return group_ai_user_dict


def check_task_can_or_not_mark(new_session, m_read_task_id: str):
    """
    检查人工阅卷任务状态，判断是否可以进行评分
    """
    msg = None
    task_state = new_session.query(ManualReadTask.task_state).filter(
        ManualReadTask.m_read_task_id == m_read_task_id).scalar()
    if task_state == 1:
        msg = "该任务暂未发起"
    elif task_state == 3:
        msg = "该任务已完成"
    elif task_state == 4:
        msg = "该任务已作废"
    elif task_state == 5:
        msg = "该任务已结束"
    return msg
