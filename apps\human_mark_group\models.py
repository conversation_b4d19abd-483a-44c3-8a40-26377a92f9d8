import datetime

from sqlalchemy import Column, String, Integer, DateTime
from sqlalchemy.orm import declarative_base


# 声明基类
Base = declarative_base()


class DateTimeBaseMixin:
    __abstract__ = True
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")


class HumanMarkGroup(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_mark_group"  # 人工阅卷小组表
    group_id = Column(String(50), primary_key=True)
    group_code = Column(String(30), unique=True, comment="小组编号，只能输入数字")
    group_name = Column(String(100), unique=False, comment="小组名称")
    group_level = Column(Integer, comment="小组层级，1 表示科目级，2 表示题组级，3 表示评卷小组级")
    project_id = Column(String(50), comment="所属项目id")
    subject_id = Column(String(50), comment="所属科目id")
    parent_group_id = Column(String(50), comment="所属父级小组id，题组级和小组级必填")
    paper_id = Column(String(50), comment="试卷id，题组级和评卷小组级必填，抽参模式为空")
    ques_order = Column(String(50), comment="试题序号，题组级和评卷小组级必填，抽参模式为空")
    ques_code = Column(String(50), comment="试题编号，题组级和评卷小组级必填")
    ques_id = Column(String(50), comment="试题id，题组级和评卷小组级必填")
    is_used = Column(Integer, default=1, comment="使用状态，0 表示不使用，1 表示使用")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class HumanGroupMember(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_group_member"  # 人工阅卷小组成员表
    member_id = Column(String(50), primary_key=True)
    group_id = Column(String(50), nullable=False, comment="组id")
    user_id = Column(String(50), nullable=False, comment="用户id")
    member_role = Column(Integer, comment="成员角色，1 表示组长，2 表示组员")
