from typing import Optional, Literal, Union
from pydantic import BaseModel, Field
from apps.base.schemas import PaginationModel


class CreateExamUserReq(BaseModel):
    name: str = Field(..., description="姓名")
    cert_number: str = Field(..., description="证件号码")
    phone: Optional[str] = Field(None, description="手机号码")
    paper_id: str = Field(..., description="试卷id")


class TemplateReq(BaseModel):
    temp_type: Literal["1", "2", "3", "4"] = Field(..., description="模板类型，1 表示试卷模板，2 表示作答信息模板，3 表示导入考生模板，4 表示导入人工阅卷人员模板")
    temp_format: Literal["1", "2"] = Field(..., description="模板文件格式，1 表示 excel， 2 表示 word")


class GetQuesReq(BaseModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    paper_id: Optional[str] = Field(None, description="试卷id")
    ques_type_code: Optional[str] = Field(None, description="试题类型编码")
    read_state: Literal[None, 1, 2] = Field(None, description="试题分配状态，1 表示未分配，2 表示已分配，None 表示所有")
    format_by_ques_type: bool = Field(True, description="是否按照试题类型进行格式化")
    is_get_all_info: bool = Field(True, description="是否获取试题所有信息")
    is_paper: bool = Field(True, description="是否为抽卷")
    business_id: Optional[str] = Field(None, description="业务题型id，只有抽参且选择组合题时时传递该参数")


class GetPaperReq(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    paper_name: Optional[str] = Field(None, description="试卷名称")
    is_create_task: bool = Field(False, description="是否是在创建人工阅卷任务是调用")
    c_name: Optional[str] = Field(None, description="创建人")


class UpdatePaperReq(BaseModel):
    paper_id: Optional[str] = Field(..., description="试卷id")
    project_id: Optional[str] = Field(..., description="项目id")
    subject_id: Optional[str] = Field(..., description="科目id")
    paper_name: Optional[str] = Field(..., description="试卷名称")
    mark_rule_id: Optional[str] = Field(..., description="评分规则id")
    remark: Optional[str] = Field(None, description="备注")


class UpdateQuesReq(BaseModel):
    paper_id: str = Field(..., description="试卷id")
    ques_id: str = Field(..., description="试题id")
    ques_code: str = Field(..., description="试题编号")
    ques_order: str = Field(..., description="试题序号")
    ques_desc: dict = Field(..., description="试题描述")
    ques_choices: list = Field([], description="试题选项")
    total_score: float = Field(..., description="试题分值")
    weight: Optional[list] = Field([], description="权重列表")
    d_out_of_order_group: Optional[str] = Field(None, description="填空题不区分顺序分组字段")
    ques_difficulty: Optional[str] = Field(None, description="试题难度")
    standard_answer: Optional[list] = Field(None, description="参考答案")
    standard_answer_html: Optional[list] = Field(None, description="参考答案html")
    standard_parse: Optional[str] = Field(None, description="参考答案解析")
    e_mark_rule: Optional[str] = Field(None, description="简答题评分规则")
    ques_mark_point: Optional[list] = Field(None, description="评分要点")
    knowledge_group_id: Optional[str] = Field(None, description="操作题评分步骤规则分组id")
    op_step_list: list = Field([], description="操作题评分步骤列表")
    # op_step_list = [
    #     {
    #         "auto_id": "60221121152807000055",
    #         "order_id": 2,
    #         "description": "2、把“考生文件夹##data”文件夹中的文件“work.dps”移动到“考生文件夹##study”文件夹中！",
    #         "is_score": 1,
    #         "step_score_gene": 3.0
    #     }
    # ]
    op_score_rule_dict: Optional[dict] = Field(None, description="操作题评分步骤规则")
    op_total_score_gene: Optional[float] = Field(None, description="操作题总权重")
    # is_score_list: Optional[list] = Field(None, description="是否评分的列表")


class DeletePaperReq(BaseModel):
    paper_id: str = Field(..., description="试卷id")


class DeleteQuesReq(BaseModel):
    paper_id: Optional[str] = Field(..., description="试卷id")
    ques_id: Optional[str] = Field(..., description="试题id")


class GetQuesTypeReq(PaginationModel):
    exclude_type_code_list: list = Field([], description="不获取的题型")


class GetStuAnswerReq(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    paper_id: Optional[str] = Field(None, description="试卷id")
    allow_exam_num: Optional[str] = Field(None, description="准考证号")
    stu_secret_num: Optional[str] = Field(None, description="考生密号")


class CreateMarkRuleReq(BaseModel):
    project_id: str = Field(..., description="项目id")
    subject_id: str = Field(..., description="科目id")
    rule_name: str = Field(..., description="评分规则名称")
    rule_year: int = Field(..., description="考试年份")
    is_params: bool = Field(False, description="是否是抽参")
    remark: Optional[str] = Field(None, description="备注")
    rule_dict: dict = Field(..., description="包含的各试题类型配置")


class UpdateMarkRuleReq(BaseModel):
    rule_id: str = Field(..., description="规则id")
    project_id: str = Field(..., description="项目id")
    subject_id: str = Field(..., description="科目id")
    rule_name: str = Field(..., description="评分规则名称")
    rule_year: int = Field(..., description="考试年份")
    remark: Optional[str] = Field(None, description="备注")
    rule_dict: dict = Field(..., description="包含的各试题类型配置")


class GetMarkRuleReq(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    rule_year: Optional[int] = Field(None, description="年份")
    c_name: Optional[str] = Field(None, description="创建人用户名")


class GetMarkRuleDetailReq(BaseModel):
    rule_id: str = Field(..., description="规则id")


class GetReadPaperReq(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    paper_code: Optional[str] = Field(None, description="试卷编号")
    ques_type_code: Optional[str] = Field(None, description="试题类型简码")
    ques_code: Optional[str] = Field(None, description="试题编号")
    ques_order: Optional[str] = Field(None, description="试题序号")
    same_answer_group_id: Optional[str] = Field(None, description="相同答案分组 id")
    stu_answer: Optional[str] = Field(None, description="考生答案")
    mark_result: Optional[int] = Field(None, description="判断结果")
    mark_state: list = Field([], description="评分状态")
    stu_score_range: Optional[list] = Field(None, description="考生分数范围")
    search_time: Optional[list] = Field([], description="评分时间范围")


class GetReadPaperDetailReq(BaseModel):
    same_answer_group_id: str = Field(..., description="相同答案分组 id")
    paper_code: str = Field(..., description="试卷编号")
    ques_code: str = Field(..., description="试题编号")


class GetReadPaperDataReq(BaseModel):
    answer_id_list: list = Field([], description="作答id列表，如果为 None，则全部阅卷")
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    paper_code: Optional[str] = Field(None, description="试卷编号")
    ques_type_code: Optional[str] = Field(None, description="试题类型简码")
    ques_code: Optional[str] = Field(None, description="试题编号")
    ques_order: Optional[str] = Field(None, description="试题序号")
    same_answer_group_id: Optional[str] = Field(None, description="相同答案分组 id")
    stu_answer: Optional[str] = Field(None, description="考生答案")
    mark_result: Optional[int] = Field(None, description="判断结果")
    mark_state: list = Field([], description="评分状态")
    stu_score_range: Optional[list] = Field(None, description="考生分数范围")
    search_time: Optional[list] = Field(None, description="评分时间范围")
    paper_code_list: list = Field([], description="评分数据的试卷列表，用于检查是否绑定了评分规则")


class InitReadPaperStateReq(BaseModel):
    answer_id_list: Optional[list] = Field(None, description="作答id列表")
    total: int = Field(..., description="智能阅卷总数")
    mark_state: list = Field([], description="评分状态")


class StartReadPaperReq(BaseModel):
    ooo_answer_id_list: Optional[list] = Field([], description="进行智能阅卷的id列表")
    same_answer_group_id_list: Optional[list] = Field([], description="进行智能阅卷的id答案组列表")
    total: int = Field(..., description="智能阅卷总数")


class GetReadPaperPercentageReq(BaseModel):
    await_mark_item_id: list = Field(..., description="待智能阅卷答案id列表")
    total: int = Field(..., description="智能阅卷总数")


class ProfessionMarkReq(BaseModel):
    same_answer_group_id: str = Field(..., description="答案分组编号")
    paper_code: str = Field(..., description="试卷编号")
    ques_id: str = Field(..., description="试题id")
    profession_score_list: list = Field(..., description="专家评分分数列表")
    modify_reason: Optional[str] = Field(None, description="修改原因")
    profession_parse: Optional[list] = Field([], description="专家评分解析")
    total_standard_score: Union[float, str] = Field(..., description="该题满分分数")
    ques_score_list: Optional[list] = Field([], description="试题每空分数")


class GetStuReq(PaginationModel):
    allow_exam_num: Optional[str] = Field(None, description="准考证号")
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    exam_province_code: Optional[str] = Field(None, description="省考区")
    exam_city_code: Optional[str] = Field(None, description="市考区")
    exam_point_code: Optional[str] = Field(None, description="考点")
    exam_room_code: Optional[str] = Field(None, description="考场")


class GetStuWithAllSubjectReq(PaginationModel):
    project_id_list: Optional[list] = Field(..., description="项目id列表，用于导入考生时查询已导入的考生数据")


class CreateStuReq(BaseModel):
    allow_exam_num: str = Field(..., description="准考证号")
    project_id: str = Field(..., description="所属项目id")
    subject_id_list: list = Field(..., description="科目id列表")
    exam_province_code: Optional[str] = Field(None, description="考区编号", max_length=15)
    exam_province_name: Optional[str] = Field(None, description="考区名称", max_length=40)
    exam_city_code: Optional[str] = Field(None, description="考区编号", max_length=15)
    exam_city_name: Optional[str] = Field(None, description="考区名称", max_length=40)
    exam_point_code: Optional[str] = Field(None, description="考点编号", max_length=15)
    exam_point_name: Optional[str] = Field(None, description="考点名称", max_length=40)
    exam_room_code: Optional[str] = Field(None, description="考场编号", max_length=15)
    exam_room_name: Optional[str] = Field(None, description="考场名称", max_length=40)
    stu_name: Optional[str] = Field(None, description="考生姓名")
    cert_number: Optional[str] = Field(None, description="证件号码")
    project_code: Optional[str] = Field(None, description="项目编号")
    subject_id: Optional[str] = Field(None, description="科目id")
    subject_code: Optional[str] = Field(None, description="科目编号")
    exam_session: Optional[int] = Field(None, description="场次")
    is_violation: Optional[int] = Field(0, description="违纪标记（0：正常、-2：违纪）")
    is_exam: Optional[int] = Field(0, description="缺考标记（0：正常、-1：缺考）")


class ImportProgressReq(BaseModel):
    record_id: str = Field(..., description="导入考生记录id")


class UpdateStuReq(BaseModel):
    stu_id: str = Field(..., description="考生id")
    allow_exam_num: str = Field(..., description="准考证号")
    project_id: str = Field(..., description="项目id")
    subject_id_list: list = Field(..., description="科目id列表")
    exam_area_code: Optional[str] = Field(None, description="考区编号")
    exam_area_name: Optional[str] = Field(None, description="考区名称")
    exam_point_code: Optional[str] = Field(None, description="考点编号")
    exam_point_name: Optional[str] = Field(None, description="考点名称")
    exam_room_code: Optional[str] = Field(None, description="考场编号")
    exam_room_name: Optional[str] = Field(None, description="考场名称")


class CreateReadWorkFlowProcessReq(BaseModel):
    process_name: str = Field(..., description="工作流主流程名称")
    process_json: dict = Field(..., description="流程json")


class GetReadWorkFlowProcessReq(PaginationModel):
    process_name: Optional[str] = Field(None, description="工作流主流程名称")
    c_name: Optional[str] = Field(None, description="创建人用户名")


class UpdateReadWorkFlowProcessReq(BaseModel):
    process_id: Optional[str] = Field(..., description="工作流主流程id")
    process_name: str = Field(..., description="工作流主流程名称")
    process_json: dict = Field(..., description="流程json")


class ReadWorkFlowProcessIdReq(BaseModel):
    process_id: Optional[str] = Field(..., description="工作流主流程id")


class GetBusinessQuesTypeReq(BaseModel):
    project_id: str = Field(..., description="项目id")
    subject_id: str = Field(..., description="科目id")
    business_id_list: list = Field([], description="业务题型列表")


class GetExamPlaceReq(BaseModel):
    project_id: str = Field(..., description="项目id")
    subject_id: str = Field(..., description="科目id")
    place_type: Literal[1, 2, 3] = Field(1, description="获取类型，1 表示考区，2 表示考点，3 表示考场")
    code: Optional[str] = Field(None, description="编号")
