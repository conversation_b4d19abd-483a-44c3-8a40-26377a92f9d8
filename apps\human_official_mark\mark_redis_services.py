import json
import traceback
from typing import List

from sqlalchemy import and_, select, exists

from apps.base.human_global_cache import get_round_group_member
from apps.grade_manage.models import HumanStudentSubjectGrade, HumanStudentSubjectGradeDetail
from apps.human_official_mark.services import get_stu_answer_info
from apps.human_task_manage.models import HumanReadTaskRound, HumanRoundDistriAnswer, HumanReadTask
from apps.human_task_manage.services import get_human_mark_round_info, parse_single_round_data, get_group_reviewer_load, distribute_human_task_data, check_round_group_member
from apps.manual_read_paper.manual_read_services import get_target_node_by_source, compare_diff_scores_with_threshold, calculate_score_by_fetch_way
from apps.models.models import WorkFlowMainProcessInstance
from factory_apps import session_depend, redis_session
from settings import configs, logger
from utils.time_func import format_now_time


def mark_process(round_id: str, answer_id: str, user_id: str, mark_point_score_list: list, mark_score: float, cost_time: int, mark_time: str):
    score_item = {
        "score_key": f"{answer_id}_{user_id}",
        "answer_id": answer_id,
        "mark_point_score_list": mark_point_score_list,
        "mark_score": mark_score,
        "cost_time": cost_time,
        "mark_time": mark_time
    }
    r = next(redis_session())
    r.hset(
        f"{configs.R_HUMAN_KEY}_{round_id}",
        score_item["score_key"],
        json.dumps(score_item, ensure_ascii=False)
    )


def get_same_answer_mark_data(r, round_id: str):
    """
    获取相同 answer_id 不同专家的评分
    """
    # 获取该轮次的所有评分
    all_scores = r.hgetall(f"{configs.R_HUMAN_KEY}_{round_id}")

    if not all_scores:
        return {}

    same_answer_mark_dict = {}

    # 将相同 answer_id 的评分数据整合到一起
    for score_key, mark_item in all_scores.items():
        answer_id, curr_user_id = score_key.split("_")
        mark_item = json.loads(mark_item)
        mark_item["reviewer_id"] = curr_user_id
        if answer_id in same_answer_mark_dict:
            same_answer_mark_dict[answer_id].append(mark_item)
        else:
            same_answer_mark_dict[answer_id] = [mark_item]
        # logger.info(f"mark_item1, {mark_item}")

    return same_answer_mark_dict


def get_into_next_step_mark_data(same_answer_mark_dict: dict, reviewer_count: int):
    """
    获取可以进入下一个阶段的数据
    reviewer_count 为 1 直接进入下一阶段，
    reviewer_count 大于 1 需等所有 reviewer 评完分才进入下一阶段
    """
    next_step_mark_data = []
    for answer_id, mark_list in same_answer_mark_dict.items():
        # logger.info(f"answer_id, mark_list: {answer_id, mark_list}")
        if reviewer_count != 1:
            if len(mark_list) == reviewer_count:
                next_step_mark_data.append({answer_id: mark_list})
        else:
            next_step_mark_data.append({answer_id: mark_list})
    return next_step_mark_data


def check_arbitration(next_step_mark_data: List[dict], reviewer_count: int, threshold: float, compare_type: int):
    """
    获取需要仲裁和不需要仲裁的数据
    """
    no_arbitration_list, arbitration_list = [], []
    for mark_dict in next_step_mark_data:
        answer_id, mark_list = next(iter(mark_dict.items()))
        if len(mark_list) == reviewer_count:

            score_list = [i["mark_score"] for i in mark_list]

            is_arbitration = compare_diff_scores_with_threshold(score_list, threshold, compare_type)

            if is_arbitration:
                arbitration_list.append(mark_dict)
            else:
                no_arbitration_list.append(mark_dict)
    return no_arbitration_list, arbitration_list


def process_arbitration(round_id: str, mark_list: List[dict]):
    # 将仲裁数据存入数据库
    logger.info("将仲裁数据存入数据库...")


def get_round_data_by_id(new_session, round_id: str, is_reload: bool = False):
    """
    缓存并获取任务信息
    """
    r = next(redis_session())
    round_data = r.get(f"round_data_{round_id}")
    if round_data and not is_reload:
        round_data = json.loads(round_data)
    else:
        task_id = new_session.query(HumanReadTaskRound.task_id).filter(HumanReadTaskRound.round_id == round_id).scalar()
        # 调用获取任务轮次信息接口
        result, msg = get_human_mark_round_info(None, [task_id], [round_id])
        try:
            round_data = result["data"][0]
            r.set(f"round_data_{round_id}", json.dumps(round_data, ensure_ascii=False))
        except IndexError:
            round_data = get_round_data_by_id(new_session, round_id, True)
    return round_data


def create_human_subject_grade(new_session, round_id, round_count, project_id, subject_id, task_type, stu_secret_num, final_score, reviewer_id, answer_id, ques_id, mark_point_score_list):
    """
    保存科目成绩
    """
    r = next(redis_session())
    grade_id = r.get(f"grade_id_{subject_id}_{stu_secret_num}")
    detail_exists = False

    if not grade_id:
        grade_condition = and_(HumanStudentSubjectGrade.subject_id == subject_id, HumanStudentSubjectGrade.stu_secret_num == stu_secret_num)
        grade_id = new_session.query(HumanStudentSubjectGrade.student_subject_grade_id).filter(grade_condition).scalar()

        if not grade_id:
            grade_id = configs.snow_worker.get_id()
            grade_item = HumanStudentSubjectGrade(student_subject_grade_id=grade_id, task_type=task_type, round_count=round_count, stu_secret_num=stu_secret_num, project_id=project_id, subject_id=subject_id)
            new_session.add(grade_item)
        else:
            detail_condition = and_(HumanStudentSubjectGradeDetail.student_subject_grade_id == grade_id, HumanStudentSubjectGradeDetail.answer_id == answer_id)
            detail_exists = new_session.query(exists().where(detail_condition)).scalar()
    else:
        detail_condition = and_(HumanStudentSubjectGradeDetail.student_subject_grade_id == grade_id, HumanStudentSubjectGradeDetail.answer_id == answer_id)
        detail_exists = new_session.query(exists().where(detail_condition)).scalar()

    r.set(f"grade_id_{subject_id}_{stu_secret_num}", grade_id)

    if detail_exists:
        detail_condition = and_(HumanStudentSubjectGradeDetail.student_subject_grade_id == grade_id, HumanStudentSubjectGradeDetail.answer_id == answer_id)
        new_session.query(HumanStudentSubjectGradeDetail).filter(detail_condition).update({
            HumanStudentSubjectGradeDetail.stu_score: final_score,
            HumanStudentSubjectGradeDetail.stu_score_list: mark_point_score_list,
            HumanStudentSubjectGradeDetail.c_user_id: reviewer_id,
            HumanStudentSubjectGradeDetail.created_time: format_now_time()
        })
    else:
        detail_item = HumanStudentSubjectGradeDetail(detail_id=configs.snow_worker.get_id(), student_subject_grade_id=grade_id, round_id=round_id, answer_id=answer_id, ques_id=ques_id,
                                                     stu_score_list=mark_point_score_list, stu_score=final_score, c_user_id=reviewer_id)
        new_session.add(detail_item)
    logger.info(f"{answer_id} 保存科目成绩成功")


def human_process_operation(round_id, source_node, start_mark_data=[], condition=True, after_arbitration_list=[]):
    """
    根据主流程评阅员打分/仲裁/质检后需要进行的操作
    source_node: 工作流来源节点
    condition: 决定是否发起仲裁的查询条件
    """
    r = next(redis_session())
    new_session = next(session_depend())

    # 调用获取任务轮次信息接口
    round_data = get_round_data_by_id(new_session, round_id, False)

    (task_id, _, project_id, subject_id, _, task_type, business_type_name, ques_id, ques_code, round_id, round_count, process_id,
     round_state, fetch_score_way, fetch_score_scope, fetch_score_option, arbitrate_threshold_type, arbitrate_threshold, arbitrate_score_diff,
     deviation_threshold_type, arbitrate_deviation, mark_score_step, ques_type_score, ques_type_code, group_id_list, reviewer_num) = parse_single_round_data(round_data)

    target_id, target_percentage = get_target_node_by_source(new_session, process_id, source_node, condition)

    # 获取下一步操作指向的id
    target_type_info = new_session.query(WorkFlowMainProcessInstance.instance_ele_type).filter(WorkFlowMainProcessInstance.instance_self_id == target_id).first()
    target_type = target_type_info[0]

    if target_type == "bpmn:userTask":
        logger.info(f"{round_id} 获取评阅员评分节点")
        same_answer_mark_dict = get_same_answer_mark_data(r, round_id)
        if not same_answer_mark_dict:
            logger.info(f"{round_id} 暂无评分分数，退出流程")
            return True

        start_mark_data = get_into_next_step_mark_data(same_answer_mark_dict, reviewer_num)
        if not start_mark_data:
            logger.info(f"{round_id} 暂无所有评阅员都评完分的作答，退出流程")
            return True

        human_process_operation(round_id, target_type, start_mark_data, condition, [])

    elif target_type == "bpmn:scoreDetection":
        logger.info(f"{round_id} 是否仲裁节点")
        # 仲裁前的 判分检测
        # 仲裁评分阈值
        if arbitrate_threshold_type == 1 and arbitrate_threshold:
            target_percentage = arbitrate_threshold
            # 获取试题分数，计算具体阈值
        elif arbitrate_threshold_type == 2 and arbitrate_threshold:
            threshold_score = arbitrate_threshold

        no_arbitration_list, arbitration_list = check_arbitration(start_mark_data, reviewer_num, threshold_score, arbitrate_score_diff)
        if arbitration_list:
            logger.info(f"{round_id} 发起仲裁流程")
            condition = WorkFlowMainProcessInstance.instance_target_logic == 2
            human_process_operation(round_id, target_type, [], condition, arbitration_list)

        if no_arbitration_list:
            logger.info(f"{round_id} 不发起仲裁流程")
            condition = WorkFlowMainProcessInstance.instance_target_logic == 1
            human_process_operation(round_id, target_type, [], condition, no_arbitration_list)

    elif target_type == "bpmn:arbitratorNode":
        logger.info(f"{round_id} 发起仲裁节点")
        process_arbitration(r, round_id, after_arbitration_list)

    elif target_type == "bpmn:isQuality":
        logger.info(f"{round_id} 是否质检节点")
        # 判断是否质检
        pass

    elif target_type == "bpmn:qualityNode":
        logger.info(f"{round_id} 发起质检节点")
        pass

    elif target_type == "bpmn:qualityInspectionScore":
        logger.info(f"{round_id} 质检判分节点")
        pass

    elif target_type == "bpmn:endEvent":
        logger.info(f"{round_id} 阅卷流程结束节点，保存最终分数")
        stu_answer_dict = get_stu_answer_info(new_session, ques_code)
        human_key = f"{configs.R_HUMAN_KEY}_{round_id}"
        # 将题目最终分数保存起来
        all_mark_data = [*start_mark_data, *after_arbitration_list]
        all_score_key_list = []
        if all_mark_data:
            for mark_dict in all_mark_data:
                answer_id, mark_list = next(iter(mark_dict.items()))
                if reviewer_num != 1:
                    mark_score_list = [i["mark_score"] for i in mark_list]
                    reviewer_id_list = [i["reviewer_id"] for i in mark_list]
                    # todo：非单评获取所取评分评阅员id和评分标准列表
                    reviewer_id, mark_point_score_list = None, []
                    final_score = calculate_score_by_fetch_way(fetch_score_way, fetch_score_option, fetch_score_scope, mark_score_list)
                else:
                    mark_item = mark_list[0]
                    final_score = mark_item["mark_score"]
                    reviewer_id = mark_item["reviewer_id"]
                    mark_point_score_list = mark_item["mark_point_score_list"]
                new_session.query(HumanRoundDistriAnswer).filter(and_(HumanRoundDistriAnswer.round_id == round_id, HumanRoundDistriAnswer.answer_id == answer_id)).update({
                    HumanRoundDistriAnswer.round_score: final_score,
                    HumanRoundDistriAnswer.reviewer_id: reviewer_id
                })
                # 删除各评阅员的独立记录
                all_score_key_list.extend([i["score_key"] for i in mark_list])

                if task_type == 1:
                    answer_info = stu_answer_dict[answer_id]
                    stu_secret_num, ques_id = answer_info["stu_secret_num"], answer_info["ques_id"]

                    logger.info(f"{answer_id} 保存科目成绩")
                    create_human_subject_grade(new_session, round_id, round_count, project_id, subject_id, task_type, stu_secret_num, final_score, reviewer_id, answer_id, ques_id, mark_point_score_list)

            new_session.commit()
            deleted_count = r.hdel(human_key, *all_score_key_list)
            logger.info(f"从 redis 中删除 {deleted_count} 个评分记录, 剩余 {r.hlen(human_key)} 条记录")
        logger.info(f"保存最终分数成功")


def start_human_work_flow_schedule():
    """
    人工阅卷 工作流 逻辑处理定时任务指向的函数入口
    """
    r = next(redis_session())
    human_keys = r.keys(f"{configs.R_HUMAN_KEY}_*")

    for key in human_keys:
        logger.info(f"{key} 人工阅卷 工作流 逻辑处理定时任务启动，评分数据：{r.hlen(key)} 条")
        _, _, round_id = key.split("_")
        human_process_operation(round_id, source_node="bpmn:startEvent")


def start_human_assign_schedule():
    """
    自动分配每个任务轮次评阅员的评分数据
    """
    new_session = next(session_depend())
    round_id_info = new_session.query(HumanReadTaskRound.round_id) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .filter(HumanReadTaskRound.round_state == 2).all()
    if not round_id_info:
        return
    round_id_list = [i.round_id for i in round_id_info]
    for round_id in round_id_list:
        try:
            # 获取 round_id 下的 group_id
            round_group_member = get_round_group_member(new_session, round_id)
            group_id_list = list(round_group_member.keys())
            assign_group_id_list = []
            for group_id in group_id_list:
                reviewer_load = get_group_reviewer_load(group_id, round_id)
                for user_id in reviewer_load:
                    load_num = reviewer_load[user_id]
                    if load_num < configs.ASSIGN_ANSWER_THRESHOLD:
                        assign_group_id_list.append(group_id)
                        break
            if assign_group_id_list:
                round_data = get_round_data_by_id(new_session, round_id, False)
                _, round_group_dict, _ = check_round_group_member(new_session, [round_data], False)
                distribute_human_task_data([round_data], round_group_dict, False, assign_group_id_list)
        except:
            logger.error(f"{round_id} 轮次自动分配考生作答数据失败")
            logger.error(traceback.format_exc())
            continue
