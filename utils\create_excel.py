import traceback

import openpyxl
from openpyxl import Workbook


def create_grade_excel(title_dict, data, file_path):
    try:
        wb = openpyxl.load_workbook(file_path)
    except FileNotFoundError:
        wb = Workbook()

    try:
        ws = wb.worksheets[0]
        if title_dict:
            for i in title_dict:
                ws[i] = title_dict[i]

        for row in data:
            ws.append(row)

        wb.save(file_path)
        return True
    except:
        traceback.print_exc()
        return False


if __name__ == '__main__':
    title_dict = {
        "A1": "姓名",
        "B1": "年龄"
    }
    data = [
        ("john", 18)
    ]
    file_path = "example.xlsx"
    create_grade_excel(title_dict, data, file_path)
