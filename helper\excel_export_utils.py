"""
Excel导出工具类
提供HTTP接口用于前端导出Excel文件
"""

import os
import tempfile
from fastapi import HTTPException
from fastapi.responses import FileResponse
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

from helper.excel_utils import ExcelWriter

# 配置日志
logger = logging.getLogger(__name__)

class ExcelExportService:
    """Excel导出服务类"""
    
    def __init__(self, default_save_path: str = "./temp_output/"):
        """
        初始化Excel导出服务
        
        Args:
            default_save_path: 默认临时保存路径
        """
        self.default_save_path = default_save_path
        # 确保默认路径存在
        os.makedirs(default_save_path, exist_ok=True)
        # 初始化Excel写入器
        self.excel_writer = ExcelWriter(default_save_path=default_save_path)
    
    async def export_to_excel_stream(
        self, 
        data: List[Dict[str, Any]], 
        file_name: str, 
        headers: Optional[List[str]] = None,
        save_path: Optional[str] = None
    ) -> FileResponse:
        """
        将数据导出为Excel文件并返回文件流
        
        Args:
            data: 要导出的数据列表
            file_name: Excel文件名
            headers: 表头列表
            save_path: 保存路径
            
        Returns:
            FileResponse: 文件响应对象
            
        Raises:
            HTTPException: 当导出失败时抛出异常
        """
        try:
            # 使用Excel写入器写入文件
            file_path = self.excel_writer.write_data_to_excel(
                data=data,
                file_name=file_name,
                headers=headers,
                save_path=save_path
            )
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise HTTPException(status_code=500, detail="导出文件生成失败")
            # 使用更安全的文件名编码方式
            import urllib.parse
            encoded_file_name = urllib.parse.quote(file_name, safe='~()*!.\'')
            # 返回文件流
            return FileResponse(
                path=file_path,
                media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                filename=file_name,
                headers={"Content-Disposition": f"attachment; filename*=utf-8''{encoded_file_name}"}
            )
            
        except Exception as e:
            logger.error(f"导出Excel文件失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            # 清理默认保存路径下的所有文件
            if os.path.exists(self.default_save_path):
                for file in os.listdir(self.default_save_path):
                    file_path = os.path.join(self.default_save_path, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
        except Exception as e:
            logger.warning(f"清理临时文件失败: {str(e)}")

# 全局实例
excel_export_service = ExcelExportService()
