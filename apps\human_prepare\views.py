import datetime
from fastapi import APIRouter, Depends
from apps.base.global_cache import get_redis_ques_info_dict
from settings import logger
from sqlalchemy import select, exists, and_, func
from sqlalchemy.orm import Session
from typing import Any
import random
import os
import shutil
from pathlib import Path
from fastapi.responses import FileResponse

from apps.permission.services import get_data_permission_sub_id_list
from apps.projects_manage.services import subject_query_condition
from apps.read_paper.common_services import get_user_data_flag
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.human_prepare.schemas import (
    CreateSampleReq,
    GetSampleReq,
    GetPrepareStuAnswerBySampleIdReq,
    GetAnswerSampleListReq,
    CreateAnswerSampleReq,
    DeleteAnswerSampleReq,
    CreatePrepareProcessReq,
    GetPrepareStuReq,
)
from apps.human_prepare.models import HumanPaperSample, HumanPaperSampleDetail, HumanAnswerSample, HumanPrepareProcess
from apps.human_prepare.services import split_into_n_parts
from apps.models.models import TaskExecuteRecord, StuAnswer, ExamStudent, ExamQuestion, BusinessQuesType, Subject, UserInfo, ExamPaper, PaperDetail, QuesType, Project
from helper import response_utils
from factory_apps import session_depend
from utils.utils import create_timestamp_id
from settings import configs
from apps.human_task_manage.models import HumanRoundDistriAnswer, HumanReadTask, HumanReadTaskRound
from helper.image_utils import concat_images_vertically
from collections import defaultdict

prepare_router = APIRouter()


@prepare_router.post(path="/get_sample_list", response_model=BaseResponse, summary="获取抽样列表")
async def get_sample_list(query: GetSampleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取抽样列表")
    c_user_id = user.get("user_id")
    sub_ids = get_data_permission_sub_id_list(new_session, c_user_id)
    if query.subject_id_list:
        sub_ids = [x for x in sub_ids if x in query.subject_id_list]

    condition = HumanPaperSample.subject_id.in_(sub_ids)

    if query.exam_mode:
        condition.append(Subject.exam_mode == query.exam_mode)

    sample_select = (
        select(
            HumanPaperSample.paper_sample_id,
            HumanPaperSample.subject_id,
            HumanPaperSample.ques_code,
            HumanPaperSample.sample_count,
            HumanPaperSample.score_count,
            HumanPaperSample.sample_state,
            HumanPaperSample.c_user_id,
            HumanPaperSample.updated_time,
            HumanPaperSample.query_conditions,
            HumanPaperSample.start_time,
            HumanPaperSample.end_time,
            BusinessQuesType.ques_type_name,
            Subject.subject_name,
            Subject.exam_mode,
            UserInfo.username,
        )
        .outerjoin(Subject, HumanPaperSample.subject_id == Subject.subject_id)
        # .outerjoin(ExamQuestion, and_(HumanPaperSample.ques_code == ExamQuestion.ques_code, ExamQuestion.parent_ques_id.is_(None)))
        .outerjoin(ExamQuestion, HumanPaperSample.ques_code == ExamQuestion.ques_id)
        .outerjoin(BusinessQuesType, ExamQuestion.business_ques_type_id == BusinessQuesType.business_ques_type_id)
        .outerjoin(UserInfo, HumanPaperSample.c_user_id == UserInfo.user_id)
        # .where(condition)
    )
    # 获取分页参数
    current_page = query.current_page
    page_size = query.page_size
    offset = (current_page - 1) * page_size

    # 添加分页限制
    sample_select = sample_select.limit(page_size).offset(offset)
    sample_list = new_session.execute(sample_select).all()
    sample_list = [
        {**dict(row._mapping), "exam_mode": "抽参" if dict(row._mapping)["exam_mode"] == 0 else "抽卷", "progress": f"{(row[4] / row[5] * 100):.2f}%" if row[4] and row[5] else "0.00%"}
        for row in sample_list
    ]

    # 构建count查询
    count_select = select(func.count()).select_from(HumanPaperSample).outerjoin(Subject, HumanPaperSample.subject_id == Subject.subject_id).where(condition)
    total = new_session.execute(count_select).scalar()

    return BaseResponse(msg="获取抽样列表", data={"list": sample_list, "total": total, "page": current_page, "page_size": page_size})


# @prepare_router.post(path="/get_stu_answer_list", response_model=BaseResponse, summary="获取考生作答列表")
# async def get_stu_answer_list(query: GetPrepareStuAnswerReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
#     logger.info(f"{user['username']} 获取考生作答列表")
#     current_page, page_size, subject_id, exam_session_list, exam_area_code_list, exam_point_code_list, exam_room_code_list, ques_type_code_list, ques_code_list, score_list, number_list = (
#         query.model_dump().values()
#     )
#     limit = current_page - 1
#     offset = limit * page_size
#     answer_ids = []
#     # 动态条件
#     conditions = [StuAnswer.subject_id == subject_id]

#     stu_answer_id_list = (
#         select(StuAnswer.answer_id)
#         .outerjoin(ExamStudent, and_(StuAnswer.allow_exam_num == ExamStudent.allow_exam_num, StuAnswer.subject_id == ExamStudent.subject_id))
#         .outerjoin(ExamQuestion, ExamQuestion.ques_id == StuAnswer.ques_id)
#         .outerjoin(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id)
#     )

#     stu_answer_list = (
#         select(
#             StuAnswer.answer_id,
#             StuAnswer.exam_session,
#             StuAnswer.set_std_score,
#             StuAnswer.ques_order,
#             ExamStudent.exam_province_name,
#             ExamStudent.exam_city_name,
#             ExamStudent.exam_room_code,
#             ExamStudent.stu_secret_num,
#             ExamStudent.exam_point_name,
#             ExamQuestion.ques_type_code,
#             QuesType.ques_type_name,
#             # BusinessQuesType.ques_type_score,
#             ExamQuestion.ques_code,
#             ExamQuestion.ques_score,
#         )
#         .outerjoin(ExamStudent, StuAnswer.allow_exam_num == ExamStudent.allow_exam_num)
#         .outerjoin(ExamQuestion, ExamQuestion.ques_id == StuAnswer.ques_id)
#         .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code)
#     )

#     if exam_session_list:
#         conditions.append(StuAnswer.exam_session.in_(exam_session_list))
#     if exam_area_code_list:
#         conditions.append(ExamStudent.exam_area_code.in_(exam_area_code_list))
#     if exam_point_code_list:
#         conditions.append(ExamStudent.exam_point_code.in_(exam_point_code_list))
#     if exam_room_code_list:
#         conditions.append(ExamStudent.exam_room_code.in_(exam_room_code_list))
#     # if ques_type_code_list:
#     #     conditions.append(BusinessQuesType.business_ques_type_id.in_(ques_type_code_list))
#     if ques_code_list:
#         qid_sql = select(ExamQuestion.ques_id).where(and_(ExamQuestion.parent_ques_id.in_(ques_code_list), (ExamQuestion.ques_type_code.in_(["D", "E", "F", "G"]))))
#         ques_ids = new_session.execute(qid_sql).all()
#         if ques_ids:
#             qids = [q[0] for q in ques_ids]
#             ques_code_list += qids
#         conditions.append(ExamQuestion.ques_id.in_(ques_code_list))

#     if score_list:
#         for index, score in enumerate(score_list):
#             new_condition = list(conditions)
#             op, value, *rest = score
#             if op == 0:
#                 new_condition.append(ExamQuestion.ques_score == value)
#             elif op == 1:
#                 new_condition.append(ExamQuestion.ques_score >= value)
#             elif op == 2:
#                 new_condition.append(ExamQuestion.ques_score > value)
#             elif op == 3:
#                 new_condition.append(ExamQuestion.ques_score < value)
#             elif op == 4:
#                 new_condition.append(ExamQuestion.ques_score <= value)
#             elif op == 5:
#                 new_condition.append(ExamQuestion.ques_score != value)
#             elif op == 6:
#                 new_condition.append(ExamQuestion.ques_score >= value)
#                 new_condition.append(ExamQuestion.ques_score <= rest[0])

#             paged_data = new_session.execute(stu_answer_id_list.where(and_(*new_condition))).all()
#             # 提取answer_id列表并随机采样
#             answer_id_list = [row.answer_id for row in paged_data]
#             if answer_id_list:
#                 a_ids = random.sample(answer_id_list, number_list[index])
#                 answer_ids.extend(a_ids)

#     # 对aid分页，再过滤返回
#     # 查询分页数据（在基础查询上加 limit/offset）
#     answer_condition = StuAnswer.answer_id.in_(answer_ids[offset : offset + page_size])
#     paged_data = new_session.execute(stu_answer_list.where(answer_condition)).all()
#     # 只返回answer_ids列表，避免序列化Row对象
#     data = {"data": [dict(row._mapping) for row in paged_data], "answer_ids": answer_ids}
#     return BaseResponse(msg="获取考生作答列表", data=data)


@prepare_router.post(path="/get_stu_list", response_model=BaseResponse, summary="获取考生列表")
async def get_stu_list(query: GetPrepareStuReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考生作答列表")
    ids_list, number = query.model_dump().values()
    stu_ids = []

    for ids in ids_list:
        # 1 根据条件过滤所有考区
        exam_city_codes_sql = select(ExamStudent.exam_city_code).where(and_((ExamStudent.subject_id == ids[1]), (ExamStudent.exam_session == ids[2]), (ExamStudent.is_all_do == 0))).distinct()
        exam_city_codes = new_session.execute(exam_city_codes_sql).all()
        if len(exam_city_codes) == 0:
            continue
        # 2 根据抽取份数分配每个考区抽取份数
        example_counts = split_into_n_parts(number, len(exam_city_codes))
        for index, city_code in enumerate(exam_city_codes):
            # 3 根据抽取份数抽取考生
            stu_ids_sql = select(ExamStudent.stu_secret_num).where(
                and_((ExamStudent.subject_id == ids[1]), (ExamStudent.exam_session == ids[2]), (ExamStudent.is_all_do == 0), (ExamStudent.exam_city_code == city_code[0]))
            )
            paged_data = new_session.execute(stu_ids_sql).all()
            # 提取answer_id列表并随机采样
            stu_id_list = [row.stu_secret_num for row in paged_data]
            if stu_id_list:
                sample_ids = random.sample(stu_id_list, example_counts[index])
                stu_ids.append({"ids": sample_ids, "condition": ids})

    projects = new_session.query(Project.project_id, Project.project_name).all()
    subjects = new_session.query(Subject.subject_id, Subject.subject_name).all()

    stus_data = []
    for stu_id in stu_ids:
        for id in stu_id["ids"]:
            p_name = next(p for p in projects if p.project_id == stu_id["condition"][0])[1]
            s_name = next(p for p in subjects if p.subject_id == stu_id["condition"][1])[1]
            stus_data.append({"stu_id": id, "project_name": p_name, "subject_name": s_name, "session": stu_id["condition"][2]})
    data = {"stus_data": stus_data, "stu_ids": stu_ids}
    return BaseResponse(msg="获取考生作答列表", data=data)


# @prepare_router.post(path="/get_stu_list_by_answer_id", response_model=BaseResponse, summary="获取考生列表by_id")
# async def get_stu_answer_list_by_answer_id(query: GetPrepareStuByIdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
#     logger.info(f"{user['username']} 获取考生作答列表by id")
#     stu_id_list = query.stu_id_list

#     stu_list = (
#         select(
#             ExamStudent.exam_session,
#             ExamStudent.stu_secret_num,
#             Project.project_name,
#             Subject.subject_name,
#         )
#         .outerjoin(Project, Project.project_id == ExamStudent.project_id)
#         .outerjoin(Subject, Subject.subject_id == ExamStudent.subject_id)
#     )

#     # 对aid分页，再过滤返回
#     # 查询分页数据（在基础查询上加 limit/offset）
#     condition = ExamStudent.stu_id.in_(stu_id_list)
#     paged_data = new_session.execute(stu_list.where(condition)).all()
#     paged_data = [dict(row._mapping) for row in paged_data]
#     data = {"data": paged_data}
#     return BaseResponse(msg="获取考生列表by id", data=data)


# 导出考生作答图片
@prepare_router.post(path="/export_stu_answer_list_by_sample_id", response_model=None, summary="导出考生作答")
async def export_stu_answer_list_by_sample_id(query: GetPrepareStuAnswerBySampleIdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 导出考生作答")
    sample_id = query.sample_id

    sample = new_session.get(HumanPaperSample, sample_id)

    # 获取大题列表
    que_list_sql = select(
        ExamQuestion.ques_id,
        ExamQuestion.parent_ques_id,
    )
    paged_data = new_session.execute(que_list_sql).all()

    big_quests = [{"ques_id": row.ques_id} for row in paged_data if row.parent_ques_id is None]
    stu_list_all = []
    # 将stu_list列表根据project_id和subject_id分组

    stu_list_by_project_subject = defaultdict(list)
    # 遍历大题列表，获取所有考生id对应的作答
    for big_quest in big_quests:
        # 获取big_quest下的小题
        sub_ques_ids = [row.ques_id for row in paged_data if row.parent_ques_id == big_quest["ques_id"]]

        stu_list_sql = select(
            StuAnswer.ques_id,
            StuAnswer.parent_ques_id,
            StuAnswer.answer_image_path,
            StuAnswer.sub_ques_order,
            StuAnswer.stu_secret_num,
            StuAnswer.project_id,
            StuAnswer.subject_id,
            StuAnswer.exam_session,
        ).order_by(StuAnswer.sub_ques_order)

        # 大题
        if not sub_ques_ids:
            for stu_id in sample.stu_ids:
                stu_list = new_session.execute(
                    stu_list_sql.where(
                        and_(
                            StuAnswer.ques_id == big_quest["ques_id"],
                            StuAnswer.stu_secret_num.in_(stu_id["ids"]),
                            StuAnswer.subject_id == stu_id["condition"][1],
                            StuAnswer.exam_session == stu_id["condition"][2],
                        )
                    )
                ).all()

                for answer in stu_list:
                    # 使用元组作为键，同时包含project_id和subject_id
                    key = (answer.project_id, answer.subject_id, answer.exam_session, answer.parent_ques_id)
                    answer_image_path = os.path.join(configs.PROJECT_PATH, "server_static", answer.answer_image_path)
                    stu_list_by_project_subject[key].append((answer.stu_secret_num, answer_image_path))

                # # 将分组后的数据添加到总列表中
                # for key, images in stu_list_by_project_subject.items():
                #     iamge_save_path = os.path.join(configs.PROJECT_PATH, "server_static/export/image", sample_id, key[5], ".png")
                #     image_save_path = Path(iamge_save_path)
                #     image_save_path.parent.mkdir(parents=True, exist_ok=True)
                #     concat_images_vertically(images, iamge_save_path)
                #     stu_list_all.append({"project_id": key[0], "subject_id": key[1], "exam_session": key[2], "stu_secret_num": key[3], "answer_image_path": iamge_save_path})

                # for answer in stu_list:
                # stu_list_all.extend(stu_list)
        else:
            # 小题
            stu_list_by_project_subject_small = defaultdict(list)
            for stu_id in sample.stu_ids:
                stu_list = new_session.execute(
                    stu_list_sql.where(
                        and_(
                            StuAnswer.ques_id.in_(sub_ques_ids),
                            StuAnswer.stu_secret_num.in_(stu_id["ids"]),
                            StuAnswer.subject_id == stu_id["condition"][1],
                            StuAnswer.exam_session == stu_id["condition"][2],
                        )
                    )
                ).all()
                for answer in stu_list:
                    # 使用元组作为键，同时包含project_id和subject_id
                    key = (answer.project_id, answer.subject_id, answer.exam_session, answer.stu_secret_num, answer.parent_ques_id)
                    stu_list_by_project_subject_small[key].append(answer.answer_image_path)

                # 将分组后的数据添加到总列表中
                for key, images in stu_list_by_project_subject_small.items():
                    iamge_save_path = os.path.join(configs.PROJECT_PATH, "server_static/export/image", sample_id, configs.snow_worker.get_id() + ".png")
                    image_save_path = Path(iamge_save_path)
                    image_save_path.parent.mkdir(parents=True, exist_ok=True)
                    concat_images_vertically(images, iamge_save_path)
                    new_key = (key[0], key[1], key[2], key[4])
                    stu_list_by_project_subject[new_key].append((key[3], iamge_save_path))

    projects = new_session.query(Project.project_id, Project.project_name).all()
    subjects = new_session.query(Subject.subject_id, Subject.subject_name).all()
    questions = new_session.query(ExamQuestion.ques_id, ExamQuestion.knowledge_show).all()

    save_path_root = os.path.join(configs.PROJECT_PATH, "server_static\\export", sample_id)

    # 遍历stu_list_all，按照要求生成作答图片目录
    for key, value in stu_list_by_project_subject.items():
        # 提取必要的信息
        project_id = key[0]
        subject_id = key[1]
        exam_session = key[2]
        ques_code = key[3]
        for v in value:
            stu_secret_num = v[0]
            image_path = v[1]

            # 获取项目和科目名称
            project_name = next((p.project_name for p in projects if p.project_id == project_id), None)
            subject_name = next((s.subject_name for s in subjects if s.subject_id == subject_id), None)
            knowledge_show = next((s.knowledge_show for s in questions if s.ques_id == ques_code), None)

            if not project_name or not subject_name:
                continue  # 如果未找到项目或科目名称，跳过当前条目

            # 创建目录路径
            if knowledge_show is None:
                knowledge_show = ques_code
            save_path_str = os.path.join(save_path_root, project_name, subject_name + str(exam_session), knowledge_show)
            save_path = Path(save_path_str)
            save_path.mkdir(parents=True, exist_ok=True)

            # 假设image_path是一个文件路径，将其复制到新目录中
            if os.path.exists(image_path):
                shutil.copy(image_path, os.path.join(save_path_str, stu_secret_num + ".png"))

    # 压缩save_path文件夹中的文件为zip包，返回前端
    zip_filename = f"{sample_id}.zip"
    zip_path = os.path.join(configs.PROJECT_PATH, "server_static\\export", zip_filename)
    shutil.make_archive(base_name=zip_path.replace(".zip", ""), format="zip", root_dir=save_path_root)

    # 返回文件响应
    return FileResponse(path=zip_path, filename=zip_filename, media_type="application/zip")


# @prepare_router.post(path="/get_stu_answer_list_count", response_model=BaseResponse, summary="获取考生作答列表数量")
# async def get_stu_answer_list_count(query: GetPrepareStuAnswerReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
#     logger.info(f"{user['username']} 获取考生作答列表数量")
#     current_page, page_size, subject_id, exam_session_list, exam_area_code_list, exam_point_code_list, exam_room_code_list, ques_type_code_list, ques_code_list, score_list, number_list = (
#         query.model_dump().values()
#     )
#     # 动态条件
#     conditions = [StuAnswer.subject_id == subject_id]

#     if exam_session_list:
#         conditions.append(StuAnswer.exam_session.in_(exam_session_list))
#     if exam_area_code_list:
#         conditions.append(ExamStudent.exam_area_code.in_(exam_area_code_list))
#     if exam_point_code_list:
#         conditions.append(ExamStudent.exam_point_code.in_(exam_point_code_list))
#     if exam_room_code_list:
#         conditions.append(ExamStudent.exam_room_code.in_(exam_room_code_list))
#     # if ques_type_code_list:
#     #     conditions.append(BusinessQuesType.business_ques_type_id.in_(ques_type_code_list))
#     if ques_code_list:
#         conditions.append(ExamQuestion.ques_id.in_(ques_code_list))

#     count = 0
#     if score_list:
#         for index, score in enumerate(score_list):
#             new_condition = list(conditions)
#             op, value, *rest = score
#             if op == 0:
#                 new_condition.append(BusinessQuesType.ques_type_score == value)
#             elif op == 1:
#                 new_condition.append(BusinessQuesType.ques_type_score >= value)
#             elif op == 2:
#                 new_condition.append(BusinessQuesType.ques_type_score > value)
#             elif op == 3:
#                 new_condition.append(BusinessQuesType.ques_type_score < value)
#             elif op == 4:
#                 new_condition.append(BusinessQuesType.ques_type_score <= value)
#             elif op == 5:
#                 new_condition.append(BusinessQuesType.ques_type_score != value)
#             elif op == 6:
#                 new_condition.append(BusinessQuesType.ques_type_score >= value)
#                 new_condition.append(BusinessQuesType.ques_type_score <= rest[0])

#             count = (
#                 new_session.query(ExamStudent, ExamQuestion, BusinessQuesType)
#                 .join(StuAnswer, and_(StuAnswer.allow_exam_num == ExamStudent.allow_exam_num, StuAnswer.subject_id == ExamStudent.subject_id))
#                 .join(ExamQuestion, ExamQuestion.ques_id == StuAnswer.ques_id)
#                 .join(BusinessQuesType, BusinessQuesType.ques_type_code == ExamQuestion.ques_type_code)
#                 .with_entities(func.count())
#                 .filter(and_(*new_condition))
#                 .scalar()
#             )

#     data = {"data": count}
#     return BaseResponse(msg="获取考生作答列表数量", data=data)


# @prepare_router.post(path="/get_stu_answer_list_by_answer_id", response_model=BaseResponse, summary="获取考生作答列表by_id")
# async def get_stu_answer_list_by_answer_id(query: GetPrepareStuAnswerByIdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
#     logger.info(f"{user['username']} 获取考生作答列表by id")
#     answer_id_list = query.answer_id_list

#     stu_answer_list = (
#         select(
#             StuAnswer.answer_id,
#             StuAnswer.exam_session,
#             StuAnswer.set_std_score,
#             StuAnswer.ques_order,
#             ExamStudent.exam_province_name,
#             ExamStudent.exam_city_name,
#             ExamStudent.exam_room_code,
#             ExamStudent.stu_secret_num,
#             ExamStudent.exam_point_name,
#             ExamQuestion.ques_type_code,
#             QuesType.ques_type_name,
#             ExamQuestion.ques_code,
#             ExamQuestion.ques_score,
#         )
#         .outerjoin(ExamStudent, StuAnswer.allow_exam_num == ExamStudent.allow_exam_num)
#         .outerjoin(ExamQuestion, ExamQuestion.ques_id == StuAnswer.ques_id)
#         .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code)
#     )

#     # 对aid分页，再过滤返回
#     # 查询分页数据（在基础查询上加 limit/offset）
#     answer_condition = StuAnswer.answer_id.in_(answer_id_list)
#     paged_data = new_session.execute(stu_answer_list.where(answer_condition)).all()
#     paged_data = [dict(row._mapping) for row in paged_data]
#     data = {"data": paged_data}
#     return BaseResponse(msg="获取考生作答列表by id", data=data)


# @prepare_router.post(path="/get_stu_answer_id_list_by_sample_id", response_model=BaseResponse, summary="获取考生作答id列表通过抽样")
# async def get_stu_answer_id_list_by_sample_id(query: GetPrepareStuAnswerBySampleIdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
#     logger.info(f"{user['username']} 获取考生作答id列表通过抽样")

#     stu_answer_list = select(
#         HumanRoundDistriAnswer.answer_id,
#     ).where(HumanRoundDistriAnswer.round_id == query.sample_id)
#     paged_data = new_session.execute(stu_answer_list).all()
#     paged_data = [dict(row._mapping) for row in paged_data]
#     data = {"data": paged_data}
#     return BaseResponse(msg="获取考生作答id列表通过抽样", data=data)


@prepare_router.post(path="/create_sample", response_model=BaseResponse, summary="创建抽样")
async def create_sample(query: CreateSampleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建抽样")
    c_user_id = user.get("user_id")
    query_conditions, stu_ids = query.model_dump().values()

    # answers = new_session.execute(select(StuAnswer.answer_id, StuAnswer.stu_secret_num, StuAnswer.ques_id).where(StuAnswer.answer_id.in_(answer_id_list))).all()
    # qids = [a.ques_id for a in answers]
    # quests = new_session.execute(select(ExamQuestion.ques_id, ExamQuestion.ques_code).where(ExamQuestion.ques_id.in_(qids))).all()

    try:
        sample = HumanPaperSample(
            paper_sample_id=configs.snow_worker.get_id(),
            # subject_id=subject_id,
            # ques_code=ques_code,
            sample_count=len(stu_ids),
            c_user_id=c_user_id,
            stu_ids=stu_ids,
            query_conditions=query_conditions,
            sample_state=0,
            updated_time=datetime.datetime.now(),
        )
        new_session.add(sample)
        # for a_id in answer_id_list:
        #     answer = next(p for p in answers if p.answer_id == a_id)
        #     quest = next(p for p in quests if p.ques_id == answer[2])
        #     detail = HumanRoundDistriAnswer(
        #         distri_id=configs.snow_worker.get_id(),
        #         round_id=sample.paper_sample_id,
        #         answer_id=a_id,
        #         ques_code=quest[1],
        #         stu_secret_num=answer[1],
        #         updated_time=datetime.datetime.now(),
        #     )
        #     new_session.add(detail)

        new_session.commit()
        return BaseResponse(msg=f"创建抽样成功", data={"id", sample.paper_sample_id})
    except Exception as e:
        logger.error(f"创建评分任务失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="创建评分任务失败")


@prepare_router.post(path="/delete_sample", response_model=BaseResponse, summary="删除抽样")
async def delete_sample(query: GetPrepareStuAnswerBySampleIdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 删除抽样")
    try:
        # # 删除抽样详情
        # detail_stmt = select(HumanPaperSampleDetail).where(HumanPaperSampleDetail.paper_sample_id == query.sample_id)
        # details = new_session.execute(detail_stmt).scalars().all()
        # for d in details:
        #     new_session.delete(d)
        # 删除抽样主记录
        sample = new_session.get(HumanPaperSample, query.sample_id)
        if sample:
            new_session.delete(sample)
        new_session.commit()
        return BaseResponse(msg="删除抽样成功")
    except Exception as e:
        logger.error(f"删除抽样失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="删除抽样失败")


# # 开始评分
# @prepare_router.post(path="/start_scoring", response_model=BaseResponse, summary="开始评分")
# async def start_scoring(query: GetPrepareStuAnswerBySampleIdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
#     """
#     根据 paper_sample_id 在 HumanPaperSample 表中查找记录，若存在则将 sample_state 修改为 1，start_time 修改为当前时间。
#     """
#     logger.info(f"{user['username']} 开始评分")
#     try:
#         # 查询 HumanPaperSample 记录
#         sample = new_session.get(HumanPaperSample, query.sample_id)
#         if not sample:
#             return BaseResponse(code=response_utils.not_found, msg="抽样记录未找到")

#         # 更新字段
#         sample.sample_state = 1
#         sample.start_time = datetime.now()
#         new_session.commit()
#         return BaseResponse(msg="开始评分成功")
#     except Exception as e:
#         logger.error(f"开始评分失败，{e}")
#         new_session.rollback()
#         return BaseResponse(code=response_utils.server_error, msg="开始评分失败")


# -------------------------------------------------
# HumanAnswerSample 接口
# -------------------------------------------------
@prepare_router.post(path="/get_answer_sample_list", response_model=BaseResponse, summary="获取作答样卷列表")
async def get_answer_sample_list(query: GetAnswerSampleListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    返回 HumanAnswerSample 表的记录，并关联科目名称，支持分页并可按科目、题型、题号过滤。
    """
    logger.info(f"{user['username']} 获取作答样卷列表")
    # 构建过滤条件
    conditions = []
    if query.subject_id:
        conditions.append(StuAnswer.subject_id == query.subject_id)
    if query.business_ques_type_id:
        conditions.append(BusinessQuesType.ques_type_name == query.business_ques_type_id)
    if query.ques_code:
        conditions.append(ExamQuestion.ques_code == query.ques_code)

    # 统计总条数（考虑过滤条件）
    total_stmt = (
        select(func.count())
        .select_from(HumanAnswerSample)
        .outerjoin(StuAnswer, HumanAnswerSample.answer_id == StuAnswer.answer_id)
        .outerjoin(Subject, StuAnswer.subject_id == Subject.subject_id)
        .outerjoin(ExamQuestion, StuAnswer.ques_id == ExamQuestion.ques_id)
        .outerjoin(BusinessQuesType, ExamQuestion.business_ques_type_id == BusinessQuesType.business_ques_type_id)
    )
    if conditions:
        total_stmt = total_stmt.where(and_(*conditions))
    total = new_session.execute(total_stmt).scalar()

    # 计算分页偏移量
    offset = (query.current_page - 1) * query.page_size

    # 查询数据
    stmt = (
        select(
            HumanAnswerSample.answer_sample_id,
            HumanAnswerSample.answer_id,
            Subject.subject_name,
            BusinessQuesType.ques_type_name,
            BusinessQuesType.ques_type_score,
            Subject.exam_mode,
            PaperDetail.ques_code,
            StuAnswer.ques_order,
            PaperDetail.ques_code,
            ExamPaper.paper_name,
            HumanPaperSampleDetail.answer_score,
        )
        .outerjoin(StuAnswer, HumanAnswerSample.answer_id == StuAnswer.answer_id)
        .outerjoin(Subject, StuAnswer.subject_id == Subject.subject_id)
        .outerjoin(ExamQuestion, StuAnswer.ques_id == ExamQuestion.ques_id)
        .outerjoin(BusinessQuesType, ExamQuestion.business_ques_type_id == BusinessQuesType.business_ques_type_id)
        .outerjoin(ExamPaper, Subject.subject_id == ExamPaper.subject_id)
        .outerjoin(PaperDetail, PaperDetail.paper_id == ExamPaper.paper_id)
        .outerjoin(HumanPaperSampleDetail, HumanPaperSampleDetail.answer_id == HumanAnswerSample.answer_id)
    )
    if conditions:
        stmt = stmt.where(and_(*conditions))
    stmt = stmt.limit(query.page_size).offset(offset)
    result = new_session.execute(stmt).all()

    # 抽参作答id

    data = [dict(row._mapping) for row in result]
    return BaseResponse(
        msg="获取作答样卷列表成功",
        data={
            "list": data,
            "total": total,
            "page": query.current_page,
            "page_size": query.page_size,
        },
    )


@prepare_router.post(path="/create_answer_sample", response_model=BaseResponse, summary="新增作答样卷")
async def create_answer_sample(query: CreateAnswerSampleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    向 HumanAnswerSample 表插入一条记录。
    """
    logger.info(f"{user['username']} 新增作答样卷")
    try:
        sample = HumanAnswerSample(
            answer_sample_id=configs.snow_worker.get_id(),
            answer_id=query.answer_id,
        )
        new_session.add(sample)
        new_session.commit()
        return BaseResponse(msg="新增作答样卷成功")
    except Exception as e:
        logger.error(f"新增作答样卷失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="新增作答样卷失败")


@prepare_router.post(path="/delete_answer_sample", response_model=BaseResponse, summary="删除作答样卷")
async def delete_answer_sample(query: DeleteAnswerSampleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    根据 answer_sample_id 删除 HumanAnswerSample 表中的记录。如果 answer_id 有值则根据 answer_id 过滤数据删除。
    """
    logger.info(f"{user['username']} 删除作答样卷")
    try:
        # 如果提供了 answer_id，则添加过滤条件
        if query.answer_sample_id:
            # 构建基础查询
            stmt = select(HumanAnswerSample).where(HumanAnswerSample.answer_sample_id == query.answer_sample_id)

        # 如果提供了 answer_id，则添加过滤条件
        if query.answer_id:
            stmt = select(HumanAnswerSample).where(HumanAnswerSample.answer_id == query.answer_id)

        # 执行查询
        obj = new_session.execute(stmt).scalars().first()

        if obj:
            new_session.delete(obj)
            new_session.commit()
            return BaseResponse(msg="删除作答样卷成功")
        else:
            return BaseResponse(msg="作答样卷未找到")
    except Exception as e:
        logger.error(f"删除作答样卷失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="删除作答样卷失败")


@prepare_router.post(path="/get_prepare_process_list", response_model=BaseResponse, summary="获取准备流程列表")
async def get_prepare_process_list(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    返回 HumanPrepareProcess 表的所有记录。
    """
    logger.info(f"{user['username']} 获取准备流程列表")
    stmt = select(
        HumanPrepareProcess.process_id,
        HumanPrepareProcess.process_name,
        HumanPrepareProcess.process_type,
        HumanPrepareProcess.parent_process_type,
        HumanPrepareProcess.process_status,
        HumanPrepareProcess.fail_reason,
        HumanPrepareProcess.progress,
    )
    result = new_session.execute(stmt).all()
    data = [dict(row._mapping) for row in result]
    return BaseResponse(msg="获取准备流程列表成功", data=data)


@prepare_router.post(path="/add_prepare_process_test_data", response_model=BaseResponse, summary="添加准备流程测试数据")
async def add_prepare_process_test_data(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    为每种流程类型（1-5）添加一条测试数据。
    """
    logger.info(f"{user['username']} 添加准备流程测试数据")
    try:
        for t in range(1, 6):
            process = HumanPrepareProcess(
                process_id=configs.snow_worker.get_id(),
                process_name=f"测试流程{t}",
                process_type=t,
                parent_process_type=0,
                process_status=0,
                fail_reason=None,
                progress=0,
            )
            new_session.add(process)
        new_session.commit()
        return BaseResponse(msg="添加准备流程测试数据成功")
    except Exception as e:
        logger.error(f"添加准备流程测试数据失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="添加准备流程测试数据失败")


@prepare_router.post(path="/create_prepare_process", response_model=BaseResponse, summary="创建准备流程")
async def create_prepare_process(query: CreatePrepareProcessReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    向 HumanPrepareProcess 表插入一条记录。
    """
    logger.info(f"{user['username']} 创建准备流程")
    try:
        process = HumanPrepareProcess(
            process_id=configs.snow_worker.get_id(),
            process_name=query.process_name,
            process_type=query.process_type,
            parent_process_type=query.parent_process_type,
            process_status=0,
            fail_reason=query.fail_reason,
            progress=query.progress,
        )
        new_session.add(process)
        new_session.commit()
        return BaseResponse(msg="创建准备流程成功")
    except Exception as e:
        logger.error(f"创建准备流程失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="创建准备流程失败")
