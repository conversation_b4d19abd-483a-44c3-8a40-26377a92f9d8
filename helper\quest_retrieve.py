import jieba

# import jieba.analyse
from helper.string_helper import calculate_similay

topK = 10


def readwordslist(filepath):
    # 截取line第一个空格前的字符串，如果没有空格就是整行
    stopwords = [line.split(" ", 1)[0] if " " in line else line.strip() for line in open(filepath, "r", encoding="utf-8").readlines()]
    return stopwords


stop_words = readwordslist(r"server_static\stopwords\hit_stopwords.txt")
dict_words = readwordslist(r"server_static\stopwords\dict.txt")
# jieba.analyse.set_stop_words("./stopwords/hit_stopwords.txt")


# 接收字符串，使用jieba分词，返回分词后的列表
def cut_string(content):
    # tags = jieba.analyse.extract_tags(content, topK=topK)
    # 使用精确模式分词
    words = list(jieba.cut(content, cut_all=False))
    # 过滤掉停用词
    filtered_words = [word for word in words if word not in stop_words and word.strip()]
    return filtered_words

    # # 过滤掉停用词
    # filtered_words = [word for word in words if word not in stop_words and word.strip()]
    # # 遍历filtered_words中的每个元素，元素如果在dict_words中就将下标添加到filtered_words_dict中，不再就添加到filtered_words_other中
    # filtered_words_dict = []
    # filtered_words_other = []
    # for word in filtered_words:
    #     if word in dict_words:
    #         filtered_words_dict.append(dict_words.index(word))
    #     else:
    #         filtered_words_other.append(word)
    # return filtered_words_dict, filtered_words_other


# 定义一个函数：比较两个列表的元素相同的个数
def compare_list(list1, list2):
    if len(list1) == 0 or len(list2) == 0:
        return 0
    count = 0
    same_word = []
    for i in list1:
        if i in list2:
            count += 1
            same_word.append(i)
    return count, same_word
    # return count / (len(list1) + len(list2) - count)


def quest_same_two(quets1, quest2List, threshold):  # ,sameCount
    same_word_count = []
    for j in range(len(quest2List)):
        left_quest_text = quets1[2]
        if quets1[2].startswith(str(quets1[1]) + "."):
            left_quest_text = left_quest_text.removeprefix(str(quets1[1]) + ".")

        right_quest_text = quest2List[j][2]
        if quest2List[j][2].startswith(str(quest2List[j][1]) + "."):
            right_quest_text = right_quest_text.removeprefix(str(quest2List[j][1]) + ".")

        words1 = cut_string(left_quest_text)
        words2 = cut_string(right_quest_text)

        cal_sim = calculate_similay(words1[1], words2[1])

        if threshold and (cal_sim[0] >= threshold):
            same_word_count.append({"i": quets1[1], "startA": quets1[3], "endA": quets1[4], "j": quest2List[j][1], "startB": quest2List[j][3], "endB": quest2List[j][4], "Similay": cal_sim})  # ,"debug":[words1,words2]

    return same_word_count


def text_same_two(left_quest_text, right_quest_text):
    words1 = cut_string(left_quest_text)
    words2 = cut_string(right_quest_text)
    result = calculate_similay(words1, words2)
    return result
