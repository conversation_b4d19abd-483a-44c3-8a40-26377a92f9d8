import os
import subprocess
import time
import traceback
from itertools import groupby
import operator
import json

import requests
from sqlalchemy import select, and_, text
from typing import Optional

from apps.base.global_cache import get_op_engine_answer_path
from apps.models.models import OperationStep, OperationStepGroup
from apps.models.models import OpEngineMarkRecord, StuAnswer, ExamQuestion
from apps.operation_ques.op_manual_mark_services import op_ques_query_condition
from apps.read_paper.ai_services import judge_mark_result
from factory_apps import session_depend
from settings import configs, logger


"""
调用操作题评分引擎评分的步骤
1.给引擎传入 .jdet 文件
2.引擎找到 .jdet 文件里的 .jvbqt 文件路径
3.解析 .jvbqt 文件
4.返回.jdetrs 文件
"""


def get_op_state_str(state):
    """
    1 表示正在获取数据中，2 表示正在标记中，3 表示正在评分中，4 表示已暂停，5 表示正在取消中，6 表示已取消，7 表示已完成
    """
    if state == 1:
        state_str = "正在获取数据中"
    elif state == 2:
        state_str = "正在标记中"
    elif state == 3:
        state_str = "正在评分中"
    elif state == 4:
        state_str = "已暂停"
    elif state == 5:
        state_str = "正在取消中"
    elif state == 6:
        state_str = "已取消"
    elif state == 7:
        state_str = "已完成"
    else:
        state_str = "未知"
    return state_str


def format_op_answer_data(result, is_dict=False):
    """
    结构化考生作答数据
    """
    # 获取所有文件路径
    file_cache = get_op_engine_answer_path()

    if is_dict:
        grouped = groupby(sorted(result, key=lambda x: x['ques_id']), key=lambda x: x['ques_id'])

        # 构建字典
        result_dict = {
            key: [
                {
                    "answer_id": row['answer_id'],
                    "op_file": file_cache[os.path.basename(row['op_file'])] if row['op_file'] else "",
                    "ques_score": row['score']
                }
                for row in group
            ]
            for key, group in grouped
        }
    else:
        # 按 ques_id 分组
        grouped = groupby(sorted(result, key=operator.attrgetter('ques_id')), key=operator.attrgetter('ques_id'))

        # 构建字典
        result_dict = {
            key: [
                {
                    "answer_id": row.answer_id,
                    "op_file": file_cache[os.path.basename(row.op_file)] if row.op_file else "",
                    "ques_score": row.score
                }
                for row in group
            ]
            for key, group in grouped
        }
    return result_dict


def get_all_op_answer(new_session, is_continue=False):
    """
    获取所有考生操作题作答数据
    """
    op_answer_data = {}
    try:
        # 1.获取所有操作题 ques_id
        ques_id_info = new_session.query(ExamQuestion.ques_id).filter(ExamQuestion.ques_type_code == "G").all()
        if not ques_id_info:
            return op_answer_data
        ques_id_list = [i[0] for i in ques_id_info]
        # 2.根据 ques_id 获取作答数据
        # 构建 SQL 语句（充分利用 ques_id 分区特性提高查询性能）
        if is_continue:
            parts = [
                f"SELECT t_stu_answer.answer_id, t_stu_answer.ques_id, t_stu_answer.op_file, t_stu_answer.score FROM `t_stu_answer` WHERE ques_id = {ques_id} AND running_state = 2"
                for ques_id in ques_id_list]
        else:
            parts = [
                f"SELECT t_stu_answer.answer_id, t_stu_answer.ques_id, t_stu_answer.op_file, t_stu_answer.score FROM `t_stu_answer` WHERE ques_id = {ques_id}"
                for ques_id in ques_id_list]
        sql = " UNION ALL ".join(parts)
        # 执行查询
        result = new_session.execute(text(sql)).fetchall()
        op_answer_data = format_op_answer_data(result) if result else {}
        return True, op_answer_data
    except Exception as e:
        logger.error(f"获取操作题列表失败，{e}")
        traceback.print_exc()
        return False, op_answer_data


def get_condition_op_data(new_session, project_id, subject_id, paper_code, ques_code, ques_order, stu_answer,
                          mark_result, mark_state, stu_score_range, search_time):
    """
    按照筛选条件获取操作题待评数据
    """
    # 操作题
    op_condition = StuAnswer.ques_type_code == "G"
    # 拼凑查询条件
    condition = op_ques_query_condition(new_session, project_id, subject_id, paper_code, ques_code, ques_order,
                                        stu_answer, mark_result, mark_state, stu_score_range, search_time)

    answer_stmt = select(StuAnswer.answer_id, StuAnswer.ques_id, StuAnswer.op_file, StuAnswer.score) \
        .where(and_(condition, op_condition))

    op_answer_data = {}
    try:
        result = new_session.execute(answer_stmt)

        op_answer_data = format_op_answer_data(result) if result else {}
        return True, op_answer_data

    except Exception as e:
        logger.error(f"获取操作题列表失败，{e}")
        traceback.print_exc()
        return False, op_answer_data


def create_new_op_task(new_session, record_id, user_id):
    """
    创建操作题引擎阅卷任务记录
    """
    new_task = OpEngineMarkRecord(record_id=record_id, finish_count=0, running_state=1, c_user_id=user_id)
    new_session.add(new_task)
    new_session.commit()

    data = {
        "record_id": record_id,
        "running_state": 1,
        "finish_count": 0,
        "total_count": 0,
        "percentage": 0,
        "state_str": get_op_state_str(1)
    }
    return data


def supplementary_op_total_record(new_session, record_id, total_count, running_state, filter_condition):
    """
    补充操作题引擎阅卷任务记录
    running_state: 任务状态，1 表示正在获取数据，2 表示正在标记中，3 表示进行中，4 表示暂停，5 表示正在取消中，6 表示已取消，7 表示已完成
    """
    new_session.query(OpEngineMarkRecord).filter(OpEngineMarkRecord.record_id == record_id).update({
        OpEngineMarkRecord.total_count: total_count,
        OpEngineMarkRecord.running_state: running_state,
        OpEngineMarkRecord.filter_condition: filter_condition
    })
    new_session.commit()


def update_op_record(record_id, add_count):
    """
    更新操作题引擎阅卷任务记录
    任务状态，1 表示正在获取数据，2 表示正在标记中，3 表示进行中，4 表示暂停，5 表示正在取消中，6 表示已取消，7 表示已完成
    """
    new_session = next(session_depend())

    try:
        # 先查询记录
        record = new_session.query(OpEngineMarkRecord).filter(
            OpEngineMarkRecord.record_id == record_id
        ).one_or_none()

        if not record:
            raise ValueError(f"没有该 record_id: {record_id}")

        # 计算新的 finish_count
        new_finish_count = record.finish_count + add_count

        # 判断是否完成
        if new_finish_count >= record.total_count:
            record.running_state = 7
            record.finish_count = record.total_count
        else:
            record.running_state = 3
            record.finish_count = new_finish_count

        # 提交事务
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        raise e


def remark_data_as_pending(answer_data_dict: dict, record_id: str, continue_remark_dict: Optional[dict] = None):
    """
    将数据按照 ques_id 分组，充分利用 ques_id 分区，以提高数据标记速度
    每个 ques_id 下的 answer_id 进行分批处理，避免一次性处理大量数据
    """
    if continue_remark_dict:
        answer_data_dict = continue_remark_dict
    new_session = next(session_depend())
    already_remark_ques_id = []
    for ques_id, answer_item in answer_data_dict.items():
        logger.info(f"开始标记 ques_id: {ques_id}")
        answer_id_list = [item["answer_id"] for item in answer_item]
        answer_id_list_length = len(answer_id_list)
        batch_size = configs.OP_ANSWER_MARK_BATCH_NUM
        total_batches = (answer_id_list_length + batch_size - 1) // batch_size  # 计算总批次
        # 分批标记 answer_id
        for batch_num in range(total_batches):
            if is_task_exit(new_session, record_id):
                new_session.query(OpEngineMarkRecord).filter(OpEngineMarkRecord.record_id == record_id).update({
                    OpEngineMarkRecord.already_remark_ques_id: already_remark_ques_id
                })
                new_session.commit()
                return 1
            start_idx = batch_num * batch_size
            end_idx = min((batch_num + 1) * batch_size, answer_id_list_length)
            batch = answer_id_list[start_idx: end_idx]
            # logger.info(f"标记 ques_id: {ques_id}, 批次: {batch_num + 1}/{total_batches}, 标记范围: {start_idx} 到 {end_idx}")
            condition = and_(
                StuAnswer.ques_id == ques_id,
                StuAnswer.answer_id.in_(batch)
            )
            new_session.query(StuAnswer).filter(condition).update({
                StuAnswer.running_state: 2,
                StuAnswer.mark_state: 1,
                StuAnswer.answer_parse: [],
                StuAnswer.mark_result: 4,
                StuAnswer.mark_fail_reason: None,
                StuAnswer.stu_score: None
            })
            new_session.commit()
            logger.info(f"标记 ques_id: {ques_id}, 批次: {batch_num + 1}/{total_batches} 成功")
        already_remark_ques_id.append(ques_id)
        logger.info(f"标记 ques_id: {ques_id} 完成")
    logger.info("所有 ques_id 标记完成")
    return 0


def get_op_rule(ques_id):
    new_session = next(session_depend())
    operation_steps = []
    knowledge_group_id = None
    operation_step_group = {}
    ques_score = None
    op_step_info = new_session.query(OperationStep).filter(OperationStep.question_id == ques_id).all()
    if op_step_info:
        for step in op_step_info:
            knowledge_group_id = step.knowledge_group_id
            para = []
            for index, value in enumerate(
                    [step.para1, step.para2, step.para3, step.para4, step.para5, step.para6, step.para7, step.para8,
                     step.para9]):
                if value:
                    para.append({
                        "id": index + 1,
                        "value": value
                    })

            step_item = {
                "autoid": step.auto_id,
                "questionid": step.question_id,
                "knowledge": step.knowledge,
                "para": para,
                "para0": step.para,
                "para1": step.para1,
                "para2": step.para2,
                "para3": step.para3,
                "para4": step.para4,
                "para5": step.para5,
                "para6": step.para6,
                "para7": step.para7,
                "para8": step.para8,
                "para9": step.para9,
                "paraA": step.paraA,
                "paraArr": [
                    step.para,
                    step.para1,
                    step.para2,
                    step.para3,
                    step.para4,
                    step.para5,
                    step.para6,
                    step.para7,
                    step.para8,
                    step.para9,
                    step.paraA
                ],
                "isscore": step.is_score,
                "scoregene": float(step.score_gene),
                "knowledgegroupid": knowledge_group_id,
                "orderid": step.order_id,
                "softType": "05",
                "takePFModel": 1,
                "isright": False,
                "description": step.description
            }
            operation_steps.append(step_item)
        step_group = new_session.query(OperationStepGroup).filter(
            OperationStepGroup.knowledge_group_id == knowledge_group_id).scalar()
        # print(step_group)
        if step_group:
            ques_score = float(step_group.score_gene)
            operation_step_group = {
                "knowledgegroupid": step_group.knowledge_group_id,
                "scoregene": ques_score,
                "description": step_group.description,
                "orderid": step_group.order_id,
                "scorerule": step_group.score_rule,
                "groupmaxscoreid": step_group.group_max_score_id,
                "grouptext": step_group.group_text,
            }
    # print(operation_steps)
    return operation_steps, operation_step_group, ques_score


def create_ques_list_file(ques_id):
    """
    生成调用操作引擎的试题文件
    """
    try:
        dst_file = os.path.join(configs.PROJECT_PATH, f"op_engine_file/{ques_id}/{ques_id}.jvbqt")
        if os.path.exists(dst_file):
            logger.info(f"操作引擎的试题文件 {ques_id}.jvbqt 已存在")
            new_session = next(session_depend())
            knowledge_group_id = new_session.query(OperationStep.knowledge_group_id).filter(OperationStep.question_id == ques_id).first()[0]
            ques_score = float(new_session.query(OperationStepGroup.score_gene).filter(OperationStepGroup.knowledge_group_id == knowledge_group_id).scalar())
            return True, dst_file, ques_score
        operation_steps, operation_step_group, ques_score = get_op_rule(ques_id)
        ques_list_template = {
            "questionlist": [
                {
                    "stuquesid": None,
                    "stuanswer": None,
                    "questionno": ques_id,
                    "softwaretype": 40,
                    "questypebaseid": "5",
                    "opfilepath": None,
                    "toperationstep": operation_steps,
                    "toperationstepgroup": [operation_step_group],
                    "pf": None,
                    "stuscore": 0.0
                }
            ]
        }
        file_path = os.path.join(configs.PROJECT_PATH, f"op_engine_file/{ques_id}")
        os.makedirs(file_path, exist_ok=True)
        with open(dst_file, "w", encoding="utf-8") as f:
            f.write(json.dumps(ques_list_template, ensure_ascii=False))
        logger.info(f"生成调用操作引擎的试题文件 {ques_id}.jvbqt 成功")
        return True, dst_file, ques_score
    except Exception as e:
        logger.error(f"生成调用操作引擎的试题文件 {ques_id}.jvbqt 失败，{e}")
        traceback.print_exc()
        return False, None, None


def get_stu_op_path(single_ques_answer_data):
    answer_list = []
    empty_op_file_answer_id_list = []
    has_op_file_answer_id_list = []
    for single_answer in single_ques_answer_data:
        answer_id, op_file, ques_score = single_answer["answer_id"], single_answer["op_file"], single_answer[
            "ques_score"]
        if op_file:
            item = {
                "oppath": op_file,
                "stuquesid": answer_id,
                "stuscore": ques_score
            }
            answer_list.append(item)
            has_op_file_answer_id_list.append(answer_id)
        else:
            empty_op_file_answer_id_list.append(answer_id)
    return has_op_file_answer_id_list, empty_op_file_answer_id_list, answer_list


def create_answer_list_file(ques_id, file_id, qt_file_path, result_path, single_ques_answer_data):
    """
    生成调用操作引擎的入参文件
    """
    file_name = f"{ques_id}_{file_id}.jdet"
    dst_file = os.path.join(configs.PROJECT_PATH, f"op_engine_file/{ques_id}/stu_data/{file_name}")
    try:
        has_op_file_answer_id_list, empty_op_file_answer_id_list, answer_list = get_stu_op_path(single_ques_answer_data)
        answer_list_template = {
            "pathlist": answer_list,
            "pfmode": "2",
            "qtfile": qt_file_path.replace("/", "\\"),
            "vbusefilepath": result_path.replace("/", "\\")
        }
        file_path = os.path.join(configs.PROJECT_PATH, f"op_engine_file/{ques_id}/stu_data")
        os.makedirs(file_path, exist_ok=True)
        with open(dst_file, "w", encoding="utf-8") as f:
            f.write(json.dumps(answer_list_template, ensure_ascii=False))
        logger.info(f"生成调用操作引擎的入参文件 {file_name} 成功")
        return True, dst_file, has_op_file_answer_id_list, empty_op_file_answer_id_list
    except Exception as e:
        logger.error(f"生成调用操作引擎的入参文件 {file_name} 失败，{e}")
        traceback.print_exc()
        return False, None, [], []


def mark_empty_answer_zero(record_id, ques_id, answer_id_list):
    """
    op_file 为空的直接判 0 分，并更新进度
    """
    new_session = next(session_depend())
    condition = and_(StuAnswer.ques_id == ques_id, StuAnswer.answer_id.in_(answer_id_list))
    new_session.query(StuAnswer).filter(condition).update({
        StuAnswer.stu_score: 0,
        StuAnswer.answer_parse: ["考生未作答，得0分"],
        StuAnswer.mark_state: 2,
        StuAnswer.running_state: 3,
        StuAnswer.mark_result: 2
    })
    # 更新进度
    update_op_record(record_id, add_count=len(answer_id_list))
    new_session.commit()


def get_op_log_content(file_id, folder_path):
    """
    获取日志文件信息
    """
    log_file_list = [os.path.join(folder_path, i) for i in os.listdir(folder_path) if i.endswith(".log")]
    log_file_list.reverse()
    for file in log_file_list:
        logger.info(f"打开文件：{file}")
        with open(file, "r", encoding="gbk") as f:
            content = f.read()
            if file_id in content:
                return content
    return None


def invoke_op_engine(file_id, params_file):
    """
    调用操作题评分引擎进行评分，返回评分是否成功
    """
    # 定义 pjoStuPfNet.exe 文件路径
    op_engine_path = configs.OP_ENGINE_ABSOLUTE_PATH

    # 构建命令字符串
    command = f"{op_engine_path} {params_file}"
    logger.info(f"执行命令: {command}")
    msg = None
    try:
        # 执行命令
        result = subprocess.run(command, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            logger.info("命令执行成功！")
            # 获取日志文件信息
            log_folder_path = os.path.join(configs.PROJECT_PATH, "VBPF/LogInfo")
            log_content = get_op_log_content(file_id, log_folder_path)
            # 获取结果文件评分结果并返回
            if "评分完成！" in log_content:
                logger.info(f"{file_id} 评分成功")
                return True, msg
            else:
                logger.error(f"{file_id} 评分失败")
                msg = "，".join(log_content.split("\n")[1:])[:-1]
                logger.error(msg)
        else:
            msg = "命令执行失败！"
            logger.error(msg)
    except Exception as e:
        msg = f"主程序发生错误：{e}"
        logger.error(msg)
        traceback.print_exc()
    return False, msg


def handle_op_mark_result(record_id, ques_id, is_success, msg, result_path, answer_id_list, ques_score):
    new_session = next(session_depend())
    if is_success:
        # 解析评分结果并保存至数据库
        update_item_list = []
        with open(result_path, "r", encoding="utf-8-sig") as f:
            mark_data = json.load(f)
        score_list = mark_data["questionlist"]
        for score_item in score_list:
            is_ok = score_item["pfisok"]
            if is_ok == 1:
                update_item = {
                    "ques_id": ques_id,
                    "answer_id": score_item["stuquesid"],
                    "stu_score": score_item["stuscore"],
                    "answer_parse": [score_item["pf"]],
                    "mark_state": 2,
                    "running_state": 3,
                    "mark_result": judge_mark_result(score_item["stuscore"], ques_score)
                }
            else:
                update_item = {
                    "ques_id": ques_id,
                    "answer_id": score_item["stuquesid"],
                    "stu_score": score_item["stuscore"],
                    "mark_fail_reason": score_item["pf"],
                    "mark_state": 3,
                    "running_state": 3,
                    "mark_result": 5
                }
            update_item_list.append(update_item)
        new_session.bulk_update_mappings(StuAnswer, update_item_list)
    else:
        new_session.query(StuAnswer).filter(and_(StuAnswer.ques_id, StuAnswer.answer_id.in_(answer_id_list))).update({
            StuAnswer.mark_fail_reason: msg,
            StuAnswer.mark_state: 3,
            StuAnswer.running_state: 3,
        })
    if is_task_exit(new_session, record_id):
        new_session.rollback()
        return 1
    new_session.commit()
    # 更新进度
    update_op_record(record_id, add_count=len(answer_id_list))
    return 0


def is_task_exit(new_session, record_id):
    """
    检测任务状态是否为 暂停评分 或 取消评分，是则退出任务
    """
    if not new_session:
        new_session = next(session_depend())
    running_state = new_session.query(OpEngineMarkRecord.running_state).filter(OpEngineMarkRecord.record_id == record_id).scalar()
    if running_state == 4:
        logger.info("操作题评分任务暂停")
        return True
    elif running_state in [5, 6]:
        logger.info("操作题评分任务取消")
        return True
    return False


def running_op_engine_mark(record_id, answer_data_dict, is_mark=True, continue_remark_dict: Optional[dict] = None):
    """
    操作题引擎评分主流程，is_mark 表示是否进行数据标记
    需要在每一步前加上检测任务状态，实现任务暂停和取消的功能
    每个步骤 exit_flag 返回 0 表示任务继续，返回 1 表示任务被暂停或被取消
    """
    if is_mark:
        # 进行数据标记
        exit_flag = remark_data_as_pending(answer_data_dict, record_id, continue_remark_dict)

        if exit_flag:
            return

    update_op_record_state(None, record_id, 3)
    # 按每题生成参数文件和进行评分
    for ques_id, answer_list in answer_data_dict.items():
        # 生成调用操作引擎的试题文件
        result, ques_file_path, ques_score = create_ques_list_file(ques_id)

        if is_task_exit(None, record_id):
            return

        # 分批处理
        answer_data_list = [item for item in answer_list]
        batch_size = configs.OP_ANSWER_MARK_BATCH_NUM
        total_batches = (len(answer_data_list) + batch_size - 1) // batch_size  # 计算总批次

        if is_task_exit(None, record_id):
            return

        for batch_num in range(total_batches):
            time.sleep(2)
            logger.info(f"正在对 ques_id: {ques_id}, 批次: {batch_num + 1}/{total_batches} 进行评分")
            start_idx = batch_num * batch_size
            end_idx = min((batch_num + 1) * batch_size, len(answer_data_list))
            batch_answer_list = answer_data_list[start_idx: end_idx]
            # 生成调用操作引擎的入参文件
            file_id = configs.snow_worker.get_id()
            part_result_path = os.path.join(configs.PROJECT_PATH,
                                            f"op_engine_file/{ques_id}/stu_data/{ques_id}_{file_id}_result")
            result, answer_file_path, has_op_file_answer_id_list, empty_op_file_answer_id_list = create_answer_list_file(
                ques_id, file_id, ques_file_path, part_result_path, batch_answer_list)

            if is_task_exit(None, record_id):
                return

            # op_file 为空的直接判 0 分
            mark_empty_answer_zero(record_id, ques_id, empty_op_file_answer_id_list)

            if is_task_exit(None, record_id):
                return

            # 调用操作题评分引擎进行评分
            is_success, msg = invoke_op_engine(file_id, answer_file_path)

            if is_task_exit(None, record_id):
                return

            # 处理评分结果
            result_path = f"{part_result_path}.jdetrs"
            exit_flag = handle_op_mark_result(record_id, ques_id, is_success, msg, result_path, has_op_file_answer_id_list, ques_score)
            if exit_flag:
                return
            logger.info(f"对 ques_id: {ques_id}, 批次: {batch_num + 1}/{total_batches} 评分成功")
    logger.info(f"{record_id} 操作题引擎评分任务执行成功")


def get_curr_op_task_info(new_session, record_id):
    """
    获取当前操作题评分任务信息
    """
    select_fields = [
        OpEngineMarkRecord.record_id, OpEngineMarkRecord.running_state,
        OpEngineMarkRecord.finish_count, OpEngineMarkRecord.total_count,
        OpEngineMarkRecord.filter_condition, OpEngineMarkRecord.pause_state
    ]
    if record_id:
        task_info = new_session.query(*select_fields) \
            .filter(OpEngineMarkRecord.record_id == record_id).first()
    else:
        task_info = new_session.query(*select_fields) \
            .filter(OpEngineMarkRecord.running_state.in_([1, 2, 3, 4, 5])) \
            .order_by(OpEngineMarkRecord.created_time.desc()).first()
    if task_info:
        record_id, running_state, finish_count, total_count, filter_condition, pause_state = task_info
        finish_count = finish_count if finish_count else 0
        percentage = round(finish_count / total_count * 100 if total_count else 0, 2)
        state_str = get_op_state_str(running_state)
        data = {
            "record_id": record_id,
            "running_state": running_state,
            "finish_count": finish_count,
            "total_count": total_count,
            "percentage": percentage if percentage <= 100 else 100,
            "state_str": state_str,
            "filter_condition": filter_condition,
            "pause_state": pause_state
        }
        return data
    return {}


def get_curr_op_task_state(new_session, record_id):
    """
    获取当前任务状态
    任务状态，1 表示正在获取数据，2 表示正在标记中，3 表示进行中，4 表示暂停，5 表示正在取消中，6 表示已取消，7 表示已完成
    """
    state = new_session.query(OpEngineMarkRecord.running_state).filter(
        OpEngineMarkRecord.record_id == record_id).scalar()
    return state


def update_op_record_state(new_session, record_id, running_state, pause_state=None):
    """
    更新操作题引擎阅卷任务记录状态
    running_state: 任务状态，1 表示正在获取数据，2 表示正在标记中，3 表示进行中，4 表示暂停，5 表示正在取消中，6 表示已取消，7 表示已完成
    """
    if not new_session:
        new_session = next(session_depend())
    update_item = {
        OpEngineMarkRecord.running_state: running_state
    }
    if pause_state:
        update_item[OpEngineMarkRecord.pause_state] = pause_state
        logger.info(f"评分暂停前评分任务状态为 {pause_state}: {get_op_state_str(pause_state)}")
    new_session.query(OpEngineMarkRecord).filter(OpEngineMarkRecord.record_id == record_id).update(update_item)
    new_session.commit()


def cancel_stu_answer_running_state(new_session):
    new_session.query(StuAnswer).filter(StuAnswer.running_state == 2).update({
        StuAnswer.running_state: 1
    })
    new_session.commit()


def get_before_pause_op_data(new_session, filter_type, filter_content, is_continue=False):
    """
    获取暂停前的评分数据
    """
    if filter_type == "1":
        answer_data_list = filter_content
        flag = True if answer_data_list else False
        answer_data_dict = format_op_answer_data(answer_data_list, True)
    elif filter_type == "2":
        args = [new_session, filter_content.get("project_id", None), filter_content.get("subject_id", None),
                filter_content.get("paper_code", None), filter_content.get("ques_code", None),
                filter_content.get("ques_order", None), filter_content.get("stu_answer", None),
                filter_content.get("mark_result", None), filter_content.get("mark_state", []),
                filter_content.get("stu_score_range", []), filter_content.get("search_time", [])]
        flag, answer_data_dict = get_condition_op_data(*args)
    else:
        flag, answer_data_dict = get_all_op_answer(new_session, is_continue)
    return flag, answer_data_dict


def get_continue_mark_op_data(new_session, answer_data_dict):
    """
    获取继续评分的待评数据
    """
    pending_data_dict = {}
    for ques_id, answer_data in answer_data_dict.items():
        answer_id_list = [i["answer_id"] for i in answer_data]
        answer_info = new_session.query(StuAnswer.answer_id).filter(
            and_(StuAnswer.ques_id == ques_id, StuAnswer.answer_id.in_(answer_id_list),
                 StuAnswer.running_state == 2)).all()
        if answer_info:
            pending_data_dict[ques_id] = []
            for i in answer_info:
                pending_data_dict[ques_id].append(i)
    return pending_data_dict


def check_op_engine_exists(user):
    headers = {"Authorization": f"Bearer {user['token']}"}
    result = {}
    for url in configs.OP_ENGINE_URL:
        try:
            res = requests.get(f"{url}/v1/salve_op/check_engine_exists", headers=headers).json()
            ip = url.split(':')[1].replace("//", "")
            result[ip] = res["code"]
        except requests.exceptions.ConnectionError:
            return f"{url} 该服务无法连接"
    msg = ""
    for ip, code in result.items():
        if code != 200:
            msg += f"请将操作题评分引擎存放于 {ip} 的 卓帆智能阅卷系统V1.0.exe 的同级目录下，"
    if msg:
        return msg[:-1]
    return None


def request_file_download(url, data, token, save_path, filename):
    headers = {"Authorization": token}
    res = requests.post(url, json=data, headers=headers, stream=True)

    # 检查请求是否成功
    if res.status_code == 200:
        if res.headers["content-type"] == "application/octet-stream":
            # 打开文件并写入流数据
            file_path = os.path.join(save_path, filename)
            with open(file_path, "wb") as file:
                for chunk in res.iter_content(chunk_size=1024 * 1024):
                    # 过滤掉保持连接的块
                    if chunk:
                        file.write(chunk)
                        file.flush()
            logger.info(f"{filename} 文件下载成功")
            return None
        else:
            logger.error(f"{filename} 文件下载失败，错误信息：{res.json()['msg']}")
            return f"{filename} 文件下载失败，错误信息：{res.json()['msg']}"
    else:
        logger.error(f"{filename} 文件下载失败，状态码: {res.status_code}")
        return f"{filename} 文件下载失败，状态码: {res.status_code}"


if __name__ == '__main__':
    # deal_answer_data()
    # print("---------------------------")
    # deal_answer_group_data()
    # get_ques_image()
    # get_op_rule("10000080")
    # stu_answer_op_path = ("D:\\PFData\\M_2022_27027006_1_1_1130160337_06926537\\作答文件\\27027006_1\\27027100606"
    #                       "\\27027100606\\10000080\\000196_10001320_20231208102705683_B092EBF3.op")
    # op_engine_mark("10000080", 10, stu_answer_op_path)
    invoke_op_engine(
        r"E:\workSpace\ai_question_system\code\ReadPaperSystem\op_engine_file\10000075\stu_data\10000075_31736939105057109706801152.jdet")
