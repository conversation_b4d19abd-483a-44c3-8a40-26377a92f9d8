from typing import Optional, Literal
from pydantic import BaseModel, Field

from apps.base.schemas import PaginationModel


class GetProjectReq(PaginationModel):
    project_id: Optional[str] = Field(None, title="项目id")
    c_name: Optional[str] = Field(None, title="创建人")
    exam_way_id: Optional[list] = Field(None, title="考试方式id")


class GetSubjectReq(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    c_name: Optional[str] = Field(None, description="创建人")
    # subject_name: Optional[str] = Field(None,description="科目名称")
    exam_mode: list = Field([], description="考试模式列表")

class GetSubjectSessionReq(BaseModel):
    project_id: str = Field(None, description="项目id")
    subject_id: str = Field(None, description="科目id")



class CreateProjectReq(BaseModel):
    project_name: str = Field(..., title="项目名称", max_length=50)
    exam_type_id: str = Field(..., title="考试类别id")
    exam_way_id: str = Field(..., title="考试形式id")
    remark: Optional[str] = Field(None, title="备注")


class DeleteProjectReq(BaseModel):
    project_id: str = Field(..., title="项目id")


class DeleteSubjectReq(BaseModel):
    subject_id: str = Field(..., title="科目id")


class UpdateProjectReq(BaseModel):
    project_id: str = Field(..., title="项目id")
    project_name: str = Field(..., title="项目名称", max_length=50)
    exam_type_id: str = Field(..., title="考试类别id")
    exam_way_id: str = Field(..., title="考试形式id")
    remark: Optional[str] = Field(None, title="备注")


class UpdateProjectStateReq(BaseModel):
    project_id: str = Field(..., title="资格id")
    is_active: int = Field(..., description="资格状态")


class CreateSubjectReq(BaseModel):
    subject_name: str = Field(..., description="科目名称", max_length=50)
    project_id: str = Field(..., description="项目id/所属资格")
    exam_mode: int = Field(..., description="考试模式，0 表示抽参，1 表示抽卷")
    subject_code: str = Field(None, description="科目编码")
    spy_num: int = Field(..., description="间谍卷，每spy_num份分发一次间谍卷")
    # quality_mode: int = Field(..., description="质检模式，0 表示不质检，1 表示评阅过程质检，2 表示评阅结束质检")
    # reevaluated_count: Optional[int] = Field(None, description="随机复评次数")
    # mark_mode: int = Field(..., description="评分模式，1 表示得分点评分，2 表示整体评分")
    # copy_ans_threshold: Optional[int] = Field(..., description="作答抄袭阈值")
    # similar_paper_threshold: Optional[int] = Field(None, description="雷同卷阈值")
    # copy_ques_threshold: Optional[int] = Field(None, description="抄袭题干阈值")
    # chat_stage: int = Field(..., description="聊天阶段，0 表示所有阶段，1 表示培新考核阶段，2 表示正评阶段")
    # chat_range: int = Field(..., description="聊天范围，1 表示科目组内，2 表示题组内，3 表示小组内")
    subject_total_score: float = Field(None, description="科目总分")
    subject_pass_score: float = Field(None, description="科目及格分")
    subject_score_interval: Optional[float] = Field(None, description="允许评分区间")
    remark: Optional[str] = Field(None, description="备注")


class UpdateSubjectReq(BaseModel):
    subject_id: str = Field(..., description="科目id")
    subject_name: str = Field(..., description="科目名称", max_length=50)
    project_id: str = Field(..., description="项目id/所属资格")
    exam_mode: int = Field(..., description="考试模式，0 表示抽参，1 表示抽卷")
    subject_code: Optional[str] = Field(None, description="科目编码")
    spy_num: int = Field(..., description="间谍卷，每spy_num份分发一次间谍卷")
    # quality_mode: int = Field(..., description="质检模式，0 表示不质检，1 表示评阅过程质检，2 表示评阅结束质检")
    # reevaluated_count: Optional[int] = Field(None, description="随机复评次数")
    # mark_mode: int = Field(..., description="评分模式，1 表示得分点评分，2 表示整体评分")
    # copy_ans_threshold: int = Field(..., description="作答抄袭阈值")
    # similar_paper_threshold: int = Field(None, description="雷同卷阈值")
    # copy_ques_threshold: int = Field(None, description="抄袭题干阈值")
    # chat_stage: int = Field(..., description="聊天阶段，0 表示所有阶段，1 表示培新考核阶段，2 表示正评阶段")
    # chat_range: int = Field(..., description="聊天范围，1 表示科目组内，2 表示题组内，3 表示小组内")
    subject_total_score: float = Field(None, description="科目总分")
    subject_pass_score: float = Field(None, description="科目及格分")
    subject_score_interval: Optional[float] = Field(None, description="允许评分区间")
    remark: Optional[str] = Field(None, description="备注")


class UpdateSubjectStateReq(BaseModel):
    subject_id: str = Field(..., description="科目id")
    is_active: int = Field(..., description="科目状态")


class GetSeveralSubjectReq(BaseModel):
    project_id_list: list = Field(..., description="项目id列表")
