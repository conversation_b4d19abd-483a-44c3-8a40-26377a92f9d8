# 卓帆智能阅卷系统 - Redis自启动集成方案

## 概述

本方案实现了Redis服务的自动启动功能，使得系统启动时无需手动启动Redis服务器。整个系统包含三个主要组件：
1. **Redis服务** - 自动启动和管理
2. **主程序** - 卓帆电子化考试阅卷管理系统
3. **定时任务程序** - 数据统计定时任务

## 项目结构变更

### 新增文件
```
├── factory_apps/redis_db/
│   └── redis_service_manager.py          # Redis服务管理模块
├── Redis-x64-3.0.504/                    # Redis可执行文件目录
│   ├── README.md                          # Redis文件说明
│   ├── redis-server.exe                  # Redis服务器（需下载）
│   └── redis-cli.exe                     # Redis客户端（需下载）
├── winjob.py                              # 统一启动脚本
├── build_all.py                          # 统一打包脚本
├── test_redis_integration.py             # Redis集成测试脚本
└── Redis集成部署说明.md                   # 本文档
```

### 修改文件
```
├── main.py                                # 添加Redis自启动逻辑
├── schedule_main.py                       # 添加Redis连接检查
├── factory_apps/redis_db/redis_cahe.py   # 改进连接和错误处理
├── 卓帆智能定标阅卷系统V1.0.spec          # 更新打包配置
└── 定时任务V1.0.spec                      # 定时任务打包配置
```

## 部署步骤

### 1. 下载Redis文件

从以下地址下载Redis Windows版本：
- **官方地址**: https://github.com/microsoftarchive/redis/releases
- **推荐版本**: Redis-x64-3.0.504

下载后将以下文件放置在 `Redis-x64-3.0.504/` 目录中：
- `redis-server.exe`
- `redis-cli.exe`

### 2. 测试Redis集成

运行测试脚本验证Redis集成功能：
```bash
python test_redis_integration.py
```

测试内容包括：
- Redis文件检查
- 端口可用性检查
- Redis服务启动/停止
- Redis连接测试
- 配置文件生成

### 3. 开发环境运行

#### 方式一：分别启动（传统方式）
```bash
# 1. 手动启动Redis（如果需要）
# 2. 启动主程序
python main.py

# 3. 启动定时任务（另一个终端）
python schedule_main.py
```

#### 方式二：统一启动（推荐）
```bash
python winjob.py
```

### 4. 生产环境打包

#### 自动打包（推荐）
```bash
python build_all.py
```

#### 手动打包
```bash
# 1. 打包主程序
pyinstaller --onefile --name "卓帆电子化考试阅卷管理系统V1.0" --icon logo.ico main.py

# 2. 打包定时任务
pyinstaller --onefile --name "定时任务V1.0" --icon logo.ico schedule_main.py

# 3. 打包统一启动器
pyinstaller 卓帆智能定标阅卷系统V1.0.spec
```

### 5. 部署运行

生成的可执行文件：
- `dist/卓帆电子化考试阅卷管理系统V1.0.exe` - 主程序
- `dist/定时任务V1.0.exe` - 定时任务程序  
- `dist/卓帆智能阅卷系统统一启动器V1.0.exe` - 统一启动器

#### 运行方式

**方式一：统一启动（推荐）**
```bash
# 运行统一启动器，自动按顺序启动所有服务
卓帆智能阅卷系统统一启动器V1.0.exe
```

**方式二：分别启动**
```bash
# 1. 启动主程序（会自动启动Redis）
卓帆电子化考试阅卷管理系统V1.0.exe

# 2. 启动定时任务
定时任务V1.0.exe
```

## 功能特性

### Redis服务管理
- **自动启动**: 程序启动时自动检查并启动Redis服务
- **端口检查**: 自动检测端口占用并处理冲突
- **进程管理**: 优雅启动和停止Redis进程
- **配置生成**: 自动生成适合的Redis配置文件
- **错误处理**: 详细的错误信息和日志记录

### 启动顺序管理
1. **Redis服务** - 首先启动Redis服务器
2. **主程序** - 启动Web服务和阅卷系统
3. **定时任务** - 启动数据统计定时任务

### 错误处理和日志
- 详细的启动过程日志
- 错误信息和解决建议
- 进程状态监控
- 优雅的关闭处理

## 配置说明

### Redis配置
Redis配置通过 `AppSetting.json` 中的 `CacheConfig` 部分控制：
```json
{
  "CacheConfig": {
    "Host": "127.0.0.1",
    "Port": 6379,
    "Db": 0,
    "MaxConnections": 1000
  }
}
```

### 系统配置
- **主程序端口**: 9558（可在SystemConfig.Port修改）
- **定时任务端口**: 9560（可在ScheduleBackendCfg.Port修改）
- **Redis端口**: 6379（可在CacheConfig.Port修改）

## 故障排除

### 常见问题

1. **Redis文件不存在**
   - 确保已下载Redis Windows版本
   - 检查文件路径：`Redis-x64-3.0.504/redis-server.exe`

2. **端口被占用**
   - 检查是否有其他Redis实例在运行
   - 修改配置文件中的端口号
   - 使用任务管理器结束相关进程

3. **权限问题**
   - 以管理员身份运行程序
   - 检查防火墙设置

4. **启动超时**
   - 检查系统资源使用情况
   - 增加启动等待时间
   - 查看详细日志信息

### 日志文件
- 主程序日志：`logs/` 目录
- Redis启动日志：控制台输出

### 测试命令
```bash
# 测试Redis连接
python -c "import redis; r=redis.Redis(host='127.0.0.1', port=6379); print('Redis连接成功' if r.ping() else '连接失败')"

# 检查端口占用
netstat -an | findstr :6379
```

## 技术实现

### 核心模块
- `RedisServiceManager`: Redis服务管理器
- `UnifiedLauncher`: 统一启动器
- `redis_service_manager`: 服务管理功能

### 关键技术
- 进程管理和监控
- 端口检测和冲突处理
- 配置文件动态生成
- 错误处理和重试机制
- 优雅关闭和资源清理

## 版本信息
- **系统版本**: V1.1.50906
- **Redis版本**: 3.0.504
- **Python版本**: 3.13+
- **打包工具**: PyInstaller

## 联系支持
如遇到问题，请提供以下信息：
1. 错误日志内容
2. 系统环境信息
3. 配置文件内容
4. 复现步骤
