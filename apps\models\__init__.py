from apps.models.models import Base as Base1
from apps.human_mark_group.models import Base as Base2
from apps.human_try_mark.models import Base as Base3
from apps.human_task_manage.models import Base as Base4
from apps.human_mark_exception.models import Base as Base5
from apps.human_mark_accept.models import Base as Base6
from apps.human_statistics.models import Base as Base7
from apps.human_prepare.models import Base as Base8
from apps.ques_manage.models import Base as Base9
from apps.human_repeat_mark.models import Base as Base10
from apps.grade_manage.models import Base as Base11
from apps.data_transfer.models import Base as Base12


target_metadata = [Base1.metadata, Base2.metadata, Base3.metadata, Base4.metadata, Base5.metadata, Base6.metadata, \
    Base7.metadata, Base8.metadata, Base9.metadata, Base10.metadata, Base11.metadata, Base12.metadata]
