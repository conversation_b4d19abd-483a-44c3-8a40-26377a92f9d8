from sqlalchemy import Column, String, Integer, Float, JSON, Text, DateTime, DECIMAL, PrimaryKeyConstraint, Boolean
from sqlalchemy.orm import declarative_base

from apps.models.models import DateTimeBaseMixin, ImportToolMixin

Base = declarative_base()


class ErrorAnalysisSummary(Base, DateTimeBaseMixin):
    __tablename__ = "t_error_analysis_summary"
    __table_args__ = {"comment": "错误信息汇总表"}
    error_message_id = Column(String(50), primary_key=True, comment="错误信息汇总表id")
    ques_id = Column(String(50), comment="试题id")
    same_answer_group_id = Column(String(50), comment="作答分组id")
    error_type_id = Column(String(50), comment="错误类型表id")
    error_analysis = Column(String(300), comment="错误分析信息")
    confidence = Column(Integer, comment="分类置信度")
    percentage = Column(Integer, comment="百分比，单位为万分之一（如 1234 表示 12.34%）")


class ErrorType(Base, DateTimeBaseMixin):
    __tablename__ = "t_error_type"
    __table_args__ = {"comment": "错误类型表"}
    type_id = Column(String(50), primary_key=True, comment="类型id")
    type_name = Column(String(100), comment="错误类型名称")
