import datetime

from sqlalchemy import Column, String, Integer, Float, JSON, Text, DateTime, DECIMAL, PrimaryKeyConstraint, Boolean
from sqlalchemy.orm import declarative_base

# 声明基类
Base = declarative_base()


class DateTimeBaseMixin:
    __abstract__ = True
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")


class HumanObjectiveQuesGrade(Base, DateTimeBaseMixin):
    __tablename__ = "t_objective_ques_grade"
    __table_args__ = {"comment": "客观题评分表"}
    objective_ques_grade_id = Column(String(50), primary_key=True, comment="客观题评分id")
    answer_id = Column(String(50), comment="作答id", index=True)
    mark_score = Column(DECIMAL(10, 2), comment="阅卷评分分数")
    is_same = Column(Bo<PERSON><PERSON>, comment="考务评分与阅卷评分是否一致")
    score_type = Column(Integer, comment="评分使用方式：0 考务评分；1 阅卷评分")
    score = Column(DECIMAL(10, 2), comment="客观题最终分数")


class HumanSubjectiveQuesGrade(Base, DateTimeBaseMixin):
    __tablename__ = "t_subjective_ques_grade"
    __table_args__ = {"comment": "主观题评分表"}
    subjective_ques_grade_id = Column(String(50), primary_key=True, comment="主观题评分id")
    task_id = Column(String(50), comment="人工阅卷任务id")
    answer_id = Column(String(50), comment="作答id", index=True)
    is_same = Column(Boolean, comment="所有轮次评分是否一致")
    round_count = Column(Integer, comment="最终分数来源于哪个轮次")
    score = Column(DECIMAL(10, 2), comment="主观题最终分数")


class HumanStudentSubjectGrade(Base, DateTimeBaseMixin):
    __tablename__ = "t_student_subject_grade"
    __table_args__ = {"comment": "考生科目成绩表"}
    student_subject_grade_id = Column(String(50), primary_key=True, comment="考生科目成绩id")
    task_type = Column(Integer, comment="任务类型，1 表示正评任务，2 表示试评任务，3 表示复评任务")
    round_id = Column(String(50), comment="轮次id，复评使用")
    round_count = Column(Integer, comment="轮次，试评、正评用")
    stu_secret_num = Column(String(30), comment="考生密号")
    project_id = Column(String(50), comment="所属项目id")
    subject_id = Column(String(50), comment="所属科目id")
    score = Column(DECIMAL(10, 2), comment="科目分数")


class HumanStudentSubjectGradeDetail(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_stu_grade_detail"
    __table_args__ = {"comment": "考生科目成绩详情表"}
    detail_id = Column(String(50), primary_key=True, comment="成绩详情id")
    student_subject_grade_id = Column(String(30), nullable=False, comment="成绩id", index=True)
    round_id = Column(String(50), comment="轮次id")
    answer_id = Column(String(50), comment="作答id")
    ques_id = Column(String(50), comment="试题id")
    stu_score_list = Column(JSON, comment="考生得分列表")
    stu_score = Column(DECIMAL(10, 2), comment="考生得分")
    c_user_id = Column(String(50), comment="评分人id")
    