import datetime
from sqlalchemy import Column, String, Integer, Float, JSON, Text, DateTime, DECIMAL, PrimaryKeyConstraint
from sqlalchemy.orm import declarative_base

from apps.models.models import DateTimeBaseMixin

# 声明基类
Base = declarative_base()


class HumanPaperSample(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_paper_sample"
    __table_args__ = {"comment": "试卷抽样表"}
    paper_sample_id = Column(String(50), primary_key=True, comment="试卷抽样id")
    subject_id = Column(String(50), comment="科目id")
    ques_code = Column(String(30), comment="试题编号")
    stu_ids = Column(JSON, comment="抽样考生")
    sample_count = Column(Integer, comment="抽样数量")
    score_count = Column(Integer, comment="已评数量")
    c_user_id = Column(String(50), comment="创建用户id")
    sample_state = Column(Integer, comment="试卷抽样状态：0 未开始；1 进行中；2 已经结束")
    query_conditions = Column(Text, comment="查询条件的JSON字符串")
    start_time = Column(DateTime(timezone=True), comment="评分开始时间")
    end_time = Column(DateTime(timezone=True), comment="评分结束时间")
    


class HumanPaperSampleDetail(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_paper_sample_detail"
    __table_args__ = {"comment": "试卷抽样详情表"}
    paper_sample_detail_id = Column(String(50), primary_key=True, comment="试卷抽样详情id")
    paper_sample_id = Column(String(50), index=True, comment="试卷抽样id")
    answer_id = Column(String(50), comment="作答id")
    answer_score = Column(DECIMAL(10, 2), comment="作答总评分")
    answer_score_list = Column(JSON, comment="作答分数列表")


class HumanPrepareProcess(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_prepare_process"
    __table_args__ = {"comment": "准备流程表"}
    process_id = Column(String(50), primary_key=True, comment="流程id")
    process_name = Column(String(100), comment="流程名称")
    process_type = Column(Integer, comment="流程类型：1 考生导入；2 作答信息导入；3 作答抄袭识别；4 雷同卷识别；5 抄袭题干识别")
    parent_process_type = Column(Integer, comment="父流程类型")
    process_status = Column(Integer, comment="流程状态：0 未开始；1 进行中；2 已完成；3 失败")
    fail_reason = Column(String(255), comment="流程失败原因")
    progress = Column(Integer, comment="流程进行中的进度（0-100）")


class HumanAnswerSample(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_answer_sample"
    __table_args__ = {"comment": "作答样卷表"}
    answer_sample_id = Column(String(50), primary_key=True, comment="作答样卷表id")
    answer_id = Column(String(50), comment="作答id")
