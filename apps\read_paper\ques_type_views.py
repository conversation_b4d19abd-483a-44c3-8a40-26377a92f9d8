from fastapi import APIRouter, Depends
from sqlalchemy import select
from sqlalchemy.orm import Session
from settings import logger
from typing import Any

from apps.read_paper import GetQuesTypeReq
from apps.users.services import get_current_user
from apps.models.models import QuesType
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils

ques_type_router = APIRouter()


@ques_type_router.post(path="/get_ques_type", response_model=BaseResponse, summary="获取题型信息")
async def get_ques_type(query: GetQuesTypeReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取题型信息")
    current_page, page_size, exclude_type_code_list = query.model_dump().values()
    ques_type_data = []
    total_condition = ~QuesType.ques_type_code.in_(exclude_type_code_list) if exclude_type_code_list else True
    if page_size == -1:
        ques_type_stmt = select(QuesType).filter(total_condition)
    else:
        limit = current_page - 1
        offset = limit * page_size
        ques_type_stmt = select(QuesType).filter(total_condition).order_by(QuesType.created_time.desc()).limit(page_size).offset(offset)

    try:
        total = new_session.query(QuesType.ques_type_id).count()
        result = new_session.execute(ques_type_stmt)
        for ques_type in result.scalars():
            ques_type_item = {
                "ques_type_id": ques_type.ques_type_id,
                "ques_type_name": ques_type.ques_type_name,
                "ques_type_code": ques_type.ques_type_code,
                "ques_mark_rule": ques_type.ques_mark_rule
            }
            ques_type_data.append(ques_type_item)
    except Exception as e:
        logger.info(f"获取所有题型信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg="获取题型信息失败")
    logger.info("获取题型信息成功")
    data = {
        "data": ques_type_data,
        "total": total
    }
    return BaseResponse(msg="获取题型信息成功", data=data)
