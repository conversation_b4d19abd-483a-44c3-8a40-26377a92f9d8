from typing import Optional,Literal, List
from pydantic import BaseModel, Field
from apps.base.schemas import PaginationModel

class CalculateReviewedCountReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class CalculateAverageScoreReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class CalculateAverageScoreBatchReq(BaseModel):
    user_task_list: List[dict] = Field(..., description="用户任务组合列表，每个元素包含user_id, task_id, round_count, task_type等字段")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class CalculateCumulativeAverageScoreReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")


class CalculateMaxScoreReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class CalculateMinScoreReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class CalculateArbitrationCountReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class CalculateWorkloadReq(BaseModel):
    user_id: str = Field(None, title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    mark_time: Optional[str] = Field(None, title="评阅时间，支持 '2023-08-08 08:30:00' 格式")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class CalculateScoreDistributionReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class RecordSpyPaperScoreReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    group_id: str = Field(None, title="小组id")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")


class CalculateInvalidReviewedCountReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class CalculateEffectiveReviewCountReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class CalculateAverageSpeedReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GetEffectiveReviewRankingReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")
    name: Optional[str] = Field(None, title="用户名，用于模糊查询")


class CalculateArbitrationPieChartReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateReviewedCountReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateAverageScoreReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateCumulativeAverageScoreReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")


class GCalculateMaxScoreReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateMinScoreReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateMaxSpeedReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateMinSpeedReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateArbitrationCountReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateScoreDistributionReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateEffectiveReviewCountReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateAverageSpeedReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GCalculateArbitrationPieChartReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class CalculateCumulativeAverageScoreWithTimeReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    group_id: str = Field(None, title="小组id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    mark_time: Optional[str] = Field(None, title="评阅时间，支持 '2023-08-08 08:30:00' 格式")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class QCalculateAverageSpeedReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class QCalculateMarkTotalReq(BaseModel):
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    task_id: Optional[str] = Field(None, title="任务id")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class QCalculateArbitrationStatusReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class TCalculateMarkTotalReq(BaseModel):
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class GetGroupIdByQuesCodeReq(BaseModel):
    ques_code: Optional[str] = Field(..., title="试题编号")

class TGroupProgressReq(BaseModel):
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")

class GetReviewStatisticsReq(BaseModel):
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(None, description="阅卷任务类型")
    project_id: Optional[str] = Field(None, title="资格ID")
    subject_id: Optional[str] = Field(None, title="科目id")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")

class ExceptionStatisticsReq(BaseModel):
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    task_id: Optional[str] = Field(None, title="任务id")
    ques_group_id: Optional[str] = Field(None, title="题组ID")

class PCalculateAverageScoreReq(BaseModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")

class PCalculateScoreDistributionReq(BaseModel):
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(None, description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class PCalculateScoreProgressReq(BaseModel):
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(None, description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class PersonSurveyMonitorReq(PaginationModel):
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    project_id: Optional[str] = Field(None, title="资格ID")
    subject_id: Optional[str] = Field(None, title="科目id")
    task_name: Optional[str] = Field(None, description="阅卷任务名称")
    round_state_list: List[int] = Field(None, description="执行状态")
    round_count: Optional[str] = Field(None, description="轮次")
    group_id: str = Field(None, title="小组id")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    name: Optional[str] = Field(None, title="用户名，用于模糊查询")
    task_id_list: list = Field([], description="任务id列表，用于后端过滤数据")
    round_id_list: list = Field([], description="轮次id列表，用于后端过滤数据")

class GetHumanStatisticsListReq(PaginationModel):
    role_id: Optional[list[str]] = Field(None,description="所属角色id")
    project_id: Optional[list[str]] = Field(None, description="所属资格id")
    subject_id: Optional[list[str]] = Field(None, description="所属科目id")
    user_name: Optional[str] = Field(None, description="用户名")

class ExportHumanStatisticsListReq(BaseModel):
    role_id: Optional[list[str]] = Field(None, description="所属角色id")
    project_id: Optional[list[str]] = Field(None, description="所属资格id")
    subject_id: Optional[list[str]] = Field(None, description="所属科目id")
    user_name: Optional[str] = Field(None, description="用户名")
    user_id: Optional[list[str]] = Field(None, description="用户id")

class GroupSurveyMonitorReq(PaginationModel):
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    project_id: Optional[str] = Field(None, title="资格ID")
    subject_id: Optional[str] = Field(None, title="科目id")
    task_name: Optional[str] = Field(None, description="阅卷任务名称")
    round_state_list: List[int] = Field(None, description="执行状态")
    round_count: Optional[int] = Field(None, description="轮次")
    group_id: str = Field(None, title="小组id")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    task_id_list: list = Field([], description="任务id列表，用于后端过滤数据")
    round_id_list: list = Field([], description="轮次id列表，用于后端过滤数据")

class QGroupSurveyMonitorReq(PaginationModel):
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    project_id: Optional[str] = Field(None, title="资格ID")
    subject_id: Optional[str] = Field(None, title="科目id")
    task_name: Optional[str] = Field(None, description="阅卷任务名称")
    round_state_list: List[int] = Field(None, description="执行状态")
    round_count: Optional[int] = Field(None, description="轮次")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    task_id_list: list = Field([], description="任务id列表，用于后端过滤数据")
    round_id_list: list = Field([], description="轮次id列表，用于后端过滤数据")

class TryMarkMonitorReq(PaginationModel):
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    round_count: Optional[int] = Field(None, description="轮次")
    project_id: Optional[str] = Field(None, title="资格ID")
    subject_id: Optional[str] = Field(None, title="科目id")
    task_name: Optional[str] = Field(None, description="阅卷任务名称")
    name: Optional[str] = Field(None, title="用户名，用于模糊查询")
    round_state_list: List[int] = Field(None, description="执行状态")
    is_office_mark: Optional[bool] = Field(None, description="是否转为正评")
    try_mark_result: Optional[str] = Field(None, description="试评结果：0 未评；1 合格；2 不合格")
    ques_group_id: Optional[str] = Field(None, title="题组ID")
    diff_count: Optional[list] = Field(None, description="试评偏差量")
    task_id_list: list = Field([], description="任务id列表，用于后端过滤数据")
    round_id_list: list = Field([], description="轮次id列表，用于后端过滤数据")

class UpdateTryMarkResultItem(PaginationModel):
    task_id: str = Field(..., max_length=50, description="人工阅卷任务id")
    round_id: str = Field(..., max_length=50, description="轮次id")
    user_id: str = Field(..., max_length=50, description="评阅员id")
    ques_group_id: str = Field(..., max_length=50, description="阅卷小组id")
    try_mark_result: int = Field(..., description="试评结果：0 未评；1 合格；2 不合格")

class UpdateTryMarkResultReq(PaginationModel):
    try_mark_result_list: List[UpdateTryMarkResultItem] = Field(..., description="试评结果更新列表")

class UpdateTryIsOfficeMarkItem(PaginationModel):
    task_id: str = Field(..., max_length=50, description="人工阅卷任务id")
    round_id: str = Field(..., max_length=50, description="轮次id")
    user_id: str = Field(..., max_length=50, description="评阅员id")
    ques_group_id: str = Field(..., max_length=50, description="阅卷小组id")
    is_office_mark: Optional[bool] = Field(None, description="是否转为正评")

class UpdateTryIsOfficeMarkReq(PaginationModel):
    is_office_mark_list: List[UpdateTryIsOfficeMarkItem] = Field(..., description="是否转为正评结果更新列表")


class TryMarkResultReq(PaginationModel):
    task_id: Optional[str] = Field(None, title="任务id")
    round_id: Optional[str] = Field(None, title="轮次id")
    user_id: Optional[str] = Field(None, title="评阅员id")
    group_id: Optional[str] = Field(None, title="小组id")

class GetTasksReq(BaseModel):
    user_id: str = Field(..., title="用户id")
    role_id: str = Field(..., title="角色id")

class GetSubjectScoreReq(BaseModel):
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(None, description="阅卷任务类型")
    subject_id: Optional[str] = Field(None, title="科目id")
    project_id: Optional[str] = Field(None, title="资格ID")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")

class TGetReviewStatisticsReq(BaseModel):
    round_count: Optional[int] = Field(None, description="轮次")
    task_type: Literal[1, 2, 3] = Field(None, description="阅卷任务类型")
    date_range: Optional[str] = Field(None, title="日期范围，格式：yyyy-mm-dd至yyyy-mm-dd")


class QualityMonitorReq(PaginationModel):
    project_id: Optional[str] = Field(None, title="资格ID")
    subject_id: Optional[str] = Field(None, title="科目id")
    task_name: Optional[str] = Field(None, description="阅卷任务名称")
    name: Optional[str] = Field(None, title="用户名，用于模糊查询")
    diff_count: Optional[list] = Field(None, description="通过率")
