from sqlalchemy import and_

from apps.models.models import SameStuAnswerGroup


def format_ques_tree(ques_data_list: list):
    """
    将试题信息形成层次分为 科目，试卷，题型，试题序号 的树状数据
    :param ques_data_list:
    :return:
    """
    # tree = {}
    tree = []

    for item in ques_data_list:
        subject_name = item["subject_name"]
        paper_id = item["paper_id"]
        ques_code = item["ques_code"]
        paper_name = item["paper_name"]
        ques_type_name = item["ques_type_name"]
        ques_order = item["ques_order"]
        ques_id = item["ques_id"]
        set_std_state = item["ques_id"]
        set_std_state_str = item["set_std_state_str"]

        # # 科目层
        # if subject_name not in tree:
        #     tree[subject_name] = {}
        # # 试卷层
        # if paper_name not in tree[subject_name]:
        #     tree[subject_name][paper_name] = {}
        # # 题型层
        # if ques_type not in tree[subject_name][paper_name]:
        #     tree[subject_name][paper_name][ques_type_name] = {}
        # # 试题序号层
        # tree[subject_name][paper_name][ques_type_name][ques_order] = {
        #     "ques_id": ques_id,
        #     "set_std_state": set_std_state,
        #     "set_std_state_str": set_std_state_str
        # }

        # 查找或创建科目节点

        current_subject = None
        for subject in tree:
            if subject.get("subject_name") == subject_name:
                current_subject = subject
                break
        if current_subject is None:
            current_subject = {
                "subject_name": subject_name,
                "children": []
            }
            tree.append(current_subject)

        # 查找或创建试卷节点
        current_paper = None
        for paper in current_subject["children"]:
            if paper.get("paper_name") == paper_name:
                current_paper = paper
                break
        if current_paper is None:
            current_paper = {
                "paper_id": paper_id,
                "paper_name": paper_name,
                "children": []
            }
            current_subject["children"].append(current_paper)

        # 查找或创建题型节点
        current_ques_type = None
        for ques_type in current_paper["children"]:
            if ques_type.get("ques_type_name") == ques_type_name:
                current_ques_type = ques_type
                break
        if current_ques_type is None:
            current_ques_type = {
                "ques_type_name": ques_type_name,
                "children": []
            }
            current_paper["children"].append(current_ques_type)

        # 添加试题节点
        ques_node = {
            "ques_order": ques_order,
            "ques_id": ques_id,
            "ques_code": ques_code,
            "set_std_state": set_std_state,
            "set_std_state_str": set_std_state_str
        }
        current_ques_type["children"].append(ques_node)
    return tree


def common_range_filter(filter_field, filter_dict: dict):
    type_, value = filter_dict.values()
    if type_ == "1":
        filter_query = filter_field < value
    elif type_ == "2":
        filter_query = filter_field > value
    elif type_ == "3":
        filter_query = filter_field == value
    else:
        # if not isinstance(value, list):
        #     return False, "选择等于时，参数格式需为列表", None
        # filter_query = filter_field.between(*value)
        return False, "参数格式错误", None
    return True, None, filter_query


def get_cluster_condition(stu_answer, set_std_state, stu_count, ai_score, manual_score, answer_percentage,
                          manual_ai_diff_score):
    stu_answer_query = SameStuAnswerGroup.stu_answer.ilike(f"%{stu_answer}%") if stu_answer else True
    set_std_state_query = SameStuAnswerGroup.set_std_state == set_std_state if set_std_state else True
    result1, msg1, stu_count_query = common_range_filter(SameStuAnswerGroup.stu_count, stu_count) if stu_count else (True, None, True)
    result2, msg2, ai_score_query = common_range_filter(SameStuAnswerGroup.ai_score, ai_score) if ai_score else (True, None, True)
    result3, msg3, manual_score_query = common_range_filter(SameStuAnswerGroup.manual_score, manual_score) if manual_score else (True, None, True)
    result4, msg4, answer_percentage_query = common_range_filter(SameStuAnswerGroup.answer_percentage, answer_percentage) if answer_percentage else (True, None, True)
    result5, msg5, manual_ai_diff_score_query = common_range_filter(SameStuAnswerGroup.manual_ai_diff_score, manual_ai_diff_score) if manual_ai_diff_score else (True, None, True)

    if not all([result1, result2, result3, result4, result5]):
        msg_list = []
        for msg in [msg1, msg2, msg3, msg4, msg5]:
            if msg:
                if msg not in msg_list:
                    msg_list.append(msg)
        return False, "，".join(msg_list)

    filter_condition = and_(stu_answer_query, set_std_state_query, stu_count_query, ai_score_query, manual_score_query,
                            answer_percentage_query, manual_ai_diff_score_query)
    return True, filter_condition
