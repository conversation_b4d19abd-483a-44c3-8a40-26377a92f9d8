import os
import uuid
import requests
import zipfile

from settings import logger

from apps.models.models import Project, Subject, ImportDbDataMonitor


def request_api(url, method, data, token, info, is_print=True):
    headers = {"Authorization": token}
    if method.lower() == "get":
        res = requests.get(url, headers=headers, params=data)
    elif method.lower() == "post":
        res = requests.post(url, headers=headers, json=data)
    else:
        raise Exception("暂不支持该请求方式")
    res_code = res.status_code
    res_json = res.json()
    if res_code == 200:
        if res_json["code"] == 200:
            if is_print:
                logger.info(f"获取{info}成功")
            return res_json["data"], f"获取{info}成功"
        else:
            if is_print:
                logger.info(f"获取{info}失败，服务异常：{res_json['msg']}")
            return 0, f"获取{info}失败，服务异常：{res_json['msg']}"
    else:
        if is_print:
            logger.info(f"获取{info}失败，服务异常")
        return 0, f"获取{info}失败，服务异常"


def load_sys_resources(new_session):
    """
    加载系统资源
    """
    project_info = new_session.query(Project.project_id, Project.project_name).all()
    if not project_info:
        return {}, {}
    subject_info = new_session.query(Subject.subject_id, Subject.subject_name).all()
    if not subject_info:
        return {}, {}
    project_dict = {pro_id: pro_name for pro_id, pro_name in project_info}
    subject_dict = {sub_id: sub_name for sub_id, sub_name in subject_info}

    return project_dict, subject_dict


def delete_keys_with_prefix(r, prefix):
    """
    使用 SCAN 查找以 prefix 开头的键，使用 delete 删除以 prefix 开头的键
    """
    cursor = 0
    pattern = f"{prefix}*"

    while True:
        cursor, keys = r.scan(cursor=cursor, match=pattern, count=100)

        if keys:
            # 删除找到的键
            r.delete(*keys)
            # print(f"Deleted keys: {keys}")

        if cursor == 0:
            # 当 cursor == 0 时表示扫描结束
            break


def get_import_db_monitor_info(new_session):
    """
    获取数据库数据导入监控信息
    """
    monitor_info = new_session.query(ImportDbDataMonitor.monitor_id, ImportDbDataMonitor.monitor_type).order_by(ImportDbDataMonitor.created_time.desc()).first()
    if monitor_info:
        monitor_id, monitor_type = monitor_info
        return monitor_id, monitor_type
    else:
        return None, None


def update_monitor_cal(new_session, monitor_id, monitor_type, index, status):
    """
    更新导入监控表特定索引状态
    """
    monitor_type_list = list(monitor_type)
    monitor_type_list[index] = status
    new_monitor_type = "".join(monitor_type_list)
    new_session.query(ImportDbDataMonitor).filter(ImportDbDataMonitor.monitor_id == monitor_id).update({ImportDbDataMonitor.monitor_type: new_monitor_type})
    new_session.commit()


async def download_file_to_local(file, save_path):
    # 保存文件到本地
    try:
        # 确保保存目录存在
        os.makedirs(save_path, exist_ok=True)

        # 保存文件
        file_path = os.path.join(save_path, file.filename)
        with open(file_path, "wb") as f:
            f.write(await file.read())

        logger.info("文件上传成功")
        return True, file_path
    except Exception as e:
        logger.error(f"上传文件时出错: {str(e)}")
        return False, None


async def download_file(file, save_path):
    # 保存文件到本地
    try:
        # 确保保存目录存在
        os.makedirs(save_path, exist_ok=True)

        # 保存文件，使用GUID生成新文件名
        ext = os.path.splitext(file.filename)[1]
        new_filename = f"{uuid.uuid4().hex}{ext}"
        file_path = os.path.join(save_path, new_filename)
        with open(file_path, "wb") as f:
            f.write(await file.read())

        logger.info("文件上传成功")
        return True, file_path
    except Exception as e:
        logger.error(f"上传文件时出错: {str(e)}")
        return False, None


async def unzip_file(zip_file_path, unzip_password):
    """
    使用密码解压 zip 文件并返回解压目录路径。
    """
    try:
        # 解压目标目录，默认在 zip 文件同级目录下创建 <zip_name>_unzipped
        extract_dir = os.path.splitext(zip_file_path)[0] + "_unzipped"
        os.makedirs(extract_dir, exist_ok=True)

        with zipfile.ZipFile(zip_file_path, 'r') as zf:
            zf.setpassword(unzip_password.encode())
            for info in zf.infolist():
                # 修正文件名编码（多层目录也会处理）
                try:
                    filename = info.filename.encode('cp437').decode('gbk')
                except UnicodeDecodeError:
                    filename = info.filename  # 已经是 UTF-8 或其它正常编码

                # 构造完整路径
                target_path = os.path.join(extract_dir, filename)

                # 如果是目录则创建
                if info.is_dir():
                    os.makedirs(target_path, exist_ok=True)
                else:
                    # 创建父目录
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    # 解压文件内容
                    with zf.open(info) as source, open(target_path, 'wb') as target:
                        target.write(source.read())        
        
        

        logger.info("zip 文件解压成功")
        return True, extract_dir
    except Exception as e:
        logger.error(f"解压 zip 文件时出错: {str(e)}")
        return False, None


def extractall_fix_encoding(zip_path, extract_path):
    with zipfile.ZipFile(zip_path, 'r') as zf:
        for info in zf.infolist():
            # 修正文件名编码（多层目录也会处理）
            try:
                filename = info.filename.encode('cp437').decode('gbk')
            except UnicodeDecodeError:
                filename = info.filename  # 已经是 UTF-8 或其它正常编码

            # 构造完整路径
            target_path = os.path.join(extract_path, filename)

            # 如果是目录则创建
            if info.is_dir():
                os.makedirs(target_path, exist_ok=True)
            else:
                # 创建父目录
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                # 解压文件内容
                with zf.open(info) as source, open(target_path, 'wb') as target:
                    target.write(source.read())


def copy_dirs(sou_dir, tar_dir):
    """
    将sou_dir文件夹下的所有文件夹拷贝到tar_dir目录下
    """
    import shutil
    os.makedirs(tar_dir, exist_ok=True)
    for name in os.listdir(sou_dir):
        src_path = os.path.join(sou_dir, name)
        dst_path = os.path.join(tar_dir, name)
        if os.path.isdir(src_path):
            shutil.copytree(src_path, dst_path, dirs_exist_ok=True)
