from datetime import datetime, timedelta
from typing import Any, Optional, Union

from fastapi import Depends, Header, HTTPException, status
from jose import JWTError, jwt
from passlib.context import CryptContext

from settings import configs
from helper import response_utils

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def get_hash_password(raw_password):
    return pwd_context.hash(raw_password)


def verify_hash_password(raw_password, hash_password):
    return pwd_context.verify(raw_password, hash_password)


def create_access_token(data: dict, expires_delta: Union[timedelta, None] = None):
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            hours=configs.ACCESS_TOKEN_EXPIRE_HOURS
        )

    to_encode.update({"exp": expire})
    encode_jwt = jwt.encode(to_encode, configs.SECRET_KEY, algorithm=configs.ALGORITHM)
    return encode_jwt, expire


def verify_access_token(token: Optional[str] = Header(...)) -> Union[str, Any]:
    try:
        payload = jwt.decode(token, configs.SECRET_KEY, configs.ALGORITHM)
        return payload
    except (jwt.JWTError, jwt.ExpiredSignatureError, AttributeError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "code": response_utils.auth_failed,
                "message": "Token Error",
                "data": "Token Error",
            },
        )


if __name__ == '__main__':
    print(get_hash_password("123456"))
