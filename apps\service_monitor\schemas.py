from typing import Optional, Literal
from pydantic import BaseModel, Field
from datetime import datetime

# 数据库模型
class MonitorRecordBase(BaseModel):
    service_name: str = Field(..., description="服务名称")
    status: Literal["running", "stopped", "error"] = Field(..., description="服务状态")
    last_check_time: datetime = Field(..., description="最后检查时间")
    error_message: Optional[str] = Field(None, description="错误信息")

class MonitorRecordCreate(MonitorRecordBase):
    pass

class MonitorRecord(MonitorRecordBase):
    id: int = Field(..., description="记录ID")
    
    class Config:
        from_attributes = True

# API请求和响应模型
class ServiceStatusRequest(BaseModel):
    service_name: str = Field(..., description="服务名称")

class ServiceStatusResponse(BaseModel):
    status: Literal[0,1] = Field(..., description="服务状态, 0为异常， 1为正常")
    error_message: Optional[str] = Field(None, description="错误信息")

class RestartServiceRequest(BaseModel):
    service_name: str = Field(..., description="服务名称")

class RestartServiceResponse(BaseModel):
    success: bool = Field(..., description="重启是否成功")
    message: str = Field(..., description="响应消息")
