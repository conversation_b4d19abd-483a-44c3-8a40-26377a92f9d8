from fastapi import APIRouter, Depends
from settings import logger
from typing import Any

from sqlalchemy.orm import Session

from apps.models.models import UserDataPermission
from apps.permission.schemas import UpdateUseDataReq
from apps.permission.services import get_user_role, update_user_data_permission
from apps.sys_manage.services import get_sys_data_permission
from apps.users.schemas import UserIdReq
from apps.users.services import get_current_user
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils

user_data_router = APIRouter()


@user_data_router.post(path="/get_user_data", response_model=BaseResponse, summary="获取用户数据权限")
def get_user_data_module(query: UserIdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    user_id = query.user_id
    logger.info(f"{user['username']} 获取 user_id 为 {user_id} 的用户的数据权限")

    all_sys_data = get_sys_data_permission()

    permission_data_list = (
        new_session.query(UserDataPermission.project_id, UserDataPermission.subject_id, UserDataPermission.paper_id, UserDataPermission.ques_type_code, UserDataPermission.ques_code)
        .filter(UserDataPermission.user_id == user_id)
        .all()
    )

    for sys_data in permission_data_list:
        permission = [x for x in sys_data if x is not None]
        mark_checked_by_name(all_sys_data, permission)

    data = {"data": all_sys_data}

    return BaseResponse(data=data)


# def strip_trailing_none(t: tuple):
#     t_list = list(t)
#     while t_list and t_list[-1] is None:
#         t_list.pop()
#     return tuple(t_list)


def mark_checked_by_name(tree_nodes, path):
    if not path:
        return

    current_name = path[0]
    for node in tree_nodes:
        if str(node.get("flag")) == current_name:
            node["show"] = True
            # if len(path) == 1:
            #     node["show"] = True
            # else:
            #     mark_checked_by_name(node.get("children", []), path[1:])


@user_data_router.post(path="/update_user_data", response_model=BaseResponse, summary="更新用户数据权限")
def update_user_data(query: UpdateUseDataReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    user_id, path_list = query.model_dump().values()
    logger.info(f"{user['username']} 更新 user_id 为 {user_id} 的用户权限")

    try:
        _, msg = update_user_data_permission(new_session, path_list, user_id=user_id, c_user_id=user.get("user_id"))
        new_session.commit()
        logger.info(msg)
        return BaseResponse(msg=msg)
    except Exception as e:
        logger.error(f"更新用户数据权限失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"更新用户数据权限失败")
