from sqlalchemy import Column, String, Integer, Float, JSON, Text, DateTime, DECIMAL, PrimaryKeyConstraint, Boolean
from sqlalchemy.orm import declarative_base

from apps.models.models import DateTimeBaseMixin

# 声明基类
Base = declarative_base()


class HumanTryMarkResult(Base):
    __tablename__ = "t_human_try_mark_result"
    __table_args__ = {"comment": "试评结果表"}
    try_mark_result_id = Column(String(50), primary_key=True, comment="试评结果id")
    task_id = Column(String(50), comment="人工阅卷任务id")
    round_id = Column(String(50), comment="轮次id")
    user_id = Column(String(50), comment="评阅员id", index=True)
    ques_group_id = Column(String(50), comment="阅卷题组id")
    is_office_mark = Column(Boolean, comment="是否转为正评")
    try_mark_result = Column(Integer, comment="试评结果：0 未评；1 合格；2 不合格")
