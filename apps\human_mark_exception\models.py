import datetime

from sqlalchemy import Column, String, Integer, Float, JSON, Text, DateTime, DECIMAL, PrimaryKeyConstraint, Boolean
from sqlalchemy.orm import declarative_base

from apps.models.models import DateTimeBaseMixin

# 声明基类
Base = declarative_base()


class HumanAnswerSimilarity(Base):
    __tablename__ = "t_human_answer_similarity"
    __table_args__ = {"comment": "作答相似度表"}
    answer_similarity_id = Column(String(50), primary_key=True, comment="作答相似度id")
    answer_id = Column(String(50), comment="作答id", index=True)
    similarity_answer_list = Column(JSON, comment="相似作答列表{'作答id':'','相似度':0.8}")


class HumanPaperSimilarity(Base):
    __tablename__ = "t_human_paper_similarity"
    __table_args__ = {"comment": "试卷相似度表"}
    paper_similarity_id = Column(String(50), primary_key=True, comment="试卷相似度id")
    stu_secret_num = Column(String(30), comment="考生密号")
    project_id = Column(String(50), comment="所属项目id")
    subject_id = Column(String(50), comment="所属科目id")
    similarity_paper_list = Column(JSON, comment="相似作答列表{'allow_exam_num': '', 'subject_id': '', '相似度':0.8}")


class HumanAnswerException(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_answer_exception"
    __table_args__ = {"comment": "问题卷表"}
    answer_exception_id = Column(String(50), primary_key=True, comment="作答异常id")
    answer_id = Column(String(50), comment="作答id")
    task_id = Column(String(50), comment="人工阅卷任务id")
    round_id = Column(String(50), comment="人工阅卷轮次id")
    reviewer_id = Column(String(50), comment="标记人id")
    exception_type = Column(Integer, comment="异常类型：0 为无问题，1 为图像错误，2 为作答位置错误，3 为作答合并，4 为其他")
    handler_state = Column(Integer, comment="处理状态：0 为处理；1 已处理")
    exception_reason = Column(String(2048), comment="异常原因")
    handler_id = Column(String(50), comment="处理人id")
    mark_point_score_list = Column(JSON, comment="评分标准分数列表")
    mark_score = Column(DECIMAL(10, 2), comment="评分分数")
    handle_time = Column(DateTime(timezone=True), default=None, comment="处理时间")


class HumanAnswerQuestSimilarity(Base):
    __tablename__ = "t_human_answer_quest_similarity"
    __table_args__ = {"comment": "作答题干相似度表"}
    answer_quest_similarity_id = Column(String(50), primary_key=True, comment="作答题干相似度id")
    ques_id = Column(String(50), nullable=False, comment="试题id")
    stu_secret_num = Column(String(30), comment="考生密号", index=True)
    similarity = Column(Float, comment="相似度")
    same_words = Column(JSON, comment="相同词列表 ：0.2,['','']")
    answer_image_paths = Column(JSON, comment="作答图片或列表")
    judgement_result = Column(Integer, default=0, comment="判定结果（是否抄袭）:0 未判定；1 抄袭；2 未抄袭")
