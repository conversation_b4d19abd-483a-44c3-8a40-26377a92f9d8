import os
import traceback

import requests
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends
from fastapi.responses import FileResponse
from sqlalchemy import select, and_, func, or_

from apps.grade_manage.models import HumanStudentSubjectGrade, HumanStudentSubjectGradeDetail
from apps.human_mark_exception.models import HumanAnswerException
from apps.human_repeat_mark.models import HumanRepeatAnswerScore, HumanRepeatRoundDetail, HumanRepeatTaskRound, \
    HumanRepeatTask
from apps.human_task_manage.models import HumanPersonDistriAnswer, HumanQualityDistriAnswer
from settings import logger
from typing import Any
from sqlalchemy.orm import Session

from apps.read_paper import ImportProgressReq
from apps.read_paper.common_services import get_user_data_flag
from apps.users.services import get_current_user
from apps.grade_manage.schemas import GetStuGradeReq, GetStuGradeDetailReq, CreateStuGradeReq, GetStuGradeItemDetailReq, \
    GetSubjectScoreReq, GetSubjectScoreDetailReq
from apps.models.models import ExamPaper, Subject, StuTotalGrade, ExamStudent, Project, CreateGradeRecord, \
    StuTotalGradeDetail, SameStuAnswerGroup, BusinessQuesType, ExamQuestion, StuAnswer, UserInfo, SubjectSession
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from apps.grade_manage.services import grade_query_condition, create_grade, get_paper_ques_info, \
    calculate_stu_subject_grade
from utils.create_excel import create_grade_excel
from utils.time_func import format_now_time
from settings import configs

grade_manage_router = APIRouter()


@grade_manage_router.post(path="/create_grade_record", response_model=BaseResponse, summary="生成考生成绩记录")
async def create_grade_record(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 生成考生成绩记录")
    curr_user_id = user["user_id"]
    record_id = configs.snow_worker.get_id()
    grade_record = CreateGradeRecord(record_id=record_id, success_count=0, total_count=0, progress=0,
                                     c_user_id=curr_user_id)
    new_session.add(grade_record)
    new_session.commit()
    return BaseResponse(msg="生成考生成绩记录成功", data={"data": {"record_id": record_id}})


@grade_manage_router.post(path="/create_stu_grade", response_model=BaseResponse, summary="统计考生成绩")
async def create_stu_grade(query: CreateStuGradeReq, user: Any = Depends(get_current_user),
                           new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 统计考生成绩")
    project_id, subject_id, paper_code, stu_secret_num, objective_grade, subjective_grade, op_grade, grade_type, record_id = query.model_dump().values()
    project_condition, subject_condition = get_user_data_flag(new_session, user)
    curr_user_id = user["user_id"]

    # 统计并生成成绩
    result, msg = create_grade(new_session, objective_grade, subjective_grade, op_grade, project_id, subject_id,
                               paper_code, stu_secret_num, project_condition, subject_condition, user, curr_user_id,
                               grade_type, record_id)
    if result:
        return BaseResponse(msg="统计成绩成功")
    new_session.rollback()
    return BaseResponse(code=response_utils.server_error, msg=msg)


@grade_manage_router.post(path="/get_create_grade_progress", response_model=BaseResponse,
                          summary="获取生成考生成绩进度")
async def get_create_grade_progress(query: ImportProgressReq, user: Any = Depends(get_current_user),
                                    new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取生成考生成绩进度")
    record_id = query.record_id
    progress = new_session.query(CreateGradeRecord.progress).filter(CreateGradeRecord.record_id == record_id).scalar()
    is_query_data = True if (progress and progress > 0) else False
    data = {
        "data": {
            "progress": progress,
            "is_query_data": is_query_data
        }
    }
    return BaseResponse(msg="获取生成考生成绩进度成功", data=data)


@grade_manage_router.post(path="/get_stu_grade", response_model=BaseResponse, summary="获取考生成绩列表")
async def get_stu_grade(query: GetStuGradeReq, user: Any = Depends(get_current_user),
                        new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考生成绩列表")
    current_page, page_size, project_id, subject_id, paper_code, stu_secret_num, grade_type, stu_score_range, is_export = query.model_dump().values()

    # 查询参数校验
    if stu_score_range and not grade_type:
        return BaseResponse(code=response_utils.params_error, msg="请输入需要查询的成绩类型")
    if grade_type == 1 or grade_type == 2:
        if not stu_score_range:
            return BaseResponse(code=response_utils.params_error, msg="请输入需要查询的分数段")
        else:
            if len(stu_score_range) != 2:
                return BaseResponse(code=response_utils.params_error, msg="成绩分数段输入不正确")

    project_condition, subject_condition = get_user_data_flag(new_session, user)

    try:
        grade_data = []
        limit = current_page - 1
        offset = limit * page_size

        # 拼凑查询条件
        condition = grade_query_condition(project_id, subject_id, paper_code, stu_secret_num, grade_type,
                                          stu_score_range)

        total = new_session.query(StuTotalGrade.grade_id) \
            .join(ExamPaper, ExamPaper.paper_code == StuTotalGrade.paper_code) \
            .join(Project, Project.project_id == StuTotalGrade.project_id) \
            .join(Subject, Subject.subject_id == StuTotalGrade.subject_id) \
            .join(ExamStudent, ExamStudent.allow_exam_num == StuTotalGrade.allow_exam_num) \
            .where(and_(condition, project_condition, subject_condition)) \
            .group_by(StuTotalGrade.grade_id).count()

        select_fields = [
            StuTotalGrade.grade_id, func.max(ExamStudent.stu_secret_num),
            func.max(StuTotalGrade.project_id), func.max(StuTotalGrade.subject_id),
            func.max(StuTotalGrade.paper_code), func.max(StuTotalGrade.ai_grade),
            func.max(StuTotalGrade.manual_grade), func.max(StuTotalGrade.created_time),
            func.max(StuTotalGrade.updated_time), func.max(ExamPaper.paper_name),
            func.max(Project.project_name), func.max(Subject.subject_name),
            func.max(ExamPaper.total_score)
        ]

        if is_export:
            select_fields.append(func.max(StuTotalGrade.allow_exam_num))
            grade_stmt = select(*select_fields) \
                .join(Project, Project.project_id == StuTotalGrade.project_id) \
                .join(Subject, Subject.subject_id == StuTotalGrade.subject_id) \
                .join(ExamPaper, ExamPaper.paper_code == StuTotalGrade.paper_code) \
                .join(ExamStudent, ExamStudent.allow_exam_num == StuTotalGrade.allow_exam_num) \
                .where(and_(condition, project_condition, subject_condition)) \
                .group_by(StuTotalGrade.grade_id) \
                .order_by(StuTotalGrade.created_time.desc())
        else:
            grade_stmt = select(*select_fields) \
                .join(Project, Project.project_id == StuTotalGrade.project_id) \
                .join(Subject, Subject.subject_id == StuTotalGrade.subject_id) \
                .join(ExamPaper, ExamPaper.paper_code == StuTotalGrade.paper_code) \
                .join(ExamStudent, ExamStudent.allow_exam_num == StuTotalGrade.allow_exam_num) \
                .where(and_(condition, project_condition, subject_condition)) \
                .group_by(StuTotalGrade.grade_id) \
                .order_by(StuTotalGrade.created_time.desc(), StuTotalGrade.grade_id.desc()) \
                .limit(page_size).offset(offset)

        result = new_session.execute(grade_stmt)
        for row in result:
            grade_item = {
                "grade_id": row[0],
                "stu_secret_num": row[1],
                "project_id": row[2],
                "subject_id": row[3],
                "paper_code": row[4],
                "ai_grade": row[5],
                "manual_grade": row[6],
                "created_time": row[7] and str(row[7]).replace("T", " "),
                "updated_time": row[8] and str(row[8]).replace("T", " "),
                "paper_name": row[9],
                "project_name": row[10],
                "subject_name": row[11],
                "total_score": row[12]
            }
            if is_export:
                grade_item["allow_exam_num"] = row[13]
            grade_data.append(grade_item)
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.info(f"获取考生成绩列表失败，{e}")
        return BaseResponse(code=response_utils.params_error, msg="获取考生成绩列表失败")
    logger.info("获取考生成绩列表成功")
    data = {
        "data": grade_data,
        "total": total
    }
    return BaseResponse(msg="获取考生成绩列表成功", data=data)


@grade_manage_router.post(path="/get_stu_grade_detail", response_model=BaseResponse, summary="获取考生成绩详情")
async def get_stu_grade_detail(query: GetStuGradeDetailReq, user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考生成绩详情")
    grade_id, grade_type, paper_code = query.model_dump().values()
    grade_detail = new_session.query(StuTotalGradeDetail.detail_data).filter(
        and_(StuTotalGradeDetail.grade_id == grade_id, StuTotalGradeDetail.grade_type == grade_type)).scalar()

    if not grade_detail:
        data = {"data": []}
        BaseResponse(msg="暂无成绩详情数据", data=data)

    _, paper_data_dict = get_paper_ques_info(paper_code, user, False, True)
    for grade_item in grade_detail:
        grade_data = grade_item["data"]
        for ques_item in grade_data:
            ques_id = ques_item["ques_id"]
            ques_item.update(paper_data_dict[ques_id])
            children = ques_item.get("children", [])
            for index, child_item in enumerate(children):
                ques_id = child_item["ques_id"]
                ques_dict = paper_data_dict[ques_id]
                child_item.update(ques_dict)

    data = {"data": grade_detail}
    return BaseResponse(msg="获取考生成绩详情成功", data=data)


@grade_manage_router.post(path="/get_create_grade_record", response_model=BaseResponse, summary="获取统计成绩记录")
async def get_create_grade_record(user: Any = Depends(get_current_user),
                                  new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取统计成绩记录")
    curr_user_id = user["user_id"]
    record_info = new_session.query(CreateGradeRecord.created_time) \
        .filter(and_(CreateGradeRecord.c_user_id == curr_user_id, CreateGradeRecord.progress >= 100)) \
        .order_by(CreateGradeRecord.created_time.desc()).first()
    if not record_info:
        logger.info("该用户暂无统计成绩记录")
        return BaseResponse(data={"record_time": None})
    record_time = str(record_info[0]).replace("T", " ")
    data = {"record_time": record_time}
    logger.info("获取统计成绩记录成功")
    return BaseResponse(data=data)

@grade_manage_router.post(path="/get_subject_grade_list", response_model=BaseResponse, summary="查询科目成绩")
async def get_subject_grade_list(query: GetSubjectScoreReq, user: Any = Depends(get_current_user),
                                  new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 查询科目成绩")
    calculate_stu_subject_grade()
    current_page, page_size, project_id, subject_id, stu_secret_num, mark_score_range = query.model_dump().values()
    limit = current_page - 1
    offset = limit * page_size
    project_query = Project.project_id == project_id if project_id else True
    subject_query = Subject.subject_id == subject_id if subject_id else True
    stu_secret_query = HumanStudentSubjectGrade.stu_secret_num == stu_secret_num if stu_secret_num else True
    mark_score_query = True
    if mark_score_range:
        mark_score_query = HumanStudentSubjectGrade.score.between(*mark_score_range)
    condition = and_(project_query,subject_query,stu_secret_query,mark_score_query)
    #考生在表里一个科目只会有一条记录
    total = new_session.query(HumanStudentSubjectGrade.student_subject_grade_id).join(Project, Project.project_id == HumanStudentSubjectGrade.project_id)\
            .join(Subject, Subject.subject_id == HumanStudentSubjectGrade.subject_id)\
            .where(condition).count()
    grade_info = new_session.query(HumanStudentSubjectGrade.student_subject_grade_id, Project.project_name, HumanStudentSubjectGrade.project_id, Subject.subject_name,
                                   HumanStudentSubjectGrade.subject_id, SubjectSession.exam_session,
                                   HumanStudentSubjectGrade.stu_secret_num, Subject.subject_total_score, HumanStudentSubjectGrade.score)\
            .join(Project, Project.project_id == HumanStudentSubjectGrade.project_id)\
            .join(Subject, Subject.subject_id == HumanStudentSubjectGrade.subject_id)\
            .outerjoin(SubjectSession, SubjectSession.subject_id == Subject.subject_id)\
            .where(condition)\
            .group_by(Project.project_id, Subject.subject_id, HumanStudentSubjectGrade.student_subject_grade_id,SubjectSession.exam_session)\
            .limit(page_size).offset(offset).all()
    grade_data = []
    for row in grade_info:
        grade_data.append({
            "student_subject_grade_id": row[0],
            "project_name": row[1],
            "project_id": row[2],
            "subject_name": row[3],
            "subject_id": row[4],
            "exam_session": row[5],
            "stu_secret_num": row[6],
            "subject_total_score": row[7],
            "score": row[8]
        })
    result = {
        "total":total,
        "data":grade_data
    }
    return BaseResponse(data=result,msg="查询科目成绩成功")

@grade_manage_router.post(path="/get_subject_grade_detail", response_model=BaseResponse, summary="获取评分详情")
async def get_subject_grade_detail(query: GetSubjectScoreDetailReq, user: Any = Depends(get_current_user),
                                  new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取评分详情")
    student_subject_grade_id = query.student_subject_grade_id
    detail_info = new_session.query(Project.project_name, Project.project_id, Subject.subject_name, Subject.subject_id, SubjectSession.exam_session,
                                    HumanStudentSubjectGrade.stu_secret_num, Subject.subject_total_score, HumanStudentSubjectGrade.score) \
        .join(Project, Project.project_id == HumanStudentSubjectGrade.project_id) \
        .join(Subject, Subject.subject_id == HumanStudentSubjectGrade.subject_id) \
        .join(SubjectSession, SubjectSession.subject_id == Subject.subject_id)\
        .filter(HumanStudentSubjectGrade.student_subject_grade_id == student_subject_grade_id).first()
    result = []
    # 评分明细 列表 业务题型表->exam_question->
    if detail_info:
        project_id, subject_id, stu_secret_num = detail_info.project_id,detail_info.subject_id,detail_info.stu_secret_num
        ques_type_infos = new_session.query(BusinessQuesType.business_ques_type_id, BusinessQuesType.ques_type_name)\
                       .filter(BusinessQuesType.project_id == project_id, BusinessQuesType.subject_id ==subject_id).all()
        for type_info in ques_type_infos:
            ques_info = new_session.query(ExamQuestion.ques_code,ExamQuestion.knowledge_show,ExamQuestion.ques_score)\
            .filter(and_(or_(ExamQuestion.parent_ques_id.is_(None), ExamQuestion.parent_ques_id == ""),ExamQuestion.business_ques_type_id == type_info.business_ques_type_id))
            for ques in ques_info:
                #每道大题的考生分数
                grade_info = new_session.query(func.sum(HumanStudentSubjectGradeDetail.stu_score).label("score"))\
                .join(ExamQuestion, ExamQuestion.ques_id == HumanStudentSubjectGradeDetail.ques_id)\
                .filter(
                    and_(ExamQuestion.ques_code == ques.ques_code,HumanStudentSubjectGradeDetail.student_subject_grade_id == student_subject_grade_id)
                    ).first()
                #每道大题的评分次数
                #每次评分的分数，轮次，时间和评分人，
                #正评
                subquery = new_session.query(
                    HumanPersonDistriAnswer.mark_score,
                    UserInfo.username,
                    HumanPersonDistriAnswer.person_distri_id,
                    HumanPersonDistriAnswer.mark_time
                ).join(
                    ExamQuestion,
                    ExamQuestion.ques_code == HumanPersonDistriAnswer.ques_code
                ).join(
                    UserInfo,
                    UserInfo.user_id == HumanPersonDistriAnswer.user_id
                ).filter(
                    ExamQuestion.ques_code == ques.ques_code,
                    ExamQuestion.parent_ques_id.isnot(None),
                    HumanPersonDistriAnswer.is_answer_marked == 1,
                    HumanPersonDistriAnswer.stu_secret_num == stu_secret_num
                ).group_by(
                    HumanPersonDistriAnswer.mark_score,
                    UserInfo.username,
                    HumanPersonDistriAnswer.person_distri_id,
                    HumanPersonDistriAnswer.mark_time
                ).subquery()

                # 主查询部分
                official_mark_result = new_session.query(
                    func.sum(subquery.c.mark_score).label('score'),
                    subquery.c.username,
                    func.max(subquery.c.mark_time).label('mark_time')
                ).group_by(subquery.c.username).first()

                official_mark = []
                if official_mark_result:
                    official_mark.append({
                        "score": float(official_mark_result.score) if official_mark_result.score else 0,
                        "mark_time": official_mark_result.mark_time and str(official_mark_result.mark_time).replace("T", " "),
                        "username": official_mark_result.username,
                    })
                #质检
                subquery = (new_session.query(
                    HumanQualityDistriAnswer.quality_id,
                    UserInfo.username,
                    HumanQualityDistriAnswer.quality_score,
                    HumanQualityDistriAnswer.created_time
                ).join(HumanPersonDistriAnswer,
                       HumanPersonDistriAnswer.person_distri_id == HumanQualityDistriAnswer.person_distri_id)
                .join(ExamQuestion, ExamQuestion.ques_code == HumanPersonDistriAnswer.ques_code)
                .join(UserInfo,UserInfo.user_id == HumanQualityDistriAnswer.quality_user_id)
                .filter(
                    ExamQuestion.ques_code == ques.ques_code,
                    ExamQuestion.parent_ques_id.isnot(None),
                    HumanPersonDistriAnswer.is_answer_marked == 1,
                    HumanPersonDistriAnswer.stu_secret_num == stu_secret_num
                ).group_by(
                    HumanQualityDistriAnswer.quality_score,
                    UserInfo.username,
                    HumanQualityDistriAnswer.quality_id,
                    HumanQualityDistriAnswer.created_time
                ).subquery())
                quality_mark_result = new_session.query(
                    func.sum(subquery.c.quality_score).label('score'),
                    subquery.c.username,
                    func.max(subquery.c.created_time).label('mark_time')
                ).group_by(subquery.c.username).first()

                if quality_mark_result:
                    official_mark.append({
                        "score": float(quality_mark_result.score) if quality_mark_result.score else 0,
                        "mark_time": quality_mark_result.mark_time and str(quality_mark_result.mark_time).replace("T"," "),
                        "username": quality_mark_result.username,
                        "type": "质检"
                    })

                #问题卷
                exception_info = new_session.query(func.sum(HumanAnswerException.mark_score).label('score'),func.max(HumanAnswerException.handle_time).label('mark_time'),UserInfo.username)\
                            .join(StuAnswer, StuAnswer.answer_id == HumanAnswerException.answer_id)\
                            .join(UserInfo, UserInfo.user_id == HumanAnswerException.handler_id)\
                            .join(ExamQuestion, ExamQuestion.ques_id == StuAnswer.ques_id)\
                            .filter(ExamQuestion.ques_code == ques.ques_code,ExamQuestion.parent_ques_id.isnot(None), StuAnswer.stu_secret_num == stu_secret_num).group_by(UserInfo.username).first()
                if exception_info:
                    official_mark.append({
                        "score": float(exception_info.score) if exception_info.score else 0,
                        "mark_time": exception_info.mark_time and str(exception_info.mark_time).replace("T", " "),
                        "username": exception_info.username,
                        "type": "问题卷"
                    })
                #复评
                repeat_subquery = new_session.query(
                    HumanRepeatAnswerScore.mark_score,
                    UserInfo.username,
                    HumanRepeatAnswerScore.repeat_answer_socre_id,
                    HumanRepeatAnswerScore.mark_time,
                    HumanRepeatTask.round_count  # 添加轮次信息
                ).join(HumanRepeatRoundDetail,HumanRepeatRoundDetail.repeat_round_detail_id == HumanRepeatAnswerScore.repeat_round_detail_id
                ).join(HumanRepeatTask,HumanRepeatTask.repeat_task_id == HumanRepeatRoundDetail.repeat_task_id
                ).join(HumanRepeatTaskRound, HumanRepeatTaskRound.repeat_task_round_id == HumanRepeatTask.repeat_task_round_id
                ).join(UserInfo,UserInfo.user_id == HumanRepeatRoundDetail.repeat_user_id
                ).join(ExamQuestion,ExamQuestion.ques_code == HumanRepeatAnswerScore.ques_code
                ).filter(ExamQuestion.parent_ques_id.isnot(None),  # 筛选小题
                    ExamQuestion.ques_code == ques.ques_code,  # 该大题下的小题
                    HumanRepeatRoundDetail.stu_secret_num == stu_secret_num, HumanRepeatTask.task_type == 3
                ).group_by(
                    HumanRepeatAnswerScore.mark_score,
                    UserInfo.username,
                    HumanRepeatAnswerScore.repeat_answer_socre_id,
                    HumanRepeatAnswerScore.mark_time,
                    HumanRepeatTask.round_count
                ).subquery()

                # 主查询部分 - 获取每个评卷员每轮次的大题总分
                repeat_mark_results = new_session.query(
                    func.sum(repeat_subquery.c.mark_score).label('score'),
                    repeat_subquery.c.username,
                    func.max(repeat_subquery.c.mark_time).label('mark_time'),
                    repeat_subquery.c.round_count  # 按轮次分组
                ).group_by(repeat_subquery.c.username, repeat_subquery.c.round_count, repeat_subquery.c.mark_time).order_by(repeat_subquery.c.mark_time.desc()).all()

                repeat_mark = []
                for repeat_result in repeat_mark_results:
                    repeat_mark.append({
                        "score": float(repeat_result.score) if repeat_result.score else 0,
                        "round_count": repeat_result.round_count,
                        "username": repeat_result.username,
                        "mark_time": repeat_result.mark_time and str(repeat_result.mark_time).replace("T", " ")
                    })
                count = 0
                if official_mark:
                    count+= len(official_mark)
                if repeat_mark:
                    count+= len(repeat_mark)
                official_mark.sort(key=lambda x: x['mark_time'], reverse=True)
                grade_item = {
                    "ques_type_name": type_info.ques_type_name,
                    "ques_code": ques.ques_code,
                    "ques_name": ques.knowledge_show,
                    "ques_score": ques.ques_score,
                    "stu_grade": grade_info.score,
                    "official_mark": official_mark,
                    "repeat_mark": repeat_mark,
                    "count":count
                }

                result.append(grade_item)
    return BaseResponse(data=result, msg="获取评分详情成功")

@grade_manage_router.post(path="/export_stu_grade", response_model=BaseResponse, summary="导出考生成绩")
async def export_stu_grade(query: GetStuGradeReq, user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 导出考生成绩")
    _, _, project_id, subject_id, paper_code, stu_secret_num, grade_type, stu_score_range, is_export = query.model_dump().values()

    data = {
        "is_export": True,
        "project_id": project_id,
        "subject_id": subject_id,
        "paper_code": paper_code,
        "stu_secret_num": stu_secret_num,
        "grade_type": grade_type,
        "stu_score_range": stu_score_range
    }
    headers = {"Authorization": f"Bearer {user['token']}"}
    res = requests.post(f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/grade_manage/get_stu_grade", json=data,
                        headers=headers)
    if res.status_code != 200:
        return BaseResponse(code=response_utils.server_error, msg="服务出错")
    res_data = res.json()
    if res_data["code"] != 200:
        return BaseResponse(code=response_utils.server_error, msg="服务出错")
    export_data = res_data["data"]["data"]
    if not export_data:
        return BaseResponse(code=response_utils.no_field, msg="数据为空")
    title_dict = {
        "A1": "准考证号",
        "B1": "考生密号",
        "C1": "项目",
        "D1": "科目",
        "E1": "试卷名称",
        "F1": "试卷总分",
        "G1": "AI评分成绩",
        "H1": "人工评分成绩",
        "I1": "创建时间"
    }
    grade_data = []
    for grade in export_data:
        single_grade = [grade["allow_exam_num"], grade["stu_secret_num"], grade["project_name"], grade["subject_name"],
                        grade["paper_name"], grade["total_score"], grade["ai_grade"], grade["manual_grade"],
                        grade["created_time"]]
        grade_data.append(single_grade)

    file_path = os.path.join(configs.PROJECT_PATH, f"server_static/export_file/grade")
    os.makedirs(file_path, exist_ok=True)
    filename = f"考生成绩_{format_now_time('%Y_%m_%d_%H_%M_%S')}.xlsx"
    absolute_path = os.path.join(file_path, filename)
    flag = create_grade_excel(title_dict, grade_data, absolute_path)
    if not flag:
        return BaseResponse(code=response_utils.server_error, msg="生成文件失败")
    return FileResponse(absolute_path, filename=filename)


@grade_manage_router.post(path="/get_stu_grade_item_detail", response_model=BaseResponse, summary="获取考生成绩单个评分的解析详情")
async def get_stu_grade_item_detail(query: GetStuGradeItemDetailReq, user: Any = Depends(get_current_user),
                                    new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考生成绩单个评分的解析详情")
    ques_id, paper_code, stu_answer = query.model_dump().values()
    stu_answer = configs.NEW_SPLIT_FLAG.join(stu_answer)
    mark_info = new_session.query(SameStuAnswerGroup.stu_score, SameStuAnswerGroup.answer_parse,
                                  SameStuAnswerGroup.profession_parse) \
        .filter(and_(SameStuAnswerGroup.ques_id == ques_id, SameStuAnswerGroup.paper_code,
                     SameStuAnswerGroup.stu_answer == stu_answer)).first()
    if mark_info:
        stu_score, answer_parse, profession_parse = mark_info
        if profession_parse:
            answer_parse = profession_parse
        else:
            answer_parse = answer_parse.split(configs.NEW_SPLIT_FLAG) if answer_parse else []
        data = {
            "answer_parse": answer_parse
        }
        return BaseResponse(data=data, msg="获取考生成绩单个评分的解析详情成功")
    return BaseResponse(data={"answer_parse": []}, msg="暂无解析")

@grade_manage_router.post(path="/calculate_subject_grade", response_model=BaseResponse, summary="计算考生科目成绩")
async def calculate_subject_grade(query: GetSubjectScoreReq, user: Any = Depends(get_current_user),
                                  new_session: Session = Depends(session_depend)):
    #1.对于student_subject_grade的每个考生，去human_stu_grade_detail找他们作答的题,answer_id
    #2.去stu_answer(redis)中进行比较，如果都对的上 就说明已经评完该考生
    #3.去human_stu_grade_detail把该条记录的考生的该科成绩加上去

    logger.info("统计考生科目成绩")
    calculate_stu_subject_grade()

    return BaseResponse(msg="统计考生科目成绩成功")