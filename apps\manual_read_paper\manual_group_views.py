import traceback

from fastapi import APIRouter, Depends
from settings import logger
from sqlalchemy import exists, select, and_, func, case, asc
from sqlalchemy.orm import aliased, Session
from typing import Any

from apps.manual_read_paper.schemas import CreateManualGroupReq, GetManualGroupReq, UpdateManualGroupReq, \
    DeleteManualGroupReq, GetManualPeopleReq, RandomDistributeGroupPeopleReq, SaveGroupUserReq
from apps.manual_read_paper.services import check_manual_user_num, random_assign_manual_user_to_group, \
    get_every_manual_role_user, get_every_manual_role_num, update_group_user_info, manual_group_query_condition, \
    get_manual_user_info, get_ai_user_by_role_id, get_ai_user_num_by_group_id, check_ai_num_when_create_group
from apps.read_paper.common_services import get_user_data_flag
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.models.models import <PERSON>ReadPaperGroup, ManualGroup<PERSON>ser, <PERSON>rInfo, UserRole, WorkFlowMainProcess, Project, Subject
from factory_apps.mysql_db.databases import session_depend
from helper import response_utils
from settings import configs

manual_group_router = APIRouter()


@manual_group_router.post(path="/create_manual_group", response_model=BaseResponse, summary="创建人工阅卷小组")
async def create_manual_group(query: CreateManualGroupReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建人工阅卷小组")
    (group_name, project_id, subject_id, process_id, create_num, expert_ai_num, arbitrator_ai_num, quality_ai_num,
     remark) = query.model_dump().values()
    if create_num <= 0:
        return BaseResponse(code=response_utils.params_error, msg="小组数量非法")

    try:
        msg = check_ai_num_when_create_group(new_session, expert_ai_num, arbitrator_ai_num, quality_ai_num)
        if msg:
            return BaseResponse(code=response_utils.no_field, msg=msg)

        new_group_list = []
        for i in range(1, create_num + 1):
            single_group_name = group_name
            if create_num > 1:
                single_group_name = f"{group_name}第{i}组"

            is_exist = new_session.query(
                exists().where(ManualReadPaperGroup.manual_group_name == single_group_name)).scalar()
            if is_exist:
                return BaseResponse(code=response_utils.fields_exist, msg=f"小组名 {group_name} 已存在")

            new_group = ManualReadPaperGroup(manual_group_id=configs.snow_worker.get_id(),
                                             manual_group_name=single_group_name, manual_project_id=project_id,
                                             manual_subject_id=subject_id, manual_process_id=process_id,
                                             expert_ai_num=expert_ai_num, arbitrator_ai_num=arbitrator_ai_num,
                                             quality_ai_num=quality_ai_num, remark=remark,
                                             c_user_id=user.get("user_id"))
            new_group_list.append(new_group)
        new_session.add_all(new_group_list)
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.error(f"创建人工阅卷小组失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg='创建人工阅卷小组失败')
    new_group_id_list = [i.manual_group_id for i in new_group_list]
    new_group_name_list = [i.manual_group_name for i in new_group_list]
    expert_num, arbitrator_num, quality_num = get_every_manual_role_num(query.process_id)
    new_session.query(WorkFlowMainProcess).filter(WorkFlowMainProcess.process_id == query.process_id).update(
        {WorkFlowMainProcess.lock_state: 2})
    data = {
        "new_group_id_list": new_group_id_list,
        "new_group_name_list": new_group_name_list,
        "expert_num": expert_num,
        "arbitrator_num": arbitrator_num,
        "quality_num": quality_num
    }
    logger.info(f"创建人工阅卷小组成功")
    return BaseResponse(msg=f'创建人工阅卷小组成功', data=data)


@manual_group_router.post(path="/get_user_by_role_id", response_model=BaseResponse, summary="获取各个角色阅卷人员信息")
async def get_manual_people_info(query: GetManualPeopleReq, user: Any = Depends(get_current_user),
                                 new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 根据角色 {query.role_id} 获取阅卷人员信息")
    current_page, page_size, role_id, selected_user_id, name, is_distri, user_type, expert_ai_num, arbitrator_ai_num, quality_ai_num = query.model_dump().values()

    user_id_in_group = new_session.query(ManualGroupUser.user_id).filter(ManualGroupUser.role_id == role_id).all()
    if not user_id_in_group:
        user_id_in_group_list = []
    else:
        user_id_in_group_list = [i[0] for i in user_id_in_group]

    role_condition = UserRole.role_id == role_id
    if role_id == "3":
        ai_condition = UserInfo.user_type != 2 if not expert_ai_num else True
    elif role_id == "4":
        ai_condition = UserInfo.user_type != 2 if not arbitrator_ai_num else True
    elif role_id == "5":
        ai_condition = UserInfo.user_type != 2 if not quality_ai_num else True
    else:
        ai_condition = True
    user_condition = UserInfo.name.ilike(f"%{name}%") if name else True
    if is_distri == 1:
        distri_condition = True
    elif is_distri == 2:
        distri_condition = ~UserInfo.user_id.in_(user_id_in_group_list)
    else:
        distri_condition = UserInfo.user_id.in_(user_id_in_group_list)

    user_type_query = UserInfo.user_type == user_type if user_type else True
    condition = and_(UserInfo.is_active == 1, role_condition, ai_condition, user_condition, distri_condition,
                     user_type_query)

    total = new_session.query(UserInfo.user_id.distinct()) \
        .join(UserRole, UserRole.user_id == UserInfo.user_id) \
        .outerjoin(ManualGroupUser, ManualGroupUser.user_id == UserInfo.user_id) \
        .where(condition).count()

    # 创建一个case表达式来定义排序顺序: user_id 在 selected_user_id 列表里的排在最前面
    order_by_selected_case = case(
        *[(UserInfo.user_id == user_id, index) for index, user_id in enumerate(selected_user_id)],
        (~UserInfo.user_id.in_(user_id_in_group_list), len(selected_user_id)),
        else_=len(selected_user_id) + 1  # 不在user_id_list中的用户的默认排序值
    )

    limit = current_page - 1
    offset = limit * page_size
    user_stmt = select(UserInfo.user_id, UserInfo.username, UserInfo.name, UserInfo.phone, UserInfo.id_card,
                       ManualGroupUser.user_id, UserInfo.user_type, UserInfo.is_active) \
        .join(UserRole, UserRole.user_id == UserInfo.user_id) \
        .outerjoin(ManualGroupUser, ManualGroupUser.user_id == UserInfo.user_id) \
        .where(condition) \
        .group_by(UserInfo.user_id, UserInfo.username, UserInfo.name, UserInfo.phone, UserInfo.id_card,
                  ManualGroupUser.user_id, UserInfo.user_type, UserInfo.is_active) \
        .order_by(asc(order_by_selected_case)) \
        .limit(page_size).offset(offset)

    user_data = []
    try:
        result = new_session.execute(user_stmt)
        for row in result:
            group_user_id = row[5]
            if is_distri == 1:
                has_group = True if group_user_id else False
            elif is_distri == 2:
                has_group = False
            elif is_distri == 3:
                has_group = True
            else:
                return BaseResponse(code=response_utils.params_error, msg="参数错误")
            user_item = {
                "user_id": row.user_id,
                "username": row.username,
                "name": row.name,
                "phone": row.phone,
                "id_card": row.id_card,
                "has_group": has_group,
                "user_type": row.user_type,
                "is_active": row.is_active
            }
            user_data.append(user_item)
    except Exception as e:
        logger.error(f"获取各个角色阅卷人员信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取各个角色阅卷人员信息失败")
    logger.info("获取各个角色阅卷人员信息成功")
    data = {
        "data": user_data,
        "total": total
    }
    return BaseResponse(msg="获取各个角色阅卷人员信息成功", data=data)


@manual_group_router.post(path="/random_distribute_group_people", response_model=BaseResponse, summary="随机分配人工阅卷小组成员")
async def random_distribute_group_people(query: RandomDistributeGroupPeopleReq, user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 随机分配人工阅卷小组成员")
    (manual_group_name, new_group_id_list, new_group_name_list, expert_num, arbitrator_num, quality_num, expert_ai_num,
     arbitrator_ai_num, quality_ai_num) = query.model_dump().values()

    try:
        expert_info, arbitrator_info, quality_info, ai_expert_info, ai_arbitrator_info, ai_quality_info = get_manual_user_info()

        msg = check_manual_user_num(len(new_group_id_list), expert_num, arbitrator_num, quality_num,
                                    len(expert_info), len(arbitrator_info), len(quality_info))
        if msg:
            return BaseResponse(code=response_utils.params_error, msg=msg)
        msg, group_user = random_assign_manual_user_to_group(new_group_id_list, new_group_name_list, expert_num,
                                                             expert_ai_num, arbitrator_num, arbitrator_ai_num,
                                                             quality_num, quality_ai_num, expert_info,
                                                             arbitrator_info, quality_info, ai_expert_info,
                                                             ai_arbitrator_info, ai_quality_info)

        if msg:
            return BaseResponse(code=response_utils.params_error, msg=msg)
    except Exception as e:
        logger.error(f"随机分配人工阅卷小组成员失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg="随机分配人工阅卷小组成员失败")
    logger.info("随机分配人工阅卷小组成员成功")
    data = {"data": group_user}
    return BaseResponse(data=data)


@manual_group_router.post(path="/save_group_people", response_model=BaseResponse, summary="保存人工阅卷小组成员")
async def save_group_people(query: SaveGroupUserReq, user: Any = Depends(get_current_user),
                            new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 保存人工阅卷小组成员")

    curr_user_id = user.get("user_id")
    manual_group_user_list = query.manual_group_user_list
    new_group_user_list, group_id_list = [], []

    all_expert_ai_list, all_arbitrator_ai_list, all_quality_ai_list = get_ai_user_by_role_id(new_session)

    group_id_list = [group.get("group_id") for group in manual_group_user_list]

    group_ai_user_dict = get_ai_user_num_by_group_id(new_session, group_id_list)

    try:
        for group in manual_group_user_list:
            manual_group_id = group.get("group_id")
            manual_group_name = group_ai_user_dict[manual_group_id]["manual_group_name"]
            (expert_user_id_list, expert_user_name_list, arbitration_user_id_list, arbitration_user_name_list,
             quality_user_id_list, quality_user_name_list) = (group.get("expert", []), group.get("expert_name", []),
                                                              group.get("arbitrator", []),
                                                              group.get("arbitrator_name", []),
                                                              group.get("quality", []), group.get("quality_name", []))
            if len(expert_user_id_list) != len(expert_user_name_list) or len(arbitration_user_id_list) != len(
                    arbitration_user_name_list) or len(quality_user_id_list) != len(quality_user_name_list):
                return BaseResponse(code=response_utils.params_error, msg="参数错误")

            common_ai_expert_id_list = list(set(all_expert_ai_list) & set(expert_user_id_list))
            need_ai_expert_num = group_ai_user_dict[manual_group_id]["expert_ai_num"]
            if len(common_ai_expert_id_list) != need_ai_expert_num:
                return BaseResponse(code=response_utils.params_error,
                                    msg=f"{manual_group_name}: AI 阅卷人员需要 {need_ai_expert_num} 位, 目前选择了 {len(common_ai_expert_id_list)} 位")

            common_ai_arbitrator_id_list = list(set(all_arbitrator_ai_list) & set(arbitration_user_id_list))
            need_ai_arbitrator_num = group_ai_user_dict[manual_group_id]["arbitrator_ai_num"]
            if len(common_ai_arbitrator_id_list) != need_ai_arbitrator_num:
                return BaseResponse(code=response_utils.params_error,
                                    msg=f"{manual_group_name}: AI 仲裁人员需要 {need_ai_arbitrator_num} 位, 目前选择了 {len(common_ai_arbitrator_id_list)} 位")

            common_ai_quality_id_list = list(set(all_quality_ai_list) & set(quality_user_id_list))
            need_ai_quality_num = group_ai_user_dict[manual_group_id]["quality_ai_num"]
            if len(common_ai_quality_id_list) != need_ai_quality_num:
                return BaseResponse(code=response_utils.params_error,
                                    msg=f"{manual_group_name}: AI 质检人员需要 {need_ai_quality_num} 位, 目前选择了 {len(common_ai_quality_id_list)} 位")

            for user_id in quality_user_id_list:
                new_group_user = ManualGroupUser(manual_group_user_id=configs.snow_worker.get_id(),
                                                 manual_group_id=manual_group_id, user_id=user_id, role_id="5",
                                                 c_user_id=curr_user_id)
                new_group_user_list.append(new_group_user)
            for user_id in arbitration_user_id_list:
                new_group_user = ManualGroupUser(manual_group_user_id=configs.snow_worker.get_id(),
                                                 manual_group_id=manual_group_id, user_id=user_id, role_id="4",
                                                 c_user_id=curr_user_id)
                new_group_user_list.append(new_group_user)
            for user_id in expert_user_id_list:
                new_group_user = ManualGroupUser(manual_group_user_id=configs.snow_worker.get_id(),
                                                 manual_group_id=manual_group_id, user_id=user_id, role_id="3",
                                                 c_user_id=curr_user_id)
                new_group_user_list.append(new_group_user)
            if new_group_user_list:
                new_session.add_all(new_group_user_list)
                new_session.commit()
    except Exception as e:
        logger.error(f"保存人工阅卷小组成员失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg="保存人工阅卷小组成员失败")
    logger.info("保存人工阅卷小组成员成功")
    return BaseResponse(msg=f"保存人工阅卷小组成员成功")


@manual_group_router.post(path="/get_manual_group", response_model=BaseResponse, summary="获取人工阅卷小组列表")
async def get_manual_group(query: GetManualGroupReq, user: Any = Depends(get_current_user),
                           new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取人工阅卷小组列表")
    (current_page, page_size, project_id, subject_id, manual_group_name, manual_process_id, c_name,
     is_create_manual_task) = query.model_dump().values()

    # 删除没有组员的小组
    group_info = new_session.query(ManualReadPaperGroup.manual_group_id) \
        .outerjoin(ManualGroupUser, ManualGroupUser.manual_group_id == ManualReadPaperGroup.manual_group_id) \
        .filter(ManualGroupUser.user_id.is_(None)) \
        .all()
    manual_group_id_list = [i[0] for i in group_info] if group_info else []
    if manual_group_id_list:
        new_session.query(ManualReadPaperGroup).filter(ManualReadPaperGroup.manual_group_id.in_(manual_group_id_list)).delete()
        new_session.commit()

    project_condition, subject_condition = get_user_data_flag(new_session, user)

    limit = current_page - 1
    offset = limit * page_size
    user_info_alias = aliased(UserInfo, name="user_info_alias")

    select_fields = [ManualReadPaperGroup.manual_group_id, ManualReadPaperGroup.manual_group_name,
                     ManualReadPaperGroup.manual_project_id, Project.project_name,
                     ManualReadPaperGroup.manual_subject_id, Subject.subject_name,
                     ManualReadPaperGroup.manual_process_id, WorkFlowMainProcess.process_name,
                     ManualReadPaperGroup.remark, ManualReadPaperGroup.created_time,
                     ManualReadPaperGroup.updated_time, user_info_alias.username,
                     ManualReadPaperGroup.expert_ai_num, ManualReadPaperGroup.arbitrator_ai_num,
                     ManualReadPaperGroup.quality_ai_num,
                     func.group_concat(ManualGroupUser.user_id),
                     func.group_concat(ManualGroupUser.role_id),
                     func.group_concat(UserInfo.name), user_info_alias.name]

    if is_create_manual_task:
        if not project_id or not subject_id or not manual_process_id:
            return BaseResponse(code=response_utils.params_error, msg="请选择项目、科目和阅卷模式")
        if manual_group_name:
            group_name_condition = ManualReadPaperGroup.manual_group_name.ilike(f"%{manual_group_name}%")
        else:
            group_name_condition = True

        total_condition = and_(ManualReadPaperGroup.manual_project_id == project_id,
                               ManualReadPaperGroup.manual_subject_id == subject_id,
                               ManualReadPaperGroup.manual_process_id == manual_process_id, group_name_condition,
                               project_condition, subject_condition)

        manual_group_stmt = select(*select_fields) \
            .outerjoin(ManualGroupUser, ManualGroupUser.manual_group_id == ManualReadPaperGroup.manual_group_id) \
            .outerjoin(UserInfo, UserInfo.user_id == ManualGroupUser.user_id) \
            .join(WorkFlowMainProcess, WorkFlowMainProcess.process_id == ManualReadPaperGroup.manual_process_id) \
            .join(Project, ManualReadPaperGroup.manual_project_id == Project.project_id) \
            .join(Subject, ManualReadPaperGroup.manual_subject_id == Subject.subject_id) \
            .join(user_info_alias, user_info_alias.user_id == ManualReadPaperGroup.c_user_id) \
            .where(total_condition) \
            .group_by(ManualReadPaperGroup.manual_group_id) \
            .order_by(ManualReadPaperGroup.created_time.desc(), ManualReadPaperGroup.manual_group_id.desc()) \
            .limit(page_size).offset(offset)

        total = new_session.query(ManualReadPaperGroup.manual_group_id) \
            .join(WorkFlowMainProcess, WorkFlowMainProcess.process_id == ManualReadPaperGroup.manual_process_id) \
            .join(Project, ManualReadPaperGroup.manual_project_id == Project.project_id) \
            .join(Subject, ManualReadPaperGroup.manual_subject_id == Subject.subject_id) \
            .where(total_condition).count()
    else:
        # 拼凑查询条件
        condition = manual_group_query_condition(project_id, subject_id, manual_group_name)

        if c_name:
            user_condition = user_info_alias.name.ilike(f"%{c_name}%")
        else:
            user_condition = True

        manual_group_stmt = select(*select_fields) \
            .outerjoin(ManualGroupUser, ManualGroupUser.manual_group_id == ManualReadPaperGroup.manual_group_id) \
            .outerjoin(UserInfo, UserInfo.user_id == ManualGroupUser.user_id) \
            .join(WorkFlowMainProcess, WorkFlowMainProcess.process_id == ManualReadPaperGroup.manual_process_id) \
            .join(Project, ManualReadPaperGroup.manual_project_id == Project.project_id) \
            .join(Subject, ManualReadPaperGroup.manual_subject_id == Subject.subject_id) \
            .join(user_info_alias, user_info_alias.user_id == ManualReadPaperGroup.c_user_id) \
            .where(and_(condition, user_condition, project_condition, subject_condition)) \
            .group_by(ManualReadPaperGroup.manual_group_id) \
            .order_by(ManualReadPaperGroup.created_time.desc(), ManualReadPaperGroup.manual_group_id.desc()) \
            .limit(page_size).offset(offset)
        total = new_session.query(ManualReadPaperGroup.manual_group_id) \
            .join(Project, ManualReadPaperGroup.manual_project_id == Project.project_id) \
            .join(Subject, ManualReadPaperGroup.manual_subject_id == Subject.subject_id) \
            .join(user_info_alias, user_info_alias.user_id == ManualReadPaperGroup.c_user_id) \
            .where(and_(condition, user_condition, project_condition, subject_condition)).count()

    manual_group_data = []
    try:
        result = new_session.execute(manual_group_stmt)
        for row in result:
            group_user_id = row[15] and [i for i in row[15].split(",")]
            role_id = row[16] and [i for i in row[16].split(",")]
            group_user_name = row[17] and row[17].split(",")
            expert, arbitrator, quality, expert_name, arbitrator_name, quality_name = (
                get_every_manual_role_user(group_user_id, role_id, group_user_name))
            process_id = row[6]
            expert_num, arbitrator_num, quality_num = get_every_manual_role_num(process_id)

            manual_group_item = {
                "manual_group_id": row[0],
                "manual_group_name": row[1],
                "project_id": row[2],
                "project_name": row[3],
                "subject_id": row[4],
                "subject_name": row[5],
                "process_id": process_id,
                "process_name": row[7],
                "remark": row[8],
                "created_time": row[9] and str(row[9]).replace("T", " "),
                "updated_time": row[10] and str(row[10]).replace("T", " "),
                "c_user_name": row[11],
                "expert_ai_num": row[12],
                "arbitrator_ai_name": row[13],
                "quality_ai_num": row[14],
                "expert": expert,
                "expert_name": expert_name,
                "expert_num": expert_num,
                "arbitrator": arbitrator,
                "arbitrator_name": arbitrator_name,
                "arbitrator_num": arbitrator_num,
                "quality": quality,
                "quality_name": quality_name,
                "quality_num": quality_num,
                "c_name": row[18]
            }
            manual_group_data.append(manual_group_item)
    except Exception as e:
        logger.error(f"获取人工阅卷小组列表失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg="获取人工阅卷小组列表失败")

    logger.info("获取人工阅卷小组列表成功")
    if page_size == -1:
        total = len(manual_group_data)
    data = {
        "data": manual_group_data,
        "total": total
    }
    return BaseResponse(msg="获取人工阅卷小组列表成功", data=data)


@manual_group_router.post(path="/update_manual_group", response_model=BaseResponse, summary="编辑人工阅卷小组")
async def update_manual_group(query: UpdateManualGroupReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 编辑人工阅卷小组")
    try:
        (manual_group_id, manual_group_name, project_id, subject_id, remark, expert, arbitrator, quality,
         is_use_ai) = query.model_dump().values()

        group_data = new_session.query(ManualReadPaperGroup.manual_group_id, ManualReadPaperGroup.lock_state).filter(
            ManualReadPaperGroup.manual_group_id == manual_group_id).first()
        if not group_data:
            return BaseResponse(code=response_utils.no_field, msg="没有该人工阅卷小组")
        if group_data[1] == 2:
            return BaseResponse(code=response_utils.permission_deny, msg="该人工阅卷小组已被使用，不允许编辑")

        new_session.query(ManualReadPaperGroup).filter(ManualReadPaperGroup.manual_group_id == manual_group_id).update({
            ManualReadPaperGroup.manual_group_name: manual_group_name,
            ManualReadPaperGroup.manual_project_id: project_id,
            ManualReadPaperGroup.manual_subject_id: subject_id,
            ManualReadPaperGroup.remark: remark,
            ManualReadPaperGroup.u_user_id: user.get("user_id")
        })
        group_user_info_list = []

        ai_user_info = new_session.query(UserInfo.user_id).join(UserRole, UserRole.user_id == UserInfo.user_id) \
            .filter(UserInfo.user_type == 2, UserRole.role_id.in_(["3", "4", "5"])).all()
        ai_user_id = [i[0] for i in ai_user_info]

        used_ai_flag = False
        for e in expert:
            group_user_info_list.append({"user_id": e, "role_id": "3"})
            if e in ai_user_id:
                used_ai_flag = True
        for a in arbitrator:
            group_user_info_list.append({"user_id": a, "role_id": "4"})
            if a in ai_user_id:
                used_ai_flag = True
        for q in quality:
            group_user_info_list.append({"user_id": q, "role_id": "5"})
            if q in ai_user_id:
                used_ai_flag = True

        if is_use_ai and not used_ai_flag:
            return BaseResponse(code=response_utils.params_error, msg="启用了AI请至少选择一个AI助手")

        result, msg = update_group_user_info(group_user_info_list, manual_group_id, user.get("user_id"), new_session)
        new_session.commit()
        if result:
            logger.info(msg)
            return BaseResponse(msg=msg)
        else:
            logger.error(msg)
            return BaseResponse(code=response_utils.server_error, msg=msg)
    except Exception as e:
        new_session.rollback()
        logger.error(f"编辑人工阅卷小组失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f'编辑人工阅卷小组失败')


@manual_group_router.post(path="/delete_manual_group", response_model=BaseResponse, summary="删除人工阅卷小组")
async def delete_manual_group(query: DeleteManualGroupReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 删除人工阅卷小组")
    manual_group_id = query.manual_group_id

    try:
        if isinstance(manual_group_id, list):
            group_data = new_session.query(ManualReadPaperGroup.manual_group_name, ManualReadPaperGroup.lock_state) \
                .filter(ManualReadPaperGroup.manual_group_id.in_(manual_group_id)).all()
            for group in group_data:
                if group[1] == 2:
                    new_session.close()
                    return BaseResponse(code=response_utils.permission_deny,
                                        msg=f"{group[0]} 该人工阅卷小组已被使用，不允许删除")

            new_session.query(ManualReadPaperGroup).filter(
                ManualReadPaperGroup.manual_group_id.in_(manual_group_id)).delete()
        else:
            group_data = new_session.query(ManualReadPaperGroup.manual_group_name, ManualReadPaperGroup.lock_state) \
                .filter(ManualReadPaperGroup.manual_group_id == manual_group_id).first()
            if group_data[1] == 2:
                return BaseResponse(code=response_utils.permission_deny,
                                    msg=f"{group_data[0]} 该人工阅卷小组已被使用，不允许删除")
            new_session.query(ManualReadPaperGroup).filter(
                ManualReadPaperGroup.manual_group_id == manual_group_id).delete()
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除人工阅卷小组信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除人工阅卷小组信息失败")
    logger.info("删除人工阅卷小组信息成功")
    return BaseResponse(msg="删除人工阅卷小组信息成功")
