import concurrent.futures
import time
import traceback

from settings import logger
from sqlalchemy import and_

from apps.base.services import request_api
from apps.models.models import ManualReadTask, ManualDistributeAnswer, ManualMark, ManualMarkHistory
from apps.read_paper.read_paper_services import ai_manual_all_space_mark
from factory_apps import session_depend
from settings import configs
from utils.utils import sum_with_precision


def get_ai_mark_again_data(new_session, m_read_task_id, curr_user_id=None):
    # 获取 AI 评分数据
    distri_answer_data = []
    try:
        if curr_user_id:
            condition = and_(ManualMark.mark_person_id == curr_user_id, ManualMark.can_expert_mark_again == 1,
                             ManualMark.expert_mark_score.isnot(None))
        else:
            condition = ~ManualMark.mark_person_id.isnot(None)
        distri_info = new_session.query(ManualDistributeAnswer.distri_answer_id,
                                        ManualDistributeAnswer.stu_answer_id, ManualDistributeAnswer.stu_answer,
                                        ManualDistributeAnswer.ques_id, ManualDistributeAnswer.stu_secret_num,
                                        ManualMark.manual_mark_id, ManualMark.mark_state, ManualMark.expert_mark_score) \
            .outerjoin(ManualMark, ManualMark.distri_answer_id == ManualDistributeAnswer.distri_answer_id).filter(
            and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id, condition)).all()

        if distri_info:
            for distri_answer_id, stu_answer_id, stu_answer, ques_id, stu_secret_num, manual_mark_id, mark_state, expert_mark_score in distri_info:
                item = {
                    "distri_answer_id": distri_answer_id,
                    "stu_answer_id": stu_answer_id,
                    "ques_id": ques_id,
                    "manual_mark_id": manual_mark_id,
                    "manual_aq_id": None,  # 重评不需要 manual_aq_id
                    "stu_secret_num": stu_secret_num,
                    "stu_answer": stu_answer,
                    "mark_state": mark_state,
                    "expert_mark_score": float(expert_mark_score)
                }
                distri_answer_data.append(item)
        return True, distri_answer_data
    except:
        traceback.print_exc()
        new_session.rollback()
        return False, []


def manual_ai_mark_task(distri_answer_data, m_read_task_id, group_id, user):
    """
    生成人工阅卷 AI 评分需要的数据和参数，并进行 AI 人工阅卷
    """
    futures_list = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=configs.AI_REQ_THREAD_COUNT) as executor:
        for answer_data in distri_answer_data:
            children = answer_data.get("children")
            if children:
                time.sleep(0.3)
                for child in children:
                    # 对单条数据进行评分
                    logger.info(f"对单条数据进行评分 {child}")
                    ques_id = child["ques_id"]
                    futures_list.append(
                        executor.submit(manual_ai_mark_thread, m_read_task_id, ques_id, group_id, child, user))
            else:
                time.sleep(0.3)
                # 对单条数据进行评分
                logger.info(f"对单条数据进行评分 {answer_data}")
                ques_id = answer_data["ques_id"]
                futures_list.append(
                    executor.submit(manual_ai_mark_thread, m_read_task_id, ques_id, group_id, answer_data, user))

        for future in concurrent.futures.as_completed(futures_list):
            try:
                result = future.result()
                logger.info(f"AI 评分任务线程结果：{result}")
            except Exception as e:
                traceback.print_exc()
                logger.error(f"Exception: {e}")


def manual_ai_mark_thread(m_read_task_id, ques_id, group_id, answer_data, user):
    """
    人工阅卷 AI 评分
    """
    distri_answer_id = answer_data["distri_answer_id"]
    stu_answer_id = answer_data["stu_answer_id"]
    manual_ai_data = {
        "distri_answer_id": distri_answer_id,
        "stu_answer_id": stu_answer_id,
        "stu_answer": answer_data["stu_answer"]
    }
    flag, _, _, mark_score, answer_parse, _ = ai_manual_all_space_mark(stu_answer_id, manual_ai_data)
    answer_parse_str = ";".join(answer_parse)

    history_id = None
    mark_state, expert_mark_score = answer_data["mark_state"], answer_data["expert_mark_score"]
    if expert_mark_score is None:
        # 第一次评分
        mark_type = 1
        logger.info("第一次评分")
    elif expert_mark_score is not None and mark_state != 6:
        # 重评
        mark_type = 2
        new_session = next(session_depend())
        history_data = new_session.query(ManualMarkHistory.history_id) \
            .filter(and_(ManualMarkHistory.distri_answer_id == distri_answer_id,
                         ManualMarkHistory.history_type.in_([1, 2, 3]))) \
            .order_by(ManualMarkHistory.created_time.desc()).first()
        history_id = history_data[0] if history_data else None
    elif expert_mark_score is not None and mark_state == 6:
        # 质检返评
        mark_type = 3
    else:
        raise Exception(f"mark_type 非法，mark_state 为 {mark_state}, expert_mark_score 为 {expert_mark_score}")

    data = {
        "m_read_task_id": m_read_task_id,
        "distri_answer_id": distri_answer_id,
        "ques_id": ques_id,
        "expert_mark_score": mark_score,
        "mark_parse": answer_parse_str,
        "manual_mark_id": answer_data["manual_mark_id"],
        "group_id": group_id,
        "mark_type": mark_type,
        "manual_aq_id": answer_data["manual_aq_id"],
        "stu_secret_num": answer_data["stu_secret_num"],
        "history_id": history_id
    }
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/manual_read/manual_mark"
    res, msg = request_api(url, "POST", data, token, "评分")
    if res == 0:
        logger.info("评分失败")
        return False
    logger.info("评分成功")
    return True


def get_ai_manual_data(m_read_task_id, paper_code, ques_code, ques_id, ques_type_code, group_id, project_id, subject_id,
                       user):
    # 获取 AI 阅卷的数据
    req_distri_data = {
        "m_read_task_id": m_read_task_id,
        "paper_code": paper_code,
        "ques_id": ques_id,
        "ques_code": ques_code,
        "ques_type_code": ques_type_code,
        "group_id": group_id,
        "project_id": project_id,
        "subject_id": subject_id,
        "batch_num": -1,
    }
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/manual_read/get_distri_task_detail"
    res, msg = request_api(url, "POST", req_distri_data, token, "AI 阅卷的数据")
    if res == 0:
        logger.info("获取 AI 阅卷的数据失败")
        return False, []
    else:
        distri_answer_data = res["data"]
        return True, distri_answer_data


def manual_ai_arbitrate_thread(m_read_task_id, ques_id, group_id, answer_data, user):
    """
    人工阅卷 AI 仲裁
    """
    distri_answer_id = answer_data["distri_answer_id"]
    stu_answer_id = answer_data["stu_answer_id"]
    manual_ai_data = {
        "distri_answer_id": distri_answer_id,
        "stu_answer_id": stu_answer_id,
        "stu_answer": answer_data["stu_answer"]
    }
    flag, _, _, mark_score, answer_parse, _ = ai_manual_all_space_mark(stu_answer_id, manual_ai_data)

    data = {
        "manual_aq_id": answer_data["manual_aq_id"],
        "m_read_task_id": m_read_task_id,
        "distri_answer_id": distri_answer_id,
        "ques_id": ques_id,
        "manual_mark_id_list": [i["manual_mark_id"] for i in answer_data["expert_data"]],
        "arbitrate_type": 1,
        "arbitrate_mark_score": mark_score,
        "group_id": group_id
    }
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/manual_read/manual_arbitration"
    res, msg = request_api(url, "POST", data, token, "仲裁")
    if res == 0:
        logger.info("仲裁失败")
        return False
    logger.info("仲裁成功")
    return True


def manual_ai_quality_thread(m_read_task_id, group_id, ques_id, answer_data, user):
    """
    人工阅卷 AI 质检
    """
    logger.info("人工阅卷 AI 质检")
    distri_answer_id = answer_data["distri_answer_id"]
    stu_answer_id = answer_data["stu_answer_id"]
    ques_score = answer_data.get("ques_score") if answer_data.get("ques_score") else sum_with_precision(
        answer_data.get("ques_score_list"))
    manual_ai_data = {
        "distri_answer_id": distri_answer_id,
        "stu_answer_id": stu_answer_id,
        "stu_answer": answer_data["stu_answer"]
    }
    flag, _, _, mark_score, answer_parse, _ = ai_manual_all_space_mark(stu_answer_id, manual_ai_data)

    new_session = next(session_depend())
    quality_limit = new_session.query(ManualReadTask.quality_upper_limit, ManualReadTask.quality_lower_limit) \
        .filter(ManualReadTask.m_read_task_id == m_read_task_id).first()
    quality_upper_limit, quality_lower_limit = quality_limit

    quality_upper_limit = quality_upper_limit / 100
    quality_upper_score = ques_score * quality_upper_limit

    quality_lower_limit = quality_lower_limit / 100
    quality_lower_score = ques_score * quality_lower_limit

    expert_data = answer_data["expert_data"]
    arbitrator_data = answer_data["arbitrator_data"]

    quality_result = True
    if arbitrator_data:
        # 有仲裁的情况
        arbitrate_score = arbitrator_data[-1]["mark_score"]
        if arbitrate_score == mark_score:
            quality_result = True
        elif arbitrate_score > mark_score:
            if arbitrate_score - mark_score > quality_upper_score:
                quality_result = False
        else:
            if mark_score - arbitrate_score > quality_lower_score:
                quality_result = False
    else:
        # 没有仲裁的情况
        expert_marked_score = [i["mark_score"] for i in expert_data]
        for expert_score in expert_marked_score:
            if expert_score == mark_score:
                quality_result = True
            elif expert_score > mark_score:
                if expert_score - mark_score > quality_upper_score:
                    quality_result = False
                    break
            else:
                if mark_score - expert_score > quality_lower_score:
                    quality_result = False
                    break
    logger.info(f"AI 质检结果为 {quality_result}")
    if not quality_result:
        answer_parse_str = ";".join(answer_parse)
    else:
        answer_parse_str = None
    data = {
        "manual_aq_id": answer_data["manual_aq_id"],
        "m_read_task_id": m_read_task_id,
        "distri_answer_id": distri_answer_id,
        "ques_id": ques_id,
        "manual_mark_id": answer_data["manual_mark_id"],
        "quality_result": quality_result,
        "quality_suggestion": answer_parse_str,
        "group_id": group_id,
        "quality_type": 1
    }
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/manual_read/manual_quality"
    res, msg = request_api(url, "POST", data, token, "质检")
    if res == 0:
        logger.info("质检失败")
    else:
        quality_result_str = "通过" if quality_result else "不通过"
        logger.info(f"质检成功，质检分数为 {mark_score}，质检结果为 {quality_result_str}")


def ai_arbitrate_task(distri_answer_data, m_read_task_id, ques_id, group_id, user):
    """
    开启 AI 仲裁任务
    """
    # 获取需要仲裁的数据
    futures_list = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=configs.AI_REQ_THREAD_COUNT) as executor:
        for answer_data in distri_answer_data:
            children = answer_data.get("children")
            if children:
                time.sleep(0.3)
                for child in children:
                    # 对单条数据进行仲裁
                    logger.info(f"对单条数据进行仲裁 {child}")
                    futures_list.append(
                        executor.submit(manual_ai_arbitrate_thread, m_read_task_id, ques_id, group_id, child, user))
            else:
                time.sleep(0.3)
                # 对单条数据进行仲裁
                logger.info(f"对单条数据进行仲裁 {answer_data}")
                futures_list.append(
                    executor.submit(manual_ai_arbitrate_thread, m_read_task_id, ques_id, group_id, answer_data, user))
        for future in concurrent.futures.as_completed(futures_list):
            try:
                result = future.result()
                logger.info(f"AI 仲裁任务线程结果：{result}")
            except Exception as e:
                traceback.print_exc()
                logger.error(f"Exception: {e}")


def ai_quality_task(distri_answer_data, m_read_task_id, ques_id, group_id, user):
    """
    开启 AI 质检任务
    """
    futures_list = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=configs.AI_REQ_THREAD_COUNT) as executor:
        for answer_data in distri_answer_data:
            children = answer_data.get("children")
            if children:
                time.sleep(0.3)
                for child in children:
                    # 对单条数据进行质检
                    logger.info(f"对单条数据进行质检 {child}")
                    futures_list.append(
                        executor.submit(manual_ai_quality_thread, m_read_task_id, group_id, ques_id, child, user))
            else:
                time.sleep(0.3)
                # 对单条数据进行质检
                logger.info(f"对单条数据进行质检 {answer_data}")
                futures_list.append(
                    executor.submit(manual_ai_quality_thread, m_read_task_id, group_id, ques_id, answer_data, user))
                # 捕获线程异常
        for future in concurrent.futures.as_completed(futures_list):
            try:
                result = future.result()
                logger.info(f"AI 质检任务线程结果：{result}")
            except Exception as e:
                traceback.print_exc()
                logger.error(f"Exception: {e}")


def trans_aq_state(aq_state):
    # 1 表示待仲裁，2 表示已仲裁，3 表示待质检，4 表示质检完成
    if aq_state == 1:
        aq_state = "待仲裁"
    elif aq_state == 2:
        aq_state = "已仲裁"
    elif aq_state == 3:
        aq_state = "待质检"
    elif aq_state == 4:
        aq_state = "质检完成"
    return aq_state


def trans_aq_result(aq_result):
    # 1 表示质检通过，2 表示质检不通过，待质检返评，3 表示质检不通过
    if aq_result == 1:
        aq_result = "质检通过"
    elif aq_result == 2:
        aq_result = "质检不通过，待质检返评"
    elif aq_result == 3:
        aq_result = "质检不通过，已质检返评"
    return aq_result
