import subprocess
import requests
import time
from typing import Optional, Dict, Any
from datetime import datetime
from apps.service_monitor.schemas import MonitorRecord, MonitorRecordCreate
from settings import logger, BaseConfig


class ServiceMonitor:
    def __init__(self):
        # 配置服务信息
        # 注意：请根据实际情况修改以下配置项：
        # 1. exe_path: 数据统计服务的exe文件完整路径
        # 2. health_check_url: 服务的健康检查接口URL
        # 3. process_name: 服务进程名称
        # 从配置文件中读取ScheduleBackendCfg的端口
        schedule_port = BaseConfig.SCHEDULE_PORT
        self.services_config = {
            "data_statistics_service": {
                "exe_path": "C:/path/to/your/data_statistics_service.exe",  # 请修改为实际exe路径
                "health_check_url": f"http://localhost:{schedule_port}/health",  # 从配置文件动态读取端口
                "process_name": "定时任务V1.0.exe"  # 请修改为实际进程名称
            }
        }

        # 用于记录自动重启尝试次数
        self.restart_attempts: Dict[str, int] = {}

        # 存储最新服务状态
        self.latest_status: Dict[str, MonitorRecord] = {}

    def check_service_status(self, service_name: str) -> MonitorRecord:
        """
        检查服务状态
        """
        try:
            config = self.services_config.get(service_name)
            if not config:
                raise ValueError(f"未知的服务名称: {service_name}")

            # 检查进程是否存在
            process_exists = self._is_process_running(config["process_name"])


            # 如果进程存在，进行健康检查
            if process_exists:
                status = "running"
                error_message = None
                try:
                    # 发送健康检查请求
                    response = requests.get(config["health_check_url"], timeout=5)
                    if response.status_code != 200:
                        status = "error"
                        error_message = f"健康检查失败，状态码: {response.status_code}"
                except requests.exceptions.RequestException as e:
                    status = "error"
                    error_message = f"健康检查请求失败: {str(e)}"
            else:
                status = "stopped"
                error_message = "服务进程未运行"

            # 如果服务状态为 error，尝试自动重启
            if status == "error" or status == "stopped":
                logger.info(f"服务 {service_name} 状态异常，尝试自动重启...")
                restart_result = self._auto_restart_service(service_name, config)
                if restart_result["success"]:
                    status = "running"
                    error_message = None
                else:
                    # 如果自动重启失败，记录需要手动重启的信息
                    status = "error"
                    error_message = restart_result["message"]

            # 创建监控记录
            record = MonitorRecord(
                id=1,  # 简化处理，因为不使用持久化存储
                service_name=service_name,
                status=status,
                last_check_time=datetime.now(),
                error_message=error_message
            )

            # 存储最新的服务状态
            self.latest_status[service_name] = record

            logger.info(f"服务 {service_name} 状态检查完成: {status}")
            return record

        except Exception as e:
            logger.error(f"检查服务 {service_name} 状态时发生错误: {str(e)}")
            record = MonitorRecord(
                id=1,
                service_name=service_name,
                status="error",
                last_check_time=datetime.now(),
                error_message=str(e)
            )
            return record

    def _auto_restart_service(self, service_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        自动重启服务，最多尝试3次
        """

        default_service_name = config.get("process_name")
        max_attempts = 3
        attempt = self.restart_attempts.get(default_service_name,0)

        # 如果已经尝试了3次，不再自动重启
        if attempt >= max_attempts:
            logger.warning(f"服务 {default_service_name} 已经尝试重启 {max_attempts} 次，不再自动重启，请手动重启")
            return {
                "success": False,
                "message": f"服务 {default_service_name} 自动重启失败，已尝试 {max_attempts} 次，请手动重启"
            }

        try:
            # 增加尝试次数
            self.restart_attempts[default_service_name] = attempt + 1

            # 停止服务（如果正在运行）
            self._stop_service(default_service_name)

            # 等待一段时间让服务完全停止
            time.sleep(1)

            # 启动服务
            self._start_service(config["exe_path"])

            # 等待一段时间让服务启动
            time.sleep(2)

            # 检查服务是否成功启动
            if self._is_process_running(config["process_name"]):
                logger.info(f"服务 {default_service_name} 重启成功")
                # 重置尝试次数
                self.restart_attempts[default_service_name] = 0
                return {"success": True, "message": "服务重启成功"}
            else:
                logger.error(f"服务 {default_service_name} 重启失败：进程未启动")
                return {"success": False, "message": "服务重启失败：进程未启动"}

        except Exception as e:
            logger.error(f"重启服务 {default_service_name} 时发生错误: {str(e)}")
            return {"success": False, "message": f"重启服务时发生错误: {str(e)}"}

    def _is_process_running(self, process_name: str) -> bool:
        """
        检查进程是否正在运行（使用cmd命令）
        """
        try:
            # 使用tasklist命令检查进程是否存在
            import subprocess
            result = subprocess.run(['tasklist', '/FI',
                                     f'IMAGENAME eq {process_name}'],
                                    capture_output=True, text=True, shell=True)
            # 如果进程存在，输出中会包含该进程名
            return process_name in result.stdout
        except Exception as e:
            logger.error(
                f"检查进程 {process_name} 时发生错误: {str(e)}")
            return False

    def _stop_service(self, default_service_name:str) -> bool:
        """
        停止服务进程（使用cmd命令）
        """
        try:
            # 使用taskkill命令终止进程
            import subprocess
            result = subprocess.run(
                ['taskkill', '/F', '/IM', default_service_name],
                capture_output=True, text=True, shell=True)
            print("result", result)
            # taskkill命令执行成功返回0，即使进程不存在也返回0
            # 我们需要检查输出来判断是否真的终止了进程
            # 如果输出包含"成功"或"已终止"，则表示成功
            if "成功" in result.stdout or "已终止" in result.stdout or result.returncode == 0:
                return True
            else:
                logger.warning(f"停止进程 {default_service_name} 时未成功: {result.stdout}")
                return False
        except Exception as e:
            logger.error(
                f"停止进程 {default_service_name} 时发生错误: {str(e)}")
            return False

    def _start_service(self, exe_path: str) -> bool:
        """
        启动服务
        """
        try:
            subprocess.Popen(exe_path)
            return True
        except Exception as e:
            logger.error(f"启动服务 {exe_path} 时发生错误: {str(e)}")
            return False

    def stop_all_services(self):
        """
        停止所有监控的服务
        """
        logger.info("开始停止所有监控的服务...")
        for service_name, config in self.services_config.items():
            try:
                logger.info(f"正在停止服务: {service_name}")
                self._stop_service(config["process_name"])
                logger.info(f"服务 {service_name} 已停止")
            except Exception as e:
                logger.error(f"停止服务 {service_name} 时发生错误: {str(e)}")
        
        logger.info("所有监控服务已停止完成")


# 创建全局服务实例
service_monitor = ServiceMonitor()
