import base64
import datetime

from PIL import Image
from io import BytesIO
from decimal import Decimal, ROUND_HALF_UP


def create_file_base64(file_path):
    with open(file_path, 'rb') as f1:
        base64_str = base64.b64encode(f1.read())  # base64类型
        src = base64_str.decode('utf-8')  # str
    return src


def base64_to_image(file_base64):
    if "," in file_base64:
        file_base64 = file_base64.split(",")[1]
    img_data = base64.b64decode(file_base64)
    # 转化为图像对象
    image = Image.open(BytesIO(img_data))
    # 获取图片格式（用于文件后缀名）
    image_format = image.format.lower()
    return image, image_format


def create_timestamp_id():
    current_time = datetime.datetime.now()
    micro_time = int(current_time.timestamp() * 1000000)
    # print(micro_time)
    return micro_time


def to_valid_format(objects):
    if not objects:  # 如果对象列表为空，返回空列表
        return []

    if not isinstance(objects, list):  # 如果传入的不是列表，转换为单个对象的列表
        objects_list = [objects]
    else:
        objects_list = objects

    result = []
    for obj in objects_list:
        # 使用对象的 as_dict() 方法（如果定义了的话），否则使用字典推导式
        if hasattr(obj, 'as_dict'):
            result.append(obj.as_dict())
        else:
            result.append({column.name: getattr(obj, column.name) for column in obj.__table__.columns})

    if len(result) == 1:
        return result[0]

    return result


def count_decimal_places(number, save_zero=True):
    """
    检查浮点数有几位小数
    """
    # 将浮点数转换为字符串
    number_str = str(number)

    # 检查是否存在小数点
    if '.' in number_str:
        if save_zero:
            # 去除小数点后的尾随零
            number_str = number_str.rstrip('0')
        # 返回小数点后的位数
        return len(number_str.split('.')[1])
    else:
        return 0


def round_half_up(number, decimal_places):
    """
    round 使用的是银行家舍入规则，需要自定义四舍五入函数
    """
    # 定义舍入精度，如 '0.01' 表示保留两位小数
    rounding_context = Decimal(f"0.{'0' * decimal_places}")
    # 使用 quantize 方法进行四舍五入
    return float(Decimal(str(number)).quantize(rounding_context, rounding=ROUND_HALF_UP))


def sum_with_precision(number_list):
    """
    高精度数字列表求和，跳过 None 元素
    """
    # 过滤掉 None，保留其他可转换为 Decimal 的值
    filtered = (i for i in number_list if i is not None)
    return float(sum(Decimal(str(i)) for i in filtered))


def find_duplicates_ordered(lst):
    """
    获取列表里的重复元素
    """
    seen = set()
    duplicates = []
    for item in lst:
        if item in seen and item not in duplicates:
            duplicates.append(item)
        else:
            seen.add(item)
    return duplicates


if __name__ == '__main__':
    # 示例用法
    # user_instances = session.query(User).filter(User.id.in_(["1", "2", "3"])).all()  # 查询多条数据
    # user_dicts = to_valid_format(user_instances)
    # print(user_dicts)
    #
    # single_user_instance = session.query(User).filter(User.id == "1").first()  # 查询单条数据
    # single_user_dict = to_valid_format(single_user_instance)
    # print(single_user_dict)

    # print(sum_with_precision([2.3, 5.3, 8.6]))

    answer_percentage = round_half_up(3 / 51516548 * 100, 7)
    print(answer_percentage)
    # 使用格式化字符串输出，避免科学计数法
    print(f"{answer_percentage:.7f}")  # 输出格式为 0.0002514
