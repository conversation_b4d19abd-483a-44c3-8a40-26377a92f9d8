def assign_stu_answer_to_reviewers(all_reviewer_list: list, p_reviewer_num: int, stu_answer_list: list):
    """
        根据考生数据，将每个考生分配给 p_reviewer_num 个负载最小的评卷员。

        参数:
        all_reviewer_list (list): 所有评卷员 ID 的列表。
        p_reviewer_num (int): 每个考生需要被多少个评卷员评分。
        stu_answer_list (list of dicts): 每个元素是一个字典，键是考生密号，值是 answer_id 列表。

        返回:
        dict: {评卷员ID: [(考生密号, answer_id), ...], ...}
    """
    n = len(all_reviewer_list)

    # 初始化负载和分配结果
    reviewer_load = {r: 0 for r in all_reviewer_list}
    assignment = {r: [] for r in all_reviewer_list}

    # 处理每个批次的数据
    for batch in stu_answer_list:
        for student_id, answer_ids in batch.items():
            # 根据当前负载排序，选择负载最小的 p_reviewer_num 个评卷员
            sorted_reviewers = sorted(all_reviewer_list, key=lambda r: reviewer_load[r])
            selected = sorted_reviewers[:p_reviewer_num]

            # 分配任务
            for reviewer in selected:
                # 更新负载
                reviewer_load[reviewer] += 1
                for answer_id in answer_ids:
                    assignment[reviewer].append((student_id, answer_id))

    return assignment


if __name__ == '__main__':
    all_reviewers = ['R1', 'R2', 'R3']
    x = 2
    students = [
        {'stu1': ['A1', 'A2'], 'stu2': ['A3', 'A4']},
        {'stu3': ['A5', 'A6'], 'stu4': ['A7', 'A8']},
        {'stu5': ['A9', 'A10'], 'stu6': ['A11', 'A12']},
        {'stu7': ['A13', 'A14'], 'stu8': ['A15', 'A16']}
    ]

    result = assign_stu_answer_to_reviewers(all_reviewers, x, students)

    # 打印结果
    for reviewer, tasks in result.items():
        print(f"Reviewer {reviewer} has {len(tasks)} tasks:")
        for student, answers in tasks:
            print(f"  Student {student} → {answers}")
