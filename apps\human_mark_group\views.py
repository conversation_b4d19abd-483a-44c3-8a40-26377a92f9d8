from typing import Any

from fastapi import APIRouter, Depends
from sqlalchemy import func, and_
from sqlalchemy.orm import Session

from apps.base.human_global_cache import get_round_group_member
from apps.base.schemas import BaseResponse
from apps.base.services import get_import_db_monitor_info
from apps.human_mark_group.schemas import GetHumanGroupReq, CreateHumanGroupReq, GetParentHumanGroupReq, \
    GetRecommendGroupCodeReq, GetGroupUnionQuesReq, FilterHumanGroupMember, SetHumanGroupMemberReq, GetHumanGroupMemberReq, BaseHumanGroupReq, UpdateHumanGroupReq, IsActiveHumanGroupReq, \
    GetHumanSmallGroupReq
from apps.human_mark_group.services import get_human_group_tree, get_human_group_condition, auto_create_human_group, \
    create_single_human_group, get_human_group_code_name, valid_group_info
from apps.models.models import Subject, PaperDetail, ExamPaper, ExamQuestion, BusinessQuesType, UserIn<PERSON>, User<PERSON><PERSON>, UserDataPermission
from apps.human_mark_group.models import HumanMarkGroup, HumanGroupMember
from apps.ques_type.services import get_all_ques_type
from apps.users.services import get_current_user
from factory_apps import session_depend
from helper import response_utils
from settings import logger, configs

human_group_router = APIRouter()


@human_group_router.post(path="/get_human_group", response_model=BaseResponse, summary="获取人工阅卷小组")
async def get_ques_detail(query: GetHumanGroupReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取人工阅卷小组")

    monitor_id, monitor_type = get_import_db_monitor_info(new_session)
    if monitor_id:
        # ques_used_index = 3 表示：第4位-表示要生成试题定标数据；值等于3 表示处理完毕
        ques_used_index, index = 1, 4
        if monitor_type[ques_used_index] == "3" and monitor_type[index] == "1":
            logger.info("自动创建科目组、题组和小组")
            result, msg = auto_create_human_group(monitor_id, monitor_type, index)
            if result:
                logger.info("自动创建科目组、题组和小组成功")
            else:
                logger.error(msg)

    current_page, page_size, project_id, subject_id, round_id, task_id = query.model_dump().values()

    limit = current_page - 1
    offset = limit * page_size
    condition = get_human_group_condition(project_id, subject_id)

    total, human_group_list = get_human_group_tree(new_session, page_size, limit, offset, condition)

    data = {
        "total": total,
        "data": human_group_list
    }
    return BaseResponse(data=data, msg="获取人工阅卷小组成功")


@human_group_router.post(path="/get_parent_human_group", response_model=BaseResponse, summary="获取父级小组编号和名称")
async def get_parent_human_group(query: GetParentHumanGroupReq, user: Any = Depends(get_current_user),
                                 new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取父级小组编号和名称")
    group_level = query.group_level

    parent_group_data = []

    parent_group = new_session.query(HumanMarkGroup.group_id, HumanMarkGroup.group_code, HumanMarkGroup.group_name, Subject.exam_mode) \
        .join(Subject, Subject.subject_id == HumanMarkGroup.subject_id) \
        .filter(HumanMarkGroup.group_level == (group_level - 1)).all()
    if parent_group:
        for group_id, group_code, group_name, exam_mode in parent_group:
            item = {
                "group_id": group_id,
                "group_name": group_name,
                "group_code": group_code,
                "exam_mode": exam_mode
            }
            parent_group_data.append(item)

    data = {
        "data": parent_group_data
    }
    return BaseResponse(data=data, msg="获取父级小组编号和名称成功")


@human_group_router.post(path="/get_recommend_group_code", response_model=BaseResponse, summary="获取推荐的小组编号")
async def get_recommend_group_code(query: GetRecommendGroupCodeReq, user: Any = Depends(get_current_user),
                                   new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取推荐的小组编号")
    parent_group_id, parent_group_code = query.model_dump().values()
    # 获取推荐的小组编号
    max_group_code = new_session.query(func.max(HumanMarkGroup.group_code)).filter(
        HumanMarkGroup.parent_group_id == parent_group_id).scalar()
    if max_group_code:
        recommend_group_code = max_group_code[len(parent_group_code):]
        recommend_group_code = str(int(recommend_group_code) + 1).rjust(len(recommend_group_code), "0")
    else:
        recommend_group_code = "001"
    data = {
        "recommend_group_code": recommend_group_code
    }
    return BaseResponse(data=data, msg="获取推荐的小组编号成功")


@human_group_router.post(path="/get_group_union_ques", response_model=BaseResponse, summary="获取父级小组关联的试题")
async def get_group_union_ques(query: GetGroupUnionQuesReq, user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取父级小组编号和名称")
    parent_group_id, is_paper = query.model_dump().values()
    pro_sub_info = new_session.query(HumanMarkGroup.project_id, HumanMarkGroup.subject_id).filter(
        HumanMarkGroup.group_id == parent_group_id).first()
    if not pro_sub_info:
        return BaseResponse(code=response_utils.params_error, msg="父小组参数错误，无法获取关联试题")
    project_id, subject_id = pro_sub_info

    all_ques_type = get_all_ques_type(new_session)

    if is_paper:
        ques_order_info = new_session.query(ExamPaper.paper_id, ExamPaper.paper_name,
                                            func.group_concat(PaperDetail.ques_order),
                                            func.group_concat(PaperDetail.ques_code)) \
            .join(PaperDetail, PaperDetail.paper_id == ExamPaper.paper_id) \
            .join(ExamQuestion, ExamQuestion.ques_id == PaperDetail.ques_id) \
            .filter(and_(ExamPaper.project_id == project_id, ExamPaper.subject_id == subject_id,
                         ExamQuestion.ques_type_code.in_(all_ques_type[2]), PaperDetail.parent_ques_id.is_(None))) \
            .order_by(PaperDetail.ques_order).all()

        if not ques_order_info:
            return BaseResponse(code=response_utils.params_error, msg="父小组参数错误，无法获取关联试题")

        paper_ques_list = []
        for paper_id, paper_name, ques_order_str, ques_code_str in ques_order_info:
            item = {
                "paper_id": paper_id,
                "paper_name": paper_name,
                "ques_order_list": ques_order_str.split(","),
                "ques_code_list": ques_code_str.split(",")
            }
            paper_ques_list.append(item)
        data = {"data": paper_ques_list}
    else:
        ques_code_info = new_session.query(ExamQuestion.ques_code) \
            .join(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
            .filter(and_(BusinessQuesType.project_id == project_id, BusinessQuesType.subject_id == subject_id,
                         ExamQuestion.ques_type_code.in_(all_ques_type[2]), ExamQuestion.parent_ques_id.is_(None))) \
            .order_by(ExamQuestion.ques_code).all()

        ques_code_list = [i[0] for i in ques_code_info] if ques_code_info else []
        data = {"ques_code_list": ques_code_list}

    return BaseResponse(data=data, msg="获取父级小组关联的试题成功")


@human_group_router.post(path="/create_human_group", response_model=BaseResponse, summary="创建人工阅卷小组")
async def create_human_group(query: CreateHumanGroupReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建人工阅卷小组")
    curr_user_id = user["user_id"]
    project_id, subject_id, group_level, group_code, group_name, parent_group_id, paper_id, ques_code, ques_order = query.model_dump().values()

    human_group_code_name = get_human_group_code_name(new_session)
    result, msg, group_id = create_single_human_group(new_session, human_group_code_name, project_id, subject_id,
                                                      group_level, group_code, group_name, parent_group_id, paper_id,
                                                      ques_code, ques_order, curr_user_id)
    if not result:
        return BaseResponse(code=response_utils.server_error, msg=msg)

    data = {"group_id": group_id}
    return BaseResponse(msg="创建人工阅卷小组成功", data=data)


@human_group_router.post(path="/filter_human_group_member", response_model=BaseResponse, summary="过滤阅卷小组可选择的组员")
async def filter_human_group_member(query: FilterHumanGroupMember, user: Any = Depends(get_current_user),
                                    new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 过滤阅卷小组可选择的组员")
    parent_group_id, group_level, team_leader = query.model_dump().values()

    if group_level == 1:
        role_id = "7"
        condition = and_(UserInfo.system_user_type == 2, UserRole.role_id == role_id)
    else:
        pro_sub_info = new_session.query(HumanMarkGroup.project_id, HumanMarkGroup.subject_id).filter(
            HumanMarkGroup.group_id == parent_group_id).first()
        if not pro_sub_info:
            return BaseResponse(code=response_utils.params_error, msg="父小组参数错误，无法获取关联试题")
        project_id, subject_id = pro_sub_info

        if group_level == 2:
            role_id = "5"
        else:
            role_id = "4" if team_leader else "3"

        condition = and_(UserInfo.system_user_type == 2, UserRole.role_id == role_id,
                         UserDataPermission.project_id == project_id, UserDataPermission.subject_id == subject_id)

    user_info = new_session.query(UserInfo.user_id, UserInfo.username, UserInfo.name) \
        .join(UserRole, UserRole.user_id == UserInfo.user_id) \
        .join(UserDataPermission, UserDataPermission.user_id == UserInfo.user_id) \
        .group_by(UserInfo.user_id, UserInfo.name) \
        .filter(condition).all()
    if not user_info:
        user_list = []
    else:
        member_info = new_session.query(HumanGroupMember.user_id).all()
        member_id_list = [i[0] for i in member_info] if member_info else []
        user_list = []
        for user_id, username, name in user_info:
            if user_id in member_id_list:
                has_group = True
            else:
                has_group = False
            item = {"user_id": user_id, "has_group": has_group, "username": username, "name": name}
            user_list.append(item)
    data = {"data": user_list}
    return BaseResponse(data=data, msg="过滤阅卷小组可选择的组员成功")


@human_group_router.post(path="/set_human_group_member", response_model=BaseResponse, summary="设置人工阅卷小组组员")
async def set_human_group_member(query: SetHumanGroupMemberReq, user: Any = Depends(get_current_user),
                                 new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 设置人工阅卷小组组员")
    group_id_list, user_id_list, leader_user_id_list = query.model_dump().values()
    member_list = []
    new_session.query(HumanGroupMember).filter(HumanGroupMember.group_id.in_(group_id_list)).delete()
    for user_id in user_id_list:
        for group_id in group_id_list:
            member = HumanGroupMember(member_id=configs.snow_worker.get_id(), group_id=group_id, user_id=user_id, member_role=2)
            member_list.append(member)

    for user_id in leader_user_id_list:
        for group_id in group_id_list:
            member = HumanGroupMember(member_id=configs.snow_worker.get_id(), group_id=group_id, user_id=user_id, member_role=1)
            member_list.append(member)

    if member_list:
        new_session.add_all(member_list)
        new_session.commit()
    return BaseResponse(msg="设置人工阅卷小组组员成功")


@human_group_router.post(path="/get_human_group_member", response_model=BaseResponse, summary="获取人工阅卷小组组员")
async def get_human_group_member(query: GetHumanGroupMemberReq, user: Any = Depends(get_current_user),
                                 new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取人工阅卷小组组员")
    group_id = query.group_id

    data = {
        "user_id_list": [],
        "leader_user_list": []
    }

    member_user_info = new_session.query(HumanGroupMember.user_id, HumanGroupMember.member_role, UserInfo.name, UserInfo.username) \
        .join(UserInfo, UserInfo.user_id == HumanGroupMember.user_id) \
        .filter(HumanGroupMember.group_id == group_id).all()

    if not member_user_info:
        return BaseResponse(data=data, msg="获取人工阅卷小组组员成功")

    member_info = new_session.query(HumanGroupMember.user_id).all()
    member_id_list = [i[0] for i in member_info] if member_info else []

    for user_id, member_role, name, username in member_user_info:
        item = {
            "user_id": user_id,
            "name": name,
            "username": username,
            "has_group": True if user_id in member_id_list else True
        }
        if member_role == 1:
            data["leader_user_list"].append(item)
        else:
            data["user_id_list"].append(item)

    return BaseResponse(data=data, msg="获取人工阅卷小组组员成功")


@human_group_router.post(path="/delete_human_group", response_model=BaseResponse, summary="删除人工阅卷小组")
async def delete_human_group(query: BaseHumanGroupReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 删除人工阅卷小组")
    group_id = query.group_id
    new_session.query(HumanMarkGroup).filter(HumanMarkGroup.group_id == group_id).delete()
    new_session.query(HumanMarkGroup).filter(HumanMarkGroup.parent_group_id == group_id).delete()
    children_group_info = new_session.query(HumanGroupMember.group_id).filter(HumanMarkGroup.parent_group_id == group_id).all()
    new_session.query(HumanGroupMember).filter(HumanGroupMember.group_id == group_id).delete()

    children_group_id_list = [i[0] for i in children_group_info] if children_group_info else []

    if children_group_id_list:
        new_session.query(HumanGroupMember).filter(HumanGroupMember.group_id.in_(children_group_id_list)).delete()
    new_session.commit()
    return BaseResponse(msg="删除人工阅卷小组成功")


@human_group_router.post(path="/update_human_group", response_model=BaseResponse, summary="编辑人工阅卷小组")
async def update_human_group(query: UpdateHumanGroupReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 编辑人工阅卷小组")
    curr_user_id = user["user_id"]
    project_id, subject_id, group_level, group_id, group_code, group_name, parent_group_id, paper_id, ques_code, ques_order, user_id_list, leader_user_id_list = query.model_dump().values()
    human_group_code_name = get_human_group_code_name(new_session)
    # 编辑小组信息
    result, msg = valid_group_info(group_level, group_code, project_id, subject_id, parent_group_id, group_name, human_group_code_name, True)
    if not result:
        logger.error(msg)
        return BaseResponse(code=response_utils.server_error, msg=msg)

    if group_level != 1 and (not project_id or not subject_id):
        project_id, subject_id = new_session.query(HumanMarkGroup.project_id, HumanMarkGroup.subject_id).filter(HumanMarkGroup.group_id == parent_group_id).first()

    # 构建更新字段的字典，如果ques_code为空则不更新该字段
    update_fields = {
        HumanMarkGroup.project_id: project_id,
        HumanMarkGroup.subject_id: subject_id,
        HumanMarkGroup.group_code: group_code,
        HumanMarkGroup.group_name: group_name,
        HumanMarkGroup.parent_group_id: parent_group_id,
        HumanMarkGroup.paper_id: paper_id,
        HumanMarkGroup.ques_order: ques_order,
        HumanMarkGroup.u_user_id: curr_user_id
    }

    # 只有当ques_code不为空时才更新该字段
    if ques_code is not None:
        update_fields[HumanMarkGroup.ques_code] = ques_code

    new_session.query(HumanMarkGroup).filter(HumanMarkGroup.group_id == group_id).update(update_fields)

    # 编辑小组成员，先删除，再添加
    new_session.query(HumanGroupMember).filter(HumanGroupMember.group_id == group_id).delete()

    # group_user_info = new_session.query(HumanGroupMember.member_role, HumanGroupMember.user_id).filter(HumanGroupMember.group_id == group_id).all()
    #
    # row_user_id_list, raw_leader_user_id_list = [], []
    #
    # if group_user_info:
    #     for member_role, user_id in group_user_info:
    #         if member_role == 1:
    #             raw_leader_user_id_list.append(user_id)
    #         else:
    #             row_user_id_list.append(user_id)

    member_list = []
    for user_id in user_id_list:
        member = HumanGroupMember(member_id=configs.snow_worker.get_id(), group_id=group_id, user_id=user_id, member_role=2)
        member_list.append(member)
    for user_id in leader_user_id_list:
        member = HumanGroupMember(member_id=configs.snow_worker.get_id(), group_id=group_id, user_id=user_id, member_role=1)
        member_list.append(member)

    if member_list:
        new_session.add_all(member_list)

    new_session.commit()

    return BaseResponse(msg="编辑人工阅卷小组成功")


@human_group_router.post(path="/is_active_human_group", response_model=BaseResponse, summary="人工阅卷小组禁用启用")
async def update_human_group(query: IsActiveHumanGroupReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 人工阅卷小组禁用启用")
    curr_user_id = user["user_id"]
    group_id, is_used = query.model_dump().values()
    new_session.query(HumanMarkGroup).filter(HumanMarkGroup.group_id == group_id).update({
        HumanMarkGroup.is_used: is_used,
        HumanMarkGroup.u_user_id: curr_user_id
    })
    new_session.commit()
    return BaseResponse(msg="人工阅卷小组禁用启用成功")


@human_group_router.post(path="/get_small_human_group", response_model=BaseResponse, summary="获取人工评阅阅卷小组")
async def get_small_human_group(query: GetHumanSmallGroupReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取人工评阅阅卷小组")
    project_id, subject_id, ques_code = query.model_dump().values()
    group_info = new_session.query(HumanMarkGroup.group_id, HumanMarkGroup.group_name).filter(
        and_(HumanMarkGroup.project_id == project_id, HumanMarkGroup.subject_id == subject_id,
             HumanMarkGroup.ques_code == ques_code, HumanMarkGroup.group_level == 3)).all()
    group_list = [{
        "group_id": i.group_id,
        "group_name": i.group_name,
    } for i in group_info] if group_info else []
    data = {"data": group_list}
    return BaseResponse(data=data, msg="获取人工评阅阅卷小组成功")
