from typing import Optional, List, Literal
from pydantic import BaseModel, Field
from apps.base.schemas import PaginationModel


class GetMarkAcceptReq(PaginationModel):
    """请求参数：查询作答抄袭列表"""

    subject_id: Optional[str] = Field(None, description="科目ID")
    accept_state: Optional[int] = Field(None, description="验收状态")
    accept_result: Optional[int] = Field(None, description="验收结果")

class EditScoreThresholdReq(BaseModel):
    subject_id: Optional[str] = Field(None, description="科目ID")
