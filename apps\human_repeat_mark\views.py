import datetime
from fastapi import APIRouter, Depends
from apps.base.global_cache import get_redis_ques_info_dict
from settings import logger
from sqlalchemy import select, exists, and_, func, delete, or_
from sqlalchemy.orm import Session
from typing import Any
from pathlib import Path
from fastapi.responses import FileResponse

from apps.grade_manage.models import HumanStudentSubjectGrade
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.human_repeat_mark.schemas import (
    GetStuReq,
    CreateRepeatMark,
    GetRepeatTaskReq,
    RepeatTaskIdReq,
    GetRepeatTaskStuReq,
    RepeatScoreReq,
    GetRepeatRoundDetailReq,
    UpdateScoreThresholdReq,
    GetSampleStuReq,
)
from apps.human_repeat_mark.models import HumanRepeatTaskRound, HumanRepeatTask, HumanRepeatAnswerScore, HumanRepeatRoundDetail
from apps.models.models import TaskExecuteRecord, StuAnswer, ExamStudent, ExamQuest<PERSON>, BusinessQues<PERSON>ype, <PERSON>ject, <PERSON>r<PERSON>n<PERSON>, ExamPaper, PaperDetail, QuesType, Project
from helper import response_utils
from apps.grade_manage.models import HumanStudentSubjectGradeDetail, HumanStudentSubjectGrade
from settings import configs
from apps.human_official_mark.services import get_official_task_score
from factory_apps import session_depend, redis_session
from settings.settings import BaseConfig
from apps.permission.services import get_data_permission_sub_code_list, get_data_permission_sub_id_list
from apps.human_repeat_mark.services import export_technology, manager_technology
import random
import os
import json
import traceback


repeat_router = APIRouter()


# 获取复评任务：HumanRepeatTaskRound第一条数据
@repeat_router.get(path="/get_repeat_task_round", response_model=BaseResponse, summary="获取复评任务轮次")
async def get_repeat_task_round(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取复评任务轮次")
    try:
        # 查询 HumanRepeatTaskRound 表的第一条数据
        first_task_round = new_session.query(HumanRepeatTaskRound).first()
        if first_task_round is None:
            return BaseResponse(msg="暂无复评任务轮次数据", data=None)

        # 将模型对象转换为字典
        task_round_data = {
            "repeat_task_round_id": first_task_round.repeat_task_round_id,
            "task_name": first_task_round.task_name,
            "round_count": first_task_round.round_count,
            # "round_state": first_task_round.round_state,
            "launch_time": first_task_round.launch_time,
            "end_time": first_task_round.end_time,
            "score_type": first_task_round.score_type,
            "select_round_count": first_task_round.select_round_count,
            "select_score_type": first_task_round.select_score_type,
            "c_user_id": first_task_round.c_user_id,
            "u_user_id": first_task_round.u_user_id,
        }
        return BaseResponse(msg="获取复评任务轮次第一条数据成功", data=task_round_data)
    except Exception as e:
        logger.error(f"获取复评任务轮次第一条数据失败，{e}{traceback.format_exc()}")
        return BaseResponse(code=response_utils.server_error, msg="获取复评任务轮次第一条数据失败")


@repeat_router.post(path="/sample_stu_list", response_model=BaseResponse, summary="抽样")
async def sample_stu_list(query: GetSampleStuReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取抽样")
    try:
        EXAM_COUNT_CONFIG_path_str = os.path.join(configs.PROJECT_PATH, "server_static\\settings\\EXAM_COUNT_CONFIG.json")
        EXAM_COUNT_CONFIG = json.load(open(EXAM_COUNT_CONFIG_path_str, encoding="utf-8"))

        task = new_session.query(HumanRepeatTask).filter(HumanRepeatTask.repeat_task_id == query.repeat_task_id).first()
        # 1 根据条件查询考生
        exam_stu_sql = select(HumanStudentSubjectGrade.stu_secret_num).where(HumanStudentSubjectGrade.subject_id == task.subject_id)
        exam_stus = new_session.execute(exam_stu_sql).all()
        stu_id_list = [row.stu_secret_num for row in exam_stus]
        # 根据考生数量选择对应的抽样数量
        exam_count = len(stu_id_list)
        extract_count = 1  # 默认值
        allowed_error_count = 0
        for config in EXAM_COUNT_CONFIG:
            if config["start"] <= exam_count <= config["end"]:
                extract_count = config["extract_count"]
                allowed_error_count = config["allowed_error_count"]
                break

        sample_ids = random.sample(stu_id_list, extract_count)
        scores = (
            new_session.query(HumanStudentSubjectGrade.stu_secret_num, HumanStudentSubjectGrade.score)
            .filter(HumanStudentSubjectGrade.subject_id == task.subject_id, HumanStudentSubjectGrade.stu_secret_num.in_(sample_ids))
            .all()
        )
        unassigned_records = []
        for stu in sample_ids:
            last_score = next(p.score for p in scores if p.stu_secret_num == stu)
            # 创建 HumanRepeatRoundDetail
            detail = HumanRepeatRoundDetail(
                repeat_round_detail_id=configs.snow_worker.get_id(),
                repeat_task_id=task.repeat_task_id,
                stu_secret_num=stu,
                stu_state=1,
                last_score=last_score,
                c_user_id=user["user_id"],
                u_user_id=user["user_id"],
            )
            new_session.add(detail)
            unassigned_records.append((task.repeat_task_id, detail.repeat_round_detail_id))

        task.task_state = 2
        task.example_count = extract_count
        task.repeat_task_count = 0
        task.bias_count = allowed_error_count
        new_session.commit()

        redis = next(redis_session())
        for repeat_task_id, repeat_round_detail_id in unassigned_records:
            redis.lpush(repeat_task_id, repeat_round_detail_id)

        return BaseResponse(msg="获取抽样")
    except Exception as e:
        logger.error(f"更新任务状态失败，{e}{traceback.format_exc()}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="抽样失败")


@repeat_router.post(path="/get_stu_list", response_model=BaseResponse, summary="获取考生列表")
async def get_stu_list(query: GetStuReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考生作答列表")
    ids_list, score_list = query.model_dump().values()
    stu_ids = []

    conditions = [HumanStudentSubjectGrade.subject_id == ids_list[1]]
    # for ids_list in ids_list:
    exam_stus = []
    if len(score_list) == 0:
        exam_stu_sql = select(
            HumanStudentSubjectGrade.stu_secret_num,
            HumanStudentSubjectGrade.student_subject_grade_id,
            HumanStudentSubjectGrade.project_id,
            HumanStudentSubjectGrade.score,
            HumanStudentSubjectGrade.subject_id,
        ).where(and_(*conditions))
        exam_stu = new_session.execute(exam_stu_sql).all()
        exam_stu = [dict(row._mapping) for row in exam_stu]
        exam_stus.extend(exam_stu)
    else:
        for score in score_list:
            # 1 根据条件查询考生
            if score[0]:
                conditions.append(HumanStudentSubjectGrade.score >= score[0])
            if score[1]:
                conditions.append(HumanStudentSubjectGrade.score <= score[1])
            exam_stu_sql = select(
                HumanStudentSubjectGrade.stu_secret_num,
                HumanStudentSubjectGrade.student_subject_grade_id,
                HumanStudentSubjectGrade.project_id,
                HumanStudentSubjectGrade.score,
                HumanStudentSubjectGrade.subject_id,
            ).where(and_(*conditions))
            exam_stu = new_session.execute(exam_stu_sql).all()
            exam_stu = [dict(row._mapping) for row in exam_stu]
            exam_stus.extend(exam_stu)
    stu_ids.append({"condition": ids_list, "exam_stus": exam_stus})

    projects = new_session.query(Project.project_id, Project.project_name).all()
    subjects = new_session.query(Subject.subject_id, Subject.subject_name).all()

    stus_data = []
    for stu_id in stu_ids:
        for id in stu_id["exam_stus"]:
            p_name = next(p for p in projects if p.project_id == stu_id["condition"][0])[1]
            s_name = next(p for p in subjects if p.subject_id == stu_id["condition"][1])[1]
            stus_data.append({"stu_id": id["stu_secret_num"], "project_name": p_name, "subject_name": s_name})  # , "session": stu_id["condition"][2]
    data = {"stus_data": stus_data, "stu_ids": stu_ids}
    return BaseResponse(msg="获取考生作答列表", data=data)


@repeat_router.post(path="/create_repeat_mark", response_model=BaseResponse, summary="创建复评")
async def create_sample(query: CreateRepeatMark, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建复评")
    c_user_id = user.get("user_id")
    stu_list = query.stu_list

    try:
        repeat_task_round = new_session.query(HumanRepeatTaskRound).filter(HumanRepeatTaskRound.task_type == 3).first()
        if repeat_task_round is None:
            task_id = configs.snow_worker.get_id()
            repeat_task_round = HumanRepeatTaskRound(
                repeat_task_round_id=task_id,
                task_name="复评任务轮次",
                round_count=1,  # 复评通常为第一轮
                score_type=0,  # 单轮次
                select_round_count=1,
                task_type=3,
                select_score_type=0,  # 最高分
                c_user_id=c_user_id,
                u_user_id=c_user_id,
            )
            new_session.add(repeat_task_round)
        else:
            repeat_task_round.round_count += 1

        # 创建 HumanRepeatTask
        task_round_id = repeat_task_round.repeat_task_round_id
        stu_task = {}
        for stu in stu_list:
            # stu_ids.append({"condition": ids, "exam_stus": exam_stus})
            condition = stu["condition"]
            exam_stus = stu["exam_stus"]
            repeat_task_round = HumanRepeatTask(
                repeat_task_id=configs.snow_worker.get_id(),
                repeat_task_round_id=task_round_id,
                project_id=condition[0],
                subject_id=condition[1],
                round_count=repeat_task_round.round_count,
                example_count=len(exam_stus),
                task_type=3,
                # exam_session=condition[2],
                repeat_task_count=0,
                c_user_id=c_user_id,
                u_user_id=c_user_id,
            )
            new_session.add(repeat_task_round)

            stu_secret_nums = [p["stu_secret_num"] for p in exam_stus]
            scores = (
                new_session.query(HumanStudentSubjectGrade.stu_secret_num, HumanStudentSubjectGrade.score)
                .filter(HumanStudentSubjectGrade.subject_id == condition[1], HumanStudentSubjectGrade.stu_secret_num.in_(stu_secret_nums))
                .all()
            )
            stu_task[repeat_task_round.repeat_task_id] = []
            for exam_stu in exam_stus:
                last_score = next(p.score for p in scores if p.stu_secret_num == exam_stu["stu_secret_num"])
                # 创建 HumanRepeatRoundDetail
                detail = HumanRepeatRoundDetail(
                    repeat_round_detail_id=configs.snow_worker.get_id(),
                    repeat_task_id=repeat_task_round.repeat_task_id,
                    stu_secret_num=exam_stu["stu_secret_num"],
                    stu_state=1,
                    last_score=last_score,
                    c_user_id=c_user_id,
                    u_user_id=c_user_id,
                )
                new_session.add(detail)
                stu_task[repeat_task_round.repeat_task_id].append(detail.repeat_round_detail_id)
        new_session.commit()
        # 遍历stu_task中的key,输出value
        redis = next(redis_session())
        for key, value in stu_task.items():
            redis.lpush(key, *value)

        return BaseResponse(msg=f"创建复评成功", data={"id": ""})
    except Exception as e:
        logger.error(f"创建复评任务失败，{e}{traceback.format_exc()}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="创建复评任务失败")


# 根据复评任务id删除复评任务t_human_repeat_task
@repeat_router.post(path="/delete_repeat_task", response_model=BaseResponse, summary="根据复评任务id删除复评任务")
async def delete_repeat_task(query: GetSampleStuReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    根据复评任务ID删除复评任务
    :param query: 请求参数，包含repeat_task_id
    :param user: 当前用户
    :param new_session: 数据库会话
    :return: 响应结果
    """
    logger.info(f"{user['username']} 删除复评任务 {query.repeat_task_id}")
    try:
        # 查询任务
        task = new_session.query(HumanRepeatTask).filter(HumanRepeatTask.repeat_task_id == query.repeat_task_id).first()
        if not task:
            return BaseResponse(code=response_utils.not_found, msg="任务不存在")

        # 删除任务关联的详情记录
        new_session.execute(delete(HumanRepeatRoundDetail).where(HumanRepeatRoundDetail.repeat_task_id == query.repeat_task_id))

        # 删除任务记录
        new_session.delete(task)
        new_session.commit()

        return BaseResponse(msg="删除复评任务成功")
    except Exception as e:
        logger.error(f"删除复评任务失败，{e}{traceback.format_exc()}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="删除复评任务失败")


# 获取HumanRepeatTask中数据，可以根据科目id、复评轮次、复评人id筛选列表
@repeat_router.post(path="/get_repeat_task_list", response_model=BaseResponse, summary="获取复评任务列表")
async def get_repeat_task_list(query: GetRepeatTaskReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取复评任务列表")
    try:
        sub_ids = get_data_permission_sub_id_list(new_session, user["user_id"])
        sub_ids = [p for p in sub_ids if p is not None]
        # 构建查询条件
        conditions = [HumanRepeatTask.task_type == query.task_type]

        # 生成验收任务
        create_task(query, user, new_session)

        if query.project_id:
            conditions.append(HumanRepeatTask.project_id == query.project_id)

        if query.task_state:
            conditions.append(HumanRepeatTask.task_state == query.task_state)

        # 根据科目ID筛选
        if query.subject_id:
            if query.subject_id in sub_ids:
                conditions.append(HumanRepeatTask.subject_id == query.subject_id)
            else:
                conditions.append(False)
        else:
            conditions.append(HumanRepeatTask.subject_id.in_(sub_ids))

        if query.is_repeater == 1:
            round_count = new_session.query(UserInfo.round_count).filter(UserInfo.user_id == user["user_id"]).scalar()
            conditions.append(HumanRepeatTask.round_count == round_count)
            conditions.append(HumanRepeatTask.task_state != 1)
        else:
            # 根据复评轮次筛选
            if query.round_count is not None:
                conditions.append(HumanRepeatTask.round_count == query.round_count)
        # 根据复评人ID列表筛选
        if query.repeat_user_id_list:
            # 使用 JSON 查询，查找包含指定用户ID的任务
            # 因为 repeat_user_id_list 是 JSON 数组，需要使用 contains 方法
            from sqlalchemy import or_

            user_conditions = []
            for user_id in query.repeat_user_id_list:
                user_conditions.append(HumanRepeatTask.repeat_user_id_list.contains([user_id]))
            if user_conditions:
                conditions.append(or_(*user_conditions))

        # 查询数据
        query_obj = (
            select(
                HumanRepeatTask.repeat_task_id,
                HumanRepeatTask.repeat_task_round_id,
                HumanRepeatTask.project_id,
                HumanRepeatTask.subject_id,
                HumanRepeatTask.example_count,
                HumanRepeatTask.pass_count,
                HumanRepeatTask.exam_count,
                HumanRepeatTask.repeat_task_count,
                # HumanRepeatTask.repeat_task_complete_count,
                HumanRepeatTask.task_state,
                HumanRepeatTask.score_threshold,
                HumanRepeatTask.c_user_id,
                HumanRepeatTask.repeat_user_id_list,
                HumanRepeatTask.u_user_id,
                Project.project_name,
                Subject.subject_name,
                Subject.subject_total_score,
                HumanRepeatTask.round_count,
                HumanRepeatTask.created_time,
                UserInfo.username,
            )
            .outerjoin(Project, Project.project_id == HumanRepeatTask.project_id)
            .outerjoin(Subject, Subject.subject_id == HumanRepeatTask.subject_id)
            # .outerjoin(HumanRepeatTaskRound, HumanRepeatTaskRound.repeat_task_round_id == HumanRepeatTask.repeat_task_round_id)
            .outerjoin(UserInfo, UserInfo.user_id == HumanRepeatTask.c_user_id)
        )
        if conditions:
            query_obj = query_obj.where(and_(*conditions))

        # 分页处理
        offset = (query.current_page - 1) * query.page_size
        query_obj = query_obj.offset(offset).limit(query.page_size)

        # 执行查询
        repeat_tasks = new_session.execute(query_obj).all()

        # 转换为字典格式
        repeat_tasks = [dict(row._mapping) for row in repeat_tasks]

        # 查询用户信息
        user_info_result = new_session.execute(select(UserInfo.user_id, UserInfo.username, UserInfo.name)).all()
        user_info_result = [dict(row._mapping) for row in user_info_result]
        for repeat_task in repeat_tasks:
            repeat_task["created_time"] = str(repeat_task["created_time"]).replace("T", " ")
            if repeat_task["example_count"] and repeat_task["pass_count"]:
                repeat_task["no_pass_count"] = repeat_task["example_count"] - repeat_task["pass_count"]
            if repeat_task["repeat_user_id_list"]:
                name_list = [p["username"] for p in user_info_result if p["user_id"] in repeat_task["repeat_user_id_list"]]
                repeat_task["repeat_user_name_list"] = ",".join(name_list)
            else:
                repeat_task["repeat_user_name_list"] = ""

        # 获取总记录数
        count_query = select(func.count(HumanRepeatTask.repeat_task_id)).select_from(HumanRepeatTask)
        if conditions:
            count_query = count_query.where(and_(*conditions))
        total = new_session.execute(count_query).scalar_one()

        if query.is_repeater == 1:
            for repeat_task in repeat_tasks:
                repeat_task_count = (
                    new_session.query(HumanRepeatRoundDetail)
                    .filter(HumanRepeatRoundDetail.repeat_task_id == repeat_task["repeat_task_id"], HumanRepeatRoundDetail.repeat_user_id == user["user_id"])
                    .count()
                )
                repeat_task["repeat_task_count"] = repeat_task_count

        return BaseResponse(msg="获取复评任务列表成功", data={"repeat_tasks": repeat_tasks, "total": total, "current_page": query.current_page, "page_size": query.page_size})
    except Exception as e:
        logger.error(f"获取复评任务列表失败，{e}{traceback.format_exc()}")
        return BaseResponse(code=response_utils.server_error, msg="获取列表失败")


# 生成验收任务
def create_task(query, user, new_session):
    if query.task_type == 4 or query.task_type == 5:
        # 从数据库查询Subject列表
        subject_list = new_session.query(Subject.subject_id, Subject.subject_name, Subject.project_id).all()
        subject_list = [{"subject_id": s.subject_id, "subject_name": s.subject_name, "project_id": s.project_id} for s in subject_list]
        # HumanRepeatTask关联HumanRepeatTaskRound，过滤task_type ==query.task_type记录数量
        # 查询关联的HumanRepeatTask数量
        task_count_query = (
            select(HumanRepeatTask.subject_id, HumanRepeatTask.repeat_task_round_id)
            .join(HumanRepeatTaskRound, HumanRepeatTask.repeat_task_round_id == HumanRepeatTaskRound.repeat_task_round_id)
            .where(HumanRepeatTaskRound.task_type == query.task_type)
        )
        task_project_ids = new_session.execute(task_count_query).all()
        if len(subject_list) > 0:
            if len(task_project_ids) == 0:
                # 创建HumanRepeatTaskRound
                task_round_id = configs.snow_worker.get_id()
                repeat_task_round = HumanRepeatTaskRound(
                    repeat_task_round_id=task_round_id,
                    task_name=f"验收任务轮次-{query.task_type}",
                    round_count=1,
                    score_type=0,  # 单轮次
                    select_round_count=1,
                    select_score_type=0,  # 最高分
                    task_type=query.task_type,
                    c_user_id=user.get("user_id"),
                    u_user_id=user.get("user_id"),
                )
                new_session.add(repeat_task_round)

                # 为每个subject创建HumanRepeatTask
                for subject in subject_list:
                    exam_count = new_session.query(ExamStudent).filter(ExamStudent.subject_id == subject["subject_id"], ExamStudent.is_exam == 0).count()
                    repeat_task = HumanRepeatTask(
                        repeat_task_id=configs.snow_worker.get_id(),
                        repeat_task_round_id=task_round_id,
                        project_id=subject["project_id"],
                        subject_id=subject["subject_id"],
                        task_type=query.task_type,
                        exam_count=exam_count,
                        repeat_task_count=0,
                        pass_count=0,
                        c_user_id=user.get("user_id"),
                        u_user_id=user.get("user_id"),
                    )
                    new_session.add(repeat_task)
                new_session.commit()
            else:
                if len(subject_list) > len(task_project_ids):
                    # 将subject_list中不在task_project_ids的subject_id添加到HumanRepeatTask
                    # 找出不在task_project_ids中的subject_id
                    existing_subject_ids = {task[0] for task in task_project_ids}
                    for subject in subject_list:
                        if subject["subject_id"] not in existing_subject_ids:
                            repeat_task = HumanRepeatTask(
                                repeat_task_id=configs.snow_worker.get_id(),
                                repeat_task_round_id=task_project_ids[0][1],
                                project_id=None,  # 根据实际需求设置
                                subject_id=subject["subject_id"],
                                pass_count=0,
                                repeat_task_count=0,
                                c_user_id=user.get("user_id"),
                                u_user_id=user.get("user_id"),
                            )
                            new_session.add(repeat_task)
                    new_session.commit()


# 根据复评任务id修改task_state
@repeat_router.post(path="/update_task_state", response_model=BaseResponse, summary="更新复评任务状态")
async def update_task_state_api(query: RepeatTaskIdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    根据复评任务ID修改任务状态
    :param task_id: 复评任务ID
    :param new_state: 新的任务状态
    :param user: 当前用户
    :param new_session: 数据库会话
    :return: 响应结果
    """
    logger.info(f"{user['username']} 更新复评任务 {query.repeat_task_id} 的状态为 ")
    try:
        # 查询任务
        task = new_session.query(HumanRepeatTask).filter(HumanRepeatTask.repeat_task_id == query.repeat_task_id).first()
        if not task:
            return BaseResponse(code=response_utils.not_found, msg="任务不存在")

        # 更新任务状态
        task.task_state = query.new_state
        task.u_user_id = user.get("user_id")
        new_session.commit()

        return BaseResponse(msg="更新任务状态成功")
    except Exception as e:
        logger.error(f"更新任务状态失败，{e}{traceback.format_exc()}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="更新任务状态失败")


@repeat_router.post(path="/get_repeat_task_stu", response_model=BaseResponse, summary="获取未复评任务考生")
async def get_repeat_task_stu(query: GetRepeatTaskStuReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取未复评任务考生")
    try:
        # 获取已复评考生
        if query.stu_secret_num:
            repeat_round_detail = (
                new_session.query(HumanRepeatRoundDetail).filter(HumanRepeatRoundDetail.repeat_task_id == query.repeat_task_id, HumanRepeatRoundDetail.stu_secret_num == query.stu_secret_num).first()
            )
            repeat_round_detail_id = repeat_round_detail.repeat_round_detail_id

            human_repeat_task = new_session.query(HumanRepeatTask).filter(HumanRepeatTask.repeat_task_id == repeat_round_detail.repeat_task_id).first()
            data = build_stu_data(new_session, repeat_round_detail, repeat_round_detail_id, human_repeat_task, True)
            return BaseResponse(msg="获取复评任务考生成功", data={"total": len(data), "data": data})

        # 查看是否有进行中的复评考生，如果有直接返回
        repeat_round_detail = (
            new_session.query(HumanRepeatRoundDetail)
            .filter(
                HumanRepeatRoundDetail.repeat_task_id == query.repeat_task_id,
                HumanRepeatRoundDetail.repeat_user_id == user["user_id"],
                HumanRepeatRoundDetail.stu_state == 2,
            )
            .first()
        )
        if repeat_round_detail:
            repeat_round_detail_id = repeat_round_detail.repeat_round_detail_id
            human_repeat_task = new_session.query(HumanRepeatTask).filter(HumanRepeatTask.repeat_task_id == repeat_round_detail.repeat_task_id).first()

            data = build_stu_data(new_session, repeat_round_detail, repeat_round_detail_id, human_repeat_task)
            return BaseResponse(msg="获取复评任务考生成功", data={"total": len(data), "data": data})

        # 分配未复评的考生
        redis = next(redis_session())
        repeat_round_detail_id = redis.rpop(query.repeat_task_id)

        if repeat_round_detail_id:
            repeat_round_detail = new_session.query(HumanRepeatRoundDetail).filter(HumanRepeatRoundDetail.repeat_round_detail_id == repeat_round_detail_id).first()
            human_repeat_task = new_session.query(HumanRepeatTask).filter(HumanRepeatTask.repeat_task_id == repeat_round_detail.repeat_task_id).first()
            # 同时关联ExamQuestion获取ques_code
            data = build_stu_data(new_session, repeat_round_detail, repeat_round_detail_id, human_repeat_task)

            repeat_round_detail.repeat_user_id = user["user_id"]
            repeat_round_detail.stu_state = 2
            new_session.commit()
            return BaseResponse(msg="获取复评任务考生成功", data={"total": len(data), "data": data})
        else:
            return BaseResponse(msg="暂无复评任务考生", data=None)

    except Exception as e:
        logger.error(f"获取复评任务考生失败，{e}{traceback.format_exc()}")
        return BaseResponse(code=response_utils.server_error, msg="获取复评任务考生失败")


def build_stu_data(new_session, repeat_round_detail, repeat_round_detail_id, human_repeat_task, repeat_score=None):
    stu_answers = (
        new_session.query(StuAnswer.answer_id, StuAnswer.ques_id, StuAnswer.answer_image_path, StuAnswer.word_count, StuAnswer.is_do)
        .filter(and_(StuAnswer.stu_secret_num == repeat_round_detail.stu_secret_num, StuAnswer.subject_id == human_repeat_task.subject_id))
        .order_by(StuAnswer.ques_order)
        .all()
    )
    exam_questions = new_session.query(ExamQuestion.ques_id, ExamQuestion.ques_code, ExamQuestion.parent_ques_id).order_by(ExamQuestion.small_ques_int).all()
    big_que_id_list = [(p.ques_id, p.ques_code) for p in exam_questions if p.parent_ques_id is None]

    if repeat_score:
        repeat_answer_socres = (
            new_session.query(
                HumanRepeatAnswerScore.answer_id,
                HumanRepeatAnswerScore.mark_score,
                HumanRepeatAnswerScore.mark_point_score_list,
            )
            .filter(HumanRepeatAnswerScore.repeat_round_detail_id == repeat_round_detail_id)
            .all()
        )

    data = []
    for big_que_id, ques_code in big_que_id_list:
        answer_data = []
        small_que_id_list = [p.ques_id for p in exam_questions if p.parent_ques_id == big_que_id]
        if small_que_id_list:
            answer_data.extend(
                [
                    {"answer_id": p.answer_id, "answer_image_path": "\\server_static\\" + p.answer_image_path, "word_count": p.word_count, "is_do": p.is_do}
                    for p in stu_answers
                    if p.ques_id in small_que_id_list
                ]
            )
        else:
            answer_data.extend(
                [
                    {"answer_id": p.answer_id, "answer_image_path": "\\server_static\\" + p.answer_image_path, "word_count": p.word_count, "is_do": p.is_do}
                    for p in stu_answers
                    if p.ques_id == big_que_id
                ]
            )

        if answer_data:
            answer_ids = [p["answer_id"] for p in answer_data]
            ques_answer_data = {ques_code: answer_ids}
            scores = get_official_task_score(new_session, ques_answer_data)
            for answer in answer_data:
                if ques_code in scores:
                    answer["official_score"] = scores[ques_code][answer["answer_id"]]["stu_score"]
                    answer["official_stu_score_list"] = scores[ques_code][answer["answer_id"]]["stu_score_list"]
                if repeat_score:
                    answer["repeat_score"] = next((p[1] for p in repeat_answer_socres if p[0] == answer["answer_id"]), 0)
                    answer["mark_point_score_list"] = next((p[2] for p in repeat_answer_socres if p[0] == answer["answer_id"]), 0)

            data.append(
                {"stu_secret_num": repeat_round_detail.stu_secret_num, "ques_code": ques_code, "answer_data": answer_data, "repeat_round_detail_id": repeat_round_detail.repeat_round_detail_id}
            )
    return data


@repeat_router.post(path="/repeat_stu_socre", response_model=BaseResponse, summary="复评考生评分")
async def repeat_stu_socre(query: RepeatScoreReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取复评考生评分")
    try:
        repeat_round_detail = new_session.query(HumanRepeatRoundDetail).filter(HumanRepeatRoundDetail.repeat_round_detail_id == query.repeat_round_detail_id).first()
        repeat_task = new_session.query(HumanRepeatTask).filter(HumanRepeatTask.repeat_task_id == repeat_round_detail.repeat_task_id).first()

        student_subject_grade = (
            new_session.query(HumanStudentSubjectGrade)
            .filter(
                and_(
                    HumanStudentSubjectGrade.subject_id == repeat_task.subject_id,
                    HumanStudentSubjectGrade.stu_secret_num == repeat_round_detail.stu_secret_num,
                )
            )
            .first()
        )

        if student_subject_grade:
            human_stu_grade_detail_list = (
                new_session.query(HumanStudentSubjectGradeDetail).filter(HumanStudentSubjectGradeDetail.student_subject_grade_id == student_subject_grade.student_subject_grade_id).all()
            )
        else:
            human_stu_grade_detail_list = []

        if query.is_repeat == 1:
            new_session.execute(delete(HumanRepeatAnswerScore).where(HumanRepeatAnswerScore.repeat_round_detail_id == query.repeat_round_detail_id))
        else:
            # 修改任务统计信息
            repeat_task.repeat_task_count += 1
            if user["user_id"] not in repeat_task.repeat_user_id_list:
                uids = list(repeat_task.repeat_user_id_list)
                uids.append(user["user_id"])
                repeat_task.repeat_user_id_list = uids

        big_ques_socre = 0.00
        for big_ques in query.big_ques_list:
            big_ques_socre += big_ques.repeat_score
            for mark_info in big_ques.mark_info:
                repeat_answer_socre = HumanRepeatAnswerScore(
                    repeat_answer_socre_id=configs.snow_worker.get_id(),
                    repeat_round_detail_id=query.repeat_round_detail_id,
                    mark_score=mark_info.mark_score,
                    ques_code=mark_info.ques_code,
                    answer_id=mark_info.answer_id,
                    mark_point_score_list=mark_info.mark_point_score_list,
                    mark_time=datetime.datetime.now(),
                )
                new_session.add(repeat_answer_socre)
                if repeat_task.task_type == 4 or repeat_task.task_type == 5:
                    continue
                human_stu_grade_detail = next((p for p in human_stu_grade_detail_list if p.answer_id == mark_info.answer_id), None)
                if human_stu_grade_detail:
                    human_stu_grade_detail.stu_score = mark_info.mark_score
                    human_stu_grade_detail.stu_score_list = mark_info.mark_point_score_list
        repeat_round_detail.stu_score = big_ques_socre
        repeat_round_detail.updated_time = datetime.datetime.now()
        repeat_round_detail.stu_state = 3
        new_session.commit()

        # if repeat_task.task_type == 3:
        #     # 查询该考生的复评详情
        #     stu_repeat_detail = (
        #         new_session.query(HumanStudentSubjectGrade)
        #         .filter(HumanStudentSubjectGrade.stu_secret_num == repeat_round_detail.stu_secret_num, HumanStudentSubjectGrade.subject_id == repeat_task.subject_id)
        #         .first()
        #     )

        #     if stu_repeat_detail:
        #         stu_repeat_detail.score = big_ques_socre
        #         stu_repeat_detail.updated_time = datetime.datetime.now()
        #         stu_repeat_detail.task_type = 3
        # new_session.commit()

        redis = next(redis_session())
        task_length = redis.llen(repeat_task.repeat_task_id)
        if task_length == 0:
            repeat_round_detail_count = (
                new_session.query(HumanRepeatRoundDetail)
                .filter(HumanRepeatRoundDetail.repeat_task_id == repeat_round_detail.repeat_task_id, or_(HumanRepeatRoundDetail.stu_state == 1, HumanRepeatRoundDetail.stu_state == 2))
                .count()
            )
            if repeat_round_detail_count == 0:
                if repeat_task.task_type == 3:
                    repeat_task.task_state = 3
                else:
                    repeat_task.task_state = 4

                new_session.commit()

        return BaseResponse(msg="复评考生评分成功")
    except Exception as e:
        logger.error(f"考生评分失败，{e}{traceback.format_exc()}")
        return BaseResponse(code=response_utils.server_error, msg="评分失败")


# 根据repeat_task_id获取HumanRepeatRoundDetail列表
@repeat_router.post(path="/get_repeat_round_detail_list", response_model=BaseResponse, summary="根据复评任务id和复评用户id获取复评详情列表")
async def get_repeat_round_detail_list(query: GetRepeatRoundDetailReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    根据复评任务ID和复评用户ID获取HumanRepeatRoundDetail列表
    :param query: 请求参数，包含repeat_task_id和repeat_user_id
    :param user: 当前用户
    :param new_session: 数据库会话
    :return: 响应结果
    """
    logger.info(f"{user['username']} 根据复评任务id和复评用户id获取复评详情列表")
    try:
        # 查询HumanRepeatRoundDetail表
        query_obj = new_session.query(HumanRepeatRoundDetail).filter(
            HumanRepeatRoundDetail.repeat_task_id == query.repeat_task_id, HumanRepeatRoundDetail.repeat_user_id == user["user_id"], HumanRepeatRoundDetail.stu_state == 3
        )

        # 分页处理
        offset = (query.current_page - 1) * query.page_size
        query_obj = query_obj.offset(offset).limit(query.page_size)

        # 执行查询
        round_details = query_obj.all()

        # stus = [p.stu_secret_num for p in round_details]
        # subject_grades = (
        #     new_session.query(HumanStudentSubjectGrade.stu_secret_num, HumanStudentSubjectGrade.score)
        #     .filter(HumanStudentSubjectGrade.subject_id == query.subject_id, HumanStudentSubjectGrade.stu_secret_num.in_(stus))
        #     .all()
        # )
        # subject_grades = [dict(row._mapping) for row in subject_grades]
        # 转换为字典格式
        result_data = []
        for task in round_details:
            result_data.append(
                {
                    "repeat_round_detail_id": task.repeat_round_detail_id,
                    "repeat_task_id": task.repeat_task_id,
                    "stu_secret_num": task.stu_secret_num,
                    "stu_state": task.stu_state,
                    "repeat_user_id": task.repeat_user_id,
                    "stu_score": task.stu_score,
                    "c_user_id": task.c_user_id,
                    "u_user_id": task.u_user_id,
                    "updated_time": str(task.updated_time).replace("T", " "),
                    "official_score": task.last_score,
                }
            )

        # 获取总记录数
        count_query = select(func.count(HumanRepeatRoundDetail.repeat_round_detail_id)).select_from(HumanRepeatRoundDetail)
        count_query = count_query.where(HumanRepeatRoundDetail.repeat_task_id == query.repeat_task_id, HumanRepeatRoundDetail.repeat_user_id == user["user_id"], HumanRepeatRoundDetail.stu_state == 3)
        total = new_session.execute(count_query).scalar_one()

        return BaseResponse(msg="获取复评详情列表成功", data={"round_details": result_data, "total": total, "current_page": query.current_page, "page_size": query.page_size})
    except Exception as e:
        logger.error(f"根据复评任务id和复评用户id获取复评详情列表失败，{e}{traceback.format_exc()}")
        return BaseResponse(code=response_utils.server_error, msg="根据复评任务id和复评用户id获取复评详情列表失败")


@repeat_router.post(path="/update_score_threshold", response_model=BaseResponse, summary="根据复评任务id修改分差阈值")
async def update_score_threshold(query: UpdateScoreThresholdReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    根据复评任务ID修改分差阈值
    :param query: 请求参数，包含repeat_task_id和score_threshold
    :param user: 当前用户
    :param new_session: 数据库会话
    :return: 响应结果
    """
    logger.info(f"{user['username']} 根据复评任务id修改分差阈值")
    try:
        # 查询任务
        task = new_session.query(HumanRepeatTask).filter(HumanRepeatTask.repeat_task_id == query.repeat_task_id).first()
        if not task:
            return BaseResponse(code=response_utils.not_found, msg="任务不存在")

        # 更新分差阈值
        task.score_threshold = query.score_threshold
        task.u_user_id = user.get("user_id")
        new_session.commit()

        return BaseResponse(msg="更新分差阈值成功")
    except Exception as e:
        logger.error(f"更新分差阈值失败，{e}{traceback.format_exc()}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="更新分差阈值失败")


# 导出技术验收Excel
@repeat_router.post(path="/export_tech_acceptance_excel", response_model=BaseResponse, summary="导出技术验收Excel")
async def export_tech_acceptance_excel(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    导出技术验收任务的Excel数据
    """
    try:
        repeat_tasks = new_session.execute(
            select(
                HumanRepeatTask.repeat_task_id,
                func.concat(Project.project_name, "/", Subject.subject_name),
                HumanRepeatTask.exam_count,
                # HumanRepeatTask.example_count,
                HumanRepeatTask.repeat_task_count,
                HumanRepeatTask.pass_count,
                HumanRepeatTask.bias_count,
                HumanRepeatTask.verify_result,
            )
            .outerjoin(Project, Project.project_id == HumanRepeatTask.project_id)
            .outerjoin(Subject, Subject.subject_id == HumanRepeatTask.subject_id)
            .where(HumanRepeatTask.task_type == 4)
        ).all()

        repeat_tasks = [tuple(row) for row in repeat_tasks]
        repeat_tasks = [(b, c, d, e, d - e, g) + ("",) for (a, b, c, d, e, f, g) in repeat_tasks]

        s1 = sum(t[1] for t in repeat_tasks)
        s2 = sum(t[2] for t in repeat_tasks)
        s3 = sum(t[3] for t in repeat_tasks)
        s4 = sum(t[4] for t in repeat_tasks)
        repeat_tasks.append(("总计", s1, s2, s3, s4, "", ""))

        repeat_round_details = new_session.execute(
            select(
                HumanRepeatRoundDetail.repeat_round_detail_id,
                func.concat(Project.project_name, "/", Subject.subject_name),
                HumanRepeatRoundDetail.stu_secret_num,
                HumanRepeatRoundDetail.last_score,
                HumanRepeatRoundDetail.stu_score,
                HumanRepeatTask.score_threshold,
                HumanRepeatTask.verify_result,
            )
            .outerjoin(HumanRepeatTask, HumanRepeatRoundDetail.repeat_task_id == HumanRepeatTask.repeat_task_id)
            .outerjoin(Project, Project.project_id == HumanRepeatTask.project_id)
            .outerjoin(Subject, Subject.subject_id == HumanRepeatTask.subject_id)
            .where(HumanRepeatTask.task_type == 4)
        ).all()
        repeat_round_details = [tuple(row) for row in repeat_round_details]
        repeat_round_details = [row[1:] + ("",) for row in repeat_round_details]

        file_path = os.path.join(configs.PROJECT_PATH, "server_static/export/", configs.snow_worker.get_id() + ".xlsx")
        file_name = "技术验收表"
        export_technology(datetime.datetime.now(), repeat_tasks, repeat_round_details, file_path)

        # 导出Excel
        from urllib.parse import quote

        encoded_file_name = quote(file_name, safe="~()*!.'")
        return FileResponse(
            path=file_path,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename=file_name,
            headers={"Content-Disposition": f"attachment; filename*=utf-8''{encoded_file_name}.xlsx"},
        )
    except Exception as e:
        logger.error(f"导出技术验收Excel失败: {e}{traceback.format_exc()}")
        return BaseResponse(code=response_utils.server_error, msg="导出技术验收Excel失败")


@repeat_router.post(path="/export_manage_acceptance_excel", response_model=BaseResponse, summary="导出管理验收Excel")
async def export_manage_acceptance_excel(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    导出技术验收任务的Excel数据
    """
    try:
        repeat_tasks = new_session.execute(
            select(
                HumanRepeatTask.repeat_task_id,
                func.concat(Project.project_name, "/", Subject.subject_name),
                HumanRepeatTask.exam_count,
                HumanRepeatTask.example_count,
                HumanRepeatTask.repeat_task_count,
                HumanRepeatTask.pass_count,
                HumanRepeatTask.bias_count,
                HumanRepeatTask.verify_result,
            )
            .outerjoin(Project, Project.project_id == HumanRepeatTask.project_id)
            .outerjoin(Subject, Subject.subject_id == HumanRepeatTask.subject_id)
            .where(HumanRepeatTask.task_type == 5)
        ).all()

        repeat_tasks = [tuple(row) for row in repeat_tasks]
        repeat_tasks = [
            row[1:]
            + (
                "",
                "",
            )
            for row in repeat_tasks
        ]

        repeat_round_details = new_session.execute(
            select(
                HumanRepeatRoundDetail.repeat_round_detail_id,
                func.concat(Project.project_name, "/", Subject.subject_name),
                HumanRepeatRoundDetail.stu_secret_num,
                HumanRepeatRoundDetail.stu_score,
                HumanRepeatTask.score_threshold,
            )
            .outerjoin(HumanRepeatTask, HumanRepeatTask.repeat_task_id == HumanRepeatRoundDetail.repeat_task_id)
            .outerjoin(Project, Project.project_id == HumanRepeatTask.project_id)
            .outerjoin(Subject, Subject.subject_id == HumanRepeatTask.subject_id)
            .where(HumanRepeatTask.task_type == 5)
        ).all()
        repeat_round_details = [tuple(row) for row in repeat_round_details]
        repeat_round_details = [row[1:] + ("",) for row in repeat_round_details]

        file_path = os.path.join(configs.PROJECT_PATH, "server_static/export/", configs.snow_worker.get_id() + ".xlsx")
        file_name = "管理验收表"

        manager_technology(datetime.datetime.now(), repeat_tasks, repeat_round_details, file_path)

        # 导出Excel
        from urllib.parse import quote

        encoded_file_name = quote(file_name, safe="~()*!.'")
        return FileResponse(
            path=file_path,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename=file_name,
            headers={"Content-Disposition": f"attachment; filename*=utf-8''{encoded_file_name}.xlsx"},
        )
    except Exception as e:
        logger.error(f"导出技术验收Excel失败: {e}{traceback.format_exc()}")
        return BaseResponse(code=response_utils.server_error, msg="导出技术验收Excel失败")
