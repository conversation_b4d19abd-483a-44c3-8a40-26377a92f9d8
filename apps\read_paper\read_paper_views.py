import re
import threading
import traceback

from fastapi import APIRouter, Depends
from sqlalchemy import select, and_
from settings import logger
from typing import Any

from sqlalchemy.orm import Session

from apps.base.services import request_api
from apps.users.services import get_current_user
from apps.models.models import StuAnswer, ExamQuestion, ExamPaper, QuesType, Subject, Project, SameStuAnswerGroup, PaperDetail
from apps.read_paper.read_paper_services import get_no_mark_group, ooo_mark_thread_control, \
    check_mark_rule, get_paper_detail
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from apps.read_paper import GetReadPaperReq, StartReadPaperReq, ProfessionMarkReq, GetReadPaperPercentageReq, \
    GetReadPaperDetailReq, InitReadPaperStateReq, GetReadPaperDataReq
from helper import response_utils
from apps.read_paper.common_services import transform_mark_state, read_paper_query_condition, get_user_data_flag, \
    mark_detail_query_condition, splice_image_path, init_mark_state
from settings import configs
from utils.utils import round_half_up, sum_with_precision, count_decimal_places

read_paper_router = APIRouter()


@read_paper_router.post(path="/get_read_paper", response_model=BaseResponse, summary="获取智能阅卷列表")
async def get_read_paper(query: GetReadPaperReq, user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend)):
    (current_page, page_size, project_id, subject_id, paper_code, ques_type_code, ques_code, ques_order,
     same_answer_group_id, stu_answer, mark_result, mark_state, stu_score_range,
     search_time) = query.model_dump().values()
    logger.info(f"{user['username']} 获取智能阅卷列表")
    project_condition, subject_condition = get_user_data_flag(new_session, user)

    answer_data = []
    limit = current_page - 1
    offset = limit * page_size

    paper_detail_dict = {}
    # paper_detail_dict = {
    #     paper_id: {
    #         ques_code: {
    #             "ques_order": ques_order,
    #             "ques_score_list": ques_score_list
    #         }
    #     }
    # }

    select_fields = [SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.paper_id, SameStuAnswerGroup.ques_id,
                     SameStuAnswerGroup.ques_code, SameStuAnswerGroup.small_ques_order, SameStuAnswerGroup.stu_answer,
                     SameStuAnswerGroup.score, SameStuAnswerGroup.mark_state, SameStuAnswerGroup.stu_score,
                     SameStuAnswerGroup.answer_parse, ExamPaper.paper_name, Subject.subject_name,
                     QuesType.ques_type_name, ExamQuestion.standard_answer, QuesType.ques_type_code,
                     Project.project_name, ExamQuestion.parent_ques_id,
                     SameStuAnswerGroup.paper_code, SameStuAnswerGroup.updated_time]

    # 排除操作题
    exclude_op_condition = SameStuAnswerGroup.ques_type_code != "G"
    # 拼凑查询条件
    condition = read_paper_query_condition(new_session, project_id, subject_id, paper_code, ques_type_code, ques_code,
                                           ques_order, same_answer_group_id, stu_answer, mark_result, mark_state,
                                           stu_score_range, search_time)

    if page_size == -1:
        answer_stmt = select(*select_fields) \
            .join(ExamQuestion, ExamQuestion.ques_id == SameStuAnswerGroup.ques_id) \
            .join(QuesType, QuesType.ques_type_code == SameStuAnswerGroup.ques_type_code) \
            .join(Project, Project.project_id == SameStuAnswerGroup.project_id) \
            .join(Subject, Subject.subject_id == SameStuAnswerGroup.subject_id) \
            .join(ExamPaper, ExamPaper.paper_code == SameStuAnswerGroup.paper_code) \
            .where(and_(condition, project_condition, subject_condition, exclude_op_condition)) \
            .order_by(SameStuAnswerGroup.updated_time.desc())
    else:
        total = new_session.query(SameStuAnswerGroup.same_answer_group_id) \
            .join(ExamPaper, ExamPaper.paper_code == SameStuAnswerGroup.paper_code) \
            .join(Project, Project.project_id == SameStuAnswerGroup.project_id) \
            .join(Subject, Subject.subject_id == SameStuAnswerGroup.subject_id) \
            .where(and_(condition, project_condition, subject_condition, exclude_op_condition)).count()

        answer_stmt = select(*select_fields) \
            .join(ExamQuestion, ExamQuestion.ques_id == SameStuAnswerGroup.ques_id) \
            .join(QuesType, QuesType.ques_type_code == SameStuAnswerGroup.ques_type_code) \
            .join(Project, Project.project_id == SameStuAnswerGroup.project_id) \
            .join(Subject, Subject.subject_id == SameStuAnswerGroup.subject_id) \
            .join(ExamPaper, ExamPaper.paper_code == SameStuAnswerGroup.paper_code) \
            .where(and_(condition, project_condition, subject_condition, exclude_op_condition)) \
            .order_by(SameStuAnswerGroup.updated_time.desc()).limit(page_size).offset(offset)

    try:
        result = list(new_session.execute(answer_stmt))
        same_answer_group_id_list = [i[0] for i in result]
        id_result = new_session.query(StuAnswer.same_answer_group_id, StuAnswer.answer_id) \
            .filter(StuAnswer.same_answer_group_id.in_(same_answer_group_id_list)).all()
        id_result_dict = {}
        for same_answer_group_id, answer_id in id_result:
            if same_answer_group_id in id_result_dict:
                id_result_dict[same_answer_group_id].append(answer_id)
            else:
                id_result_dict[same_answer_group_id] = [answer_id]

        for row in result:
            raw_paper_id, raw_ques_id = row.paper_id, row.ques_id
            is_add, ques_order, ques_score_list = get_paper_detail(new_session, paper_detail_dict, raw_paper_id,
                                                                   raw_ques_id)
            if is_add:
                if paper_detail_dict.get(raw_paper_id):
                    paper_detail_dict[raw_paper_id][raw_ques_id] = {
                        "ques_order": ques_order,
                        "ques_score_list": ques_score_list
                    }
                else:
                    paper_detail_dict[raw_paper_id] = {
                        raw_ques_id: {
                            "ques_order": ques_order,
                            "ques_score_list": ques_score_list
                        }
                    }

            answer_id_list = id_result_dict[row.same_answer_group_id]
            (standard_answer_list, ques_type_code, small_ques_order, score, answer_parse, parent_ques_id) = \
                (row.standard_answer, row.ques_type_code, row.small_ques_order, row.score, row.answer_parse,
                 row.parent_ques_id)
            ques_score_list_length = len(ques_score_list)
            answer_id_by_ques_list = [answer_id_list[i: i + ques_score_list_length] for i in
                                      range(0, len(answer_id_list), ques_score_list_length)]
            origin_small_ques_order = small_ques_order
            if ques_type_code == "C":
                single_standard_answer = ["".join(standard_answer_list)]
            else:
                if standard_answer_list and small_ques_order:
                    if configs.NEW_SPLIT_FLAG in small_ques_order:
                        small_ques_order = small_ques_order.replace(configs.NEW_SPLIT_FLAG, "")
                        single_standard_answer = standard_answer_list
                    else:
                        if not parent_ques_id:
                            single_standard_answer = [standard_answer_list[int(small_ques_order) - 1]]
                        else:
                            single_standard_answer = standard_answer_list
                else:
                    single_standard_answer = None

            # 可乱序填空题相同答案个数需要除以填空数
            same_answer_count = len(answer_id_list)
            if configs.NEW_SPLIT_FLAG in origin_small_ques_order:
                order_list_length = len(origin_small_ques_order.split(configs.NEW_SPLIT_FLAG))
                same_answer_count = same_answer_count / order_list_length

            total_score = sum_with_precision(score.split(configs.NEW_SPLIT_FLAG))
            stu_score = row.stu_score
            if stu_score:
                stu_score = float(stu_score)
                if stu_score > total_score:
                    stu_score = None

            answer_item = {
                "same_answer_group_id": row.same_answer_group_id,
                "answer_id": answer_id_by_ques_list,
                "ques_code": row.ques_code,
                "small_ques_order": small_ques_order,
                "stu_answer": row.stu_answer.split(configs.NEW_SPLIT_FLAG) if row.stu_answer else None,
                "score": score,
                "total_score": sum_with_precision(score.split(configs.NEW_SPLIT_FLAG)),
                "mark_state": transform_mark_state(row.mark_state),
                "stu_score": stu_score,
                "answer_parse": answer_parse.split(configs.NEW_SPLIT_FLAG) if answer_parse else None,
                "paper_code": row.paper_code,
                "paper_name": row.paper_name,
                "subject_name": row.subject_name,
                "ques_type_name": row.ques_type_name,
                "standard_answer": single_standard_answer,
                "project_name": row.project_name,
                "same_answer_count": same_answer_count,
                "ques_order": ques_order,
                "updated_time": row.updated_time and str(row.updated_time).replace("T",
                                                                                   " ") if row.mark_state != 1 and row.mark_state != 5 else None
            }
            answer_data.append(answer_item)
    except Exception as e:
        logger.error(f"获取智能阅卷列表失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg=f"获取智能阅卷列表失败")
    logger.info("获取智能阅卷列表成功")
    if page_size == -1:
        total = len(answer_data)
    data = {
        "data": answer_data,
        "total": total
    }
    return BaseResponse(msg="获取智能阅卷列表成功", data=data)


@read_paper_router.post(path="/get_ques_mark_detail", response_model=BaseResponse,
                        summary="获取智能阅卷试题评分详情数据")
async def get_ques_mark_detail(query: GetReadPaperDetailReq, user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend)):
    same_answer_group_id, paper_code, ques_code = query.model_dump().values()
    logger.info(f"{user['username']} 获取智能阅卷试题评分详情数据")

    select_fields = [SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.stu_answer,
                     SameStuAnswerGroup.stu_score, SameStuAnswerGroup.answer_parse, ExamQuestion.ques_desc,
                     ExamQuestion.ques_choices, ExamQuestion.standard_answer, SameStuAnswerGroup.profession_parse,
                     SameStuAnswerGroup.modify_reason, ExamQuestion.ques_mark_point, PaperDetail.parent_ques_id,
                     PaperDetail.ques_score_list, SameStuAnswerGroup.ques_type_code,
                     SameStuAnswerGroup.small_ques_order, SameStuAnswerGroup.modify_reason,
                     SameStuAnswerGroup.mark_result, SameStuAnswerGroup.paper_code, SameStuAnswerGroup.ques_id,
                     SameStuAnswerGroup.mark_fail_reason, SameStuAnswerGroup.mark_state, PaperDetail.ques_order,
                     ExamQuestion.e_mark_rule]

    # 拼凑查询条件
    condition = mark_detail_query_condition(same_answer_group_id, paper_code, ques_code)

    # 简答题的试题材料
    ques_material, ques_material_order = None, None

    try:
        mark_detail = new_session.query(*select_fields) \
            .join(ExamQuestion, ExamQuestion.ques_id == SameStuAnswerGroup.ques_id) \
            .join(PaperDetail, PaperDetail.ques_id == SameStuAnswerGroup.ques_id) \
            .where(condition).first()

        if not mark_detail:
            return BaseResponse(data={"data": {}}, msg="暂无数据")

        standard_answer_list, ques_type_code, small_ques_order, answer_parse, parent_ques_id, ques_score_list, profession_parse = mark_detail.standard_answer, mark_detail.ques_type_code, mark_detail.small_ques_order, mark_detail.answer_parse, mark_detail.parent_ques_id, mark_detail.ques_score_list, mark_detail.profession_parse

        if profession_parse:
            answer_parse = profession_parse
        else:
            answer_parse = answer_parse.split(configs.NEW_SPLIT_FLAG) if answer_parse else None

        if parent_ques_id:
            ques_material_order, ques_material = new_session.query(PaperDetail.ques_order, ExamQuestion.ques_desc) \
                .join(ExamQuestion, ExamQuestion.ques_id == PaperDetail.ques_id) \
                .filter(and_(PaperDetail.paper_code == paper_code, PaperDetail.ques_id == parent_ques_id)).first()
            if ques_material:
                ques_material, _ = splice_image_path(ques_material, [])

        if ques_type_code == "C":
            single_standard_answer = ["".join(standard_answer_list)]
        else:
            if standard_answer_list and small_ques_order:
                if configs.NEW_SPLIT_FLAG in small_ques_order:
                    single_standard_answer = standard_answer_list
                else:
                    if not parent_ques_id:
                        single_standard_answer = [standard_answer_list[int(small_ques_order) - 1]]
                    else:
                        single_standard_answer = standard_answer_list
            else:
                single_standard_answer = None

        total_score = sum_with_precision(ques_score_list)
        stu_score = mark_detail.stu_score
        if stu_score:
            stu_score = float(stu_score)
            if stu_score > total_score:
                stu_score = None

        ques_desc, ques_choices = mark_detail.ques_desc, mark_detail.ques_choices
        ques_desc, ques_choices = splice_image_path(ques_desc, ques_choices)

        answer_data = {
            "same_answer_group_id": mark_detail.same_answer_group_id,
            "ques_material": ques_material,
            "ques_desc": ques_desc,
            "ques_choices": ques_choices if ques_type_code in ["A", "C"] else [],
            "ques_score_list": ques_score_list,
            "ques_type_code": ques_type_code,
            "stu_answer": mark_detail.stu_answer.split(configs.NEW_SPLIT_FLAG) if mark_detail.stu_answer else None,
            "score": configs.NEW_SPLIT_FLAG.join(ques_score_list),
            "total_score": total_score,
            "standard_answer": single_standard_answer if ques_type_code != "G" else [],
            "ques_mark_point": mark_detail.ques_mark_point,
            "stu_score": stu_score,
            "answer_parse": answer_parse,
            "mark_result": mark_detail.mark_result,
            # "profession_parse": mark_detail.profession_parse,
            "modify_reason": mark_detail.modify_reason,
            "mark_fail_reason": mark_detail.mark_fail_reason,
            "mark_state": mark_detail.mark_state,
            "ques_id": mark_detail.ques_id,
            "ques_material_order": ques_material_order,
            "ques_order": re.sub(r"^\d+", "",
                                 mark_detail.ques_order) if "(" in mark_detail.ques_order or "（" in mark_detail.ques_order else mark_detail.ques_order,
            "e_mark_rule": mark_detail.e_mark_rule
        }
    except Exception as e:
        logger.error(f"获取智能阅卷试题评分详情数据失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg="获取智能阅卷试题评分详情数据失败")
    logger.info("获取智能阅卷试题评分详情数据成功")
    data = {
        "data": answer_data
    }
    return BaseResponse(msg="获取智能阅卷试题评分详情数据成功", data=data)


@read_paper_router.post(path="/get_ai_mark_data", response_model=BaseResponse, summary="获取智能阅卷的数据")
async def get_ai_mark_data(query: GetReadPaperDataReq, user: Any = Depends(get_current_user),
                           new_session: Session = Depends(session_depend)):
    (answer_id_list, project_id, subject_id, paper_code, ques_type_code, ques_code, ques_order, same_answer_group_id,
     stu_answer, mark_result, mark_state, stu_score_range, search_time, paper_code_list) = query.model_dump().values()
    logger.info(f"{user['username']} 获取智能阅卷的数据并进行状态初始化")
    filter_condition = any(
        [project_id, subject_id, paper_code, ques_type_code, ques_code, ques_order, same_answer_group_id, stu_answer,
         mark_result, mark_state, search_time, stu_score_range])

    if answer_id_list:
        logger.info(f"{user['username']} 获取勾选的数据")
        total = len(answer_id_list)
    else:
        if filter_condition:
            logger.info(f"{user['username']} 根据查询条件获取智能阅卷的数据")
            req_data = {
                "page_size": -1,
                "project_id": project_id,
                "subject_id": subject_id,
                "paper_code": paper_code,
                "ques_type_code": ques_type_code,
                "ques_code": ques_code,
                "ques_order": ques_order,
                "same_answer_group_id": same_answer_group_id,
                "stu_answer": stu_answer,
                "mark_result": mark_result,
                "mark_state": mark_state,
                "search_time": search_time,
                "stu_score_range": stu_score_range
            }
            token = f"Bearer {user['token']}"
            url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/read_paper/get_read_paper"
            res, msg = request_api(url, "POST", req_data, token, "智能阅卷的数据")
            if res == 0:
                return BaseResponse(code=response_utils.server_error, msg=msg)
            read_paper_list = res["data"]
            total = res["total"]
            if not read_paper_list:
                return BaseResponse(code=response_utils.no_field, msg="未找到待阅卷数据")
            for i in read_paper_list:
                answer_id_list.append(i["answer_id"])
                paper_code_list.append(i["paper_code"])
        else:
            logger.info(f"{user['username']} 获取智能阅卷全部数据")
            _, subject_condition = get_user_data_flag(new_session, user)
            answer_id_list, paper_code_list = get_no_mark_group(new_session, subject_condition=subject_condition)
            total = len(answer_id_list)
            if not answer_id_list:
                return BaseResponse(code=response_utils.fields_exist, msg="作答信息已全部阅卷完成")

    result, msg = check_mark_rule(new_session, paper_code_list)
    if not result:
        return BaseResponse(code=response_utils.no_field, msg=msg)
    data = {
        "answer_id_list": answer_id_list,
        "total": total,
        "mark_state": mark_state
    }
    return BaseResponse(data=data)


@read_paper_router.post(path="/init_ai_mark_state", response_model=BaseResponse, summary="智能阅卷的数据进行状态初始化")
async def init_ai_mark_state(query: InitReadPaperStateReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    answer_id_list, total, mark_state = query.model_dump().values()
    logger.info(f"{user['username']} 获取智能阅卷的数据并进行状态初始化")

    # 可乱序填空题需要将几个答案的 answer_id 组成一个列表传给阅卷线程
    # 因为同一组答案都是一样的，所以取第一组进行评分即可
    ooo_answer_id_list = [group_answer_id_list[0] for group_answer_id_list in answer_id_list]

    # 初始化再次智能评分的作答信息的评分信息
    all_answer_id_list = [i for sub_list in ooo_answer_id_list for i in sub_list]
    same_answer_group_id_list = init_mark_state(new_session, all_answer_id_list, mark_state)

    data = {
        "ooo_answer_id_list": ooo_answer_id_list,
        "same_answer_group_id_list": same_answer_group_id_list,
        "total": total
    }

    return BaseResponse(data=data, msg="操作成功")


# @read_paper_router.post(path="/init_ai_mark_state", response_model=BaseResponse,
#                         summary="获取智能阅卷的数据并进行状态初始化")
# async def init_ai_mark_state(query: InitReadPaperStateReq, user: Any = Depends(get_current_user),
#                              new_session: Session = Depends(session_depend)):
#     (answer_id_list, project_id, subject_id, paper_code, ques_type_code, ques_code, stu_answer, mark_result,
#      mark_state) = query.model_dump().values()
#     logger.info(f"{user['username']} 获取智能阅卷的数据并进行状态初始化")
#     filter_condition = any(
#         [project_id, subject_id, paper_code, ques_type_code, ques_code, stu_answer, mark_result, mark_state])
#
#     if answer_id_list:
#         logger.info(f"{user['username']} 获取根据勾选的数据")
#         total = len(answer_id_list)
#     else:
#         if filter_condition:
#             logger.info(f"{user['username']} 获取根据查询条件获取智能阅卷的数据")
#             req_data = {
#                 "page_size": -1,
#                 "project_id": project_id,
#                 "subject_id": subject_id,
#                 "paper_code": paper_code,
#                 "ques_type_code": ques_type_code,
#                 "ques_code": ques_code,
#                 "stu_answer": stu_answer,
#                 "mark_result": mark_result,
#                 "mark_state": mark_state
#             }
#             token = f"Bearer {user['token']}"
#             url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/read_paper/get_read_paper"
#             res, msg = request_api(url, "POST", req_data, token, "智能阅卷的数据")
#             if res == 0:
#                 return BaseResponse(code=response_utils.server_error, msg=msg)
#             read_paper_list = res["data"]
#             total = res["total"]
#             if not read_paper_list:
#                 return BaseResponse(code=response_utils.no_field, msg="未找到待阅卷数据")
#             answer_id_list = [i["answer_id"] for i in read_paper_list]
#         else:
#             logger.info(f"{user['username']} 获取智能阅卷全部数据")
#             _, subject_condition = get_user_data_flag(new_session, user)
#             answer_id_list, paper_code_list = get_no_mark_group(new_session, subject_condition=subject_condition)
#             total = len(answer_id_list)
#             if not answer_id_list:
#                 return BaseResponse(code=response_utils.fields_exist, msg="作答信息已全部阅卷完成")
#
#     result, msg = check_mark_rule(new_session, answer_id_list)
#     if not result:
#         return BaseResponse(code=response_utils.no_field, msg=msg)
#
#     # 可乱序填空题需要将几个答案的 answer_id 组成一个列表传给阅卷线程
#     # 因为同一组答案都是一样的，所以取第一组进行评分即可
#     ooo_answer_id_list = [group_answer_id_list[0] for group_answer_id_list in answer_id_list]
#
#     # 初始化再次智能评分的作答信息的评分信息
#     all_answer_id_list = [i for sub_list in ooo_answer_id_list for i in sub_list]
#     same_answer_group_id_list = init_mark_state(new_session, all_answer_id_list, mark_state)
#
#     data = {
#         "ooo_answer_id_list": ooo_answer_id_list,
#         "same_answer_group_id_list": same_answer_group_id_list,
#         "total": total
#     }
#
#     return BaseResponse(data=data, msg="操作成功")


@read_paper_router.post(path="/start_read_paper", response_model=BaseResponse, summary="开启智能阅卷")
async def start_read_paper_api(query: StartReadPaperReq, user: Any = Depends(get_current_user)):
    ooo_answer_id_list, same_answer_group_id_list, total = query.model_dump().values()
    curr_user_id = user.get("user_id")
    logger.info(f"{user['username']} 开启智能阅卷")

    # 开启阅卷线程
    if ooo_answer_id_list:
        # print("ooo_answer_id_list", ooo_answer_id_list)
        threading.Thread(target=ooo_mark_thread_control, args=(ooo_answer_id_list, curr_user_id)).start()

    data = {
        "await_mark_item_id": same_answer_group_id_list,
        "total": total
    }
    return BaseResponse(data=data, msg="操作成功")


@read_paper_router.post(path="/get_read_percentage", response_model=BaseResponse, summary="获取阅卷进度")
async def get_read_percentage(query: GetReadPaperPercentageReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info("获取阅卷进度")
    _, subject_condition = get_user_data_flag(new_session, user)
    await_mark_item_id, total = query.await_mark_item_id, query.total
    curr_total = len(await_mark_item_id)
    # 获取选中的目标的阅卷进度
    finish_count = new_session.query(SameStuAnswerGroup.same_answer_group_id).where(
        and_(SameStuAnswerGroup.same_answer_group_id.in_(await_mark_item_id),
             SameStuAnswerGroup.mark_state.in_([2, 3, 4]))).count()
    percentage = round_half_up(finish_count / curr_total * 100, 2)
    percentage = percentage if percentage <= 100 else 100

    if percentage >= 100:
        data = {
            "curr_mark_count": finish_count,
            "total": total,
            "curr_total": curr_total,
            "execute_step": "处理完成",
            "percentage": percentage
        }
        return BaseResponse(msg="智能评分完成", data=data)

    data = {
        "curr_mark_count": finish_count,
        "total": total,
        "curr_total": curr_total,
        "execute_step": "智能评分",
        "percentage": percentage
    }
    return BaseResponse(msg="操作成功", data=data)


@read_paper_router.post(path="/profession_mark", response_model=BaseResponse, summary="专家评分")
async def profession_mark(query: ProfessionMarkReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 进行专家评分")
    (same_answer_group_id, paper_code, ques_id, profession_score_list, modify_reason, profession_parse,
     total_standard_score, ques_score_list) = query.model_dump().values()

    count = 0
    for ques_score, profession_score in zip(ques_score_list, profession_score_list):
        count += 1
        if count_decimal_places(profession_score, False) > 2:
            return BaseResponse(code=response_utils.params_error, msg="评分分数最多只能有两位小数")
        if float(profession_score) > float(ques_score):
            if len(ques_score_list) > 1:
                msg = f"第{count}空的评分分数{profession_score}大于该空的分数{ques_score}"
            else:
                msg = f"评分分数{profession_score}大于该题的分数{ques_score}"
            return BaseResponse(code=response_utils.params_error, msg=msg)

    if isinstance(total_standard_score, str):
        total_standard_score = float(total_standard_score)

    total_profession_score = sum_with_precision(profession_score_list)
    if total_profession_score <= 0:
        mark_result = 2
    elif 0 < total_profession_score < total_standard_score:
        mark_result = 3
    elif total_profession_score == total_standard_score:
        mark_result = 1
    else:
        return BaseResponse(code=response_utils.params_error, msg="专家评分分数大于试题分数")

    try:
        new_session.query(SameStuAnswerGroup) \
            .filter(SameStuAnswerGroup.same_answer_group_id == same_answer_group_id).update({
            SameStuAnswerGroup.mark_state: 2,
            SameStuAnswerGroup.mark_result: mark_result,
            SameStuAnswerGroup.stu_score: total_profession_score,
            SameStuAnswerGroup.profession_parse: profession_parse,
            SameStuAnswerGroup.modify_reason: modify_reason,
            SameStuAnswerGroup.u_user_id: user.get("user_id")
        })
        for index, profession_score in enumerate(profession_score_list):
            new_session.query(StuAnswer).filter(
                and_(StuAnswer.same_answer_group_id == same_answer_group_id, StuAnswer.paper_code == paper_code,
                     StuAnswer.ques_id == ques_id, StuAnswer.small_ques_order == index + 1)).update({
                StuAnswer.mark_state: 2,
                StuAnswer.mark_result: mark_result,
                StuAnswer.stu_score: profession_score,
                StuAnswer.profession_parse: profession_parse,
                StuAnswer.modify_reason: modify_reason,
                StuAnswer.u_user_id: user.get("user_id")
            })
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"专家评分失败: {e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg="专家评分失败")
    logger.info(f"专家评分成功")
    return BaseResponse(msg="专家评分成功")
