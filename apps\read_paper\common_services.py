import json
import os
import time
import traceback
import re
from decimal import Decimal

from bs4 import BeautifulSoup
from typing import Optional, Union

from factory_apps import session_depend
from factory_apps.mysql_db.databases import ddl_session
from settings import logger

from apps.base.services import request_api
from apps.models.models import ExamQuestion, ExamPaper, StuAnswer, MarkRule, ExamStudent, Project, Subject, StudentSubject, SameStuAnswerGroup, QuesType, UserInfo, StuTraj, PaperDetail
from sqlalchemy import and_, func, text

from apps.permission.services import get_user_selected_data_flag, get_data_permission_sub_id_list
from helper.rich_text_utils import parse_html_entities
from settings import configs
from utils.utils import base64_to_image, round_half_up, sum_with_precision, create_file_base64


def check_rule_ques_type_code(new_session, mark_rule_id, paper_ques_type_code):
    """
    检查评分规则的题型是否包含了试卷的所有题型
    """
    # 获取评分规则的题型
    mark_ques_type_code = new_session.query(MarkRule.include_ques_type_code).filter(MarkRule.rule_id == mark_rule_id).scalar()

    mark_lack_code = [code for code in paper_ques_type_code if code not in mark_ques_type_code]
    if mark_lack_code:
        mark_lack_code_info = new_session.query(QuesType.ques_type_name).filter(QuesType.ques_type_code.in_(mark_lack_code)).all()
        mark_lack_code_zh = "，".join([i[0] for i in mark_lack_code_info])
        return False, f"导入的评分规则缺少 {mark_lack_code_zh} 题型的评分规则"
    return True, None


def get_order_max_length(ques_order_dict: dict):
    """
    获得最长的题号位数
    """
    max_order_length_dict = {}
    for paper_name in ques_order_dict:
        max_order_length_dict[paper_name] = 1
        ques_order_list = ques_order_dict[paper_name]
        if ques_order_list:
            max_order_len_value = max(ques_order_list, key=len)
            max_order_length = len(max_order_len_value)
            max_order_length_dict[paper_name] = max_order_length
    return max_order_length_dict


def clean_and_pad_order(order: str, max_length: int):
    """
    将试卷的试题序号统一
    比如 1 转为为 01
        2（1）转化为 02（1）
    """
    replace_order = order.replace("(", "")
    # 使用正则表达式提取括号外的数字部分
    match = re.match(r"(\d+)(（.*?）)", replace_order)
    if match:
        number_part = match.group(1)
        bracket_part = match.group(2)
        # 将数字部分调整为长度为题号最大长度并在左边补0
        padded_number = number_part.zfill(max_length)
        # 拼接调整后的数字部分和原始的括号内容
        return f"{padded_number}{bracket_part}"
    else:
        # 如果不符合预期格式，直接返回原字符串
        return order.zfill(max_length)


def get_ques_score_list(ques_score, total_weight, space_weight_list):
    """
    计算填空题每一空的得分
    """
    round_score = [(round_half_up((ques_score / total_weight * float(space_weight)), 2)) for space_weight in space_weight_list]

    total_score = sum_with_precision(round_score)

    if total_score != ques_score:
        # Decimal(str()) 可以解决计算机由于使用二进制表示浮点数而导致计算出现的误差
        front_score = sum_with_precision(round_score[:-1])
        round_score[-1] = Decimal(str(ques_score)) - Decimal(str(front_score))

    score = [str(i) for i in round_score]
    return score


def parse_paper_template(all_rows: list, project_id: str, subject_id: str, ques_code_id_dict: dict, max_order_length_dict: dict, mark_rule_id: str, remark: Optional[str], f_ques_score: dict, user_id: str):
    """
    解析试卷，生成可入库的格式化试题数据和试卷数据
    :param all_rows: 模板的所有的行数据
    :param project_id: 项目id
    :param subject_id: 科目id
    :param ques_code_id_dict: 已导入的所有试题编号对应的试题 id 字段
    :param max_order_length_dict: 每套试卷最长的试题序号位数
    :param mark_rule_id: 评分规则id
    :param remark: 备注
    :param f_ques_score: 组合题整道题分数
    :param user_id: 用户id
    :return: flag: bool, msg: str, data: [], paper_score_dict: dict, paper_code_dict: dict
    """
    data = []
    count = 0
    try:
        # 获取所有的试题编号，模板的试题编号已存在该题直接跳过
        all_ques_code_list = list(ques_code_id_dict.keys())

        f_ques_info = {}
        paper_code_dict = {}
        paper_score_dict = {}
        paper_detail_list = []
        # paper_detail_list = {
        #     paper_name: [
        #         [paper_detail_id, paper_id, paper_code, parent_ques_id, ques_id, ques_code, ques_order, ques_score_list, 1]
        #     ]
        # }

        for row in all_rows[1:]:
            count += 1
            row = list(row)
            if not any(row):
                continue

            (paper_name, ques_code, subject_name, ques_order, ques_type_code, ques_desc, ques_difficulty, standard_answer_str, standard_parse, d_out_of_order_group, weight, ques_mark_point, e_mark_rule) = (
                row[0],
                row[1],
                row[2],
                str(row[3]),
                row[4],
                row[5],
                row[8],
                row[9],
                row[10],
                row[11],
                row[12],
                row[13],
                row[14],
            )

            # 生成试卷编号
            if paper_name not in paper_code_dict:
                paper_code_dict[paper_name] = configs.snow_worker.get_id()
                paper_score_dict[paper_name] = 0

            if not ques_type_code:
                return False, f"第 {count + 1} 行题型不能为空", data, paper_score_dict, {}, paper_detail_list

            ques_desc_text = row[5]
            if not ques_desc_text:
                return False, f"第 {count + 1} 行试题描述不能为空", data, paper_score_dict, {}, paper_detail_list
            # 去除 html，只保留纯文本
            text, _ = parse_html_entities(ques_desc_text)
            ques_desc = {"text": text}

            standard_choices_code = []
            parent_ques_id = None
            if ques_type_code != "F":
                choices = row[6] if row[6] else []
                if not row[7]:
                    return False, f"第 {count + 1} 行试题分数不能为空", data, paper_score_dict, {}, paper_detail_list
                ques_score = float(row[7])
                paper_score_dict[paper_name] += ques_score
                # 计算每空分数
                if ques_type_code == "D":
                    if not weight:
                        return False, f"第 {count + 1} 行该填空题权重不能为空", data, paper_score_dict, {}, paper_detail_list
                    # 比号和中文冒号都替换为英文冒号，中文逗号替换为英文冒号
                    weight = weight.replace("∶", ",").replace("：", ",").replace(":", ",").replace("，", ",")
                    try:
                        weight = json.loads(weight)
                    except:
                        return False, f"第 {count + 1} 行权重格式有误", data, paper_score_dict, {}, paper_detail_list
                    if len(weight) != 2:
                        return False, f"第 {count + 1} 行权重格式有误", data, paper_score_dict, {}, paper_detail_list
                    space_weight_list, total_weight = weight[0], float(weight[1][0])
                    if total_weight <= 0:
                        return False, f"第 {count + 1} 行权重格式有误，总权重不能小于或者等于 0", data, paper_score_dict, {}, paper_detail_list
                    score = get_ques_score_list(ques_score, total_weight, space_weight_list)
                else:
                    score = [str(ques_score)]
                    weight = []
                if ques_type_code == "E":
                    if standard_answer_str:
                        standard_answer = standard_answer_str.split(configs.NEW_SPLIT_FLAG)
                    else:
                        standard_answer = []
                else:
                    if not standard_answer_str:
                        return False, f"第 {count + 1} 行参考答案不能为空", data, paper_score_dict, {}, paper_detail_list
                    standard_answer = standard_answer_str.split(configs.NEW_SPLIT_FLAG)
                    if ques_type_code != "C" and len(score) != len(standard_answer):
                        return False, f"第 {count + 1} 行分数个数与参考答案个数不匹配", data, paper_score_dict, {}, paper_detail_list
                    if ques_type_code in ["A", "B", "C"]:
                        if not choices:
                            return False, f"第 {count + 1} 行缺少选项", data, paper_score_dict, {}, paper_detail_list
                        options_list = choices.split(configs.NEW_SPLIT_FLAG)
                        if ques_type_code == "B":
                            choices = [{"code": "0", "options": "错误"}, {"code": "1", "options": "正确"}]

                        else:
                            first_options = options_list[0].strip()
                            if (first_options[0] == "A" or first_options[0] == "a") and first_options[1] in ["．", ".", "、", ":", "；", " "]:
                                choices = [{"code": str(index + 1), "options": value.strip()} for index, value in enumerate(options_list)]
                            else:
                                choices = [{"code": str(index + 1), "options": f"{configs.LETTER_LIST[index]}.{value.strip()}"} for index, value in enumerate(options_list)]

                        if ques_type_code == "B":
                            standard_choices_code = standard_answer
                        else:
                            for answer in standard_answer:
                                for choice in choices:
                                    if choice["options"].startswith(answer):
                                        standard_choices_code.append(choice["code"])

                ques_mark_point_list = []
                if ques_mark_point:
                    mark_point_mix_list = ques_mark_point.split(configs.NEW_SPLIT_FLAG)
                    for i in mark_point_mix_list:
                        point_text = re.sub(r"（.*?）", "", i)
                        score_match = re.findall(r"（\w?(\d+)\w?）", i)
                        if len(score_match) == 0:
                            point_score = None
                        elif len(score_match) == 1:
                            point_score = float(score_match[0])
                        else:
                            return False, f"第 {count + 1} 行评分点格式不正确", data, paper_score_dict, {}, paper_detail_list
                        ques_mark_point_dict = {"point": point_text, "score": point_score}
                        ques_mark_point_list.append(ques_mark_point_dict)

                if d_out_of_order_group:
                    try:
                        d_out_of_order_group = json.loads(d_out_of_order_group)
                    except Exception as e:
                        logger.error(f"第 {count + 1} 行填空题答案乱序序号列表 该字段格式不正确：{e}")
                        return False, f"第 {count + 1} 行填空题答案乱序序号列表 该字段格式不正确", data, paper_score_dict, {}, paper_detail_list
                else:
                    d_out_of_order_group = []

                small_ques_num_list = re.findall(r"\d+[（(](\d+)[)）]", ques_order)
                small_ques_num = int(small_ques_num_list[0]) if small_ques_num_list else 0

                if ques_code in f_ques_info:
                    # 获取小题所属组合题的题号
                    parent_ques_order_list = re.findall(r"(\d+)[（(].*?[)）]", ques_order)
                    if parent_ques_order_list:
                        parent_ques_order = parent_ques_order_list[0]
                    else:
                        return False, f"第 {count + 1} 行试题序号格式不正确", data, paper_score_dict, {}, paper_detail_list
                    if f_ques_info[ques_code]["ques_order"] == parent_ques_order:
                        parent_ques_id = f_ques_info[ques_code]["ques_id"]

                pad_ques_order = clean_and_pad_order(ques_order, max_order_length_dict[paper_name])

                if ques_code not in all_ques_code_list:
                    ques_id = configs.snow_worker.get_id()
                    small_ques_num = small_ques_num if small_ques_num else None
                    ques_data = ExamQuestion(
                        ques_id=ques_id,
                        parent_ques_id=parent_ques_id,
                        ques_code=ques_code,
                        ques_type_code=ques_type_code,
                        small_ques_num=small_ques_num,
                        ques_desc=ques_desc,
                        ques_choices=choices,
                        ques_difficulty=ques_difficulty,
                        standard_answer=standard_answer,
                        standard_choices_code=standard_choices_code,
                        d_out_of_order_group=d_out_of_order_group,
                        weight=weight,
                        standard_parse=standard_parse,
                        e_mark_rule=e_mark_rule,
                        ques_mark_point=ques_mark_point_list,
                        c_user_id=user_id,
                    )
                    data.append(ques_data)
                else:
                    ques_id = ques_code_id_dict[ques_code][small_ques_num]

            else:
                # 如果是组合题试题材料
                score = [f_ques_score[ques_code]]
                pad_ques_order = clean_and_pad_order(ques_order, max_order_length_dict[paper_name])

                if ques_code not in all_ques_code_list:
                    ques_id = configs.snow_worker.get_id()
                    ques_data = ExamQuestion(ques_id=ques_id, ques_code=ques_code, ques_type_code=ques_type_code, ques_desc=ques_desc, ques_difficulty=ques_difficulty, e_mark_rule=e_mark_rule, c_user_id=user_id)
                    data.append(ques_data)
                else:
                    ques_id = ques_code_id_dict[ques_code][0]

                if ques_code not in f_ques_info:
                    f_ques_info[ques_code] = {"ques_id": ques_id, "ques_order": ques_order}

            # 生成试卷详情需要的数据
            paper_detail_item = [configs.snow_worker.get_id(), paper_code_dict[paper_name], paper_code_dict[paper_name], parent_ques_id, ques_id, ques_code, pad_ques_order, score, 1]
            paper_detail_list.append(paper_detail_item)

        logger.info("解析试题信息成功")

    except Exception as e:
        logger.error(f"试卷解析失败，导入的文件第 {count + 1} 行数据不正确：{e}")
        traceback.print_exc()
        return False, f"导入的文件第 {count + 1} 行数据不正确", data, {}, {}, []

    logger.info("正在生成试卷...")
    for paper_name in list(paper_code_dict.keys()):
        exam_paper = ExamPaper(paper_id=paper_code_dict[paper_name], paper_code=paper_code_dict[paper_name], paper_name=paper_name, project_id=project_id, subject_id=subject_id, remark=remark, total_score=paper_score_dict[paper_name], mark_rule_id=mark_rule_id, save_grade=1, c_user_id=user_id)
        data.append(exam_paper)
    logger.info("生成试卷成功")

    return True, "", data, paper_score_dict, paper_code_dict, paper_detail_list


def create_paper_detail(paper_detail_list: list):
    """
    生成试卷详情
    """
    insert_data = []
    for paper_detail in paper_detail_list:
        detail_item = PaperDetail(paper_detail_id=paper_detail[0], paper_id=paper_detail[1], paper_code=paper_detail[2], parent_ques_id=paper_detail[3], ques_id=paper_detail[4], ques_code=paper_detail[5], ques_order=paper_detail[6], ques_score_list=paper_detail[7], manual_read_state=paper_detail[8])
        insert_data.append(detail_item)
    return insert_data


def paper_query_condition(project_id: Optional[str], subject_id: Optional[str], paper_name: Optional[str], c_name: Optional[str]):
    """
    拼凑查询条件
    :param project_id:
    :param subject_id:
    :param paper_name:
    :param c_name:
    :return:
    """
    project_query = ExamPaper.project_id == project_id if project_id else True
    subject_query = ExamPaper.subject_id == subject_id if subject_id else True
    paper_name_query = ExamPaper.paper_name.ilike(f"%{paper_name}%") if paper_name else True
    user_condition = UserInfo.name.ilike(f"%{c_name}%") if c_name else True

    condition = and_(project_query, subject_query, paper_name_query, user_condition)
    return condition


def format_info_by_ques_type(ques_list: list):
    """
    按照试题类型格式化数据
    :param ques_list: 试题列表
    :return:
    """
    transformed_questions = []
    small_ques_info = {}
    # 遍历试题列表，根据题型进行分组
    for question in ques_list:
        parent_ques_id = question["parent_ques_id"]
        if parent_ques_id:
            if parent_ques_id in small_ques_info:
                small_ques_info[parent_ques_id].append(question)
            else:
                small_ques_info[parent_ques_id] = [question]
            continue
        # 检查试题类型是否已经存在于转化后的列表中
        type_exists = False
        for item in transformed_questions:
            if item["type_code"] == question["ques_type_code"]:
                type_exists = True
                item["data"].append(question)
                item["score"] += question["total_score"]
                break
        # 如果试题类型不存在，则创建一个新的类型项
        if not type_exists:
            # if question.get("ques_score_list"):
            #     score = question["ques_score_list"][0].replace(".00", "")
            # else:
            #     score = "未知"

            transformed_questions.append({"type": question["ques_type_name"], "type_code": question["ques_type_code"], "data": [question], "score": question["total_score"]})

    for ques_item in transformed_questions:
        ques_type = ques_item["type_code"]
        if ques_type == "F":
            ques_data_list = ques_item["data"]
            for single_ques in ques_data_list:
                single_ques_id = single_ques["ques_id"]
                if single_ques_id in small_ques_info:
                    single_ques["children"] = small_ques_info[single_ques_id]

    # 打印转化后的试题列表
    # print(transformed_questions)
    return transformed_questions


def transform_mark_state(mark_state):
    # 0 或 None 表示未评分，1 表示评分成功，2 表示评分失败，3 表示作答答案待人工判断
    if mark_state == 1:
        mark_state = "未评分"
    elif mark_state == 2:
        mark_state = "评分成功"
    elif mark_state == 3:
        mark_state = "评分失败"
    elif mark_state == 4:
        mark_state = "待人工判断"
    else:
        mark_state = "未知"
    return mark_state


def stu_answer_query_condition(project_id: Optional[str], subject_id: Optional[str], ques_type_code: Optional[str]) -> Union[str, bool]:
    project_query = StuAnswer.project_id == project_id if project_id else True
    subject_query = StuAnswer.subject_id == subject_id if subject_id else True
    ques_type_query = StuAnswer.ques_type_code == ques_type_code if ques_type_code else True

    condition = and_(ques_type_query, subject_query, project_query)
    return condition


def read_paper_query_condition(
    new_session,
    project_id: Optional[str],
    subject_id: Optional[str],
    paper_code: Optional[str],
    ques_type_code: Optional[str],
    ques_code: Optional[str],
    ques_order: Optional[str],
    same_answer_group_id: Optional[str],
    stu_answer: Optional[str],
    mark_result: Optional[int],
    mark_state: list,
    stu_score_range: list,
    search_time: list,
):
    project_query = SameStuAnswerGroup.project_id == project_id if project_id else True
    subject_query = SameStuAnswerGroup.subject_id == subject_id if subject_id else True
    paper_query = SameStuAnswerGroup.paper_code == paper_code if paper_code else True
    ques_code_query = SameStuAnswerGroup.ques_code.ilike(f"%{ques_code}%") if ques_code else True
    group_id_query = SameStuAnswerGroup.same_answer_group_id == same_answer_group_id if same_answer_group_id else True
    stu_answer_query = SameStuAnswerGroup.stu_answer.ilike(f"%{stu_answer}%") if stu_answer else True
    mark_result_query = SameStuAnswerGroup.mark_result == mark_result if mark_result else True
    mark_state_query = SameStuAnswerGroup.mark_state.in_(mark_state) if mark_state else True
    score_range_query = SameStuAnswerGroup.stu_score.between(*stu_score_range) if stu_score_range else True
    time_query = and_(SameStuAnswerGroup.mark_state != 1, SameStuAnswerGroup.mark_state != 5, SameStuAnswerGroup.updated_time.between(*search_time)) if search_time else True

    ques_order_query = True
    if ques_order:
        ques_id_info = new_session.query(PaperDetail.ques_id).filter(PaperDetail.ques_order == ques_order).all()
        ques_id_list = [i[0] for i in ques_id_info] if ques_id_info else []
        ques_order_query = SameStuAnswerGroup.ques_id.in_(ques_id_list) if ques_id_list else True

    if ques_type_code:
        if ques_type_code == "F":
            ques_type_query = SameStuAnswerGroup.parent_ques_id.isnot(None)
        else:
            ques_type_query = SameStuAnswerGroup.ques_type_code == ques_type_code
    else:
        ques_type_query = True

    condition = and_(project_query, subject_query, paper_query, ques_code_query, ques_order_query, group_id_query, stu_answer_query, mark_result_query, mark_state_query, score_range_query, ques_type_query, time_query)
    return condition


def mark_detail_query_condition(same_answer_group_id: str, paper_code: str, ques_code: str):
    project_query = SameStuAnswerGroup.same_answer_group_id == same_answer_group_id
    paper_query = SameStuAnswerGroup.paper_code == paper_code
    ques_code_query = SameStuAnswerGroup.ques_code == ques_code

    condition = and_(project_query, paper_query, ques_code_query)
    return condition


def mark_rule_query_condition(project_id: Optional[str], subject_id: Optional[str], rule_year: Optional[int]) -> (Union[str, bool], Union[str, bool]):
    project_query = MarkRule.project_id == project_id if project_id else True
    subject_query = MarkRule.subject_id == subject_id if subject_id else True
    year_query = MarkRule.rule_year == rule_year if rule_year else True

    condition = and_(project_query, subject_query, year_query)
    return condition


def student_query_condition(project_id, subject_id, allow_exam_num, exam_province_code, exam_city_code, exam_point_code, exam_room_code):
    project_id_query = ExamStudent.project_id == project_id if project_id else True
    subject_id_query = StudentSubject.subject_id == subject_id if subject_id else True
    exam_province_code = ExamStudent.exam_province_code == exam_province_code if exam_province_code else True
    exam_city_code = ExamStudent.exam_city_code == exam_city_code if exam_city_code else True
    exam_point_query = ExamStudent.exam_point_code == exam_point_code if exam_point_code else True
    exam_room_query = ExamStudent.exam_room_code == exam_room_code if exam_room_code else True
    allow_exam_num_query = ExamStudent.allow_exam_num.ilike(f"%{allow_exam_num}%") if allow_exam_num else True

    condition = and_(project_id_query, subject_id_query, allow_exam_num_query, exam_province_code, exam_city_code, exam_point_query, exam_room_query)
    return condition


def get_user_data_flag(new_session, user):
    """
    根据token里的用户信息获取用户对应的数据权限查询条件
    """
    user_project_id, user_subject_id = get_user_selected_data_flag(new_session, user["user_id"], user["role"])
    project_condition = Project.project_id.in_(user_project_id)
    subject_condition = Subject.subject_id.in_(user_subject_id)
    # return project_condition, subject_condition
    return True, True

def get_project_data_id(new_session, user):
    user_project_id, user_subject_id = get_user_selected_data_flag(new_session, user["user_id"], user["role"])
    subject_condition = Subject.project_id.in_(user_project_id)
    # return subject_condition
    return True

def get_sub_data_id(new_session, user):
    user_subject_ids = get_data_permission_sub_id_list(new_session, user["user_id"])
    subject_condition = Subject.subject_id.in_(user_subject_ids)
    # return subject_condition
    return True


def get_project_subject_info(new_session):
    """
    获取所有项目和科目的信息并格式化
    """
    # 格式化后的数据
    # data = {
    #     "高中": {
    #         "project_id": 1,
    #         "subject": {
    #             "生物": 1,
    #             "地理": 2
    #         }
    #     }
    # }
    # 获取所有项目及其科目
    pro_sub_data = new_session.query(Project.project_id, Project.project_name, func.group_concat(Subject.subject_id), func.group_concat(Subject.subject_name)).outerjoin(Subject, Subject.project_id == Project.project_id).group_by(Project.project_id, Project.project_name).all()
    if not pro_sub_data:
        return {}
    format_data = {}
    for row in pro_sub_data:
        project_id, project_name, subject_id_str, subject_name_str = row[0], row[1], row[2], row[3]
        format_data[project_name] = {"project_id": project_id, "subject": {}}
        if subject_id_str:
            for subject_id, subject_name in zip(subject_id_str.split(","), subject_name_str.split(",")):
                format_data[project_name]["subject"][subject_name] = subject_id
    return format_data


def get_project_id_subject_id_info(new_session):
    """
    获取所有项目和科目的信息并格式化
    """
    # 格式化后的数据
    # data = {
    #     "123": {
    #         "project_name": "高中",
    #         "subject": {
    #             "1": "生物",
    #             "2": "地理"
    #         }
    #     }
    # }
    # 获取所有项目及其科目
    pro_sub_data = new_session.query(Project.project_id, Project.project_name, func.group_concat(Subject.subject_id), func.group_concat(Subject.subject_name)).outerjoin(Subject, Subject.project_id == Project.project_id).group_by(Project.project_id, Project.project_name).all()
    if not pro_sub_data:
        return {}
    format_data = {}
    for row in pro_sub_data:
        project_id, project_name, subject_id_str, subject_name_str = row[0], row[1], row[2], row[3]
        format_data[project_id] = {"project_name": project_name, "subject": {}}
        if subject_id_str:
            for subject_id, subject_name in zip(subject_id_str.split(","), subject_name_str.split(",")):
                format_data[project_id]["subject"][subject_id] = subject_name
    return format_data


def check_project_subject_exist(new_sheet, format_data):
    """
    检查导入考生时的项目和科目是否存在
    """
    project_id_list = []
    try:
        index = 0
        for row in new_sheet.iter_rows(min_col=1, max_col=3, values_only=True):
            index += 1
            if index == 1:
                continue
            if not any(row):
                continue
            project_name, subject_name_str = row[1], row[2]
            if not project_name:
                return f"第 {index + 1} 行的项目不能为空", project_id_list
            if not subject_name_str:
                return f"第 {index + 1} 行的科目不能为空", project_id_list

            if not format_data.get(project_name):
                return f"系统暂无第 {index + 1} 行 {project_name} 该项目", project_id_list

            project_id = format_data.get(project_name)["project_id"]
            if project_id not in project_id_list:
                project_id_list.append(project_id)

            subject_name_list = subject_name_str.split(configs.NEW_SPLIT_FLAG)
            for subject_name in subject_name_list:
                if not format_data[project_name]["subject"].get(subject_name):
                    return f"{project_name} 该项目下暂无第 {index + 1} 行 {subject_name} 该科目", project_id_list
    except IndexError:
        traceback.print_exc()
        return "导入的文件格式不符合模板标准", project_id_list
    return None, project_id_list


def update_stu_subject(new_session, raw_subject_id_list: list, subject_id_list: list, allow_exam_num: str, c_user_id: str) -> (bool, str):
    """
    更新考生科目
    """
    if set(raw_subject_id_list) == set(subject_id_list):
        return True, "所选科目无更新"

    # 新增的科目插入
    insert_data = []
    for subject_id in subject_id_list:
        if subject_id not in raw_subject_id_list:
            new_subject_data = StudentSubject(stu_subject_id=configs.snow_worker.get_id(), allow_exam_num=allow_exam_num, subject_id=subject_id, c_user_id=c_user_id)
            insert_data.append(new_subject_data)

    if insert_data:
        new_session.add_all(insert_data)

    # # 减少的科目删除
    # delete_data = []
    # for subject_id in raw_subject_id_list:
    #     if subject_id not in subject_id_list:
    #         delete_data.append(subject_id)
    #
    # if delete_data:
    #     new_session.query(StudentSubject).filter(
    #         and_(StudentSubject.allow_exam_num == allow_exam_num, StudentSubject.subject_id.in_(delete_data))).delete()
    return True, "更新考生科目成功"


def download_ques_image(img_src_list):
    relative_file_path_list = []
    relative_path = configs.QUES_IMAGE_PATH
    absolute_path = os.path.join(configs.PROJECT_PATH, relative_path)
    os.makedirs(absolute_path, exist_ok=True)
    for index, img_src in enumerate(img_src_list):
        if relative_path in img_src:
            # 如果前端传的是链接，直接保存起来
            relative_file_path_list.append(img_src)
        else:
            # 如果前端传的是base64，下载图片后将src替换成图片路径
            image, image_format = base64_to_image(img_src)
            file_name = f"{configs.snow_worker.get_id()}.{image_format}"
            save_file_name = os.path.join(absolute_path, file_name).replace("\\", "/")
            image.save(save_file_name)
            logger.info(f"图片保存路径为: {save_file_name}")
            relative_file_path = os.path.join(relative_path, file_name).replace("\\", "/")
            relative_file_path_list.append(relative_file_path)
    return relative_file_path_list


def instead_src_to_path(html, path_list):
    soup = BeautifulSoup(html, "lxml")
    img_tags = soup.find_all("img")
    # 修改 src 属性
    for img_tag, path in zip(img_tags, path_list):
        img_tag["src"] = path
    new_html = soup.prettify()
    # print("new_html", new_html)
    return new_html


def splice_image_path(ques_desc, ques_choices):
    """
    拼接试题描述和试题选项的图片路径
    <img crossorigin=\"anonymous\" 是前端要求添加的
    """
    desc_html = ques_desc.get("html")
    if desc_html:
        if "data:image" in desc_html:
            if "<table " in desc_html:
                ques_desc["html"] = desc_html.replace("/File/Download?id=", configs.QUES_IMAGE_PATH).replace("<img ", '<img crossorigin="anonymous" ').replace("color: rgb(0, 0, 0); ", "").replace("<table ", '<table border="1"')
            else:
                ques_desc["html"] = desc_html.replace("/File/Download?id=", configs.QUES_IMAGE_PATH).replace("<img ", '<img crossorigin="anonymous" ').replace("color: rgb(0, 0, 0); ", "")
        else:
            ques_desc["html"] = desc_html.replace("/File/Download?id=", configs.QUES_IMAGE_PATH).replace("//", "/").replace("<img ", '<img crossorigin="anonymous" ').replace("color: rgb(0, 0, 0); ", "")
    for choice in ques_choices:
        choice_html = choice.get("html")
        if choice_html:
            if "data:image" in choice_html:
                if "<table " in desc_html:
                    choice["html"] = desc_html.replace("/File/Download?id=", configs.QUES_IMAGE_PATH).replace("<img ", '<img crossorigin="anonymous" ').replace("color: rgb(0, 0, 0); ", "").replace("<table ", '<table border="1"')
                else:
                    choice["html"] = choice_html.replace("/File/Download?id=", configs.QUES_IMAGE_PATH).replace("<img ", '<img crossorigin="anonymous" ').replace("color: rgb(0, 0, 0); ", "")
            else:
                choice["html"] = choice_html.replace("/File/Download?id=", configs.QUES_IMAGE_PATH).replace("//", "/").replace("<img ", '<img crossorigin="anonymous" ').replace("color: rgb(0, 0, 0); ", "")
    return ques_desc, ques_choices


def splice_table_style(html):
    """
    处理表格样式
    """
    if html and "<table " in html:
        html = html.replace("<table ", '<table border="1"')
    return html


def clean_ques_text(ques_text: str):
    """
    清洗发送给 AI 的试题描述数据
    """
    raw_ques_text = ques_text.strip().replace("\n", "").replace("\t", "").replace(" ", "")
    new_ques_text = re.sub(r"（\d+分）", "", raw_ques_text)
    return new_ques_text


def struct_ques_with_img(ques_material: Optional[dict], ques_desc: Optional[dict]) -> dict:
    """
    将试题的信息整合起来，在图片位置使用占位符，并提取出图片的路径转化为 base64 存起来
    """
    final_ques_dict = {"ques_text": "", "img_base64_list": []}

    img_src_list = []

    ques_material_html, ques_desc_html, text = "", "", ""
    if ques_material:
        ques_material_html = ques_material.get("html") if ques_material.get("html") else ques_material.get("text", "")
        ques_material_text = ques_material.get("text", "")
        if ques_material_text and ques_material_text[-1] not in [".", "。", "?", "？"]:
            ques_material_text = ques_material_text + "。"
        text += ques_material_text
    if ques_desc:
        ques_desc_html = ques_desc.get("html") if ques_desc.get("html") else ques_desc.get("text", "")
        text += ques_desc.get("text", "")
    merge_html = {"html": ques_material_html + ques_desc_html}
    splice_desc, _ = splice_image_path(merge_html, [])

    html = splice_desc.get("html")
    _, img_list = parse_html_entities(html)
    img_src_list.extend([f"{os.path.join(configs.PROJECT_PATH, i)}" for i in img_list])

    # 用占位符标志图片所在位置
    # sub_img_material = re.sub(r"<img crossorigin=\".*?\"", f"{configs.MATERIAL_PLACEHOLDER}★✦★", html)
    # soup = BeautifulSoup(sub_img_material, "html.parser")
    # text = soup.text
    # if text:
    #     text = text.strip("\n").strip(" ").replace("\n", "").replace("\t", "")
    #     text = re.sub(r"★✦★.*?>", "", text)
    # else:
    #     text = ""

    final_ques_dict["ques_text"] = clean_ques_text(text)
    for src in img_src_list:
        try:
            src_base64 = create_file_base64(src)
            final_ques_dict["img_base64_list"].append(src_base64)
        except FileNotFoundError:
            logger.warning(f"{src} 路径未找到")
    # print("final_ques_dict", final_ques_dict)
    return final_ques_dict


def get_saved_stu_info(project_id_list, user):
    """
    获取已经导入过的考生数据
    """
    # stu_info = {
    #     "project_id(xx)": {
    #         "allow_exam_num(xx)": {}
    #     }
    # }
    data = {"project_id_list": project_id_list}
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/exam_stu/get_stu_with_all_subject"
    res, msg = request_api(url, "POST", data, token, "已保存考生信息")
    if res == 0:
        return False, []
    stu_data = res["data"]
    stu_info_dict = {}
    for stu in stu_data:
        project_id = stu["project_id"]
        if project_id not in stu_info_dict:
            stu_info_dict[project_id] = {stu["allow_exam_num"]: stu}
        else:
            stu_info_dict[project_id][stu["allow_exam_num"]] = stu
    return True, stu_info_dict


def del_excel_empty_row(sheet):
    """
    删除传入的工作表中的空行，并返回删除后的工作表和最大行数。
    参数: sheet 要处理的工作表
    返回:
    sheet: 删除空行后的工作表
    max_row (int): 删除空行后的最大行数
    """
    # 记录需要删除的空行
    rows_to_delete = []

    # 遍历工作表中的所有行
    for row in sheet.iter_rows():
        if all(cell.value is None for cell in row):  # 如果整行的所有单元格都为空
            rows_to_delete.append(row[0].row)  # 记录空行的行号

    # 反向删除空行，避免行号混乱
    for row_index in reversed(rows_to_delete):
        sheet.delete_rows(row_index)

    # 返回删除空行后的工作表和最大行数
    return sheet, sheet.max_row


def stu_answer_condition(project_id: Optional[str] = None, subject_id: Optional[str] = None, paper_code: Optional[str] = None, allow_exam_num: Optional[str] = None, stu_secret_num: Optional[str] = None):
    project_query = StuAnswer.project_id == project_id if project_id else True
    subject_query = StuAnswer.subject_id == subject_id if subject_id else True
    paper_query = StuAnswer.paper_code == paper_code if paper_code else True
    allow_num_query = StuAnswer.allow_exam_num == allow_exam_num if allow_exam_num else True
    secret_num_query = ExamStudent.stu_secret_num == stu_secret_num if stu_secret_num else True

    condition = and_(project_query, subject_query, paper_query, allow_num_query, secret_num_query)
    return condition


def stu_answer_display_condition(new_session, project_id: Optional[str] = None, subject_id: Optional[str] = None, paper_id: Optional[str] = None, allow_exam_num: Optional[str] = None, stu_secret_num: Optional[str] = None):
    project_query = StuTraj.project_id == project_id if project_id else True
    subject_query = StuTraj.subject_id == subject_id if subject_id else True
    allow_num_query = StuTraj.allow_exam_num.ilike(f"%{allow_exam_num}%") if allow_exam_num else True
    secret_num_query = True
    if stu_secret_num:
        allow_exam_num_info = new_session.query(ExamStudent.allow_exam_num).filter(ExamStudent.stu_secret_num.ilike(f"%{stu_secret_num}%")).all()
        allow_exam_num_list = [i[0] for i in allow_exam_num_info] if allow_exam_num_info else []
        secret_num_query = StuTraj.allow_exam_num.in_(allow_exam_num_list) if allow_exam_num_list else True
    paper_query = StuTraj.paper_id == paper_id if paper_id else True

    condition = and_(project_query, subject_query, allow_num_query, secret_num_query, paper_query)
    return condition


def init_mark_state(new_session, all_answer_id_list: list, mark_state: list):
    """
    分批初始化再次智能评分的作答信息的评分信息
    """
    logger.info("初始化开始")
    all_same_answer_group_id_list = []
    count = 0
    all_answer_id_list_length = len(all_answer_id_list)
    a_batch_num = configs.AI_INIT_COUNT
    answer_id_list = []
    for answer_id in all_answer_id_list:
        count += 1
        answer_id_list.append(answer_id)
        if count % a_batch_num == 0 or count == all_answer_id_list_length:
            if mark_state != [1]:
                new_session.query(StuAnswer).filter(StuAnswer.answer_id.in_(answer_id_list)).update({StuAnswer.mark_state: 1, StuAnswer.answer_parse: [], StuAnswer.mark_result: 4, StuAnswer.mark_fail_reason: None, StuAnswer.profession_parse: [], StuAnswer.retry_count: None, StuAnswer.stu_score: None, StuAnswer.modify_reason: None})

            same_answer_group_id_set = set()
            same_answer_group_info = new_session.query(StuAnswer.same_answer_group_id).filter(StuAnswer.answer_id.in_(answer_id_list)).all()
            if same_answer_group_info:
                for i in same_answer_group_info:
                    same_answer_group_id_set.add(i[0])
            same_answer_group_id_list = list(same_answer_group_id_set)

            if same_answer_group_id_list:
                all_same_answer_group_id_list.extend(same_answer_group_id_list)
                if mark_state != [1]:
                    new_session.query(SameStuAnswerGroup).filter(SameStuAnswerGroup.same_answer_group_id.in_(same_answer_group_id_list)).update(
                        {
                            SameStuAnswerGroup.mark_state: 1,
                            SameStuAnswerGroup.answer_parse: None,
                            SameStuAnswerGroup.mark_result: 4,
                            SameStuAnswerGroup.mark_fail_reason: None,
                            SameStuAnswerGroup.profession_parse: [],
                            SameStuAnswerGroup.retry_count: 0,
                            SameStuAnswerGroup.stu_score: None,
                            SameStuAnswerGroup.modify_reason: None,
                        }
                    )
            new_session.commit()
            answer_id_list = []
    logger.info("初始化成功")
    return list(set(all_same_answer_group_id_list))


def get_history_answer(new_session, paper_code_list):
    """
    加载作答数据到内存里，方便后面判断作答信息是否重复
    """
    # history_answer_dict = {
    #     allow_exam_num: [paper_code]
    # }
    history_answer_dict = {}
    stu_allow_num_paper_info = new_session.query(StuAnswer.allow_exam_num, func.group_concat(StuAnswer.paper_code.distinct())).filter(StuAnswer.paper_code.in_(paper_code_list)).group_by(StuAnswer.allow_exam_num).all()

    if stu_allow_num_paper_info:
        for allow_exam_num, paper_code_str in stu_allow_num_paper_info:
            history_answer_dict[allow_exam_num] = paper_code_str.split(",") if paper_code_str else []
    return history_answer_dict


def delete_same_answer_group(new_session):
    """
    删除多余的 same_answer_group
    """
    delete_same_group_info = new_session.query(SameStuAnswerGroup.same_answer_group_id).outerjoin(StuAnswer, StuAnswer.same_answer_group_id == SameStuAnswerGroup.same_answer_group_id).filter(StuAnswer.same_answer_group_id.is_(None)).all()

    if delete_same_group_info:
        delete_same_group_id = [i[0] for i in delete_same_group_info]
        new_session.query(SameStuAnswerGroup).filter(SameStuAnswerGroup.same_answer_group_id.in_(delete_same_group_id)).delete()


def is_all_punctuation(s: Union[str, list]):
    """
    判断字符串或者列表是否全部字符都是字符串
    """
    if type(s) == list:
        s = "".join(s)
    return all(char in configs.ALL_PUNCTUATION for char in s)

def batch_create_partition_session(new_ddl_session, table_name_list, check_par_list):
    """
    检查表里是否存在这些分区，不存在则创建
    :param check_par_list: 值列表，如 ["20230820", "20230821"] 将创建 p20230820, p20230821 分区
    """
    for table_name in table_name_list:
        try:
            result = new_ddl_session.execute(text(f"EXPLAIN SELECT * FROM {table_name}")).fetchone()
            partitions = result[3].split(",") if (result and result[3]) else []
            
            # 收集需要创建的分区
            partitions_to_create = []
            for check_par in check_par_list:
                partition_name = f"p{check_par}"
                if partitions and partition_name not in partitions:
                    partitions_to_create.append(partition_name)
            
            # 批量创建分区
            if partitions_to_create:
                partition_clauses = ",\n    ".join(
                    f"PARTITION {name} VALUES IN ('{name[1:]}')" for name in partitions_to_create
                )
                # logger.info(f"{table_name} 表创建分区：{', '.join(partitions_to_create)}")
                sql = f"""
                    ALTER TABLE {table_name} ADD PARTITION (
                        {partition_clauses}
                    );
                """
                # 添加超时和错误处理
                new_ddl_session.execute(text(sql))
                new_ddl_session.commit()
                logger.info(f"{table_name} 表批量创建分区成功")
        except Exception as e:
            logger.error(f"创建分区失败 {table_name}: {str(e)}")
            # 如果分区已存在，记录日志但不中断程序
            if "already exists" in str(e).lower() or "partition" in str(e).lower():
                logger.info(f"分区已存在，跳过创建: {table_name}")
            else:
                # 对于其他错误，重新抛出异常
                raise


def toggle_partition_session(new_ddl_session, table_name_list, check_par_list):
    """
    检查表里是否存在这些分区，不存在则创建
    :param check_par_list: 值列表，如 ["20230820", "20230821"] 将创建 p20230820, p20230821 分区
    """
    for table_name in table_name_list:
        result = new_ddl_session.execute(text(f"EXPLAIN SELECT * FROM {table_name}")).fetchone()
        partitions = result[3].split(",") if (result and result[3]) else []
        
        for check_par in check_par_list:
            partition_name = f"p{check_par}"
            if partitions and partition_name not in partitions:
                logger.info(f"{table_name} 表创建分区：{partition_name}")
                sql = f"""
                    ALTER TABLE {table_name} ADD PARTITION (
                        PARTITION {partition_name} VALUES IN ('{check_par}')
                    );
                """
                new_ddl_session.execute(text(sql))
                new_ddl_session.commit()
                # final_result = new_ddl_session.execute(text(f"EXPLAIN SELECT * FROM {table_name}")).fetchone()
                # partitions = final_result[3].split(",") if (final_result and final_result[3]) else []
                # if partitions and partition_name in partitions:
                logger.info(f"{table_name} 表创建分区 {partition_name} 成功")


def toggle_partition(table_name_list, check_par):
    """
    检查表里是否存在该分区，不存在则创建
    """
    new_session = next(ddl_session())
    for table_name in table_name_list:
        result = new_session.execute(text(f"EXPLAIN SELECT * FROM {table_name}")).fetchone()
        partitions = result[3].split(",") if (result and result[3]) else []
        partition_name = f"p{check_par}"
        if partitions and partition_name not in partitions:
            logger.info(f"{table_name} 表创建分区：{partition_name}")
            sql = f"""
                ALTER TABLE {table_name} ADD PARTITION (
                    PARTITION {partition_name} VALUES IN ('{check_par}')
                );
            """
            new_session.execute(text(sql))
            new_session.commit()
            logger.info(f"{table_name} 表创建分区 {partition_name} 成功")


if __name__ == "__main__":
    # ques_desc = {
    #     "html": "<p style=\"line-height: 1.5em; font-family: 宋体; font-size: 16px;\"><span style=\"font-family: 宋体; font-size: 16px; color: rgb(0, 0, 0); line-height: 1.5em;\">用冷水洗红凤菜时，水不变色；用热水煮<span style=\"font-family: 宋体; font-size: 16px; line-height: 1.5em;\">红凤菜</span>时，汤汁变色。造成这种现象的主要原因是高温破坏了细胞的</span></p><p style=\"line-height: 1.5em; font-family: 宋体; font-size: 16px;\"><span style=\"font-family: 宋体; font-size: 16px; color: rgb(0, 0, 0); line-height: 1.5em;\"><img name=\"7b530fbb787b-9958\" zfplugin=\"zf_uploadimg\" zfattachfile=\"\" src=\"/File/Download?id=D280A47BA951C3EAF167EA4DE367315F.jpg\" style=\"max-width: 100%; max-height: 1000px; font-family: 宋体; font-size: 16px; line-height: 1.5em;\"/></span></p>",
    #     "text": "用冷水洗红凤菜时，水不变色；用热水煮红凤菜时，汤汁变色。造成这种现象的主要原因是高温破坏了细胞的"}
    # ques_choices = [{"code": "1",
    #                  "html": "A．<p style=\"line-height: 1.5em; font-family: 宋体; font-size: 16px;\"><span style=\"font-family: 宋体; font-size: 16px; color: rgb(0, 0, 0); line-height: 1.5em;\">移动玻片标本 &nbsp;&nbsp;&nbsp;&nbsp;</span></p>",
    #                  "options": "A．移动玻片标本"}, {"code": "2",
    #                                                 "html": "B．<p style=\"line-height: 1.5em; font-family: 宋体; font-size: 16px;\"><span style=\"font-family: 宋体; font-size: 16px; color: rgb(0, 0, 0); line-height: 1.5em;\">将视野调暗 &nbsp;&nbsp;</span></p>",
    #                                                 "options": "B．将视野调暗"}, {"code": "3",
    #                                                                              "html": "C．<p style=\"line-height: 1.5em; font-family: 宋体; font-size: 16px;\"><span style=\"font-family: 宋体; font-size: 16px; color: rgb(0, 0, 0); line-height: 1.5em;\">转动细准焦螺旋 &nbsp;</span></p>",
    #                                                                              "options": "C．转动细准焦螺旋"},
    #                 {"code": "4",
    #                  "html": "D．<p style=\"line-height: 1.5em; font-family: 宋体; font-size: 16px;\"><span style=\"font-family: 宋体; font-size: 16px; color: rgb(0, 0, 0); line-height: 1.5em;\">物镜由低倍镜转换成高倍镜<br style=\"font-family: 宋体; font-size: 16px; line-height: 1.5em;\"/></span></p>",
    #                  "options": "D．物镜由低倍镜转换成高倍镜"}]
    # splice_image_path(ques_desc, ques_choices)

    print(is_all_punctuation("，。、、、。。"))
