#获取两个字符串数组相同的部分
def get_common_elements(list1, list2):
	if len(list2) > len(list1):
		tem=list1
		list1=list2
		list2=tem
    
	common_elements = []
	for element in list1:
		if element in list2:
			common_elements.append(element)
	return common_elements

#获取两个字符串数组最大连续子串的长度 
def get_max_common_substring_length(list1, list2):
	max_length = 0
	for i in range(len(list1)):
		for j in range(len(list2)):
			length = 0
			while (i + length < len(list1) and j + length < len(list2) and list1[i + length] == list2[j + length]):
				length += 1
			if length > max_length:
				max_length = length
	return max_length


def calculate_similay(q1,q2):
    similay = 0
    if len(q1) == 0 or len(q2) ==0:
        return similay,[]
    
    common_str = get_common_elements(q1,q2)
    common_len=len(common_str)
    max = get_max_common_substring_length(q1, q2)
    similay=0.5*common_len/(len(q1)+len(q2) - common_len) + 0.5*max/min(len(q1),len(q2))
    return similay,common_str