import io
import traceback

import openpyxl
from fastapi import APIRouter, Depends, UploadFile, File

from apps.data_transfer.services import get_sub_pro_dict, get_pro_sub_dict
from apps.human_mark_group.services import auto_create_excel_human_group
from apps.permission.services import update_user_data_permission, add_user_data_permission
from settings import logger, configs
from typing import Any

from sqlalchemy.orm import Session

from apps.manual_read_paper.services import get_all_username_and_id_card, verify_manual_people, create_single_user, \
    verify_pro_sub
from apps.users.services import get_current_user, get_default_password
from apps.base.schemas import BaseResponse
from factory_apps.mysql_db.databases import session_depend
from helper import response_utils
from helper.token import get_hash_password
from apps.models.models import Project
from apps.human_mark_group.models import HumanMarkGroup, HumanGroupMember
manual_people_router = APIRouter()


@manual_people_router.post(path="/import_manual_people", response_model=BaseResponse, summary="导入阅卷人员信息")
async def import_manual_people(file: UploadFile = File(...), user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 导入阅卷人员信息")
    curr_user_id = user["user_id"]

    if not file.filename.endswith(".xlsx"):
        return BaseResponse(code=response_utils.params_error, msg="请上传 .xlsx 格式的文件")

    all_username, all_id_card, all_username_role, all_phone = get_all_username_and_id_card()

    # 查询默认密码并哈希加密
    result, msg, de_default_password = get_default_password()
    private_password = get_hash_password(de_default_password)

    pro_sub_id_dict = get_pro_sub_dict(new_session)

    content = await file.read()
    wb = openpyxl.load_workbook(filename=io.BytesIO(content), read_only=True)

    insert_data = []
    user_count = 0
    is_add_user_role = False

    # 需要创建的科目小组、题组、评阅小组
    create_groups = [[], [], []]

    group_members = {}

    # 轮次映射关系
    round_mapping = {
        "第一轮": 1,
        "第二轮": 2,
        "第三轮": 3,
        "第四轮": 4,
        "第五轮": 5,
        "第六轮": 6
    }

    try:
        # 遍历所有工作表，根据标题识别类型
        for sheet in wb.worksheets:
            sheet_title = sheet.title
            logger.info(f"正在处理 Sheet: {sheet_title}")
            all_rows = sheet.iter_rows(values_only=True)

            count = 0

            for row in all_rows:
                count += 1
                if count == 1:
                    continue
                row = list(row)
                if not any(row):
                    continue

                # 根据sheet标题确定处理逻辑
                if sheet_title == "评阅员":
                    # 评阅员模板处理逻辑
                    project_name, subject_name, username, name, phone, id_card, role_name, group_code, is_leader = row[0], row[1], row[2], row[3], row[4], row[5], row[6], row[7], row[8]
                    # name 姓名改成非必填项
                    if not all([project_name, subject_name, username, role_name]):   # any
                        return BaseResponse(code=response_utils.no_field, msg=f"{sheet_title} 第 {count} 行数据缺失，请补全数据")

                    # 数据校验
                    result, msg = verify_manual_people(username, phone, id_card, all_username, all_id_card, all_phone, True)
                    if not result:
                        return BaseResponse(code=response_utils.params_error, msg=f"{sheet_title} 第 {count} 行数据：{msg}")

                    # 资格是否存在校验
                    project_name_exist = new_session.query(Project.project_id).filter(Project.project_name == project_name).scalar()
                    if not project_name_exist:
                        return BaseResponse(code=response_utils.no_field, msg=f"{sheet_title} 第 {count} 行 {project_name} 资格不存在")

                    result, msg, project_id, subject_id = verify_pro_sub(new_session, project_name, subject_name, pro_sub_id_dict)
                    if not result:
                        return BaseResponse(code=response_utils.params_error, msg=msg)

                    # 根据角色名称确定角色ID和分组层级
                    if role_name in ("小组长","评阅员"):
                        role_id = "4" if role_name == "小组长" else "3"
                        group_level = 3
                    elif role_name == "题组长":
                        role_id = "5"
                        group_level = 2
                    else:
                        # 资格组长
                        role_id = "7"
                        group_level = 1

                    # 处理用户创建逻辑
                    create_user = True
                    if username in all_username:
                        if role_id not in all_username_role.get(username, []):
                            logger.info(f"{username} 账号已存在，角色不存在，跳过创建用户，创建角色")
                            create_user = False
                            is_add_user_role = True
                        else:
                            logger.info(f"{username} 账号和角色都已存在，跳过")
                            continue

                    all_username.append(username)
                    if id_card:
                        all_id_card.append(id_card)
                    if phone:
                        all_phone.append(phone)

                    user_data_list, user_count, create_user_id = create_single_user(username, name, phone, id_card, None, project_id, subject_id, user["user_id"], [role_id],
                                                                                    private_password, None, user_count, 2, 1, create_user)
                    if user_data_list:
                        insert_data.extend(user_data_list)

                    add_user_data_permission(new_session, create_user_id, [project_id], [subject_id])

                    # 添加小组成员
                    if not group_code:
                        continue
                    group_id = new_session.query(HumanMarkGroup.group_id).filter(HumanMarkGroup.group_code == group_code).scalar()
                    if not group_id:
                        return BaseResponse(code=response_utils.no_field, msg=f"{sheet_title} 第 {count} 行小组编号不存在")
                    else:
                        member_role = 1 if role_name in ("小组长","资格组长","题组长") else 2
                        member_list = []
                        if member_role == 2:
                            member = HumanGroupMember(member_id=configs.snow_worker.get_id(), group_id=group_id,
                                                  user_id=create_user_id, member_role=2)
                            member_list.append(member)
                        else:
                            member = HumanGroupMember(member_id=configs.snow_worker.get_id(), group_id=group_id,
                                                  user_id=create_user_id, member_role=1)
                            member_list.append(member)

                        if member_list:
                            new_session.add_all(member_list)

                    # new_session.commit()

                elif sheet_title == "验收员":
                    # 验收员模板处理逻辑
                    project_name, subject_name, username, name, phone, id_card, inspector_name = row[0], row[1], row[2], row[3], row[4], row[5], row[6]
                    # name 姓名改成非必填项
                    if not all([project_name, subject_name, username, inspector_name]):   # any
                        return BaseResponse(code=response_utils.no_field, msg=f"{sheet_title} 第 {count} 行数据缺失，请补全数据")

                    # 数据校验
                    result, msg = verify_manual_people(username, phone, id_card, all_username, all_id_card, all_phone, True)
                    if not result:
                        return BaseResponse(code=response_utils.params_error, msg=f"{sheet_title} 第 {count} 行数据：{msg}")

                    # 资格是否存在校验
                    project_name_exist = new_session.query(Project.project_id).filter(Project.project_name == project_name).scalar()
                    if not project_name_exist:
                        return BaseResponse(code=response_utils.no_field, msg=f"{sheet_title} 第 {count} 行 {project_name} 资格不存在")

                    result, msg, project_id, subject_id = verify_pro_sub(new_session, project_name, subject_name, pro_sub_id_dict)
                    if not result:
                        return BaseResponse(code=response_utils.params_error, msg=msg)

                    # 根据验收员类型确定角色ID
                    role_id = "10" if inspector_name == "技术验收员" else "11"   # 管理验收员
                    group_level = 0

                    # 处理用户创建逻辑
                    create_user = True
                    if username in all_username:
                        if role_id not in all_username_role.get(username, []):
                            logger.info(f"{username} 账号已存在，角色不存在，跳过创建用户，创建角色")
                            create_user = False
                            is_add_user_role = True
                        else:
                            logger.info(f"{username} 账号和角色都已存在，跳过")
                            continue

                    all_username.append(username)
                    if id_card:
                        all_id_card.append(id_card)
                    if phone:
                        all_phone.append(phone)

                    user_data_list, user_count, create_user_id = create_single_user(username, name, phone, id_card, None, project_id, subject_id, user["user_id"], [role_id],
                                                                                    private_password, None, user_count, 2, 1, create_user)
                    if user_data_list:
                        insert_data.extend(user_data_list)

                    add_user_data_permission(new_session, create_user_id, [project_id], [subject_id])

                elif sheet_title == "复评员":
                    # 复评员模板处理逻辑
                    project_name, subject_name, username, name, phone, id_card, round_count_name = row[0], row[1], row[2], row[3], row[4], row[5], row[6]
                    # name 姓名改成非必填项
                    if not all([project_name, subject_name, username, round_count_name]):   # any
                        return BaseResponse(code=response_utils.no_field, msg=f"{sheet_title} 第 {count} 行数据缺失，请补全数据")

                    # 数据校验
                    result, msg = verify_manual_people(username, phone, id_card, all_username, all_id_card, all_phone, True)
                    if not result:
                        return BaseResponse(code=response_utils.params_error, msg=f"{sheet_title} 第 {count} 行数据：{msg}")

                    # 资格是否存在校验
                    project_name_exist = new_session.query(Project.project_id).filter(Project.project_name == project_name).scalar()
                    if not project_name_exist:
                        return BaseResponse(code=response_utils.no_field, msg=f"{sheet_title} 第 {count} 行 {project_name} 资格不存在")

                    result, msg, project_id, subject_id = verify_pro_sub(new_session, project_name, subject_name, pro_sub_id_dict)
                    if not result:
                        return BaseResponse(code=response_utils.params_error, msg=msg)

                    # 转换轮次名称为数字
                    round_count = round_mapping.get(round_count_name)
                    role_id = "9"
                    group_level = 0

                    # 处理用户创建逻辑
                    create_user = True
                    if username in all_username:
                        if role_id not in all_username_role.get(username, []):
                            logger.info(f"{username} 账号已存在，角色不存在，跳过创建用户，创建角色")
                            create_user = False
                            is_add_user_role = True
                        else:
                            logger.info(f"{username} 账号和角色都已存在，跳过")
                            continue

                    all_username.append(username)
                    if id_card:
                        all_id_card.append(id_card)
                    if phone:
                        all_phone.append(phone)

                    user_data_list, user_count, create_user_id = create_single_user(username, name, phone, id_card, None, project_id, subject_id, user["user_id"], [role_id],
                                                                                    private_password, round_count, user_count, 2, 1, create_user)
                    if user_data_list:
                        insert_data.extend(user_data_list)

                    add_user_data_permission(new_session, create_user_id, [project_id], [subject_id])

                else:
                    # 不识别的sheet标题，跳过处理
                    return BaseResponse(code=response_utils.no_field, msg=f"{sheet_title} 模板命名不规范，请参照模板重新命名")
                    # logger.warning(f"未知的sheet标题: {sheet_title}，跳过处理")


        if insert_data:
            new_session.add_all(insert_data)
            new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"导入阅卷人员信息失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg=f"导入阅卷人员信息失败")
    logger.info("导入阅卷人员信息成功")

    # # 创建小组
    # print("create_groups", create_groups)
    # result, msg = auto_create_excel_human_group(new_session, create_groups, curr_user_id)
    # if result:
    #     logger.info("创建小组成功")
    # else:
    #     logger.error(msg)
    # # todo: 添加小组成员
    # # group_id, user_id_list, leader_user_id_list

    if user_count == 0:
        if is_add_user_role:
            return BaseResponse(msg="导入的阅卷人员信息都已存在，增加用户角色成功")
        else:
            return BaseResponse(msg="导入的阅卷人员信息都已存在")
    return BaseResponse(msg=f"导入阅卷人员信息成功，共导入 {user_count} 位，密码为：{de_default_password}")

