"""
主模式用到的 api
"""
import os
import re
import threading
import traceback
from typing import Any

from fastapi import APIRouter, Depends
from sqlalchemy import and_, select
from starlette.responses import StreamingResponse

from apps.models.models import ExamStudent, StuAnswer, ExamPaper, Project, Subject, ExamQuestion, OpEngineMarkRecord
from apps.operation_ques.op_engine_mark_services import get_condition_op_data, get_all_op_answer, \
    get_curr_op_task_info, running_op_engine_mark, create_new_op_task, format_op_answer_data, update_op_record_state, \
    cancel_stu_answer_running_state, supplementary_op_total_record, get_curr_op_task_state, get_before_pause_op_data, \
    check_op_engine_exists
from apps.operation_ques.op_manual_mark_services import op_ques_query_condition, file_iterator
from apps.read_paper.common_services import get_user_data_flag, transform_mark_state
from apps.read_paper.read_paper_services import get_paper_detail
from settings import logger, configs
from sqlalchemy.orm import Session

from apps.base.schemas import BaseResponse
from apps.operation_ques.schemas import StartOpEngineMarkReq, GetOpMarkProgressReq, GetOpQuesReq, RecordIdReq, \
    OpFileNameReq
from apps.users.services import get_current_user
from factory_apps import session_depend
from helper import response_utils

op_engine_mark_router = APIRouter()


@op_engine_mark_router.post(path="/get_op_ques_list", response_model=BaseResponse, summary="获取操作题列表")
async def get_op_ques_list(query: GetOpQuesReq, user: Any = Depends(get_current_user),
                           new_session: Session = Depends(session_depend)):
    (current_page, page_size, project_id, subject_id, paper_code, ques_code, ques_order, stu_answer, mark_result,
     mark_state, stu_score_range, search_time) = query.model_dump().values()
    logger.info(f"{user['username']} 获取操作题列表")
    project_condition, subject_condition = get_user_data_flag(new_session, user)

    answer_data = []
    limit = current_page - 1
    offset = limit * page_size

    paper_detail_dict = {}
    # paper_detail_dict = {
    #     paper_id: {
    #         ques_code: {
    #             "ques_order": ques_order,
    #             "ques_score_list": ques_score_list
    #         }
    #     }
    # }

    select_fields = [StuAnswer.answer_id, StuAnswer.allow_exam_num, StuAnswer.paper_id, StuAnswer.ques_id,
                     StuAnswer.ques_code, StuAnswer.small_ques_order, StuAnswer.op_file, StuAnswer.score,
                     StuAnswer.mark_state, StuAnswer.stu_score, StuAnswer.answer_parse, ExamPaper.paper_name,
                     Subject.subject_name, ExamQuestion.standard_answer, Project.project_name,
                     ExamQuestion.parent_ques_id, StuAnswer.paper_code, StuAnswer.updated_time]

    # 操作题
    op_condition = StuAnswer.ques_type_code == "G"
    # 拼凑查询条件
    condition = op_ques_query_condition(new_session, project_id, subject_id, paper_code, ques_code, ques_order,
                                        stu_answer, mark_result, mark_state, stu_score_range, search_time)

    total = new_session.query(StuAnswer.answer_id) \
        .join(ExamPaper, ExamPaper.paper_code == StuAnswer.paper_code) \
        .join(Project, Project.project_id == StuAnswer.project_id) \
        .join(Subject, Subject.subject_id == StuAnswer.subject_id) \
        .where(and_(condition, project_condition, subject_condition, op_condition)).count()

    answer_stmt = select(*select_fields) \
        .join(ExamQuestion, ExamQuestion.ques_id == StuAnswer.ques_id) \
        .join(Project, Project.project_id == StuAnswer.project_id) \
        .join(Subject, Subject.subject_id == StuAnswer.subject_id) \
        .join(ExamPaper, ExamPaper.paper_code == StuAnswer.paper_code) \
        .where(and_(condition, project_condition, subject_condition, op_condition)) \
        .order_by(StuAnswer.updated_time.desc()).limit(page_size).offset(offset)

    try:
        result = list(new_session.execute(answer_stmt))

        stu_secret_num_dict = {}
        if result:
            stu_secret_num_list = [i.allow_exam_num for i in result]
            stu_secret_num_info = new_session.query(ExamStudent.allow_exam_num, ExamStudent.stu_secret_num).filter(
                ExamStudent.allow_exam_num.in_(stu_secret_num_list)).all()
            if stu_secret_num_info:
                for stu in stu_secret_num_info:
                    stu_secret_num_dict[stu.allow_exam_num] = stu.stu_secret_num

        for row in result:
            raw_paper_id, raw_ques_id = row.paper_id, row.ques_id
            is_add, ques_order, ques_score_list = get_paper_detail(new_session, paper_detail_dict, raw_paper_id,
                                                                   raw_ques_id)
            ques_order = re.sub(r'^0+', '', ques_order) if ques_order else None
            if is_add:
                if paper_detail_dict.get(raw_paper_id):
                    paper_detail_dict[raw_paper_id][raw_ques_id] = {
                        "ques_order": re.sub(r'^0+', '', ques_order) if ques_order else None,
                        "ques_score_list": ques_score_list
                    }
                else:
                    paper_detail_dict[raw_paper_id] = {
                        raw_ques_id: {
                            "ques_order": re.sub(r'^0+', '', ques_order),
                            "ques_score_list": ques_score_list
                        }
                    }

            (standard_answer_list, small_ques_order, total_score, answer_parse, parent_ques_id) = \
                (row.standard_answer, row.small_ques_order, row.score, row.answer_parse,
                 row.parent_ques_id)

            stu_score = row.stu_score
            if stu_score:
                stu_score = float(stu_score)
                if stu_score > total_score:
                    stu_score = None

            answer_item = {
                "answer_id": row.answer_id,
                "stu_secret_num": stu_secret_num_dict[row.allow_exam_num],
                "ques_code": row.ques_code,
                "ques_id": row.ques_id,
                "small_ques_order": small_ques_order,
                "stu_answer": row.op_file,
                "score": total_score,
                "total_score": total_score,
                "mark_state": transform_mark_state(row.mark_state),
                "stu_score": stu_score,
                "answer_parse": answer_parse,
                "paper_code": row.paper_code,
                "paper_name": row.paper_name,
                "subject_name": row.subject_name,
                "project_name": row.project_name,
                "ques_order": ques_order,
                "updated_time": row.updated_time and str(row.updated_time).replace("T",
                                                                                   " ") if row.mark_state != 1 and row.mark_state != 5 else None
            }
            answer_data.append(answer_item)
    except Exception as e:
        logger.error(f"获取操作题列表失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg=f"获取操作题列表失败")
    logger.info("获取操作题列表成功")
    data = {
        "data": answer_data,
        "total": total
    }
    return BaseResponse(msg="获取操作题列表成功", data=data)


@op_engine_mark_router.get(path="/create_op_mark_record", response_model=BaseResponse,
                           summary="创建操作题引擎评分的记录")
async def create_op_mark_record(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建操作题引擎评分的记录")
    # 先检测 评分引擎 是否存在
    msg = check_op_engine_exists(user)
    if msg:
        return BaseResponse(code=response_utils.timeout, msg=msg[:-1])

    # 创建任务记录
    user_id = user.get("user_id")
    record_id = configs.snow_worker.get_id()
    data = create_new_op_task(new_session, record_id, user_id)
    return BaseResponse(msg="创建操作题引擎评分的记录成功", data=data)


@op_engine_mark_router.post(path="/op_engine_mark", response_model=BaseResponse, summary="开始进行操作题引擎评分")
async def op_engine_mark(query: StartOpEngineMarkReq, user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 开始进行操作题引擎评分")
    (record_id, answer_data_list, project_id, subject_id, paper_code, ques_code, ques_order, stu_answer, mark_result,
     mark_state, stu_score_range, search_time) = query.model_dump().values()

    if answer_data_list:
        filter_content = {"1": answer_data_list}
        flag = True
        logger.info(f"{user['username']} 获取勾选的操作题数据")
        answer_data_dict = format_op_answer_data(answer_data_list, True)
    else:
        filter_list = [project_id, subject_id, paper_code, ques_code, ques_order, stu_answer, mark_result, mark_state,
                       stu_score_range, search_time]
        filter_condition = any(filter_list)
        if filter_condition:
            filter_content = {
                "2": {
                    "project_id": project_id, "subject_id": subject_id, "paper_code": paper_code,
                    "ques_code": ques_code, "ques_order": ques_order, "stu_answer": stu_answer,
                    "mark_result": mark_result, "mark_state": mark_state, "stu_score_range": stu_score_range,
                    "search_time": search_time
                }
            }
            logger.info(f"{user['username']} 根据查询条件获取操作题的数据")
            flag, answer_data_dict = get_condition_op_data(new_session, *filter_list)
        else:
            filter_content = {"3": "all"}
            logger.info(f"{user['username']} 获取操作题全部数据")
            flag, answer_data_dict = get_all_op_answer(new_session)
    logger.info("获取操作题全部数据成功")

    if not flag:
        BaseResponse(code=response_utils.server_error, msg="获取数据异常")
    if not answer_data_dict:
        return BaseResponse(code=response_utils.no_field, msg="未找到操作题待阅卷数据")

    # 计算总共有多少条数据
    total = sum(len(lst) for lst in answer_data_dict.values())
    # 更新记录总数，记录筛选条件
    supplementary_op_total_record(new_session, record_id, total, 2, filter_content)

    # 开启线程进行数据标记和执行任务
    threading.Thread(target=running_op_engine_mark, args=(record_id, answer_data_dict)).start()

    return BaseResponse()


@op_engine_mark_router.post(path="/get_op_mark_progress", response_model=BaseResponse, summary="获取操作题引擎评分进度")
async def get_op_mark_progress(query: GetOpMarkProgressReq, user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取操作题引擎评分进度")
    record_id = query.record_id
    # 前端一进入操作题页面会调用当前接口，不携带参数
    # 有任务则返回任务数据，前端轮询接口展示进度条
    # 没有则返回空，前端调用列表页接口
    data = get_curr_op_task_info(new_session, record_id)
    return BaseResponse(data=data)


@op_engine_mark_router.post(path="/pause_op_engine_mark", response_model=BaseResponse, summary="暂停操作题引擎评分")
async def pause_op_engine_mark(query: RecordIdReq, user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 暂停操作题引擎评分")
    record_id = query.record_id
    # 获取当前任务状态
    pause_state = get_curr_op_task_state(new_session, record_id)
    # 标记任务状态为暂停
    update_op_record_state(new_session, record_id, 4, pause_state)
    return BaseResponse()


@op_engine_mark_router.post(path="/continue_op_engine_mark", response_model=BaseResponse, summary="继续操作题引擎评分")
async def continue_op_engine_mark(query: RecordIdReq, user: Any = Depends(get_current_user),
                                  new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 继续操作题引擎评分")
    record_id = query.record_id
    # 获取当前任务状态
    data = get_curr_op_task_info(new_session, record_id)

    # 获取评分记录的筛选要求和暂停前的状态
    filter_condition, pause_state = data["filter_condition"], data["pause_state"]
    filter_type, filter_content = next(iter(filter_condition.items()))

    flag, answer_data_dict = True, {}
    if pause_state == 1 or pause_state == 2:
        flag, answer_data_dict = get_before_pause_op_data(new_session, filter_type, filter_content)
    elif pause_state == 3:
        logger.info("暂停前为评分中，查询未评分数据")
        flag, answer_data_dict = get_before_pause_op_data(new_session, filter_type, filter_content, True)
    else:
        BaseResponse(code=response_utils.params_error, msg="参数错误")

    if not flag:
        BaseResponse(code=response_utils.server_error, msg="获取数据失败")
    if not answer_data_dict:
        BaseResponse(code=response_utils.no_field, msg="暂无待评分数据")

    # 根据暂停前的状态进行不同的处理
    if pause_state == 1:
        logger.info("暂停前为数据获取中，则直接获取数据后评分")
        update_op_record_state(new_session, record_id, 1)
        threading.Thread(target=running_op_engine_mark, args=(record_id, answer_data_dict)).start()
    elif pause_state == 2:
        logger.info("暂停前为数据标记中，查询未标记数据")
        update_op_record_state(new_session, record_id, 2)
        already_remark_ques_id = new_session.query(OpEngineMarkRecord.already_remark_ques_id).filter(
            OpEngineMarkRecord.record_id == record_id).scalar()
        need_remark_data = {}
        for ques_id in answer_data_dict:
            if ques_id not in already_remark_ques_id:
                need_remark_data[ques_id] = answer_data_dict[ques_id]
        threading.Thread(target=running_op_engine_mark,
                         args=(record_id, answer_data_dict, True, need_remark_data)).start()
    elif pause_state == 3:
        update_op_record_state(new_session, record_id, 3)
        threading.Thread(target=running_op_engine_mark, args=(record_id, answer_data_dict, False)).start()

    return BaseResponse()


@op_engine_mark_router.post(path="/cancel_op_engine_mark", response_model=BaseResponse, summary="取消操作题引擎评分")
async def cancel_op_engine_mark(query: RecordIdReq, user: Any = Depends(get_current_user),
                                new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 取消操作题引擎评分")
    record_id = query.record_id
    # 标记任务状态为取消中
    update_op_record_state(new_session, record_id, 5)
    # 取消待评分数据标记
    cancel_stu_answer_running_state(new_session)
    # 标记任务状态为已取消
    update_op_record_state(new_session, record_id, 6)
    return BaseResponse()


@op_engine_mark_router.post(path="/download_op_engine_file_stream", response_model=BaseResponse,
                            summary="下载操作题引擎评分所需文件")
async def download_op_engine_file_stream(query: OpFileNameReq, user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 下载操作题引擎评分所需文件")
    ques_id, file_name = query.model_dump().values()

    ques_file_path = os.path.join(configs.PROJECT_PATH, f"op_engine_file/{ques_id}")

    if file_name.endswith(".jvbqt"):
        file_path = os.path.join(ques_file_path, file_name)
    elif file_name.endswith("jdet"):
        file_path = os.path.join(ques_file_path, f"stu_data/{file_name}")
    else:
        return BaseResponse(code=response_utils.params_error, msg="文件后缀名有误")

    if not os.path.exists(file_path):
        return BaseResponse(code=response_utils.no_field, msg=f"{file_path} 该文件不存在")

    return StreamingResponse(
        file_iterator(file_path),
        media_type="application/octet-stream",
        headers={"Content-Disposition": f"attachment; filename={file_name}"}
    )
