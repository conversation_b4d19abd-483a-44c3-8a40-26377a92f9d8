import re
import threading

from settings import logger
from sqlalchemy import and_, func, or_

from apps.base.global_cache import set_manual_mark_info
from apps.manual_read_paper.manual_read_services import get_curr_state, get_chinese_fetch_way, get_chinese_fetch_option, \
    statistic_final_score, get_manual_type, get_f_marked_distri_answer_data
from apps.manual_read_paper.manual_task_services import launch_manual_task_main
from apps.models.models import ExamQuestion, ExamPaper, ManualReadTask, ManualArbitrateQuality, ManualMark, \
    ExamStudent, ManualDistributeAnswer, UserInfo, PaperDetail, BusinessQuesType
from apps.read_paper.common_services import splice_image_path


def get_f_single_mark_state(mark_state_str, mark_person_id_list, curr_user_id):
    mark_state = None
    if mark_state_str:
        if "," in mark_state_str:
            for index, mark_person_id in enumerate(mark_person_id_list):
                if mark_person_id == curr_user_id:
                    single_mark_state = mark_state_str.split(",")[index]
                    mark_state = int(single_mark_state) if single_mark_state else None
                    break
        else:
            mark_state = int(mark_state_str)
    return mark_state


def get_f_manual_read_data(new_session, m_read_task_id, paper_id, ques_id, ques_code, group_id, project_id,
                           subject_id, subject_name, batch_num, curr_user_id, role_id):
    if paper_id:
        f_ques_info = new_session.query(PaperDetail.ques_order, ExamQuestion.ques_desc) \
            .join(ExamQuestion, ExamQuestion.ques_id == PaperDetail.ques_id) \
            .filter(PaperDetail.ques_id == ques_id).first()
        f_ques_order, f_ques_desc = f_ques_info
    else:
        f_ques_order = None
        f_ques_desc = new_session.query(ExamQuestion.ques_desc).filter(ExamQuestion.ques_id == ques_id).scalar()

    f_ques_desc, _ = splice_image_path(f_ques_desc, [])

    select_fields = [ManualDistributeAnswer.distri_answer_id, ManualDistributeAnswer.stu_secret_num,
                     ManualDistributeAnswer.stu_answer, ExamPaper.paper_name,
                     ExamQuestion.ques_desc, ExamQuestion.ques_choices,
                     ExamQuestion.ques_difficulty, ExamQuestion.standard_answer,
                     ExamQuestion.standard_choices_code, ExamQuestion.standard_parse, ExamQuestion.ques_mark_point,
                     func.max(ManualMark.expert_mark_score), func.max(ManualMark.mark_parse),
                     func.group_concat(ManualMark.manual_mark_id), func.max(ManualMark.can_expert_mark_again),
                     func.max(ManualMark.not_mark_again_reason), ManualArbitrateQuality.aq_data,
                     ManualArbitrateQuality.aq_mark_score, ManualArbitrateQuality.can_aq_again,
                     func.group_concat(ManualMark.mark_state), ManualArbitrateQuality.aq_suggestion,
                     ManualArbitrateQuality.aq_result, ManualArbitrateQuality.manual_aq_id,
                     func.max(ManualMark.final_mark_time), ManualReadTask.fetch_score_way,
                     ManualReadTask.fetch_score_option, ExamQuestion.standard_answer_html,
                     ManualDistributeAnswer.stu_answer_id, ManualReadTask.fetch_score_scope,
                     ExamQuestion.ques_type_code, ExamQuestion.ques_id, ManualDistributeAnswer.final_mark_score,
                     func.group_concat(ManualMark.mark_person_id)
                     ]

    group_fields = [
        ManualDistributeAnswer.distri_answer_id, ManualDistributeAnswer.stu_secret_num,
        ManualDistributeAnswer.stu_answer, ExamPaper.paper_name,
        ExamQuestion.ques_desc, ExamQuestion.ques_choices, ExamQuestion.ques_difficulty,
        ExamQuestion.standard_answer, ExamQuestion.standard_choices_code, ExamQuestion.standard_parse,
        ExamQuestion.ques_mark_point, ManualArbitrateQuality.aq_data, ManualArbitrateQuality.aq_mark_score,
        ManualArbitrateQuality.can_aq_again, ManualArbitrateQuality.aq_suggestion, ManualArbitrateQuality.aq_result,
        ManualArbitrateQuality.manual_aq_id, ManualReadTask.fetch_score_way, ManualReadTask.fetch_score_option,
        ExamQuestion.standard_answer_html, ManualDistributeAnswer.stu_answer_id, ManualReadTask.fetch_score_scope,
        ExamQuestion.ques_type_code, ExamQuestion.ques_id, ManualDistributeAnswer.final_mark_score
    ]

    if paper_id:
        small_ques_order_info = new_session.query(PaperDetail.ques_order, ExamQuestion.ques_id,
                                                  ExamQuestion.ques_type_code, PaperDetail.ques_score_list) \
            .join(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
            .filter(PaperDetail.parent_ques_id == ques_id).order_by(PaperDetail.ques_order).all()
        small_ques_order_list = [i[0] for i in small_ques_order_info]
    else:
        small_ques_order_info = new_session.query(ExamQuestion.small_ques_num, ExamQuestion.ques_id,
                                                  ExamQuestion.ques_type_code, BusinessQuesType.ques_score_list) \
            .join(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
            .filter(ExamQuestion.parent_ques_id == ques_id).order_by(ExamQuestion.small_ques_num).all()
        small_ques_order_list = [i[0] for i in small_ques_order_info]

    is_arbitrate_condition, is_quality_condition = True, True
    marked_distri_answer_id = []
    if role_id == "3":
        # 获取已经评过分的数据
        marked_distri_answer_id = get_f_marked_distri_answer_data(new_session, m_read_task_id, group_id, ques_code,
                                                                  [curr_user_id])
    elif role_id == "4":
        is_arbitrate_condition = and_(ManualArbitrateQuality.aq_type == 1, ManualArbitrateQuality.aq_state == 1)
    elif role_id == "5":
        is_quality_condition = and_(ManualArbitrateQuality.aq_type == 2, ManualArbitrateQuality.aq_state == 3)
    else:
        return []

    small_ques_info_dict = {}
    group_condition = ManualDistributeAnswer.manual_group_id == group_id
    for small_ques_order, small_ques_id, small_ques_type_code, ques_score_list in small_ques_order_info:
        total_condition = and_(ExamQuestion.parent_ques_id == ques_id, ExamQuestion.ques_id == small_ques_id,
                               ManualDistributeAnswer.ques_id == small_ques_id, group_condition,
                               ~ManualDistributeAnswer.distri_answer_id.in_(marked_distri_answer_id),
                               is_arbitrate_condition, is_quality_condition, ManualDistributeAnswer.final_mark_score.is_(None))

        if role_id == "3":
            # 需要考虑还未质检返评的数据
            total_condition = or_(total_condition, and_(ExamQuestion.parent_ques_id == ques_id,
                                                        ExamQuestion.ques_id == small_ques_id,
                                                        ManualDistributeAnswer.ques_id == small_ques_id,
                                                        group_condition,
                                                        ~ManualDistributeAnswer.distri_answer_id.in_(
                                                            marked_distri_answer_id),
                                                        ManualArbitrateQuality.aq_result == 2))
            total = new_session.query(ManualDistributeAnswer.distri_answer_id) \
                .join(ExamQuestion, ExamQuestion.ques_code == ManualDistributeAnswer.ques_code) \
                .outerjoin(ManualMark, ManualMark.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
                .outerjoin(ManualArbitrateQuality,
                           ManualArbitrateQuality.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
                .where(total_condition).count()

            if total <= batch_num:
                small_result, msg = launch_manual_task_main(new_session, m_read_task_id, project_id, subject_id,
                                                            paper_id, ques_code, small_ques_type_code, [group_id],
                                                            small_ques_id, ques_id, False)
                if small_result:
                    new_session.commit()
                    # 开启线程获取该任务的阅卷人员对应的阅卷数据
                    condition = ManualReadTask.m_read_task_id == m_read_task_id
                    threading.Thread(target=set_manual_mark_info, args=(condition,)).start()
                    logger.info(f"自动分配组合题小题 {small_ques_order} 评分数据成功")
                else:
                    logger.info(msg)

        small_ques_infos = new_session.query(*select_fields) \
            .outerjoin(ExamPaper, ExamPaper.paper_id == ManualDistributeAnswer.paper_id) \
            .join(ExamQuestion, ExamQuestion.ques_code == ManualDistributeAnswer.ques_code) \
            .join(ExamStudent, ExamStudent.stu_secret_num == ManualDistributeAnswer.stu_secret_num) \
            .join(ManualReadTask, ManualReadTask.m_read_task_id == ManualDistributeAnswer.m_read_task_id) \
            .outerjoin(ManualMark, ManualMark.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
            .outerjoin(ManualArbitrateQuality,
                       ManualArbitrateQuality.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
            .filter(total_condition) \
            .group_by(*group_fields) \
            .order_by(ManualDistributeAnswer.stu_secret_num) \
            .limit(batch_num)

        small_ques_item = []
        for small_ques_info in small_ques_infos:
            is_show_arbitrate = True
            aq_data = small_ques_info[16]
            if aq_data:
                expert_data = aq_data.get("expert_data")
                arbitrator_data = aq_data.get("arbitrate_data")
                if (expert_data and role_id == "4") or (expert_data and role_id == "5"):
                    for index, distri_data in enumerate(expert_data):
                        mark_person_id = distri_data["mark_person_id"]
                        expert_info = new_session.query(UserInfo.username, UserInfo.name).filter(
                            UserInfo.user_id == mark_person_id).first()
                        expert_data[index]["username"] = expert_info[0]
                        expert_data[index]["name"] = expert_info[1]
                    if arbitrator_data and role_id == "5":
                        for index, distri_data in enumerate(arbitrator_data):
                            mark_person_id = distri_data["mark_person_id"]
                            arbitrator_info = new_session.query(UserInfo.username, UserInfo.name).filter(
                                UserInfo.user_id == mark_person_id).first()
                            arbitrator_data[index]["username"] = arbitrator_info[0]
                            arbitrator_data[index]["name"] = arbitrator_info[1]
                else:
                    expert_data = None
                    arbitrator_data = None
            else:
                expert_data = None
                arbitrator_data = None

            ques_desc, ques_choices = small_ques_info[4], small_ques_info[5]
            ques_desc, ques_choices = splice_image_path(ques_desc, ques_choices)
            manual_type = get_manual_type(role_id)

            expert_mark_score, mark_person_id_list = small_ques_info[11], small_ques_info[32].split(",") if \
                small_ques_info[32] else []

            mark_state_str = small_ques_info[19]

            if role_id == "3":
                if curr_user_id not in mark_person_id_list:
                    expert_mark_score = None
                    # is_show_arbitrate = False
                mark_state = get_f_single_mark_state(mark_state_str, mark_person_id_list, curr_user_id)
            else:
                mark_state = int(mark_state_str.split(",")[0]) if mark_state_str.split(",")[0] is not None else None
            curr_state = get_curr_state(mark_state)

            real_final_score, is_show_arbitrate, _ = statistic_final_score(new_session, m_read_task_id, small_ques_info[0], small_ques_info[24], small_ques_info[25], small_ques_info[28])

            distri_item = {
                "distri_answer_id": small_ques_info[0],
                "stu_secret_num": small_ques_info[1],
                "paper_id": paper_id,
                "subject_id": subject_id,
                "ques_code": ques_code,
                "stu_answer": small_ques_info[2],
                "paper_name": small_ques_info[3],
                "subject_name": subject_name,
                "ques_order": re.sub(r'^0+', '', str(small_ques_order)) if small_ques_order else None,
                "ques_desc": ques_desc,
                "ques_choices": ques_choices,
                "ques_score_list": ques_score_list,
                "ques_difficulty": small_ques_info[6],
                "standard_answer": small_ques_info[7],
                "standard_choices_code": small_ques_info[8],
                "standard_parse": small_ques_info[9],
                "ques_mark_point": small_ques_info[10],
                "expert_mark_score": expert_mark_score,
                "mark_parse": small_ques_info[12],
                "manual_mark_id": small_ques_info[13],
                "can_expert_mark_again": small_ques_info[14],
                "not_mark_again_reason": small_ques_info[15],
                "expert_data": expert_data,
                "arbitrator_mark_score": small_ques_info[17] if role_id == "4" else None,
                "can_arbitrator_mark_again": small_ques_info[18] if role_id == "4" else None,
                "mark_state": mark_state,
                "quality_suggestion": small_ques_info[20],
                "quality_result": small_ques_info[21],
                "manual_aq_id": small_ques_info[22],
                "final_mark_time": small_ques_info[23] and str(small_ques_info[23]).replace("T", " "),
                "fetch_score_way": get_chinese_fetch_way(small_ques_info[24]),
                "fetch_score_option": get_chinese_fetch_option(small_ques_info[25]),
                "standard_answer_html": small_ques_info[26],
                "stu_answer_id": small_ques_info[27],
                "final_score": real_final_score,
                "ques_type_code": small_ques_info[29],
                "ques_id": small_ques_info[30],
                "final_mark_score": small_ques_info[31],
                "is_show_arbitrate": is_show_arbitrate,
                "arbitrator_data": arbitrator_data,
                "manual_type": manual_type,
                "curr_state": curr_state
            }
            small_ques_item.append(distri_item)
        small_ques_info_dict[small_ques_order] = small_ques_item

    small_ques_info_list = []

    small_ques_count = 0
    for i in small_ques_info_dict:
        if small_ques_info_dict[i]:
            small_ques_count = len(small_ques_info_dict[i])
            break

    for index in range(small_ques_count):
        distri_item = {
            "f_ques_order": f_ques_order,
            "f_ques_desc": f_ques_desc,
            "f_ques_type_code": "F",
            "children": [],
            "distri_answer_id_list": []
        }
        for small_ques_order in small_ques_order_list:
            try:
                child = small_ques_info_dict[small_ques_order][index]
            except IndexError:
                continue
            distri_item["children"].append(child)
            try:
                distri_item["distri_answer_id_list"].append(child["distri_answer_id"])
            except:
                pass
        small_ques_info_list.append(distri_item)

    # pprint(small_ques_info_list)
    return small_ques_info_list
