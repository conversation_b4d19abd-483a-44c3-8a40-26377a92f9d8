from typing import Optional, Literal,Union
from pydantic import BaseModel, Field
from apps.base.schemas import PaginationModel


class UserIdReq(BaseModel):
    user_id: str = Field(..., description="用户id")


class RegisterReq(BaseModel):
    username: str = Field(..., description="用户名")
    role_id: list = Field(..., description="角色id列表")
    system_user_type: Literal[1, 2] = Field(..., description="1 表示系统用户，2 表示业务用户")
    project_id_list: list = Field([], description="项目id")
    subject_id_list: list = Field([], description="科目id")
    round_count: Optional[int] = Field(None, description="所属轮次", ge=1, le=6)  # 添加轮次范围验证
    name: str = Field(..., description="姓名")
    phone: Optional[str] = Field(None, description="手机号")
    id_card: Optional[str] = Field(None, description="身份证")
    province_code: Optional[str] = Field(None, description="行政区域（省编号）")
    city_code: Optional[str] = Field(None, description="行政区域（市编号）")
    district_code: Optional[str] = Field(None, description="行政区域（区县编号）")
    work_unit: Optional[str] = Field(None, description="所在单位")
    user_type: Literal[1, 2] = Field(1, description="账户类型，1 为人，2 为 AI")


class UserBaseReq(BaseModel):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    
    


class UserLoginReq(UserBaseReq):
    role_id_list: list = Field(..., description="角色id列表")


class GetUserReq(PaginationModel):
    username: Optional[str] = Field(None, description="用户名")
    name: Optional[str] = Field(None, description="姓名")
    role_id: Optional[str] = Field(None, description="角色id")
    province_code_list: Optional[list] = Field(None, description="行政区域（省编号）列表")
    city_code_list: Optional[list] = Field(None, description="行政区域（市编号）列表")
    district_code_list: Optional[list] = Field(None, description="行政区域（区县编号）列表")
    work_unit: Optional[str] = Field(None, description="所在单位")
    user_type: Literal[None, 1, 2] = Field(None, description="账户类型，1 为人，2 为 AI")
    is_active: Optional[bool] = Field(None, description="禁用或启用")
    system_user_type: Literal[0, 1, 2] = Field(0, description="0 表示所有角色，1 表示系统角色，2 表示业务角色")
    round_count_list: Optional[list] = Field(None, description="所属轮次")
    is_real_name:Optional[bool] = Field(None, description="是否实名")
    role_id_list: Optional[list] = Field(None, description="角色id列表")
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")



class ChangePasswordReq(BaseModel):
    user_id: str = Field(..., description="用户id")
    raw_password: Optional[str] = Field(None, description="原始密码")
    new_password: str = Field(..., description="新密码")

class FirstChangePassword(BaseModel):
    user_id: str = Field(..., description="用户id")
    new_password: str = Field(..., description="新密码")
    user_phone_number: Optional[str] = Field(..., description="电话号码")
    user_ID_number: Optional[str] = Field(..., description="身份证号码")
    user_real_name: Optional[str] = Field(..., description="用户实名")
    sure_new_password: Union[str,None] = Field(..., description="确认密码")
    


class UpdateUserReq(BaseModel):
    user_id: str = Field(..., description="用户id")
    name: str = Field(..., description="姓名")
    role_id_list: list = Field(..., description="角色id列表")
    phone: Optional[str] = Field(..., description="手机号")
    id_card: Optional[str] = Field(..., description="身份证")
    round_count: Optional[int] = Field(None, description="所属轮次", ge=1, le=6)  # 添加轮次范围验证
    province_code: Optional[str] = Field(None, description="行政区域（省编号）")
    city_code: Optional[str] = Field(None, description="行政区域（市编号）")
    district_code: Optional[str] = Field(None, description="行政区域（区县编号）")
    work_unit: Optional[str] = Field(None, description="所在单位")
    user_type: Optional[Literal[1, 2]] = Field(None, description="账户类型，1 为人，2 为 AI")   # 必填改成选填
    project_id_list: list = Field([], description="项目id")
    subject_id_list: list = Field([], description="科目id")


class ChangeRoleReq(BaseModel):
    role_id_list: list = Field(..., description="角色id列表")


class UpdateUserStateReq(BaseModel):
    user_id: str = Field(..., description="用户id")
    is_active: bool = Field(..., description="用户状态")
