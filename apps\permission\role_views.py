from fastapi import APIRouter, Depends
from settings import logger
from sqlalchemy import select, exists, func, and_, text
from typing import Any

from sqlalchemy.orm import aliased,Session

from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.permission.schemas import CreateRoleReq, GetRoleReq, UpdateRoleReq, DeleteRoleReq
from apps.models.models import UserInfo, Role, UserRole
from helper import response_utils
from factory_apps import session_depend
from settings import configs


role_router = APIRouter()


@role_router.post(path="/create_role", response_model=BaseResponse, summary="创建角色")
async def create_role(query: CreateRoleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建角色")
    is_exist = new_session.query(exists().where(Role.role_name == query.role_name)).scalar()
    if is_exist:
        new_session.close()
        return BaseResponse(code=response_utils.fields_exist, msg=f"角色名 {query.role_name} 已存在")

    try:
        # max_role_id = new_session.query(func.max(Role.role_id)).scalar()
        # role_id = str(int(max_role_id) + 1)
        # 使用原子操作获取新的role_id
        result = new_session.execute(text("""SELECT CAST(IFNULL(MAX(CAST(role_id AS UNSIGNED)), 0) + 1 AS CHAR) as new_role_id FROM t_role"""))
        new_role_id = result.scalar()
        new_role = Role(real_role_id=configs.snow_worker.get_id(), role_id=new_role_id , role_name=query.role_name,
                        role_desc=query.role_desc, c_user_id=user.get("user_id"))
        new_session.add(new_role)
        new_session.commit()
        logger.info("创建角色成功")
        return BaseResponse(msg=f"创建角色成功")
    except Exception as e:
        logger.error(f"创建角色失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"创建角色失败")
    
    
@role_router.post(path="/get_role", response_model=BaseResponse, summary="获取角色列表")
async def get_role(query: GetRoleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取角色列表")
    current_page, page_size, system_user_type, role_name = query.model_dump().values()
    role_data = []
    user_info_u_alias = aliased(UserInfo, name="user_info_u_alias")

    system_user_type_query = Role.system_user_type == system_user_type if system_user_type else True
    if page_size == -1:
        total = new_session.query(Role.role_id).join(UserInfo, UserInfo.user_id == Role.c_user_id).where(system_user_type_query).count()
        role_stmt = select(Role.role_id, Role.role_name, Role.role_desc, UserInfo.username, Role.created_time,
                           Role.updated_time, Role.system_user_type, user_info_u_alias.username).join(UserInfo, Role.c_user_id == UserInfo.user_id).outerjoin(user_info_u_alias, Role.u_user_id == user_info_u_alias.user_id).where(system_user_type_query)
    else:
        limit = current_page - 1
        offset = limit * page_size
        # 拼凑查询条件
        if role_name:
            condition = and_(Role.role_name.ilike(f"%{role_name}%"), system_user_type_query)
        else:
            condition = system_user_type_query

        total = new_session.query(Role.role_id).join(UserInfo, UserInfo.user_id == Role.c_user_id).where(
            condition).count()
        role_stmt = select(Role.role_id, Role.role_name, Role.role_desc, UserInfo.username, Role.created_time,
                           Role.updated_time, Role.system_user_type, user_info_u_alias.username).where(condition) \
            .join(UserInfo, Role.c_user_id == UserInfo.user_id) \
            .outerjoin(user_info_u_alias, Role.u_user_id == user_info_u_alias.user_id) \
            .limit(page_size).offset(offset)

    try:
        result = new_session.execute(role_stmt)
        for row in result:
            # 处理updated_time字段：如果为null则使用created_time
            updated_time_str = None
            u_user_name_str = None
            if row[5] is not None:  # updated_time不为null
                updated_time_str = row[5] and str(row[5]).replace("T", " ")
                u_user_name_str = row[7]
            else:  # updated_time为null，使用created_time
                if row[4] is not None:
                    updated_time_str = row[4] and str(row[4]).replace("T", " ")
                    u_user_name_str = row[3]
            role_item = {
                "role_id": row[0],
                "role_name": row[1],
                "role_desc": row[2],
                "c_user_name": row[3],
                "created_time": row[4] and str(row[4]).replace("T", " "),
                "updated_time": updated_time_str,
                "system_user_type": row[6],
                "u_user_name": u_user_name_str
            }
            role_data.append(role_item)
    except Exception as e:
        logger.error(f"获取角色列表失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取角色列表失败")
    logger.info("获取角色列表成功")
    data = {
        "data": role_data,
        "total": total
    }
    return BaseResponse(msg="获取角色列表成功", data=data)


@role_router.post(path="/update_role", response_model=BaseResponse, summary="编辑角色信息")
async def update_role(query: UpdateRoleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    role_id = query.role_id
    logger.info(f"{user['username']} 编辑角色信息，id 为 {role_id}")
    role_data = new_session.query(Role.role_id, Role.lock_state).filter(Role.role_id == role_id).first()
    if not role_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该角色")
    else:
        if role_data[1] == 2:
            return BaseResponse(code=response_utils.permission_deny, msg="该数据为内置数据，不允许编辑该角色")

    try:
        new_session.query(Role).filter(Role.role_id == role_id).update({
            Role.role_name: query.role_name,
            Role.role_desc: query.role_desc,
            Role.u_user_id: user.get("user_id")
        })
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"编辑角色信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"编辑角色信息失败")
    logger.info("编辑角色信息成功")
    return BaseResponse(msg="编辑角色信息成功")


@role_router.post(path="/delete_role", response_model=BaseResponse, summary="删除角色信息")
async def delete_paper(query: DeleteRoleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    role_id = query.role_id
    logger.info(f"{user['username']} 删除角色信息，id 为 {role_id}")
    role_data = new_session.query(Role.role_id, Role.lock_state, Role.is_builtin).filter(Role.role_id == role_id).first()
    if not role_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该角色")
    else:
        role_id, lock_state, is_builtin = role_data
        if role_data[1] == 2:
            return BaseResponse(code=response_utils.permission_deny, msg="该数据已被使用，不允许删除该角色")
        if role_data[2] == 1:
            return BaseResponse(code=response_utils.permission_deny, msg="该数据为内置数据，不允许删除该角色")

    has_user_count = new_session.query(UserRole.user_role_id).filter(UserRole.role_id == role_id).count()
    if has_user_count > 0:
        return BaseResponse(code=response_utils.verify_error, msg="该角色下已存在用户，不允许删除该角色")

    try:
        new_session.query(Role).filter(Role.role_id == role_id).delete()
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除角色信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除角色信息失败")
    logger.info("删除角色信息成功")
    return BaseResponse(msg="删除角色信息成功")
