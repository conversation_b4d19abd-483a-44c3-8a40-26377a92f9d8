import re
import threading
import traceback

from fastapi import APIRouter, Depends
from settings import logger
from sqlalchemy import select, and_, func
from typing import Any
from sqlalchemy.orm import Session

from apps.base.global_cache import get_small_ques_count, get_stu_num_by_task_id, get_task_state_by_task_id, \
    set_manual_mark_info, get_redis_ques_info_dict
from apps.base.services import request_api
from apps.manual_read_paper.f_manual_read_services import get_f_manual_read_data
from apps.manual_read_paper.manual_read_services import get_user_manual_group_id, get_marked_distri_answer_data, \
    sort_distri_answer, manual_process_operation, get_curr_state, get_chinese_fetch_way, get_chinese_fetch_option, \
    statistic_final_score, add_manual_mark_record, get_manual_type, sort_f_distri_answer, create_manual_history, \
    get_last_aq_suggestion, update_quality_history, update_manual_task_progress, update_manual_mark_history
from apps.manual_read_paper.manual_task_services import launch_manual_task_main
from apps.manual_read_paper.schemas import GetDistriManualTaskReq, GetDistriManualTaskDetailReq, ManualMarkReq, \
    GetMarkHistoryReq, DistriAnswerIdReq, GetManualTaskProcessReq, GetPreNextMarkHistoryReq, ManualArbitrateReq, \
    ManualQualityReq, GetExpertTaskProcessReq, GetArbitrateProcessReq, GetManualQualityProcessReq, \
    GetMarkTaskResultReq
from apps.manual_read_paper.services import get_manual_task_by_conditions, check_task_can_or_not_mark
from apps.operation_ques.op_manual_mark_services import format_op_step_info, create_op_step_group
from apps.read_paper.common_services import splice_image_path
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.models.models import Subject, ManualReadTask, ExamPaper, ExamQuestion, ManualMark, ManualReadTaskGroup, \
    ManualGroupUser, UserInfo, ManualArbitrateQuality, ManualDistributeAnswer, PaperDetail, ManualMarkHistory
from helper import response_utils
from factory_apps import session_depend
from settings import configs
from utils.time_func import format_now_time
from utils.utils import round_half_up, sum_with_precision

manual_read_router = APIRouter()


@manual_read_router.post(path="/get_distri_task", response_model=BaseResponse, summary="获取分配给自己的阅卷任务")
async def get_distri_task(query: GetDistriManualTaskReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取分配给自己的阅卷任务")
    current_page, page_size, m_read_task_name, project_id, subject_id, paper_id, paper_name, task_type = query.model_dump().values()
    user_id = user["user_id"]
    if task_type:
        role_id = str(task_type)
    else:
        role_id = user["role"][0]
    data = {
        "data": [],
        "total": 0
    }
    group_id_list = get_user_manual_group_id(user_id, role_id)
    if not group_id_list:
        return BaseResponse(data=data, msg="暂无阅卷任务")

    distri_info = new_session.query(ManualReadTaskGroup.m_read_task_id, ManualReadTaskGroup.manual_group_id) \
        .filter(ManualReadTaskGroup.manual_group_id.in_(group_id_list)).all()
    if not distri_info:
        return BaseResponse(data=data, msg="暂无阅卷任务")

    m_read_task_id_list = [i[0] for i in distri_info]
    real_group_id_list = [i[1] for i in distri_info]
    total, task_data_list, msg, code = get_manual_task_by_conditions(m_read_task_id_list, real_group_id_list,
                                                                     f"Bearer {user['token']}",
                                                                     current_page=current_page, page_size=page_size,
                                                                     m_read_task_name=m_read_task_name,
                                                                     project_id=project_id, subject_id=subject_id,
                                                                     paper_id=paper_id, paper_name=paper_name,
                                                                     is_distri_task=True)

    if code != 200:
        logger.error(msg)
        return BaseResponse(code=response_utils.params_error, msg=msg)

    # for task_info in task_data_list:
    #     task_id = task_info["m_read_task_id"]
    #     task_id_index = m_read_task_id_list.index(task_id)
    #     task_info["group_id"] = real_group_id_list[task_id_index]
    data = {
        "data": task_data_list,
        "total": total
    }
    logger.info("获取分配给自己的阅卷任务成功")
    return BaseResponse(data=data)


@manual_read_router.post(path="/get_distri_task_detail", response_model=BaseResponse,
                         summary="获取分配的阅卷任务（阅卷、仲裁、质检）的具体信息")
async def get_distri_task_detail(query: GetDistriManualTaskDetailReq, user: Any = Depends(get_current_user),
                                 new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取分配的阅卷任务（阅卷、仲裁、质检）的具体信息")
    (m_read_task_id, paper_id, ques_id, ques_code, ques_type_code, group_id, project_id, subject_id, batch_num,
     history_id, distri_answer_id, curr_user_id, role_id) = query.model_dump().values()

    msg = check_task_can_or_not_mark(new_session, m_read_task_id)
    if msg:
        return BaseResponse(code=response_utils.permission_deny, msg=msg)

    if not curr_user_id:
        curr_user_id = user["user_id"]
    if not role_id:
        role_id = user["role"][0]

    manual_type = get_manual_type(role_id)
    distri_info = []

    ques_info = get_redis_ques_info_dict(new_session)[ques_id]
    subject_id = ques_info["subject_id"]
    subject_name = ques_info["subject_name"]
    ques_code = ques_info["ques_code"]

    parent_ques_info = new_session.query(ExamQuestion.parent_ques_id).filter(
        and_(ExamQuestion.ques_code == ques_code, ExamQuestion.ques_type_code != "F")).first()
    parent_ques_id = parent_ques_info[0] if parent_ques_info else None

    try:
        if ques_type_code != "F" or history_id:
            # 无论是否为非组合组，历史记录详情都由该if分支返回，因为历史记录只需要显示一道题
            is_f_type = False
            # 要查询的字段
            manual_mark_id_field = func.max(ManualMark.manual_mark_id)
            if role_id == "4" or role_id == "5":
                manual_mark_id_field = func.group_concat(ManualMark.manual_mark_id)
            select_fields = [func.max(ManualDistributeAnswer.distri_answer_id),
                             func.max(ManualDistributeAnswer.stu_secret_num),
                             func.max(ManualDistributeAnswer.stu_answer), func.max(ExamPaper.paper_name),
                             func.max(PaperDetail.ques_order), func.max(ExamQuestion.ques_desc),
                             func.max(ExamQuestion.ques_choices), func.max(PaperDetail.ques_score_list),
                             func.max(ExamQuestion.ques_difficulty), func.max(ExamQuestion.standard_answer),
                             func.max(ExamQuestion.standard_choices_code), func.max(ExamQuestion.standard_parse),
                             func.max(ExamQuestion.ques_mark_point), func.group_concat(ManualMark.expert_mark_score),
                             func.max(ManualMark.mark_parse), manual_mark_id_field,
                             func.max(ManualMark.can_expert_mark_again), func.max(ManualMark.not_mark_again_reason),
                             func.max(ManualArbitrateQuality.aq_data), func.max(ManualArbitrateQuality.aq_mark_score),
                             func.max(ManualArbitrateQuality.can_aq_again), func.max(ManualMark.mark_state),
                             func.group_concat(ManualArbitrateQuality.aq_suggestion),
                             func.max(ManualArbitrateQuality.aq_result),
                             func.max(ManualArbitrateQuality.manual_aq_id), func.max(ManualMark.final_mark_time),
                             func.max(ManualReadTask.fetch_score_way), func.max(ManualReadTask.fetch_score_option),
                             func.max(ExamQuestion.standard_answer_html),
                             func.max(ManualDistributeAnswer.stu_answer_id),
                             func.max(ManualReadTask.fetch_score_scope), func.max(ExamQuestion.ques_type_code),
                             func.max(ManualDistributeAnswer.final_mark_score), func.max(ExamQuestion.ques_id),
                             func.group_concat(ManualMark.mark_person_id),
                             func.group_concat(ManualArbitrateQuality.updated_time),
                             func.max(ManualDistributeAnswer.op_file)]

            is_arbitrate_condition = True
            is_quality_condition = True
            marked_distri_answer_id = []
            arbitrator_parse = None

            if history_id:
                m_read_task_info = new_session.query(ManualDistributeAnswer.m_read_task_id) \
                    .filter(ManualDistributeAnswer.distri_answer_id == distri_answer_id).first()
                m_read_task_id = m_read_task_info[0]
                total_condition = and_(ManualDistributeAnswer.distri_answer_id == distri_answer_id,
                                       is_arbitrate_condition, ManualMark.mark_person_id == curr_user_id)
                if role_id == "3":
                    # print("history_id", history_id)
                    score_info = new_session.query(ManualMarkHistory.mark_score,
                                                   ManualMarkHistory.op_step_score).filter(
                        ManualMarkHistory.history_id == history_id).first()
                    final_score, op_step_score = score_info if score_info else (None, None)
                    print("score_info", score_info)
                    history_aq_data, history_aq_result, history_aq_suggestion = None, None, None
                if role_id == "4":
                    is_arbitrate_condition = and_(ManualArbitrateQuality.aq_type == 1,
                                                  ManualArbitrateQuality.aq_state == 2)
                    # 获取已经仲裁过的数据
                    arbitrator_person_id_condition = ManualArbitrateQuality.aq_user_id == curr_user_id
                    total_condition = and_(ManualDistributeAnswer.distri_answer_id == distri_answer_id,
                                           is_arbitrate_condition, arbitrator_person_id_condition)

                    history_data = new_session.query(ManualArbitrateQuality.aq_data,
                                                     ManualArbitrateQuality.aq_suggestion).filter(
                        ManualArbitrateQuality.history_id == history_id).first()
                    history_aq_data, history_aq_suggestion = history_data
                    score_info = new_session.query(ManualMarkHistory.mark_score,
                                                   ManualMarkHistory.op_step_score).filter(
                        ManualMarkHistory.history_id == history_id).first()
                    final_score, op_step_score = score_info if score_info else (None, None)
                    history_aq_result = None
                if role_id == "5":
                    is_arbitrate_condition = and_(ManualArbitrateQuality.aq_type == 2,
                                                  ManualArbitrateQuality.aq_state == 4)
                    # 获取已经质检过的数据
                    arbitrator_person_id_condition = ManualArbitrateQuality.aq_user_id == curr_user_id
                    total_condition = and_(ManualDistributeAnswer.distri_answer_id == distri_answer_id,
                                           is_arbitrate_condition, arbitrator_person_id_condition)

                    history_data = new_session.query(ManualArbitrateQuality.aq_data, ManualArbitrateQuality.aq_result,
                                                     ManualArbitrateQuality.aq_suggestion) \
                        .filter(ManualArbitrateQuality.history_id == history_id).first()
                    history_aq_data, history_aq_result, history_aq_suggestion = history_data
                    arbitrate_data = history_aq_data.get("arbitrate_data")
                    if arbitrate_data:
                        op_step_score = history_aq_data["arbitrate_data"][0].get("op_step_score")
                    else:
                        op_step_score = []
                    task_info = new_session.query(ManualReadTask.fetch_score_way, ManualReadTask.fetch_score_option,
                                                  ManualReadTask.fetch_score_scope).filter(
                        ManualReadTask.m_read_task_id == m_read_task_id).first()
                    fetch_score_way, fetch_score_option, fetch_score_scope = task_info
                    final_score, is_show_arbitrate, _ = statistic_final_score(new_session, m_read_task_id,
                                                                              distri_answer_id, fetch_score_way,
                                                                              fetch_score_option, fetch_score_scope)

                is_f_type = True if parent_ques_id else False

                if ques_id and is_f_type:
                    if paper_id:
                        f_ques_info = new_session.query(PaperDetail.ques_order, ExamQuestion.ques_desc) \
                            .join(ExamQuestion, ExamQuestion.ques_id == PaperDetail.ques_id) \
                            .filter(and_(PaperDetail.paper_id == paper_id, PaperDetail.ques_id == ques_id)).first()
                        f_ques_order, f_ques_desc = f_ques_info if f_ques_info else (None, None)
                    else:
                        f_ques_order = None
                        f_ques_desc = new_session.query(ExamQuestion.ques_desc).filter(
                            ExamQuestion.ques_id == parent_ques_id).scalar()

                    if f_ques_desc:
                        f_ques_desc, _ = splice_image_path(f_ques_desc, [])
                else:
                    f_ques_order, f_ques_desc = None, None

                print("curr_user_id", curr_user_id)
                distri_stmt = select(*select_fields) \
                    .outerjoin(ExamPaper, ExamPaper.paper_id == ManualDistributeAnswer.paper_id) \
                    .join(ExamQuestion, ExamQuestion.ques_id == ManualDistributeAnswer.ques_id) \
                    .outerjoin(PaperDetail, PaperDetail.ques_id == ManualDistributeAnswer.ques_id) \
                    .join(ManualReadTask, ManualReadTask.m_read_task_id == ManualDistributeAnswer.m_read_task_id) \
                    .outerjoin(ManualMark, ManualMark.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
                    .outerjoin(ManualArbitrateQuality,
                               ManualArbitrateQuality.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
                    .where(total_condition).limit(1)
            else:
                if role_id == "3":
                    # 获取已经评过分的数据
                    marked_distri_answer_id, _ = get_marked_distri_answer_data(new_session, m_read_task_id, group_id,
                                                                               [curr_user_id])
                elif role_id == "4":
                    is_arbitrate_condition = and_(ManualArbitrateQuality.aq_type == 1,
                                                  ManualArbitrateQuality.aq_state == 1)
                elif role_id == "5":
                    is_quality_condition = and_(ManualArbitrateQuality.aq_type == 2,
                                                ManualArbitrateQuality.aq_state == 3)
                else:
                    return BaseResponse(msg="角色不匹配", data={"data": [], "total": 0})

                paper_condition = ManualDistributeAnswer.paper_id == paper_id if paper_id else True
                total_condition = and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id,
                                       ManualDistributeAnswer.manual_group_id == group_id,
                                       paper_condition,
                                       ManualDistributeAnswer.ques_id == ques_id,
                                       ~ManualDistributeAnswer.distri_answer_id.in_(marked_distri_answer_id),
                                       is_arbitrate_condition, is_quality_condition)

                total = new_session.query(ManualDistributeAnswer.distri_answer_id) \
                    .outerjoin(ManualMark, ManualMark.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
                    .outerjoin(ManualArbitrateQuality,
                               ManualArbitrateQuality.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
                    .where(total_condition).count()

                if role_id == "3":
                    if total <= batch_num:
                        # 生成待评分数据
                        result, msg = launch_manual_task_main(new_session, m_read_task_id, project_id, subject_id,
                                                              paper_id, ques_code, ques_type_code, [group_id],
                                                              ques_id, parent_ques_id, False)
                        if result:
                            if msg:
                                if total == 0:
                                    return BaseResponse(msg="已无待评分数据", data={"data": [], "total": 0})
                            # 开启线程获取该任务的阅卷人员对应的阅卷数据
                            condition = ManualReadTask.m_read_task_id == m_read_task_id
                            threading.Thread(target=set_manual_mark_info, args=(condition,)).start()
                            logger.info(f"id 为 {group_id} 的小组分配新的待评分数据成功")

                distri_stmt = select(*select_fields) \
                    .outerjoin(ExamPaper, ExamPaper.paper_id == ManualDistributeAnswer.paper_id) \
                    .join(ExamQuestion, ExamQuestion.ques_id == ManualDistributeAnswer.ques_id) \
                    .outerjoin(PaperDetail, PaperDetail.ques_id == ManualDistributeAnswer.ques_id) \
                    .join(ManualReadTask, ManualReadTask.m_read_task_id == ManualDistributeAnswer.m_read_task_id) \
                    .outerjoin(ManualMark, ManualMark.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
                    .outerjoin(ManualArbitrateQuality,
                               ManualArbitrateQuality.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
                    .where(total_condition).group_by(ManualDistributeAnswer.distri_answer_id)

                if batch_num != -1:
                    distri_stmt = distri_stmt.limit(batch_num)

            new_session.commit()

            # 获取操作题的评分步骤
            op_step_list = []
            if ques_type_code == "G":
                op_ques_dict = format_op_step_info(new_session, paper_id, ques_id)
                final_op_ques_dict = create_op_step_group(op_ques_dict)
                op_step_list = final_op_ques_dict.get(ques_id, {}).get("op_step_list", [])

            await_distri_answer_id_list = []
            result = new_session.execute(distri_stmt)
            for row in result:
                is_show_arbitrate = True
                arbitrator_mark_score = None
                if history_id:
                    if role_id == "3":
                        aq_data, real_final_score, quality_result, quality_suggestion, expert_mark_score, op_step_score = history_aq_data, None, history_aq_result, history_aq_suggestion, final_score, op_step_score
                    else:
                        if role_id == "4":
                            arbitrator_mark_score = final_score
                            arbitrator_parse = history_aq_suggestion
                        aq_data, real_final_score, quality_result, quality_suggestion, expert_mark_score, op_step_score = history_aq_data, final_score, history_aq_result, history_aq_suggestion, None, op_step_score
                else:
                    if role_id == "4":
                        arbitrator_mark_score = row[19]
                        arbitrator_parse = get_last_aq_suggestion(row[22], row[35])
                    aq_data, quality_result = row[18], row[23]
                    # 获取更新时间最大的对应索引的质检意见
                    quality_suggestion = get_last_aq_suggestion(row[22], row[35])
                    expert_mark_score_concat, mark_person_id_concat = row[13], row[34]
                    if role_id == "3":
                        real_final_score, is_show_arbitrate = None, False
                        if expert_mark_score_concat and mark_person_id_concat:
                            for mark_score, mark_person_id in zip(expert_mark_score_concat.split(","),
                                                                  mark_person_id_concat.split(",")):
                                if mark_person_id == curr_user_id:
                                    expert_mark_score = float(mark_score) if mark_score is not None else mark_score
                                    break
                                else:
                                    expert_mark_score = None
                        else:
                            expert_mark_score = None
                    else:
                        expert_mark_score = expert_mark_score_concat
                        real_final_score, is_show_arbitrate, _ = statistic_final_score(new_session, m_read_task_id,
                                                                                       row[0], row[26], row[27],
                                                                                       row[30])

                if aq_data:
                    expert_data = aq_data.get("expert_data")
                    arbitrator_data = aq_data.get("arbitrate_data")
                    if (expert_data and role_id == "4") or (expert_data and role_id == "5"):
                        for index, distri_data in enumerate(expert_data):
                            mark_person_id = distri_data["mark_person_id"]
                            expert_info = new_session.query(UserInfo.username, UserInfo.name).filter(
                                UserInfo.user_id == mark_person_id).first()
                            expert_data[index]["username"] = expert_info[0]
                            expert_data[index]["name"] = expert_info[1]
                        if arbitrator_data and role_id == "5":
                            for index, distri_data in enumerate(arbitrator_data):
                                mark_person_id = distri_data["mark_person_id"]
                                arbitrator_info = new_session.query(UserInfo.username, UserInfo.name).filter(
                                    UserInfo.user_id == mark_person_id).first()
                                arbitrator_data[index]["username"] = arbitrator_info[0]
                                arbitrator_data[index]["name"] = arbitrator_info[1]
                    else:
                        expert_data = None
                        arbitrator_data = None
                else:
                    expert_data = None
                    arbitrator_data = None

                mark_state = row[21]
                manual_aq_id = row[24]
                curr_state = get_curr_state(mark_state)

                ques_desc, ques_choices = row[5], row[6]

                if ques_desc:
                    ques_desc, ques_choices = splice_image_path(ques_desc, ques_choices)

                if paper_id:
                    ques_score = sum_with_precision(row[7])
                    ques_order = re.sub(r'^0+', '', row[4]) if row[4] else None
                else:
                    ques_score = ques_info["ques_type_score"]
                    ques_order = f'（{ques_info["small_ques_num"]}）' if ques_info["small_ques_num"] else None

                distri_item = {
                    "distri_answer_id": row[0],
                    "stu_secret_num": row[1],
                    "paper_id": paper_id,
                    "subject_id": subject_id,
                    "ques_code": ques_code,
                    "stu_answer": row[2],
                    "paper_name": row[3],
                    "subject_name": subject_name,
                    "ques_order": ques_order,
                    "ques_desc": ques_desc,
                    "ques_choices": ques_choices,
                    "ques_score_list": row[7] if row[7] else ques_info["ques_score_list"],
                    "ques_score": ques_score,
                    "ques_difficulty": row[8],
                    "standard_answer": row[9],
                    "standard_choices_code": row[10],
                    "standard_parse": row[11],
                    "ques_mark_point": row[12],
                    "expert_mark_score": expert_mark_score,
                    "mark_parse": row[14],
                    "manual_mark_id": row[15],
                    "can_expert_mark_again": row[16],
                    "not_mark_again_reason": row[17],
                    "expert_data": expert_data,
                    "arbitrator_mark_score": arbitrator_mark_score,
                    "can_arbitrator_mark_again": row[20] if role_id == "4" else None,
                    "mark_state": mark_state,
                    "arbitrator_parse": arbitrator_parse,
                    "quality_suggestion": quality_suggestion,
                    "quality_result": quality_result,
                    "manual_aq_id": manual_aq_id,
                    "final_mark_time": row[25] and str(row[25]).replace("T", " "),
                    "fetch_score_way": get_chinese_fetch_way(row[26]),
                    "fetch_score_option": get_chinese_fetch_option(row[27]),
                    "standard_answer_html": row[28],
                    "stu_answer_id": row[29],
                    "final_mark_score": real_final_score,
                    "ques_type_code": row[31],
                    # "final_mark_score": row[32],
                    "ques_id": row[33],
                    "arbitrator_data": arbitrator_data,
                    "manual_type": manual_type,
                    "curr_state": curr_state,
                    "is_f_type": is_f_type,
                    "is_show_arbitrate": is_show_arbitrate,
                    "op_step_list": op_step_list,
                    "op_file": row[36],
                    "step_score_list": op_step_score if history_id else [None for _ in range(len(op_step_list))]
                }
                if distri_answer_id and is_f_type:
                    distri_item["f_ques_order"] = f_ques_order
                    distri_item["f_ques_desc"] = f_ques_desc
                await_distri_answer_id_list.append(row[0])
                distri_info.append(distri_item)
        else:
            # 组合题处理逻辑
            distri_info = get_f_manual_read_data(new_session, m_read_task_id, paper_id, ques_id, ques_code,
                                                 group_id, project_id, subject_id, subject_name, batch_num,
                                                 curr_user_id, role_id)
    except Exception as e:
        logger.error(f"获取分配的阅卷任务的具体信息失败，{e}")
        traceback.print_exc()
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"获取分配的阅卷任务的具体信息失败")
    logger.info("获取分配的阅卷任务的具体信息成功")
    if ques_type_code != "F" and not distri_answer_id:
        # 已经被其他专家评完分的显示在前面
        distri_info = sort_distri_answer(new_session, m_read_task_id, ques_id, group_id, curr_user_id, distri_info)
    else:
        distri_info = sort_f_distri_answer(new_session, m_read_task_id, ques_code, group_id, curr_user_id, distri_info)
    data = {
        "data": distri_info
    }
    return BaseResponse(msg="获取分配的阅卷任务的具体信息成功", data=data)


@manual_read_router.post(path="/manual_mark", response_model=BaseResponse, summary="人工评分")
async def manual_mark(query: ManualMarkReq, user: Any = Depends(get_current_user),
                      new_session: Session = Depends(session_depend)):
    (m_read_task_id, distri_answer_id, ques_id, expert_mark_score, mark_parse, manual_mark_id, group_id, mark_type,
     manual_aq_id, stu_secret_num, history_id, step_score_list) = query.model_dump().values()

    # mark_distri_manage = json.loads(redis.get("mark_distri_manage"))
    # is_distri = mark_distri_manage[m_read_task_id][read_group_id].get(distri_answer_id, False)
    # if not is_distri:
    #     return BaseResponse(code=response_utils.permission_deny, msg="无该评分权限")

    curr_user_id = user["user_id"]
    mark_type_str = "评分"
    record_expert_progress, is_check_finish_state = True, False
    new_step_score_list = []
    if step_score_list:
        for i in step_score_list:
            new_step_score_list.append(i) if i else new_step_score_list.append(0)

    task_state = get_task_state_by_task_id(new_session, m_read_task_id)
    if task_state == 5:
        return BaseResponse(code=response_utils.operation_repeat, msg="该任务已结束，无法进行评分")

    try:
        if mark_type == 1:
            logger.info(f"{user['username']} 进行评分")
            exist_manual_mark_id = new_session.query(ManualMark.manual_mark_id).filter(
                and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.distri_answer_id == distri_answer_id,
                     ManualMark.mark_person_id == curr_user_id, ManualMark.mark_state != 6)).count()
            if exist_manual_mark_id > 0:
                return BaseResponse(code=response_utils.operation_repeat, msg="该评分信息已评分")
            result = add_manual_mark_record(new_session, distri_answer_id, m_read_task_id, ques_id, curr_user_id,
                                            expert_mark_score, mark_parse, op_step_score=new_step_score_list)
            if not result:
                return BaseResponse(code=response_utils.server_error, msg=f"{mark_type_str} 失败")
            else:
                record_expert_progress = True
        elif mark_type == 2:
            if not manual_mark_id or not history_id:
                return BaseResponse(code=response_utils.params_error, msg="参数错误")
            logger.info(f"{user['username']} 进行重评")
            mark_type_str = "重评"
            not_mark_again_reason, mark_count = new_session.query(ManualMark.not_mark_again_reason,
                                                                  ManualMark.mark_count).filter(
                ManualMark.manual_mark_id == manual_mark_id).first()
            if not_mark_again_reason:
                return BaseResponse(code=response_utils.operation_repeat, msg=not_mark_again_reason)
            new_session.query(ManualMark).filter(and_(ManualMark.m_read_task_id == m_read_task_id,
                                                      ManualMark.distri_answer_id == distri_answer_id,
                                                      ManualMark.mark_person_id == curr_user_id)).update({
                ManualMark.expert_mark_score: expert_mark_score,
                ManualMark.mark_parse: mark_parse,
                ManualMark.mark_count: mark_count + 1,
                ManualMark.op_step_score: new_step_score_list,
                ManualMark.final_mark_time: format_now_time(),
                ManualMark.expert_mark_time: format_now_time()
            })
            new_session.query(ManualMarkHistory).filter(ManualMarkHistory.history_id == history_id).update({
                ManualMarkHistory.mark_score: expert_mark_score,
                ManualMarkHistory.op_step_score: new_step_score_list,
                ManualMarkHistory.updated_time: format_now_time()
            })
            # 如果有最终分数则清空并更新阅卷任务进度
            new_session.query(ManualDistributeAnswer).filter(
                ManualDistributeAnswer.distri_answer_id == distri_answer_id).update({
                ManualDistributeAnswer.final_mark_score: None,
                ManualDistributeAnswer.answer_parse: None
            })
            new_session.commit()
            record_expert_progress, is_check_finish_state = True, True
        elif mark_type == 3:
            if not manual_mark_id or not manual_aq_id:
                return BaseResponse(code=response_utils.params_error, msg="参数错误")
            logger.info(f"{user['username']} 进行质检返评")
            mark_type_str = "质检返评"
            new_session.query(ManualMark).filter(and_(ManualMark.m_read_task_id == m_read_task_id,
                                                      ManualMark.distri_answer_id == distri_answer_id,
                                                      ManualMark.mark_person_id == curr_user_id)).update({
                ManualMark.expert_mark_score: expert_mark_score,
                ManualMark.mark_parse: mark_parse,
                ManualMark.op_step_score: new_step_score_list,
                ManualMark.mark_count: ManualMark.mark_count + 1,
                ManualMark.mark_state: 7,
                ManualMark.can_expert_mark_again: 1,
                ManualMark.not_mark_again_reason: None,
                ManualMark.final_mark_time: format_now_time(),
                ManualMark.expert_mark_time: format_now_time()
            })
            yet_return_user_id_list = new_session.query(ManualArbitrateQuality.yet_return_user_id).filter(
                ManualArbitrateQuality.manual_aq_id == manual_aq_id).scalar()
            yet_return_user_id_list.append(curr_user_id)
            new_session.query(ManualArbitrateQuality).filter(
                ManualArbitrateQuality.manual_aq_id == manual_aq_id).update({
                ManualArbitrateQuality.yet_return_user_id: yet_return_user_id_list
            })
            new_session.commit()
        else:
            return BaseResponse(code=response_utils.params_error, msg="参数错误")
    except Exception as e:
        logger.error(f"{mark_type_str}失败，{e}")
        traceback.print_exc()
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"{mark_type_str}失败")
    logger.info(f"{mark_type_str}成功")
    # 开启线程，根据模式判断下一步操作
    threading.Thread(target=manual_process_operation,
                     args=(m_read_task_id, distri_answer_id, group_id, ques_id, "bpmn:userTask", True, 1,
                           manual_aq_id, record_expert_progress, is_check_finish_state)).start()

    if mark_type != 2:
        # 生成历史记录
        threading.Thread(target=create_manual_history,
                         args=(None, m_read_task_id, distri_answer_id, mark_type, expert_mark_score, mark_parse,
                               curr_user_id, None, new_step_score_list)).start()
    else:
        threading.Thread(target=update_manual_mark_history, args=(history_id, expert_mark_score, mark_parse)).start()
    return BaseResponse(msg=f"{mark_type_str}成功")


@manual_read_router.post(path="/manual_arbitration", response_model=BaseResponse, summary="仲裁")
async def manual_arbitration(query: ManualArbitrateReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    (manual_aq_id, m_read_task_id, distri_answer_id, ques_id, manual_mark_id_list, arbitrate_type, arbitrate_mark_score,
     arbitrator_parse, group_id, history_id, step_score_list) = query.model_dump().values()
    curr_user_id = user["user_id"]
    new_step_score_list = []
    if step_score_list:
        for i in step_score_list:
            new_step_score_list.append(i) if i else new_step_score_list.append(0)
    mark_type, history_type = "仲裁", 4

    record_expert_progress, is_check_finish_state = True, False

    try:
        task_state = get_task_state_by_task_id(new_session, m_read_task_id)
        if task_state == 5:
            return BaseResponse(code=response_utils.operation_repeat, msg="该任务已结束，无法进行仲裁")

        if arbitrate_type == 2:
            logger.info(f"{user['username']} 进行重新仲裁")
            mark_type, history_type = "重新仲裁", 5
            if new_session.query(ManualArbitrateQuality.can_aq_again).filter(
                    ManualArbitrateQuality.manual_aq_id == manual_aq_id).scalar() == 0:
                return BaseResponse(code=response_utils.operation_repeat, msg="该评分信息已进入质检，无法进行重新仲裁")
            # 如果有最终分数则清空并更新阅卷任务进度
            new_session.query(ManualDistributeAnswer).filter(
                ManualDistributeAnswer.distri_answer_id == distri_answer_id).update({
                ManualDistributeAnswer.final_mark_score: None,
                ManualDistributeAnswer.answer_parse: None
            })
            new_session.commit()
            record_expert_progress, is_check_finish_state = True, True
        else:
            logger.info(f"{user['username']} 进行仲裁")
            wait_arbitrated = new_session.query(ManualArbitrateQuality.manual_aq_id == manual_aq_id) \
                .join(ManualDistributeAnswer,
                      ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
                .filter(and_(ManualArbitrateQuality.manual_aq_id == manual_aq_id,
                             ManualArbitrateQuality.aq_type == 1, ManualArbitrateQuality.aq_state == 1,
                             ManualDistributeAnswer.manual_group_id == group_id)).count()
            if not wait_arbitrated:
                return BaseResponse(code=response_utils.operation_repeat, msg="该评分信息已仲裁")

        new_session.query(ManualMark).filter(ManualMark.manual_mark_id.in_(manual_mark_id_list)).update({
            ManualMark.can_expert_mark_again: 0,
            ManualMark.not_mark_again_reason: "该作答信息已仲裁",
            ManualMark.mark_state: 3,
            ManualMark.final_mark_time: format_now_time()
        })
        new_session.query(ManualArbitrateQuality).filter(ManualArbitrateQuality.manual_aq_id == manual_aq_id) \
            .update({
            ManualArbitrateQuality.aq_state: 2,
            ManualArbitrateQuality.aq_mark_score: arbitrate_mark_score,
            ManualArbitrateQuality.aq_suggestion: arbitrator_parse,
            ManualArbitrateQuality.op_step_score: new_step_score_list,
            ManualArbitrateQuality.aq_user_id: curr_user_id
        })
        if history_id:
            new_session.query(ManualMarkHistory).filter(ManualMarkHistory.history_id == history_id).update({
                ManualMarkHistory.mark_score: arbitrate_mark_score,
                ManualMarkHistory.op_step_score: new_step_score_list,
                ManualMarkHistory.updated_time: format_now_time()
            })
        new_session.commit()
    except Exception as e:
        logger.error(f"{mark_type}失败，{e}")
        traceback.print_exc()
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"{mark_type}失败")
    logger.info(f"{mark_type}成功")
    # 开启线程，根据模式判断下一步操作
    threading.Thread(target=manual_process_operation,
                     args=(m_read_task_id, distri_answer_id, group_id, ques_id, "bpmn:arbitratorNode", True, 1,
                           manual_aq_id, record_expert_progress, is_check_finish_state)).start()
    if arbitrate_type != 2:
        # 生成历史记录
        threading.Thread(target=create_manual_history, args=(None, m_read_task_id, distri_answer_id, history_type,
                                                             arbitrate_mark_score, None, curr_user_id,
                                                             manual_aq_id, new_step_score_list)).start()
    return BaseResponse(msg=f"{mark_type}成功")


@manual_read_router.post(path="/manual_quality", response_model=BaseResponse, summary="质检")
async def manual_quality(query: ManualQualityReq, user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend)):
    (manual_aq_id, m_read_task_id, distri_answer_id, ques_id, manual_mark_id, quality_result, quality_suggestion,
     group_id, quality_type, history_id) = query.model_dump().values()
    curr_user_id = user["user_id"]

    task_state = get_task_state_by_task_id(new_session, m_read_task_id)
    if task_state == 5:
        return BaseResponse(code=response_utils.operation_repeat, msg="该任务已结束，无法进行质检")

    aq_result = 1 if quality_result else 2
    if not quality_result and not quality_suggestion:
        return BaseResponse(code=response_utils.params_error, msg="质检不通过请输入质检意见")

    if quality_type == 1:
        mark_type, history_type = "质检", 6
        yet_quality = new_session.query(ManualArbitrateQuality).filter(
            and_(ManualArbitrateQuality.manual_aq_id == manual_aq_id, ManualArbitrateQuality.aq_state == 4)).first()
        if yet_quality:
            return BaseResponse(code=response_utils.operation_repeat, msg="该数据已质检，请勿重复操作")
    else:
        mark_type, history_type = "重新质检", 7

    try:
        new_session.query(ManualMark).filter(
            and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.distri_answer_id == distri_answer_id)).update({
            ManualMark.can_expert_mark_again: 0 if quality_result else 1,
            ManualMark.not_mark_again_reason: "该作答信息已质检",
            ManualMark.mark_state: 5 if quality_result else 6,
            ManualMark.final_mark_time: format_now_time()
        })
        if not history_id:
            history_id = configs.snow_worker.get_id()
        new_session.query(ManualArbitrateQuality).filter(ManualArbitrateQuality.manual_aq_id == manual_aq_id).update({
            ManualArbitrateQuality.aq_state: 4,
            ManualArbitrateQuality.aq_result: aq_result,
            ManualArbitrateQuality.aq_suggestion: quality_suggestion,
            ManualArbitrateQuality.aq_user_id: curr_user_id,
            ManualArbitrateQuality.history_id: history_id
        })
        new_session.commit()
    except Exception as e:
        logger.error(f"{distri_answer_id} {mark_type}失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"{mark_type}失败")
    logger.info(f"{distri_answer_id} {mark_type}成功")
    # 开启线程，根据模式判断下一步操作
    threading.Thread(target=manual_process_operation,
                     args=(m_read_task_id, distri_answer_id, group_id, ques_id, "bpmn:qualityNode", True,
                           aq_result)).start()

    quality_num = 1 if quality_result else 0
    if quality_type != 2:
        # 生成历史记录
        threading.Thread(target=create_manual_history,
                         args=(history_id, m_read_task_id, distri_answer_id, history_type, quality_num,
                               quality_suggestion, curr_user_id)).start()
    else:
        # 更新历史记录
        threading.Thread(target=update_quality_history,
                         args=(history_id, history_type, quality_num, quality_suggestion)).start()
    return BaseResponse(msg=f"{mark_type}成功")


@manual_read_router.post(path="/get_mark_history", response_model=BaseResponse, summary="获取人工评分历史记录列表")
async def get_mark_history(query: GetMarkHistoryReq, user: Any = Depends(get_current_user),
                           new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取人工评分历史记录列表")
    current_page, page_size, m_read_task_id, stu_secret_num, start_time, end_time = query.model_dump().values()
    user_id = user["user_id"]
    role_id = user["role"][0]

    secret_num_condition = ManualDistributeAnswer.stu_secret_num.ilike(
        f"%{stu_secret_num}%") if stu_secret_num else True
    time_condition = ManualMarkHistory.created_time.between(start_time, end_time) if start_time and end_time else True

    limit = query.current_page - 1
    offset = limit * query.page_size

    history_data = []
    # 1 表示专家评分，2 表示专家重评，3 表示专家质检返评，4 表示仲裁，5 表示重新仲裁，6 表示质检，7 表示重新质检
    if role_id == "3":
        history_type = [1, 2, 3]
    elif role_id == "4":
        history_type = [4, 5]
    elif role_id == "5":
        history_type = [6, 7]
    else:
        return BaseResponse(code=response_utils.params_error, msg="角色错误")

    task_condition = ManualMarkHistory.m_read_task_id == m_read_task_id
    user_condition = ManualMarkHistory.mark_user_id == user_id
    history_type_condition = ManualMarkHistory.history_type.in_(history_type)
    total_condition = and_(task_condition, user_condition, history_type_condition, secret_num_condition, time_condition)

    total = new_session.query(ManualMarkHistory.history_id) \
        .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualMarkHistory.distri_answer_id) \
        .filter(total_condition).count()

    history_info = new_session.query(ManualMarkHistory.history_id, ManualMarkHistory.distri_answer_id,
                                     ManualMarkHistory.mark_score, ManualDistributeAnswer.stu_secret_num,
                                     ManualMarkHistory.created_time) \
        .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualMarkHistory.distri_answer_id) \
        .filter(total_condition) \
        .order_by(ManualMarkHistory.created_time.desc(), ManualMarkHistory.history_id.desc()) \
        .limit(page_size).offset(offset)

    if history_info:
        for history_id, distri_answer_id, mark_score, stu_secret_num, created_time in history_info:
            if role_id == "5":
                mark_score = "通过" if mark_score else "不通过"
            history_item = {
                "history_id": history_id,
                "distri_answer_id": distri_answer_id,
                "stu_secret_num": stu_secret_num,
                "expert_mark_score": mark_score,
                "created_time": created_time and str(created_time).replace("T", " ")
            }
            history_data.append(history_item)

    logger.info("获取人工评分历史记录列表成功")
    data = {
        "data": history_data,
        "total": total
    }
    return BaseResponse(msg="获取人工评分历史记录列表成功", data=data)


@manual_read_router.post(path="/get_mark_history_detail", response_model=BaseResponse,
                         summary="获取人工评分历史记录详情")
async def get_mark_history_detail(query: DistriAnswerIdReq, user: Any = Depends(get_current_user)):
    history_id, distri_answer_id, paper_id, ques_id, ques_type_code = query.model_dump().values()
    logger.info(f"{user['username']} 获取人工评分历史记录id为 {distri_answer_id} 的详情")

    data = {
        "history_id": history_id,
        "distri_answer_id": distri_answer_id,
        "ques_id": ques_id,
        "ques_type_code": ques_type_code,
        "paper_id": paper_id
    }
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/manual_read/get_distri_task_detail"
    res, msg = request_api(url, "POST", data, token, "人工评分历史记录详情")
    if res == 0:
        return BaseResponse(code=response_utils.server_error, msg=msg)
    distri_answer_data = res["data"][0]
    distri_answer_data["history_id"] = history_id
    data = {
        "data": distri_answer_data
    }
    return BaseResponse(msg=msg, data=data)


@manual_read_router.post(path="/get_pre_next_history", response_model=BaseResponse,
                         summary="获取上一条或者下一条的历史记录详情")
async def get_pre_next_history(query: GetPreNextMarkHistoryReq, user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend)):
    history_id, get_type, paper_id, ques_id, m_read_task_id, manual_mark_id, current_page, page_size = query.model_dump().values()
    role_id = user["role"][0]
    curr_user_id = user["user_id"]
    if role_id == "3":
        history_type = [1, 2, 3]
    elif role_id == "4":
        history_type = [4, 5]
    elif role_id == "5":
        history_type = [6, 7]
    else:
        return BaseResponse(code=response_utils.permission_deny, msg="角色有误")
    # 获取所有 history_id 的列表
    history_info = new_session.query(ManualMarkHistory.history_id) \
        .filter(and_(ManualMarkHistory.m_read_task_id == m_read_task_id,
                     ManualMarkHistory.history_type.in_(history_type),
                     ManualMarkHistory.mark_user_id == curr_user_id)) \
        .order_by(ManualMarkHistory.created_time.desc()).all()
    history_id_list = [i[0] for i in history_info]
    index = history_id_list.index(history_id)
    if get_type:
        if index + 1 > len(history_id_list) - 1:
            return BaseResponse(code=response_utils.no_field, msg="没有下一条历史记录")
        return_history_id = history_id_list[index + 1]
    else:
        if index - 1 < 0:
            return BaseResponse(code=response_utils.no_field, msg="没有上一条历史记录")
        return_history_id = history_id_list[index - 1]

    return_history_info = new_session.query(ManualMarkHistory.distri_answer_id, ExamQuestion.parent_ques_id,
                                            ExamQuestion.ques_type_code) \
        .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualMarkHistory.distri_answer_id) \
        .join(ExamQuestion, ExamQuestion.ques_id == ManualDistributeAnswer.ques_id) \
        .filter(ManualMarkHistory.history_id == return_history_id).first()
    distri_answer_id, parent_ques_id, ques_type_code = return_history_info

    req_data = {
        "history_id": return_history_id,
        "distri_answer_id": distri_answer_id,
        "ques_id": ques_id,
        "ques_type_code": ques_type_code,
        "paper_id": paper_id
    }
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/manual_read/get_distri_task_detail"
    res, msg = request_api(url, "POST", req_data, token, "人工评分历史记录详情")
    if res == 0:
        return BaseResponse(code=response_utils.server_error, msg=msg)
    distri_answer_data = res["data"][0]
    distri_answer_data["history_id"] = return_history_id
    data = {"data": distri_answer_data}

    return BaseResponse(msg=msg, data=data)


@manual_read_router.post(path="/get_distri_task_process", response_model=BaseResponse, summary="获取阅卷总进度")
async def get_distri_task_process(query: GetManualTaskProcessReq, user: Any = Depends(get_current_user),
                                  new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取阅卷进度")
    # 阅卷总进度 = (已评分数量 + 已质检返评数量) / (考生数量 * 每组专家数量 + 质检不通过的个数 * 每组专家数量)
    m_read_task_id, project_id, subject_id, paper_id, ques_code, ques_id, group_id = query.model_dump().values()
    # 考生数量
    stu_count = get_stu_num_by_task_id(new_session, m_read_task_id, project_id, subject_id, paper_id, ques_id,
                                       ques_code)
    # 每组专家数量
    expert_count = new_session.query(ManualGroupUser.manual_group_user_id).filter(
        and_(ManualGroupUser.manual_group_id == group_id, ManualGroupUser.role_id == 3)).count()
    # 人工已评分数量
    expert_marked_count = new_session.query(ManualMark.manual_mark_id).filter(
        ManualMark.m_read_task_id == m_read_task_id).count()

    # 已质检返评数量
    quality_return_user_id = new_session.query(ManualArbitrateQuality.yet_return_user_id) \
        .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id,
                     ManualArbitrateQuality.aq_type == 2)).all()
    quality_return_count = 0
    for i in quality_return_user_id:
        quality_return_count += len(i[0])

    # 质检不通过的个数
    quality_not_pass_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 2,
             ManualArbitrateQuality.aq_result.in_([2, 3]))).count()

    # 组合题要乘以小题数量
    small_ques_count = get_small_ques_count(ques_code)

    small_ques_count = small_ques_count if small_ques_count else 1

    marked_count = expert_marked_count + quality_return_count

    total = int(stu_count) * expert_count * small_ques_count + quality_not_pass_count * expert_count

    percentage = round_half_up(marked_count / total, 2) * 100 if total else 0

    data = {
        "data": {
            "percentage": percentage,
            "total": total,
            "marked_count": marked_count
        }
    }
    return BaseResponse(data=data)


@manual_read_router.post(path="/get_distri_task_expert_process", response_model=BaseResponse,
                         summary="获取阅卷专家个人进度")
async def get_distri_task_expert_process(query: GetExpertTaskProcessReq, user: Any = Depends(get_current_user),
                                         new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取阅卷专家个人进度")
    # 阅卷专家个人进度 = (该专家已评分数量 + 该专家已质检返评数量) / (该组被分配的作答数量 + 质检返评数量)
    m_read_task_id, group_id, paper_id, ques_code, ques_id = query.model_dump().values()
    curr_user_id = user["user_id"]
    paper_condition = ManualDistributeAnswer.paper_id == paper_id if paper_id else True
    distri_answer_condition = and_(
        ManualDistributeAnswer.m_read_task_id == m_read_task_id,
        ManualDistributeAnswer.manual_group_id == group_id,
        paper_condition,
        ManualDistributeAnswer.ques_code == ques_code)

    # 该组被分配的作答数量
    distri_answer_count = new_session.query(ManualDistributeAnswer.distri_answer_id).filter(
        distri_answer_condition).count()

    # 该专家已评分数量
    expert_marked_count = new_session.query(ManualMark.manual_mark_id) \
        .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualMark.distri_answer_id) \
        .filter(and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id,
                     distri_answer_condition)).count()

    # 该专家已质检返评数量
    quality_return_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
        .join(ManualDistributeAnswer,
              ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
        .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 2,
                     ManualArbitrateQuality.yet_return_user_id.contains(curr_user_id),
                     distri_answer_condition)).count()

    # 该专家待质检和已质检返评数量
    expert_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
        .join(ManualDistributeAnswer,
              ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
        .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 2,
                     ManualArbitrateQuality.aq_result.in_([2, 3]),
                     distri_answer_condition)).count()

    marked_count = expert_marked_count + quality_return_count
    total = expert_quality_count + distri_answer_count
    if total == 0:
        percentage = 0
    else:
        percentage = round_half_up(marked_count / total, 2) * 100
    data = {
        "data": {
            "percentage": percentage,
            "total": total,
            "marked_count": marked_count
        }
    }
    return BaseResponse(data=data)


@manual_read_router.post(path="/get_arbitration_ratio", response_model=BaseResponse, summary="获取已仲裁总比例")
async def get_arbitration_ratio(query: GetManualTaskProcessReq, user: Any = Depends(get_current_user),
                                new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取已仲裁总比例")
    # 仲裁比例 = 已仲裁数量 / 考生数量
    m_read_task_id, project_id, subject_id, paper_id, ques_code, ques_id, group_id = query.model_dump().values()
    # 考生数量
    stu_count = get_stu_num_by_task_id(new_session, m_read_task_id, project_id, subject_id, paper_id, ques_id,
                                       ques_code)

    small_ques_count = get_small_ques_count(ques_code)
    total = stu_count * small_ques_count if small_ques_count else stu_count
    # 已仲裁数量
    arbitrate_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 1,
             ManualArbitrateQuality.aq_mark_score.isnot(None))).count()

    if total == 0:
        percentage = 0
    else:
        percentage = round_half_up(arbitrate_count / total, 2) * 100
    data = {
        "data": {
            "percentage": percentage,
            "total": total,
            "marked_count": arbitrate_count
        }
    }
    return BaseResponse(data=data)


@manual_read_router.post(path="/get_arbitration_process", response_model=BaseResponse, summary="获取阅卷组长仲裁进度")
async def get_arbitration_process(query: GetArbitrateProcessReq, user: Any = Depends(get_current_user),
                                  new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取阅卷组长仲裁进度")
    # 仲裁进度 = 已仲裁数量 / 仲裁数量
    task_group_list = query.task_group_list

    data_list = []
    for task_dict in task_group_list:
        m_read_task_id, group_id, ques_id = task_dict["m_read_task_id"], task_dict["group_id"], task_dict["ques_id"]
        # 已仲裁数量
        arbitrate_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
            and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 1,
                 ManualArbitrateQuality.aq_mark_score.isnot(None),
                 ManualArbitrateQuality.aq_user_id == user["user_id"])).count()

        all_arbitrate_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
            .join(ManualDistributeAnswer,
                  ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
            .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 1,
                         ManualDistributeAnswer.manual_group_id == group_id)).count()

        if all_arbitrate_count:
            percentage = round_half_up(arbitrate_count / all_arbitrate_count, 2) * 100
        else:
            percentage = 0
        item = {
            "m_read_task_id": m_read_task_id,
            "percentage": percentage,
            "total": all_arbitrate_count,
            "arbitrate_count": arbitrate_count
        }
        data_list.append(item)
    data = {
        "data": data_list
    }
    return BaseResponse(data=data)


@manual_read_router.post(path="/get_quality_ratio", response_model=BaseResponse, summary="获取质检进度")
async def get_arbitration_ratio(query: GetManualQualityProcessReq, user: Any = Depends(get_current_user),
                                new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取质检进度")
    # 质检进度 = 已质检数量 / (已质检数量 + 待质检数量)
    m_read_task_id = query.m_read_task_id
    data_list = []
    for task_id in m_read_task_id:
        # 已质检数量
        yet_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
            and_(ManualArbitrateQuality.m_read_task_id == task_id, ManualArbitrateQuality.aq_type == 2,
                 ManualArbitrateQuality.aq_state == 4)).count()

        # 所有质检数量
        all_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
            and_(ManualArbitrateQuality.m_read_task_id == task_id, ManualArbitrateQuality.aq_type == 2)).count()

        if all_quality_count:
            percentage = round_half_up(yet_quality_count / all_quality_count, 2) * 100
        else:
            percentage = 0
        item = {
            "m_read_task_id": task_id,
            "percentage": percentage,
            "total": all_quality_count,
            "marked_count": yet_quality_count
        }
        data_list.append(item)
    data = {
        "data": data_list
    }
    return BaseResponse(data=data)


@manual_read_router.post(path="/get_quality_general", response_model=BaseResponse, summary="获取质检总览数据和质检比例")
async def get_quality_general(query: GetManualTaskProcessReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取质检总览数据")
    m_read_task_id, project_id, subject_id, paper_id, ques_code, ques_id, group_id = query.model_dump().values()
    try:
        # 考生数量
        stu_count = get_stu_num_by_task_id(new_session, m_read_task_id, project_id, subject_id, paper_id, ques_id,
                                           ques_code)
        # 小题数量
        small_ques_count = get_small_ques_count(ques_code)
        small_ques_count = small_ques_count if small_ques_count else 1
        # 所有质检数量
        all_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
            and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 2)).count()
        # 当前组的质检数量
        group_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
            .join(ManualDistributeAnswer,
                  ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
            .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 2,
                         ManualDistributeAnswer.manual_group_id == group_id)).count()
        # 已质检数量
        yet_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
            .join(ManualDistributeAnswer,
                  ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
            .filter(
            and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 2,
                 ManualArbitrateQuality.aq_state == 4, ManualDistributeAnswer.manual_group_id == group_id)).count()
        quality_pass_count = new_session.query(ManualArbitrateQuality.manual_aq_id) \
            .join(ManualDistributeAnswer,
                  ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
            .filter(
            and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id, ManualArbitrateQuality.aq_type == 2,
                 ManualArbitrateQuality.aq_result == 1, ManualDistributeAnswer.manual_group_id == group_id)).count()
        quality_not_pass_count = yet_quality_count - quality_pass_count
        quality_not_pass_rate = f"{round_half_up(quality_not_pass_count / yet_quality_count * 100, 2)}%" if yet_quality_count else "0%"

        data = {
            "all_quality_count": all_quality_count,
            "yet_quality_count": yet_quality_count,
            "group_quality_count": group_quality_count,
            "quality_pass_count": quality_pass_count,
            "quality_not_pass_count": quality_not_pass_count if quality_not_pass_count >= 0 else 0,
            "quality_not_pass_rate": quality_not_pass_rate,
            "total": stu_count * small_ques_count
        }
        return BaseResponse(data=data, msg="获取质检总览数据成功")
    except Exception as e:
        logger.error(f"获取质检总览数据失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg="获取质检总览数据失败")


@manual_read_router.post(path="/get_read_task_result", response_model=BaseResponse, summary="获取评分结果")
async def get_read_task_result(query: GetMarkTaskResultReq, user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取评分结果")
    (current_page, page_size, m_read_task_id, paper_id, execute_user_id, execute_role_id, score_range, time_range,
     stu_secret_num) = query.model_dump().values()
    mark_data = []
    limit = current_page - 1
    offset = limit * page_size

    paper_name = new_session.query(ExamPaper.paper_name).filter(ExamPaper.paper_id == paper_id).scalar()

    stu_secret_condition = ManualDistributeAnswer.stu_secret_num == stu_secret_num if stu_secret_num else True

    paper_condition = PaperDetail.paper_id == paper_id if paper_id else True
    if execute_role_id == "3":
        score_condition = ManualMark.expert_mark_score.between(*score_range) if score_range else True
        time_condition = ManualMark.created_time.between(*time_range) if time_range else True
        user_condition = ManualMark.mark_person_id == execute_user_id

        total_condition = and_(ManualMark.m_read_task_id == m_read_task_id, score_condition, time_condition,
                               user_condition, paper_condition, stu_secret_condition)

        total = new_session.query(ManualMark.manual_mark_id) \
            .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualMark.distri_answer_id) \
            .outerjoin(PaperDetail, PaperDetail.ques_id == ManualMark.ques_id) \
            .where(total_condition).count()

        mark_stmt = select(ManualMark.manual_mark_id, ManualDistributeAnswer.stu_secret_num,
                           ManualDistributeAnswer.stu_answer, ExamQuestion.ques_desc, ExamQuestion.standard_answer,
                           ManualMark.final_mark_time, PaperDetail.ques_score_list, ManualMark.expert_mark_score,
                           ExamQuestion.ques_id) \
            .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualMark.distri_answer_id) \
            .join(ExamQuestion, ExamQuestion.ques_id == ManualMark.ques_id) \
            .outerjoin(PaperDetail, PaperDetail.ques_id == ManualMark.ques_id) \
            .where(total_condition) \
            .order_by(ManualMark.final_mark_time.desc(), ManualMark.manual_mark_id.desc()) \
            .limit(page_size).offset(offset)
    else:
        score_condition = ManualArbitrateQuality.aq_mark_score.between(*score_range) if score_range else True
        time_condition = ManualArbitrateQuality.updated_time.between(*time_range) if time_range else True
        user_condition = ManualArbitrateQuality.aq_user_id == execute_user_id
        select_fields = [ManualArbitrateQuality.manual_aq_id, ManualDistributeAnswer.stu_secret_num,
                         ManualDistributeAnswer.stu_answer, ExamQuestion.ques_desc, ExamQuestion.standard_answer,
                         ManualArbitrateQuality.updated_time, PaperDetail.ques_score_list, ExamQuestion.ques_id]

        if execute_role_id == "4":
            select_fields.append(ManualArbitrateQuality.aq_mark_score)
            total_condition = and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id,
                                   ManualArbitrateQuality.aq_type == 1, ManualArbitrateQuality.aq_state == 2,
                                   score_condition, time_condition, user_condition, stu_secret_condition)

        elif execute_role_id == "5":
            select_fields.extend([ManualArbitrateQuality.aq_result, ManualArbitrateQuality.aq_suggestion,
                                  ManualDistributeAnswer.final_mark_score])
            total_condition = and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id,
                                   ManualArbitrateQuality.aq_type == 2, ManualArbitrateQuality.aq_state == 4,
                                   score_condition, time_condition, user_condition, stu_secret_condition,
                                   paper_condition)
        else:
            return BaseResponse(code=response_utils.params_error, msg=f"参数有误")

        total = new_session.query(ManualArbitrateQuality.manual_aq_id) \
            .join(ManualDistributeAnswer,
                  ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
            .join(ManualReadTask, ManualReadTask.m_read_task_id == ManualArbitrateQuality.m_read_task_id) \
            .outerjoin(PaperDetail, PaperDetail.ques_id == ManualReadTask.ques_id) \
            .where(total_condition).count()

        mark_stmt = select(*select_fields) \
            .join(ManualDistributeAnswer,
                  ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
            .join(ExamQuestion, ExamQuestion.ques_id == ManualDistributeAnswer.ques_id) \
            .outerjoin(PaperDetail, PaperDetail.ques_id == ManualDistributeAnswer.ques_id) \
            .where(total_condition) \
            .order_by(ManualArbitrateQuality.updated_time.desc(), ManualArbitrateQuality.manual_aq_id.desc()) \
            .limit(page_size).offset(offset)

    try:
        all_ques_info = get_redis_ques_info_dict(new_session)
        result = new_session.execute(mark_stmt)
        for row in result:
            ques_id = row.ques_id

            if paper_id:
                ques_score = sum_with_precision(row.ques_score_list)
            else:
                ques_info = all_ques_info[ques_id]
                ques_score = ques_info["ques_type_score"]

            mark_item = {
                "manual_mark_id": row[0],
                "stu_secret_num": row.stu_secret_num,
                "stu_answer": row.stu_answer,
                "ques_desc": row.ques_desc,
                "standard_answer": row.standard_answer,
                "marked_time": row[5] and str(row[5]).replace("T", " "),
                "paper_name": paper_name,
                "ques_score": ques_score,
            }
            if execute_role_id == "3":
                mark_item["mark_score"] = row.expert_mark_score
            elif execute_role_id == "4":
                mark_item["mark_score"] = row.aq_mark_score
            elif execute_role_id == "5":
                mark_item["quality_result"] = "通过" if row[8] == 1 else "不通过"
                mark_item["quality_suggestion"] = row[9]
                mark_item["mark_score"] = row[9] if row[8] == 1 else None
            mark_data.append(mark_item)
    except Exception as e:
        logger.error(f"获取评分结果失败，{e}")
        traceback.print_exc()
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"获取评分结果失败")
    logger.info("获取评分结果成功")
    data = {
        "data": mark_data,
        "total": total
    }
    return BaseResponse(msg="获取评分结果成功", data=data)
