import json
import re
import time
from typing import Optional

from sqlalchemy import text, and_, func

# from apps.base.global_cache import get_redis_ques_info_dict
from apps.base.services import request_api
from apps.models.models import QuesAiMark, ExamQuestion, SameStuAnswerGroup, StuAnswerDetail, StuAnswer, QuesSetStdState, \
    ExamPaper, Project, Subject, StuTotalGrade, ExamStudent, PaperDetail, QuesType, CreateGradeRecord, \
    StuTotalGradeDetail, ManualDistributeAnswer, BusinessQuesType, UserInfo, ImportDataRecord, MarkRule, MarkRuleDetail
from apps.read_paper.read_paper_services import type_a_and_b_mark, type_c_mark
from factory_apps import session_depend
from settings import configs, logger
from utils.time_func import format_now_time
from utils.utils import round_half_up, sum_with_precision


def trans_ai_mark_state(mark_state):
    """
    AI 评分状态，1 表示未评分，2 表示评分中，3 表示已评分
    """
    if mark_state == 1:
        state_str = "未评分"
    elif mark_state == 2:
        state_str = "评分中"
    elif mark_state == 3:
        state_str = "已评分"
    else:
        state_str = "未知"
    return state_str


def create_ai_mark_data(new_session):
    all_ques_info = new_session.query(ExamQuestion.ques_id, ExamQuestion.ques_code, ExamQuestion.small_ques_num) \
        .filter(ExamQuestion.ques_type_code != "F") \
        .order_by(ExamQuestion.ques_code, ExamQuestion.small_ques_num).all()
    if not all_ques_info:
        return
    add_data = []
    ques_id_data = {}
    all_ques_id_list = []
    for ques_id, ques_code, small_ques_num in all_ques_info:
        ques_id_data[ques_id] = {
            "ques_code": ques_code,
            "small_ques_num": small_ques_num
        }
        all_ques_id_list.append(ques_id)
    ai_mark_ques_info = new_session.query(QuesAiMark.ques_id).all()
    if not ai_mark_ques_info:
        for ques_id in all_ques_id_list:
            add_item = QuesAiMark(mark_id=configs.snow_worker.get_id(), ques_id=ques_id,
                                  ques_code=ques_id_data[ques_id]["ques_code"], from_tool=2,
                                  small_ques_num=ques_id_data[ques_id]["small_ques_num"], mark_state=1, running_state=0)
            add_data.append(add_item)
    else:
        ai_mark_ques_id = [i[0] for i in ai_mark_ques_info]
        add_ques_id = [x for x in all_ques_id_list if x not in ai_mark_ques_id]
        for ques_id in add_ques_id:
            add_item = QuesAiMark(mark_id=configs.snow_worker.get_id(), ques_id=ques_id,
                                  ques_code=ques_id_data[ques_id]["ques_code"], from_tool=2,
                                  small_ques_num=ques_id_data[ques_id]["small_ques_num"], mark_state=1, running_state=0)
            add_data.append(add_item)
    if add_data:
        new_session.add_all(add_data)
        new_session.commit()


def check_ques_set_std_finished(new_session, ques_id_list):
    """
    检测试题是否已经定标完成
    """
    set_std_state_info = new_session.query(QuesSetStdState.ques_id, ExamQuestion.ques_code, ExamQuestion.small_ques_num) \
        .join(ExamQuestion, ExamQuestion.ques_id == QuesSetStdState.ques_id) \
        .filter(and_(QuesSetStdState.ques_id.in_(ques_id_list), QuesSetStdState.set_std_state != 3,
                     ExamQuestion.ques_type_code.in_(["D", "E"]))).all()
    if set_std_state_info:
        not_set_std_list = []
        for ques_id, ques_code, small_ques_num in set_std_state_info:
            if small_ques_num:
                not_set_std_list.append(f"{ques_code}({small_ques_num})")
            else:
                not_set_std_list.append(f"{ques_code}")
            msg = "试题编号：" + "，".join(not_set_std_list) + "的试题未定标，请先定标。"
            return msg
    return None


def get_ai_mark_process(new_session, ques_id_state_dict: dict):
    """
    获取 AI 评分进度
    mark_state: 评分状态，1 表示未评分，2 表示评分中，3 表示已评分
    """
    ques_id_list = list(ques_id_state_dict.keys())
    total_count_dict, finish_count_dict = {}, {}

    need_query_ques_id_list = []
    for ques_id, mark_state in ques_id_state_dict.items():
        if mark_state == 2:
            need_query_ques_id_list.append(ques_id)
        elif mark_state == 1:
            finish_count_dict[ques_id] = 0
        else:
            finish_count_dict[ques_id] = -1

    all_total_count = new_session.query(StuAnswer.ques_id, func.count(StuAnswer.answer_id)) \
        .filter(StuAnswer.ques_id.in_(ques_id_list)) \
        .group_by(StuAnswer.ques_id).all()

    query_finish_count = new_session.query(StuAnswer.ques_id, func.coalesce(func.count(StuAnswer.answer_id), 0)) \
        .filter(and_(StuAnswer.ques_id.in_(need_query_ques_id_list), StuAnswer.set_std_score.isnot(None))) \
        .group_by(StuAnswer.ques_id).all()

    for ques_id, total_count in all_total_count:
        total_count_dict[ques_id] = total_count
        if finish_count_dict.get(ques_id) == -1:
            finish_count_dict[ques_id] = total_count

    for ques_id, finish_count in query_finish_count:
        finish_count_dict[ques_id] = finish_count

    process_num_dict, process_dict = {}, {}
    for ques_id in total_count_dict:
        finish_count = finish_count_dict.get(ques_id, 0)
        process_num_dict[ques_id] = f"{finish_count}/{total_count_dict[ques_id]}" if total_count_dict[
            ques_id] else "0/0"
        process_dict[ques_id] = round(finish_count / total_count_dict[ques_id] * 100, 2) if total_count_dict[
            ques_id] else 0
    return process_num_dict, process_dict


def start_ai_mark_main(ques_info, rule_detail_dict):
    """
    AI 评分主函数
    rule_detail_dict 为客观题评分规则：{'10001555': {'type': 1}}
    """
    new_session = next(session_depend())
    # 定义更新语句
    detail_update_stmt = text("""
            UPDATE t_stu_answer_detail
            SET set_std_score = :set_std_score, set_std_answer_parse = :set_std_answer_parse
            WHERE ques_id = :ques_id AND same_answer_group_id = :same_answer_group_id
        """)

    stu_answer_update_stmt = text("""
            UPDATE t_stu_answer
            SET set_std_score = :set_std_score
            WHERE ques_id = :ques_id AND answer_id = :answer_id
        """)

    all_ques_info = get_redis_ques_info_dict(new_session)

    for ques in ques_info:
        paper_id, ques_id = ques["paper_id"], ques["ques_id"]
        curr_ques_info = all_ques_info[ques_id]
        ques_type_code = curr_ques_info["ques_type_code"]

        if ques_type_code in ["D", "E"]:
            logger.info("获取该试题所有答案聚类信息")
            if paper_id:
                condition = and_(SameStuAnswerGroup.ques_id == ques_id, SameStuAnswerGroup.paper_id == paper_id)
            else:
                condition = SameStuAnswerGroup.ques_id == ques_id
            group_info = new_session.query(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.ai_score,
                                           SameStuAnswerGroup.ai_answer_parse).filter(condition).all()
            group_score_list = [{
                "same_answer_group_id": same_answer_group_id,
                "ai_score": ai_score,
                "set_std_answer_parse": json.dumps(set_std_answer_parse, ensure_ascii=False)
            } for same_answer_group_id, ai_score, set_std_answer_parse in group_info] if group_info else []

            logger.info("获取该试题的考生作答明细表信息并进行赋分")
            # 分批
            batch_size = 2000  # 每批处理的数量
            total_batches = (len(group_score_list) + batch_size - 1) // batch_size

            for batch in range(total_batches):
                start = batch * batch_size
                end = start + batch_size
                batch_data = group_score_list[start: end]
                update_list = []
                for item in batch_data:
                    same_answer_group_id, ai_score, set_std_answer_parse = item["same_answer_group_id"], item["ai_score"], \
                        item["set_std_answer_parse"]
                    update_item = {
                        "ques_id": ques_id,
                        "same_answer_group_id": same_answer_group_id,
                        "set_std_score": round_half_up(ai_score, 2) if ai_score is not None else None,
                        "set_std_answer_parse": set_std_answer_parse
                    }
                    update_list.append(update_item)

                new_session.execute(detail_update_stmt, update_list)
                new_session.commit()
                logger.info(f"考生作答明细表赋分：{ques_id} 第 {batch + 1}/{total_batches} 批完成")

            # 获取该试题所有考生作答明细信息
            if paper_id:
                condition = and_(StuAnswerDetail.ques_id == ques_id, StuAnswerDetail.paper_id == paper_id)
            else:
                condition = StuAnswerDetail.ques_id == ques_id
            detail_info = new_session.query(StuAnswerDetail.answer_id,
                                            func.group_concat(func.ifnull(StuAnswerDetail.set_std_score, 0))).filter(
                condition).group_by(StuAnswerDetail.answer_id).all()
            detail_score_list = [{
                "answer_id": answer_id,
                "set_std_score": round_half_up(sum_with_precision([float(i) for i in score_concat.split(",")]), 2)
            } for answer_id, score_concat in detail_info] if detail_info else []

            # 获取该试题的考生作答表信息并进行赋分
            # 分批
            total_batches = (len(detail_score_list) + batch_size - 1) // batch_size

            for batch in range(total_batches):
                start = batch * batch_size
                end = start + batch_size
                batch_data = detail_score_list[start: end]
                update_list = []
                for item in batch_data:
                    answer_id, ai_score = item["answer_id"], item["set_std_score"]
                    update_item = {
                        "ques_id": ques_id,
                        "answer_id": answer_id,
                        "set_std_score": ai_score if ai_score is not None else None
                    }
                    update_list.append(update_item)

                new_session.execute(stu_answer_update_stmt, update_list)
                new_session.commit()
                logger.info(f"AI 评分：{ques_id} 第 {batch + 1}/{total_batches} 批完成")
        else:
            # 客观题评分
            mark_rule_detail = rule_detail_dict[ques_id]
            standard_answer, ques_score_list, ques_type_score = curr_ques_info["standard_answer"], curr_ques_info["ques_score_list"], curr_ques_info["ques_type_score"]
            if ques_score_list:
                ques_score = sum_with_precision(ques_score_list)
            else:
                ques_score = ques_type_score

            new_session.execute(text("SET SESSION group_concat_max_len = 9999999;"))
            answer_info = new_session.query(func.group_concat(StuAnswer.answer_id), StuAnswer.stu_answer) \
                .filter(StuAnswer.ques_id == ques_id).group_by(StuAnswer.stu_answer)

            for answer_id_str, stu_answer in answer_info:
                if stu_answer is None:
                    mark_score = 0
                elif ques_type_code == "A" or ques_type_code == "B":
                    _, mark_score, _, _, _, _ = type_a_and_b_mark(mark_rule_detail, standard_answer, stu_answer, ques_score)
                else:
                    _, mark_score, _, _, _, _ = type_c_mark(mark_rule_detail, standard_answer, stu_answer, ques_score)

                answer_id_list = answer_id_str.split(",")
                batch_size = 10000
                total_batches = (len(answer_id_list) + batch_size - 1) // batch_size
                for batch in range(total_batches):
                    start = batch * batch_size
                    end = start + batch_size
                    answer_id_data = answer_id_list[start: end]
                    new_session.query(StuAnswer).filter(and_(StuAnswer.ques_id == ques_id, StuAnswer.answer_id.in_(answer_id_data))).update({
                        "set_std_score": mark_score if mark_score is not None else None
                    })
                    new_session.commit()
                    logger.info(f"机器评分：{ques_id} 第 {batch + 1}/{total_batches} 批完成")

        new_session.query(QuesAiMark).filter(QuesAiMark.ques_id == ques_id).update({
            QuesAiMark.mark_state: 3,
            QuesAiMark.running_state: 7,
            QuesAiMark.end_time: format_now_time()
        })
        new_session.commit()
        logger.info(f"{ques_id} AI 评分完成")


def paper_ai_mark_query_condition(project_id: Optional[str], subject_id: Optional[str], paper_id: Optional[str]):
    project_query = ExamPaper.project_id == project_id if project_id else True
    subject_query = ExamPaper.subject_id == subject_id if subject_id else True
    paper_query = ExamPaper.paper_id == paper_id if paper_id else True

    condition = and_(project_query, subject_query, paper_query)
    return condition


def get_paper_data(new_session, project_id, subject_id, paper_id):
    # 根据筛选条件获取要评分的试卷
    condition = paper_ai_mark_query_condition(project_id, subject_id, paper_id)
    need_grade_paper_list = new_session.query(ExamPaper.paper_id, ExamPaper.project_id, ExamPaper.subject_id,
                                              ExamPaper.paper_name) \
        .outerjoin(Project, Project.project_id == ExamPaper.project_id) \
        .outerjoin(Subject, Subject.subject_id == ExamPaper.subject_id) \
        .filter(condition).all()

    paper_id_list = []
    paper_dict = {}
    for paper_id, project_id, subject_id, paper_name in need_grade_paper_list:
        paper_id_list.append(paper_id)
        paper_dict[paper_id] = {
            "project_id": project_id,
            "subject_id": subject_id,
            "paper_name": paper_name
        }
    return paper_id_list, paper_dict


def get_pro_sub_data(new_session, project_id, subject_id):
    # 根据筛选条件获取要评分的项目和科目
    project_query = Project.project_id == project_id if project_id else True
    subject_query = Subject.project_id == subject_id if subject_id else True
    condition = and_(project_query, subject_query)
    need_grade_info = new_session.query(Project.project_id, Subject.subject_id) \
        .join(Subject, Subject.project_id == Project.project_id) \
        .filter(condition).all()

    pro_sub_dict, subject_id_list = {}, []
    if need_grade_info:
        for project_id, subject_id in need_grade_info:
            subject_id_list.append(subject_id)
            if project_id in pro_sub_dict:
                pro_sub_dict[project_id].append(subject_id)
            else:
                pro_sub_dict[project_id] = [subject_id]

    return pro_sub_dict, subject_id_list


def get_stu_answer_dict(new_session, ques_id_list, paper_id, allow_exam_num, ques_type_code_list):
    stu_answer_dict = {}
    stu_query_condition = StuAnswer.allow_exam_num == allow_exam_num if allow_exam_num else True
    stu_answer_info = new_session.query(StuAnswer.answer_id, StuAnswer.allow_exam_num, StuAnswer.parent_ques_id,
                                        StuAnswer.ques_id, StuAnswer.ques_type_code, StuAnswer.stu_answer,
                                        StuAnswer.set_std_score) \
        .filter(and_(StuAnswer.ques_id.in_(ques_id_list), StuAnswer.paper_id == paper_id, stu_query_condition,
                     StuAnswer.ques_type_code.in_(ques_type_code_list))).all()

    for answer_id, allow_exam_num, parent_ques_id, ques_id, ques_type_code, stu_answer, set_std_score in stu_answer_info:
        item = {
            "answer_id": answer_id,
            "parent_ques_id": parent_ques_id,
            "ques_type_code": ques_type_code,
            "stu_answer": stu_answer,
            "set_std_score": set_std_score
        }
        if allow_exam_num in stu_answer_dict:
            stu_answer_dict[allow_exam_num][ques_id] = item
        else:
            stu_answer_dict[allow_exam_num] = {ques_id: item}
    return stu_answer_dict


def get_stu_answer_detail_dict(new_session, paper_id, ques_type_code_list):
    answer_detail_info = new_session.query(StuAnswerDetail.answer_id, StuAnswerDetail.set_std_score,
                                           StuAnswerDetail.set_std_answer_parse) \
        .filter(and_(StuAnswerDetail.paper_id == paper_id, StuAnswerDetail.ques_type_code.in_(ques_type_code_list)))

    answer_detail = dict()
    if answer_detail_info:
        for answer_id, set_std_score, set_std_answer_parse in answer_detail_info:
            if set_std_score is not None:
                set_std_score = float(set_std_score)
            if answer_id in answer_detail:
                answer_detail[answer_id]["stu_score_list"].append(set_std_score)
                answer_detail[answer_id]["answer_parse_list"].append(set_std_answer_parse)
            else:
                answer_detail[answer_id] = {
                    "stu_score_list": [set_std_score],
                    "answer_parse_list": [set_std_answer_parse]
                }
    return answer_detail


def get_params_stu_answer_dict(new_session, project_id, subject_id, allow_exam_num, ques_type_code_list, grade_type, user_dict):
    stu_answer_dict = {}
    stu_query_condition = StuAnswer.allow_exam_num == allow_exam_num if allow_exam_num else True
    if grade_type == 1:
        stu_answer_info = new_session.query(StuAnswer.answer_id, StuAnswer.allow_exam_num, StuAnswer.parent_ques_id,
                                            StuAnswer.ques_id, StuAnswer.sub_ques_order, StuAnswer.ques_type_code,
                                            StuAnswer.stu_answer, StuAnswer.set_std_score) \
            .filter(and_(StuAnswer.project_id == project_id, StuAnswer.subject_id == subject_id, stu_query_condition,
                         StuAnswer.ques_type_code.in_(ques_type_code_list))).all()
    else:
        stu_answer_info = new_session.query(StuAnswer.answer_id, StuAnswer.allow_exam_num, StuAnswer.parent_ques_id,
                                            StuAnswer.ques_id, StuAnswer.sub_ques_order, StuAnswer.ques_type_code,
                                            StuAnswer.stu_answer, ManualDistributeAnswer.final_mark_score,
                                            ManualDistributeAnswer.answer_parse) \
            .outerjoin(ManualDistributeAnswer, ManualDistributeAnswer.stu_answer_id == StuAnswer.answer_id) \
            .filter(and_(StuAnswer.project_id == project_id, StuAnswer.subject_id == subject_id, stu_query_condition,
                         StuAnswer.ques_type_code.in_(ques_type_code_list))).all()

    ques_id_list, answer_id_list = [], []
    for row in stu_answer_info:
        ques_id, allow_exam_num = row.ques_id, row.allow_exam_num
        ques_id_list.append(ques_id)
        answer_id_list.append(row.answer_id)
        item = {
            "answer_id": row.answer_id,
            "parent_ques_id": row.parent_ques_id,
            "ques_type_code": row.ques_type_code,
            "ques_order": row.sub_ques_order,
            "stu_answer": row.stu_answer,
            "set_std_score": float(row[7]) if row[7] is not None else None
        }
        if grade_type == 2:
            answer_parse = row.answer_parse
            if answer_parse:
                item["answer_parse"] = parse_manual_mark_parse(answer_parse, user_dict)
            else:
                item["answer_parse"] = []
        if allow_exam_num in stu_answer_dict:
            stu_answer_dict[allow_exam_num][ques_id] = item
        else:
            stu_answer_dict[allow_exam_num] = {ques_id: item}
    return stu_answer_dict, ques_id_list, answer_id_list


def get_params_stu_answer_detail_dict(new_session, ques_id_list, answer_id_list):
    answer_detail_info = new_session.query(StuAnswerDetail.answer_id, StuAnswerDetail.set_std_score,
                                           StuAnswerDetail.set_std_answer_parse) \
        .filter(and_(StuAnswerDetail.ques_id.in_(ques_id_list), StuAnswerDetail.answer_id.in_(answer_id_list)))

    answer_detail = dict()
    if answer_detail_info:
        for answer_id, set_std_score, set_std_answer_parse in answer_detail_info:
            if set_std_score is not None:
                set_std_score = float(set_std_score)
            if answer_id in answer_detail:
                answer_detail[answer_id]["stu_score_list"].append(set_std_score)
                answer_detail[answer_id]["answer_parse_list"].append(set_std_answer_parse)
            else:
                answer_detail[answer_id] = {
                    "stu_score_list": [set_std_score],
                    "answer_parse_list": [set_std_answer_parse]
                }
    return answer_detail


def get_ques_type_score(parent_ques_id, ques_type_code, f_grade, a_grade, b_grade, c_grade, d_grade, e_grade,
                        set_std_score):
    # 获取各个题型的分数
    if set_std_score is not None:
        if parent_ques_id:
            f_grade += set_std_score
        elif ques_type_code == "A":
            a_grade += set_std_score
        elif ques_type_code == "B":
            b_grade += set_std_score
        elif ques_type_code == "C":
            c_grade += set_std_score
        elif ques_type_code == "D":
            d_grade += set_std_score
        elif ques_type_code == "E":
            e_grade += set_std_score
    return f_grade, a_grade, b_grade, c_grade, d_grade, e_grade


def ai_grade_query_condition(new_session, project_id: Optional[str], subject_id: Optional[str], paper_id: Optional[str],
                             stu_secret_num: Optional[str], stu_score_range: Optional[list], query_score_type: Optional[int]):
    allow_exam_num = None
    if stu_secret_num:
        allow_exam_num_info = new_session.query(ExamStudent.allow_exam_num).filter(
            ExamStudent.stu_secret_num == stu_secret_num).first()
        allow_exam_num = allow_exam_num_info[0]
    project_query = StuTotalGrade.project_id == project_id if project_id else True
    subject_query = StuTotalGrade.subject_id == subject_id if subject_id else True
    paper_query = StuTotalGrade.paper_id == paper_id if paper_id else True
    allow_exam_num_query = StuTotalGrade.allow_exam_num.ilike(f"%{allow_exam_num}%") if allow_exam_num else True
    if query_score_type and stu_score_range:
        if query_score_type == 1:
            stu_score_range_query = StuTotalGrade.total_grade.between(*stu_score_range)
        else:
            stu_score_range_query = StuTotalGrade.manual_total_grade.between(*stu_score_range)
    else:
        stu_score_range_query = True
    condition = and_(project_query, subject_query, paper_query, allow_exam_num_query, stu_score_range_query)
    return allow_exam_num, condition


def get_ques_in_paper_data(new_session):
    """
    获取所有试题在试卷里的序号、分数
    """
    ques_in_paper_data = {}
    ques_in_paper_info = new_session.query(PaperDetail.paper_id, PaperDetail.ques_id, PaperDetail.ques_order,
                                           PaperDetail.ques_score_list).all()
    if ques_in_paper_info:
        for paper_id, ques_id, ques_order, ques_score_list in ques_in_paper_info:
            item = {
                "ques_order": re.sub(r'^0+', '', ques_order) if ques_order else None,
                "ques_score_list": ques_score_list,
                "ques_score": sum_with_precision(ques_score_list) if ques_score_list else None
            }
            if paper_id in ques_in_paper_data:
                ques_in_paper_data[paper_id][ques_id] = item
            else:
                ques_in_paper_data[paper_id] = {ques_id: item}
    return ques_in_paper_data


def parse_manual_mark_parse(parse_dict, user_dict):
    """
    解析人工阅卷评析
    """
    answer_parse_list = []
    fetch_way, final_score, mark_process = parse_dict["fetch_way"], parse_dict["final_score"], parse_dict["mark_process"]
    has_arbitration = False
    for item in mark_process:
        user_id, user_int, score, parse = item["user_id"], item["type"], item["score"], item["parse"]
        if user_int != 1:
            has_arbitration = True
        if parse:
            answer_parse = f"{user_dict[user_id]}：{parse}（{score}分）"
        else:
            answer_parse = f"{user_dict[user_id]}：（{score}分）"
        answer_parse_list.append(answer_parse)
    if has_arbitration:
        answer_parse_list.append(f"最终分数 取仲裁分数：{final_score}分")
    else:
        if fetch_way:
            answer_parse_list.append(f"最终分数 {fetch_way}：{final_score}分")
    return answer_parse_list


def get_ques_type_trans(new_session):
    """
    获取题型编码对应的中文
    """
    ques_type_trans = {}
    ques_type_code_info = new_session.query(QuesType.ques_type_code, QuesType.ques_type_name).all()
    for ques_type_code, ques_type_name in ques_type_code_info:
        ques_type_trans[ques_type_code] = ques_type_name
    return ques_type_trans


def create_grade_record(new_session, record_id, stu_secret_num, subject_id_list, paper_id_list):
    # 获取考生人数
    allow_exam_num = None
    ques_type_query = StuAnswer.ques_type_code.in_(["D", "E"])
    if stu_secret_num:
        allow_exam_num_info = new_session.query(ExamStudent.allow_exam_num).filter(
            ExamStudent.stu_secret_num == stu_secret_num).first()
        allow_exam_num = allow_exam_num_info[0]
        total_count = 1
    else:
        if subject_id_list:
            total_count = new_session.query(StuAnswer.allow_exam_num, StuAnswer.subject_id).distinct().filter(
                and_(StuAnswer.subject_id.in_(subject_id_list), ques_type_query)).count()
        else:
            total_count = new_session.query(StuAnswer.allow_exam_num, StuAnswer.paper_id).distinct().filter(
                and_(StuAnswer.paper_id.in_(paper_id_list), ques_type_query)).count()

    record_item = CreateGradeRecord(record_id=record_id, success_count=0, total_count=total_count, progress=0)
    new_session.add(record_item)
    new_session.commit()
    return allow_exam_num, total_count


def query_exist_stu_grade(new_session, project_id, subject_id, paper_id, allow_exam_num):
    """
    获取已存在成绩表里的考生准考证号
    """
    exist_stu_dict = {}
    project_query = StuTotalGrade.project_id == project_id if project_id else True
    subject_query = StuTotalGrade.subject_id == subject_id if subject_id else True
    paper_query = StuTotalGrade.paper_id == paper_id if paper_id else True
    exam_num_query = StuTotalGrade.allow_exam_num == allow_exam_num if allow_exam_num else True
    condition = and_(project_query, subject_query, paper_query, exam_num_query)
    exist_stu_info = new_session.query(StuTotalGrade.allow_exam_num, StuTotalGrade.grade_id).filter(condition).all()
    if exist_stu_info:
        for allow_exam_num, grade_id in exist_stu_info:
            exist_stu_dict[allow_exam_num] = grade_id
    return exist_stu_dict


def get_update_grade_item(grade_type, grade_id, a_grade, b_grade, c_grade, d_grade, e_grade, f_grade, total_grade,
                          c_user_id):
    if grade_type == 1:
        update_item = {
            "grade_id": grade_id,
            "a_grade": a_grade,
            "b_grade": b_grade,
            "c_grade": c_grade,
            "d_grade": d_grade,
            "e_grade": e_grade,
            "f_grade": f_grade,
            "total_grade": total_grade,
            "u_user_id": c_user_id,
            "updated_time": format_now_time()
        }
    else:
        update_item = {
            "grade_id": grade_id,
            "manual_a_grade": a_grade,
            "manual_b_grade": b_grade,
            "manual_c_grade": c_grade,
            "manual_d_grade": d_grade,
            "manual_e_grade": e_grade,
            "manual_f_grade": f_grade,
            "manual_total_grade": total_grade,
            "u_user_id": c_user_id,
            "updated_time": format_now_time()
        }
    return update_item


def get_add_grade_item(grade_type, grade_id, allow_exam_num, project_id, subject_id, paper_id, a_grade, b_grade,
                       c_grade, d_grade, e_grade, f_grade, total_grade, c_user_id):
    if grade_type == 1:
        add_item = StuTotalGrade(grade_id=grade_id, allow_exam_num=allow_exam_num, project_id=project_id,
                                 subject_id=subject_id, paper_id=paper_id, a_grade=a_grade, b_grade=b_grade,
                                 c_grade=c_grade, d_grade=d_grade, e_grade=e_grade, f_grade=f_grade,
                                 total_grade=total_grade, c_user_id=c_user_id,  from_tool=2,
                                 created_time=format_now_time())
    else:
        add_item = StuTotalGrade(grade_id=grade_id, allow_exam_num=allow_exam_num, project_id=project_id,
                                 subject_id=subject_id, paper_id=paper_id, manual_a_grade=a_grade,
                                 manual_b_grade=b_grade, manual_c_grade=c_grade, manual_d_grade=d_grade,
                                 manual_e_grade=e_grade, manual_f_grade=f_grade, manual_total_grade=total_grade,
                                 c_user_id=c_user_id,  from_tool=2, created_time=format_now_time())
    return add_item


def save_grade(new_session, project_id, subject_id, paper_id, allow_exam_num_list, grade_type, total_add_list,
               detail_add_list, grade_update_data, record_id, curr_count, total_count, curr_time):
    if paper_id:
        condition = StuTotalGrade.paper_id == paper_id
    else:
        condition = and_(StuTotalGrade.project_id == project_id, StuTotalGrade.subject_id == subject_id)

    grade_info = new_session.query(StuTotalGrade.grade_id).filter(condition).all()
    grade_id_list = [i[0] for i in grade_info] if grade_info else []

    new_session.query(StuTotalGradeDetail).filter(and_(
        StuTotalGradeDetail.grade_id.in_(grade_id_list), StuTotalGradeDetail.allow_exam_num.in_(allow_exam_num_list),
        StuTotalGradeDetail.grade_type == grade_type, StuTotalGradeDetail.created_time < curr_time)).delete()

    if grade_update_data:
        new_session.bulk_update_mappings(StuTotalGrade, grade_update_data)
    if total_add_list:
        new_session.add_all(total_add_list)
    if detail_add_list:
        new_session.add_all(detail_add_list)

    progress = round((curr_count / total_count) * 100, 2) if total_count else 0
    new_session.query(CreateGradeRecord).filter(CreateGradeRecord.record_id == record_id).update({
        CreateGradeRecord.success_count: curr_count,
        CreateGradeRecord.progress: progress if progress <= 100 else 100
    })
    new_session.commit()


def create_paper_grade(new_session, project_id, subject_id, paper_id, stu_secret_num, grade_type, user):
    """
    生成抽卷考试评分成绩
    """
    logger.info(f"{user['username']} 生成抽卷考试评分成绩")
    c_user_id = user['user_id']
    curr_time = format_now_time()

    # 根据筛选条件获取要评分的试卷信息
    paper_id_list, paper_dict = get_paper_data(new_session, project_id, subject_id, paper_id)

    # 插入一条生成成绩记录
    record_id = configs.snow_worker.get_id()
    row_allow_exam_num, total_count = create_grade_record(new_session, record_id, stu_secret_num, [], paper_id_list)

    curr_count = 0
    for paper_id in paper_id_list:

        exist_stu_dict = query_exist_stu_grade(new_session, project_id, subject_id, paper_id, row_allow_exam_num)

        ques_type_code_list = ["D", "E"]
        # 按照试卷生成成绩
        paper_info = new_session.query(PaperDetail.ques_id) \
            .join(ExamQuestion, ExamQuestion.ques_id == PaperDetail.ques_id) \
            .filter(and_(PaperDetail.paper_id == paper_id, ExamQuestion.ques_type_code.in_(ques_type_code_list))).all()
        ques_id_list = [i[0] for i in paper_info] if paper_info else []

        # 获取作答详情
        stu_answer_detail_dict = get_stu_answer_detail_dict(new_session, paper_id, ques_type_code_list)
        stu_answer_dict = get_stu_answer_dict(new_session, ques_id_list, paper_id, row_allow_exam_num,
                                              ques_type_code_list)

        total_add_list, detail_add_list, grade_update_data = [], [], []

        # 每批数量，按批提交
        commit_count = 200
        allow_exam_num_list = []
        allow_exam_num_length = len(stu_answer_dict.keys())

        single_paper_count = 0

        for allow_exam_num, ques_info in stu_answer_dict.items():
            curr_count += 1
            single_paper_count += 1
            if allow_exam_num in exist_stu_dict:
                is_exist = True
                grade_id = exist_stu_dict[allow_exam_num]
            else:
                is_exist = False
                grade_id = configs.snow_worker.get_id()
            allow_exam_num_list.append(allow_exam_num)
            # 按照每个考生计算
            a_grade, b_grade, c_grade, d_grade, e_grade, f_grade, total_grade = 0, 0, 0, 0, 0, 0, 0

            for ques_id, ques_item in ques_info.items():
                answer_id, parent_ques_id, ques_type_code, stu_answer, set_std_score = ques_item["answer_id"], \
                    ques_item["parent_ques_id"], ques_item["ques_type_code"], ques_item["stu_answer"], \
                    ques_item["set_std_score"]
                # 获取总数
                if set_std_score is not None:
                    total_grade += set_std_score
                # 获取各个题型的分数
                f_grade, a_grade, b_grade, c_grade, d_grade, e_grade = \
                    get_ques_type_score(parent_ques_id, ques_type_code, f_grade, a_grade, b_grade, c_grade, d_grade,
                                        e_grade, set_std_score)

                # 获取成绩详情信息
                answer_id = ques_item["answer_id"]
                detail_item = stu_answer_detail_dict[answer_id]
                stu_score_list, answer_parse_list = detail_item["stu_score_list"], detail_item["answer_parse_list"]
                stu_score_sum = round_half_up(sum_with_precision([i for i in stu_score_list if i]), 2)

                stu_score_list = [float(i) for i in stu_score_list if i is not None]
                detail_add_item = StuTotalGradeDetail(detail_id=configs.snow_worker.get_id(), grade_id=grade_id,
                                                      grade_type=grade_type, allow_exam_num=allow_exam_num,
                                                      paper_id=paper_id, ques_id=ques_id, stu_answer=stu_answer,
                                                      stu_score_list=stu_score_list, stu_score=stu_score_sum,
                                                      answer_parse=answer_parse_list, from_tool=2,
                                                      created_time=format_now_time())
                detail_add_list.append(detail_add_item)

            if is_exist:
                update_item = get_update_grade_item(grade_type, grade_id, a_grade, b_grade, c_grade, d_grade, e_grade,
                                                    f_grade, total_grade, c_user_id)
                grade_update_data.append(update_item)
            else:
                project_id = paper_dict[paper_id]["project_id"]
                subject_id = paper_dict[paper_id]["subject_id"]

                add_item = get_add_grade_item(grade_type, grade_id, allow_exam_num, project_id, subject_id, paper_id,
                                              a_grade, b_grade, c_grade, d_grade, e_grade, f_grade, total_grade,
                                              c_user_id)

                total_add_list.append(add_item)

            if single_paper_count % commit_count == 0 or single_paper_count == allow_exam_num_length:
                save_grade(new_session, None, None, paper_id, allow_exam_num_list, grade_type, total_add_list,
                           detail_add_list, grade_update_data, record_id, curr_count, total_count, curr_time)
                grade_update_data, detail_add_list, total_add_list, allow_exam_num_list = [], [], [], []
    return True, None


def create_params_grade(new_session, project_id, subject_id, paper_id, stu_secret_num, grade_type, user):
    """
    生成抽参评分成绩
    """
    logger.info(f"{user['username']} 生成抽参评分成绩")
    c_user_id = user['user_id']
    curr_time = format_now_time()

    pro_sub_dict, all_subject_id_list = get_pro_sub_data(new_session, project_id, subject_id)

    # 插入一条生成成绩记录
    record_id = configs.snow_worker.get_id()
    row_allow_exam_num, total_count = create_grade_record(new_session, record_id, stu_secret_num, all_subject_id_list,
                                                          [])

    curr_count = 0
    ques_type_code_list = ["D", "E"]

    user_dict = {}
    user_info = new_session.query(UserInfo.user_id, UserInfo.name).all()
    for user_id, name in user_info:
        user_dict[user_id] = name

    for project_id, subject_id_list in pro_sub_dict.items():
        for subject_id in subject_id_list:
            # 查询已存在数据库的考生成绩数据
            exist_stu_dict = query_exist_stu_grade(new_session, project_id, subject_id, paper_id, row_allow_exam_num)

            # 获取作答详情
            stu_answer_dict, ques_id_list, answer_id_list = get_params_stu_answer_dict(new_session, project_id,
                                                                                       subject_id, row_allow_exam_num,
                                                                                       ques_type_code_list, grade_type,
                                                                                       user_dict)
            stu_answer_detail_dict = get_params_stu_answer_detail_dict(new_session, ques_id_list, answer_id_list)

            total_add_list, detail_add_list, grade_update_data = [], [], []

            # 每批数量，按批提交
            commit_count = 200
            allow_exam_num_list = []
            allow_exam_num_length = len(stu_answer_dict.keys())

            single_paper_count = 0

            for allow_exam_num, ques_info in stu_answer_dict.items():
                curr_count += 1
                single_paper_count += 1
                if allow_exam_num in exist_stu_dict:
                    is_exist = True
                    grade_id = exist_stu_dict[allow_exam_num]
                else:
                    is_exist = False
                    grade_id = configs.snow_worker.get_id()
                allow_exam_num_list.append(allow_exam_num)
                # 按照每个考生计算
                a_grade, b_grade, c_grade, d_grade, e_grade, f_grade, total_grade = 0, 0, 0, 0, 0, 0, 0

                for ques_id, ques_item in ques_info.items():
                    answer_id, parent_ques_id, ques_order, ques_type_code, stu_answer, set_std_score = ques_item[
                        "answer_id"], \
                        ques_item["parent_ques_id"], ques_item["ques_order"], ques_item["ques_type_code"], ques_item[
                        "stu_answer"], \
                        ques_item["set_std_score"]
                    # 获取总数
                    if set_std_score is not None:
                        total_grade += set_std_score
                    # 获取各个题型的分数
                    f_grade, a_grade, b_grade, c_grade, d_grade, e_grade = \
                        get_ques_type_score(parent_ques_id, ques_type_code, f_grade, a_grade, b_grade, c_grade, d_grade,
                                            e_grade, set_std_score)

                    # 获取成绩详情信息
                    answer_id = ques_item["answer_id"]
                    if grade_type == 1:
                        detail_item = stu_answer_detail_dict[answer_id]
                        stu_score_list, answer_parse_list = detail_item["stu_score_list"], detail_item[
                            "answer_parse_list"]
                        stu_score_sum = round_half_up(sum_with_precision([i for i in stu_score_list if i]), 2)
                        stu_score_list = [float(i) for i in stu_score_list if i is not None]
                    else:
                        answer_parse_list = [ques_item.get("answer_parse")]
                        stu_score_list = [set_std_score] if set_std_score is not None else []
                        stu_score_sum = set_std_score

                    detail_add_item = StuTotalGradeDetail(detail_id=configs.snow_worker.get_id(), grade_id=grade_id,
                                                          grade_type=grade_type, allow_exam_num=allow_exam_num,
                                                          paper_id=None, ques_id=ques_id, ques_order=ques_order,
                                                          stu_answer=stu_answer, stu_score_list=stu_score_list,
                                                          stu_score=stu_score_sum, answer_parse=answer_parse_list,
                                                          from_tool=2, created_time=format_now_time())
                    detail_add_list.append(detail_add_item)

                if is_exist:
                    update_item = get_update_grade_item(grade_type, grade_id, a_grade, b_grade, c_grade, d_grade,
                                                        e_grade, f_grade, total_grade, c_user_id)
                    grade_update_data.append(update_item)
                else:
                    paper_id = None
                    add_item = get_add_grade_item(grade_type, grade_id, allow_exam_num, project_id, subject_id,
                                                  paper_id, a_grade, b_grade, c_grade, d_grade, e_grade, f_grade,
                                                  total_grade, c_user_id)

                    total_add_list.append(add_item)

                if single_paper_count % commit_count == 0 or single_paper_count == allow_exam_num_length:
                    save_grade(new_session, project_id, subject_id, paper_id, allow_exam_num_list, grade_type,
                               total_add_list, detail_add_list, grade_update_data, record_id, curr_count, total_count,
                               curr_time)
                    grade_update_data, detail_add_list, total_add_list, allow_exam_num_list = [], [], [], []
    return True, None


def get_params_exam_total_score(new_session, business_data, project_id, subject_id):
    """
    获取抽参考试试题总分
    """
    stu_info = new_session.query(StuAnswer.allow_exam_num).filter(and_(StuAnswer.project_id == project_id, StuAnswer.subject_id == subject_id)).first()
    allow_exam_num = stu_info[0]
    business_id_num_dict = {}
    business_id_info = new_session.query(ExamQuestion.business_ques_type_id) \
        .join(StuAnswer, StuAnswer.ques_id == ExamQuestion.ques_id) \
        .filter(and_(StuAnswer.project_id == project_id, StuAnswer.subject_id == subject_id, StuAnswer.allow_exam_num == allow_exam_num)).all()
    if business_id_info:
        for business_item in business_id_info:
            business_id = business_item[0]
            if business_id in business_id_num_dict:
                business_id_num_dict[business_id] += 1
            else:
                business_id_num_dict[business_id] = 1

    total_score = 0
    for business_id, count in business_id_num_dict.items():
        ques_type_score = business_data[business_id]["ques_type_score"]
        total_score += ques_type_score * count
    return total_score


def create_import_record(new_session, record_type, total_count, c_user_id):
    record_id = configs.snow_worker.get_id()
    add_record = ImportDataRecord(record_id=record_id, record_type=record_type, success_count=0, total_count=total_count, progress=0, c_user_id=c_user_id)
    new_session.add(add_record)
    new_session.commit()
    return record_id


def get_clear_stu_data(project_id, subject_id, paper_id, stu_secret_num, score_range, query_score_type, export_type, grade_type, user):
    """
    获取需要清除的考生数据
    """
    req_data = {
        "project_id": project_id,
        "subject_id": subject_id,
        "paper_id": paper_id,
        "stu_secret_num": stu_secret_num,
        "score_range": score_range,
        "query_score_type": query_score_type,
        "export_type": export_type,
        "grade_type": grade_type
    }
    token = f"Bearer {user['token']}"
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/ai_mark/get_ai_grade_list"
    res, msg = request_api(url, "POST", req_data, token, "获取需要清除的考生数据")
    stu_secret_num_list = []
    if res == 0:
        result = False
        msg = "获取数据失败"
    else:
        result = True
        msg = None
        data = res["data"]
        stu_secret_num_list = [i["stu_secret_num"] for i in data] if data else []
    return result, msg, stu_secret_num_list


def check_objective_mark_rule(new_session, ques_info):
    # 检查客观题是否都有对应的评分规则
    paper_id_list = []
    paper_ques_dict = {}
    all_ques_info = get_redis_ques_info_dict(new_session)

    for ques in ques_info:
        paper_id = ques["paper_id"]
        ques_id = ques["ques_id"]

        if paper_id not in paper_id_list:
            paper_id_list.append(paper_id)

        if paper_id in paper_ques_dict:
            if ques_id not in paper_ques_dict[paper_id]:
                paper_ques_dict[paper_id].append(ques_id)
        else:
            paper_ques_dict[paper_id] = [ques_id]

    rule_detail_dict = {}

    for paper_id, ques_id_list in paper_ques_dict.items():
        ques_type_code_list = []
        ques_id_type_code_dict = {}
        for ques_id in ques_id_list:
            ques_type_code = all_ques_info[ques_id]["ques_type_code"]
            if ques_type_code not in ["D", "E"]:
                ques_type_code_list.append(ques_type_code)
                ques_id_type_code_dict[ques_id] = ques_type_code

        if ques_type_code_list:
            mark_info = new_session.query(ExamPaper.paper_name, MarkRule.rule_id, MarkRule.rule_name, MarkRule.include_ques_type_code) \
                .outerjoin(MarkRule, MarkRule.rule_id == ExamPaper.mark_rule_id) \
                .filter(ExamPaper.paper_id == paper_id).first()

            paper_name, rule_id, rule_name, include_ques_type_code = mark_info
            if not include_ques_type_code:
                return False, rule_detail_dict, f"试题对应的试卷 {paper_name} 未设置评分规则"

            not_set_ques_type_code = [i for i in ques_type_code_list if i not in include_ques_type_code]
            if not_set_ques_type_code:
                ques_type_info = new_session.query(QuesType.ques_type_name).filter(QuesType.ques_type_code.in_(not_set_ques_type_code)).all()
                ques_type_name_list = [i[0] for i in ques_type_info]
                return False, rule_detail_dict, f"试题对应的试卷 {paper_name} 题型 {'，'.join(ques_type_name_list)} 未设置评分规则"

            rule_detail_info = new_session.query(MarkRuleDetail.ques_type_code, MarkRuleDetail.mark_rule_detail).all()
            if not rule_detail_info:
                return False, rule_detail_dict, f"评分规则 {rule_name} 缺少题型详细评分规则"

            code_detail = {}
            for ques_type_code, mark_rule_detail in rule_detail_info:
                code_detail[ques_type_code] = mark_rule_detail

            for ques_id, ques_type_code in ques_id_type_code_dict.items():
                rule_detail_dict[ques_id] = code_detail[ques_type_code]
    return True, rule_detail_dict, None



