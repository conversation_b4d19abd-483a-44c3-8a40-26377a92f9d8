from sqlalchemy import func

from apps.models.models import QuesType


def get_all_ques_type(new_session):
    """
    获取 主观题、客观题、操作题 类型
    """
    ques_type_info = new_session.query(QuesType.parent_type, func.group_concat(QuesType.ques_type_code)).group_by(QuesType.parent_type)
    ques_type_dict = {}
    for parent_type, ques_type_code_str in ques_type_info:
        ques_type_dict[parent_type] = ques_type_code_str.split(",")
    return ques_type_dict
