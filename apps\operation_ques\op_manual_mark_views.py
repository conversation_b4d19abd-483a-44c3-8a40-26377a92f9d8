import base64
import os
import traceback

from typing import Any

from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from starlette.responses import FileResponse

from helper.aes_cbc_manage import MyAesCbc
from settings import logger
from sqlalchemy.orm import Session

from apps.base.schemas import BaseResponse
from apps.models.models import ExamStudent
from apps.operation_ques.schemas import DownloadMaterialReq
from apps.operation_ques.op_manual_mark_services import match_single_stu_op_path, file_iterator
from apps.users.services import get_current_user
from factory_apps import session_depend
from helper import response_utils
from settings import configs

op_manual_mark_router = APIRouter()


@op_manual_mark_router.get(path="/send_op_pwd", response_model=BaseResponse, summary="发送加密后的解压op文件的密码")
async def send_pwd(user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 发送加密后的解压op文件的密码")
    try:
        # 加密
        key = configs.OP_KEY
        e = MyAesCbc(key=key).encrypt(configs.OP_PWD)
        encrypt_pwd = str(base64.b64encode(e), encoding="utf-8")
        data = {"encrypt_pwd": encrypt_pwd}
        return BaseResponse(data=data)
    except Exception as e:
        logger.error(f"发送加密后的解压op文件的密码失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=50001, msg="发送加密后的解压op文件的密码失败")


@op_manual_mark_router.post(path="/download_single_material_stream", response_model=BaseResponse,
                            summary="下载单个考生操作题素材")
async def download_material_stream(query: DownloadMaterialReq, user: Any = Depends(get_current_user),
                                   new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 下载单个考生操作题素材")
    ques_code, stu_secret_num, stu_file_path = query.model_dump().values()
    exam_room_code = new_session.query(ExamStudent.exam_room_code).filter(
        ExamStudent.stu_secret_num == stu_secret_num).scalar()
    if not exam_room_code:
        return BaseResponse(code=response_utils.no_field, msg="找不到该考生的作答数据")

    # 匹配考生的操作题作答信息的文件夹路径
    op_file_path = match_single_stu_op_path(stu_file_path)
    # print(op_file_path)

    if not op_file_path:
        return BaseResponse(code=response_utils.no_field, msg="找不到该考生的作答数据")

    op_file_name = f"{stu_secret_num}_{ques_code}.op"

    return StreamingResponse(
        file_iterator(op_file_path),
        media_type="application/octet-stream",
        headers={"Content-Disposition": f"attachment; filename={op_file_name}"}
    )


@op_manual_mark_router.post(path="/download_op_plugin", response_model=BaseResponse, summary="下载操作题人工阅卷插件")
async def download_op_plugin(user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 下载卓帆智能阅卷系统操作题人工阅卷插件")
    try:
        file_path = os.path.join(configs.PROJECT_PATH, "卓帆智能阅卷系统操作题人工阅卷插件.zip")
        if not os.path.exists(file_path):
            return BaseResponse(code=response_utils.no_field, msg="插件不存在，请联系管理员")
        return FileResponse(file_path, filename="卓帆智能阅卷系统操作题人工阅卷插件.zip")
    except Exception as e:
        logger.error(f"下载操作题人工阅卷插件失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg="下载失败")
