stu_answer_dict = {
    '0000985': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000101_5201.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001153': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000122_5201.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001243': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000102_5201.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001328': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000103_5201.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001416': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000111_5201.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001754': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000112_5201.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001843': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000121_5201.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0000986': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000101_5202.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001154': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000122_5202.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001244': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000102_5202.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001329': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000103_5202.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001417': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000111_5202.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001755': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000112_5202.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2},
    '0001844': {'answer_image_path': 'server_static//Data/Synthesis/1954735397365092352/52_系统架构设计师/52/1_2/0063/63022001003/20262036302000121_5202.png', 'word_count': 0, 'is_do': True,
                'exam_session': 2}}
