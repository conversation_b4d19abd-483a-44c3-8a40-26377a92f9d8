import copy
import os
import re
import shutil
from decimal import Decimal
from typing import Optional

from sqlalchemy import and_

from apps.base.global_cache import get_manual_op_file_path
from apps.models.models import PaperDetail, OperationStep, OperationStepGroup, StuAnswer
from settings import configs
from utils.utils import sum_with_precision


def get_new_score_rule_list(score_rule):
    """
    将操作题的评分逻辑列表格式化
    {} 表示单一步骤
    [] 表示 and
    () 表示 or
    """
    new_score_rule_list = []
    score_rule_list = score_rule.split(configs.OP_PARSE_SPLIT_FLAG)[0].split(configs.OP_STEP_SPLIT_FLAG)
    for score_rule in score_rule_list:
        if "and" in score_rule:
            score_rule_item = [int(i.strip()) for i in score_rule.split("and")]
            new_score_rule_list.append(score_rule_item)
        elif "or" in score_rule:
            score_rule_item = tuple([int(i.strip()) for i in score_rule.replace("（", "").replace("）", "").split("or")])
            new_score_rule_list.append(score_rule_item)
        else:
            new_score_rule_list.append({int(score_rule)})

    return new_score_rule_list


def parse_element(element):
    # 处理包含 and 或 or 的元素，将其转化为数字
    if "and" in element or "or" in element:
        numbers = map(int, re.findall(r"\d+", element))
        return min(numbers)  # 返回最小的数字
    return int(element)


def restore_op_score_rule(op_score_rule_dict):
    """
    将前端传回来的操作题评分步骤规则格式化为数据库的格式：1U2 and 3 and 4U5U6U7U8U9 or 10
    """
    logic_op_score_rule = []
    for logic in op_score_rule_dict:
        score_rule = []
        if logic == "1":
            score_rule = [str(i[0]) for i in op_score_rule_dict[logic]]
        if logic == "2":
            score_rule = [" and ".join([str(j) for j in i if j]) for i in op_score_rule_dict[logic]]
        if logic == "3":
            score_rule = [" or ".join([str(j) for j in i if j]) for i in op_score_rule_dict[logic]]

        if score_rule:
            logic_op_score_rule.append(score_rule)

    # 操作题步骤之间分隔符
    op_step_split_flag = configs.OP_STEP_SPLIT_FLAG
    sorted_op_score_rule = sorted([i for sublist in logic_op_score_rule for i in sublist], key=parse_element)
    op_score_str = op_step_split_flag.join(sorted_op_score_rule)
    return True, op_score_str


def format_op_step_info(new_session, paper_id: str, ques_id: Optional[str] = None):
    """
    结构化操作题数据
    """
    op_ques_dict = {}
    if ques_id:
        condition = and_(PaperDetail.paper_id == paper_id, PaperDetail.ques_id == ques_id)
    else:
        condition = PaperDetail.paper_id == paper_id
    op_ques_info = new_session.query(OperationStep.question_id, OperationStep.auto_id, OperationStep.order_id,
                                     OperationStep.description, OperationStep.is_score, OperationStep.score_gene,
                                     OperationStepGroup.score_rule, OperationStepGroup.score_gene,
                                     PaperDetail.ques_score_list, OperationStep.knowledge_group_id) \
        .join(OperationStepGroup, OperationStepGroup.knowledge_group_id == OperationStep.knowledge_group_id) \
        .join(PaperDetail, PaperDetail.ques_id == OperationStep.question_id) \
        .filter(condition) \
        .order_by(OperationStep.question_id, OperationStep.order_id).all()
    if op_ques_info:
        for ques_id, auto_id, order_id, description, is_score, step_score_gene, score_rule, total_score_gene, ques_score_list, knowledge_group_id in op_ques_info:
            # print("description", description)
            op_step_item = {
                'auto_id': auto_id,
                'order_id': order_id,
                'description': description,
                'is_score': is_score,
                'step_score_gene': float(step_score_gene) if step_score_gene is not None else step_score_gene
            }
            if ques_id in op_ques_dict:
                op_ques_dict[ques_id]["op_step_list"].append(op_step_item)
            else:
                new_score_rule_list = get_new_score_rule_list(score_rule)
                op_ques_dict[ques_id] = {
                    "op_step_list": [op_step_item],
                    "score_rule": new_score_rule_list,
                    "total_score_gene": total_score_gene,
                    "ques_score": Decimal(str(sum_with_precision(ques_score_list))),
                    "knowledge_group_id": knowledge_group_id
                }
    return op_ques_dict


def create_op_step_group(op_ques_dict):
    new_op_ques_dict = {}
    for ques_id, op_ques in op_ques_dict.items():
        op_step_group = []
        op_step_list = copy.deepcopy(op_ques["op_step_list"])
        score_rule = op_ques["score_rule"]
        ques_score = op_ques["ques_score"]
        total_score_gene = op_ques["total_score_gene"]
        knowledge_group_id = op_ques["knowledge_group_id"]
        # 一个权重的分数
        single_gene_score = ques_score / total_score_gene

        for rule in score_rule:
            single_op_step_group = []
            # logic = 1 表示 单一步骤，logic = 2 表示 and，logic = 3 表示 or
            if type(rule) == list:
                logic = 2
                logic_text = "满足以下所有步骤才得分"
                for rule_item in rule:
                    single_op_step_group.append(op_step_list[rule_item - 1])
            elif type(rule) == tuple:
                logic = 3
                logic_text = "满足以下任一步骤即得分"
                for rule_item in rule:
                    single_op_step_group.append(op_step_list[rule_item - 1])
            else:
                logic = 1
                logic_text = "单一步骤"
                single_op_step_group = [op_step_list[list(rule)[0] - 1]]

            group_score, step_score_gene = None, None
            is_score = single_op_step_group[0]["is_score"]
            if is_score:
                step_score_gene = Decimal(str(single_op_step_group[0]["step_score_gene"]))
                group_score = single_gene_score * step_score_gene

            op_step_item = {
                "logic": logic,
                "logic_text": logic_text,
                "step_score_gene": float(step_score_gene) if step_score_gene is not None else step_score_gene,
                "group_score": float(group_score) if group_score is not None else group_score,
                "op_step_group": single_op_step_group,
                "is_score": is_score,
                "show": True
            }
            op_step_group.append(op_step_item)

        new_op_ques_dict[ques_id] = {
            "op_step_list": op_step_group,
            "op_total_score_gene": float(total_score_gene) if total_score_gene is not None else total_score_gene,
            "score_rule": score_rule,
            "knowledge_group_id": knowledge_group_id
        }
    return new_op_ques_dict


def match_single_stu_op_path(op_root_path):
    """
    匹配一个考生的操作题作答信息的文件路径
    """
    op_file_name = os.path.basename(op_root_path)
    manual_op_file_dict = get_manual_op_file_path()
    manual_op_file = manual_op_file_dict[op_file_name]
    return manual_op_file


def file_iterator(file_path: str, chunk_size: int = 1024 * 1024):
    """
    生成要发送的文件
    """
    with open(file_path, "rb") as f:
        while chunk := f.read(chunk_size):
            yield chunk


def update_op_ques(new_session, ques_id, knowledge_group_id, op_total_score_gene, op_score_rule_dict, op_step_list):
    """
    保存更新的操作题信息
    """
    # 更新操作步骤分组
    result, op_score_str = restore_op_score_rule(op_score_rule_dict)
    new_session.query(OperationStepGroup).filter(OperationStepGroup.knowledge_group_id == knowledge_group_id).update({
        OperationStepGroup.score_gene: op_total_score_gene,
        OperationStepGroup.score_rule: op_score_str
    })
    # 更新每一个操作步骤
    op_step_info = new_session.query(OperationStep.auto_id).filter(OperationStep.question_id == ques_id).all()
    raw_auto_id_list = [i[0] for i in op_step_info] if op_step_info else []
    add_data, update_data, delete_data = [], [], []
    for index, step in enumerate(op_step_list):
        auto_id, order_id, description, is_score, step_score_gene = step["auto_id"], step["order_id"], step["description"], \
        step["is_score"], step["step_score_gene"]
        if auto_id:
            if auto_id in raw_auto_id_list:
                # auto_id 存在在 raw_auto_id_list 列表里，进行更新
                update_data.append({
                    "auto_id": auto_id,
                    "order_id": order_id,
                    "description": description,
                    "is_score": is_score,
                    "score_gene": step_score_gene
                })
            else:
                # auto_id 不存在在老的 auto_id 列表里，进行删除
                delete_data.append(auto_id)
        else:
            # auto_id 为 None, 则生成新的 auto_id 并进行插入

            step_item = OperationStep(auto_id=configs.snow_worker.get_id(), question_id=ques_id, order_id=order_id,
                                      description=description, knowledge_group_id=knowledge_group_id,
                                      is_score=is_score, score_gene=step_score_gene, from_tool=1)
            add_data.append(step_item)

    if add_data:
        new_session.add_all(add_data)
    if update_data:
        new_session.bulk_update_mappings(OperationStep, update_data)
    if delete_data:
        new_session.query(OperationStep).filter(OperationStep.in_(delete_data)).delete()
        
        
def op_ques_query_condition(new_session, project_id: Optional[str], subject_id: Optional[str],
                            paper_code: Optional[str], ques_code: Optional[str], ques_order: Optional[str],
                            stu_answer: Optional[str], mark_result: Optional[int], mark_state: list,
                            stu_score_range: list, search_time: list):
    project_query = StuAnswer.project_id == project_id if project_id else True
    subject_query = StuAnswer.subject_id == subject_id if subject_id else True
    paper_query = StuAnswer.paper_code == paper_code if paper_code else True
    ques_code_query = StuAnswer.ques_code.ilike(f"%{ques_code}%") if ques_code else True
    stu_answer_query = StuAnswer.stu_answer.ilike(f"%{stu_answer}%") if stu_answer else True
    mark_result_query = StuAnswer.mark_result == mark_result if mark_result else True
    mark_state_query = StuAnswer.mark_state.in_(mark_state) if mark_state else True
    score_range_query = StuAnswer.stu_score.between(*stu_score_range) if stu_score_range else True
    time_query = and_(StuAnswer.mark_state != 1, StuAnswer.mark_state != 5, StuAnswer.updated_time.between(*search_time)) if search_time else True

    ques_order_query = True
    if ques_order:
        ques_id_info = new_session.query(PaperDetail.ques_id).filter(PaperDetail.ques_order == ques_order).all()
        ques_id_list = [i[0] for i in ques_id_info] if ques_id_info else []
        ques_order_query = StuAnswer.ques_id.in_(ques_id_list) if ques_id_list else True

    condition = and_(project_query, subject_query, paper_query, ques_code_query, ques_order_query, stu_answer_query,
                     mark_result_query, mark_state_query, score_range_query, time_query)
    return condition
