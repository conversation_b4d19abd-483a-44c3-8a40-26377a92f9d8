import os

from fastapi import APIRouter, Depends
from fastapi.responses import FileResponse
from settings import logger
from typing import Any

from apps.users.services import get_current_user
from apps.read_paper import TemplateReq
from apps.base.schemas import BaseResponse
from helper import response_utils
from settings import configs

common_router = APIRouter()


@common_router.get(path="/get_template_file", response_model=BaseResponse, summary="获取模板文件")
async def get_template_file(query: TemplateReq = Depends(), user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 获取模板文件")
    template_type = query.temp_type
    template_format = query.temp_format
    file_type_conf = {
        "1": "server_static/template_file/导入试卷模板",
        "2": "server_static/template_file/导入作答模板",
        "3": "server_static/template_file/导入考生模板",
        "4": "server_static/template_file/阅卷人员模板"
    }
    file_format_conf = {
        "1": ".xlsx",
        # "2": ".docx"  # 暂无
    }
    if template_format == "2":
        return BaseResponse(code=response_utils.no_field, msg="暂无该格式模板文件")
    template_file_path = os.path.join(configs.PROJECT_PATH,
                                      f"{file_type_conf[template_type]}{file_format_conf[template_format]}")
    return FileResponse(template_file_path, filename=os.path.basename(template_file_path))
