import re
import threading
import time
import traceback
import requests
import base64
import os
from datetime import datetime

from fastapi import APIRouter, Depends
from sqlalchemy import select, func, and_

from apps.ai_mark.schemas import GetAiMarkReq, StartAiMarkReq, CreateGradeReq, GetGradeListReq, GetAiGradeDetailReq, \
    ClearStuMarkRecordReq
from apps.ai_mark.services import create_ai_mark_data, trans_ai_mark_state, start_ai_mark_main, get_ai_mark_process, \
    check_ques_set_std_finished, ai_grade_query_condition, get_ques_in_paper_data, get_ques_type_trans, \
    create_paper_grade, create_params_grade, get_params_exam_total_score, get_clear_stu_data, check_objective_mark_rule
from apps.ai_set_std.set_std_services import get_set_std_ques_condition, get_ai_mark_condition, \
    get_has_answer_ques_id_list
from apps.base.global_cache import get_redis_ques_info_dict
from apps.ques_manage.services import get_business_data
from apps.read_paper.ai_services import judge_mark_result
from helper import response_utils
from settings import logger, configs
from typing import Any
from sqlalchemy.orm import Session

from apps.users.services import get_current_user
from apps.models.models import ExamQuestion, Project, PaperDetail, ExamPaper, Subject, QuesType, QuesAiMark, \
    SameStuAnswerGroup, StuTotalGrade, StuTotalGradeDetail, ExamStudent, CreateGradeRecord, BusinessQuesType, \
    ManualDistributeAnswer, ManualMark, ManualArbitrateQuality, ManualReadTask, StudentSubject, ManualMarkHistory, \
    TaskExecuteRecord, StuAnswer, StuAnswerSimilarity
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from utils.time_func import format_now_time
from utils.utils import sum_with_precision, round_half_up

ai_mark_router = APIRouter()


@ai_mark_router.post(path="/get_ai_mark_list", response_model=BaseResponse, summary="获取AI评分列表")
async def get_ai_mark_list(query: GetAiMarkReq, user: Any = Depends(get_current_user),
                           new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取AI评分列表")
    # 创建表数据
    create_ai_mark_data(new_session)
    current_page, page_size, project_id, subject_id, paper_id, ques_type_code_list, ques_code, mark_state_list = query.model_dump().values()

    # 查询有考生作答信息的试题
    has_answer_ques_id_list = get_has_answer_ques_id_list(new_session, is_reload=False)

    ai_mark_ques_data = []
    limit = current_page - 1
    offset = limit * page_size
    # 拼凑查询条件
    filter_condition = get_ai_mark_condition(project_id, subject_id, paper_id, ques_type_code_list, ques_code,
                                             mark_state_list)
    condition = and_(filter_condition, QuesAiMark.ques_id.in_(has_answer_ques_id_list))
    total = new_session.query(QuesAiMark) \
        .join(ExamQuestion, ExamQuestion.ques_id == QuesAiMark.ques_id) \
        .outerjoin(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
        .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
        .join(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
        .outerjoin(Project, Project.project_id == ExamPaper.project_id) \
        .outerjoin(Subject, Subject.subject_id == ExamPaper.subject_id) \
        .where(condition) \
        .group_by(QuesAiMark.mark_id, QuesAiMark.ques_id, Subject.subject_name, QuesType.ques_type_name,
                  ExamQuestion.ques_code, QuesAiMark.mark_state, QuesAiMark.start_time, QuesAiMark.end_time).count()
    state_stmt = select(QuesAiMark.mark_id, QuesAiMark.ques_id, Subject.subject_name,
                        func.group_concat(ExamPaper.paper_id).label("paper_id"),
                        func.group_concat(ExamPaper.paper_name).label("paper_name"), QuesType.ques_type_name,
                        ExamQuestion.ques_code, ExamQuestion.business_ques_type_id, PaperDetail.ques_order,
                        QuesAiMark.mark_state, QuesAiMark.start_time, QuesAiMark.end_time) \
        .join(ExamQuestion, ExamQuestion.ques_id == QuesAiMark.ques_id) \
        .outerjoin(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
        .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
        .join(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
        .outerjoin(Project, Project.project_id == ExamPaper.project_id) \
        .outerjoin(Subject, Subject.subject_id == ExamPaper.subject_id) \
        .where(condition) \
        .group_by(QuesAiMark.mark_id, QuesAiMark.ques_id, Subject.subject_name, QuesType.ques_type_name,
                  ExamQuestion.ques_code, PaperDetail.ques_order, QuesAiMark.mark_state, QuesAiMark.start_time,
                  QuesAiMark.end_time, ExamPaper.paper_id, ExamPaper.paper_name) \
        .order_by(QuesAiMark.start_time.desc(), QuesAiMark.mark_id) \
        .limit(page_size).offset(offset)

    try:
        result = list(new_session.execute(state_stmt))

        ques_id_state_dict = {}
        for i in result:
            ques_id_state_dict[i.ques_id] = i.mark_state

        # 进度
        process_num_dict, process_dict = get_ai_mark_process(new_session, ques_id_state_dict)

        # 前端是否刷新列表参数，0:不刷新，1:刷新
        is_refresh = 0

        business_subject = get_business_data(new_session)

        for row in result:
            business_ques_type_id, subject_name = row.business_ques_type_id, row.subject_name
            if not subject_name and business_ques_type_id:
                subject_name = business_subject.get(business_ques_type_id, {}).get("subject_name")

            mark_item = {
                "mark_id": row.mark_id,
                "ques_id": row.ques_id,
                "subject_name": subject_name,
                "paper_id": row.paper_id,
                "paper_name": row.paper_name,
                "ques_type_name": row.ques_type_name,
                "ques_code": row.ques_code,
                "ques_order": re.sub(r'^0+', '', row.ques_order) if row.ques_order else None,
                "mark_state": row.mark_state,
                "mark_state_str": trans_ai_mark_state(row.mark_state),
                "progress_count": process_num_dict[row.ques_id],
                "progress": process_dict[row.ques_id],
                "start_time": row.start_time and str(row.start_time).replace("T", " "),
                "end_time": row.end_time and str(row.end_time).replace("T", " ")
            }
            ai_mark_ques_data.append(mark_item)

            if row.mark_state == 2:
                is_refresh = 1
    except Exception as e:
        logger.error(f"获取AI评分列表失败，{e}")
        logger.error(traceback.print_exc())
        return BaseResponse(code=response_utils.server_error, msg="获取AI评分列表失败")

    logger.info("获取AI评分列表成功")
    data = {
        "data": ai_mark_ques_data,
        "total": total,
        "is_refresh": is_refresh
    }
    return BaseResponse(msg="获取AI评分列表成功", data=data)


@ai_mark_router.post(path="/start_ai_mark", response_model=BaseResponse, summary="开始 AI 评分")
async def start_ai_mark(query: StartAiMarkReq, user: Any = Depends(get_current_user),
                        new_session: Session = Depends(session_depend)):
    """
    给考生答案赋分
    """
    logger.info(f"{user['username']} 开始 AI 评分")
    ques_info = query.ques_info
    ques_id_list = [ques["ques_id"] for ques in ques_info]

    # 检查客观题是否都有对应的评分规则
    result, rule_detail_dict, msg = check_objective_mark_rule(new_session, ques_info)
    if not result:
        return BaseResponse(code=response_utils.no_field, msg=msg)

    # 检查主观题题所有答案聚类是否都已经进行 AI 定标
    msg = check_ques_set_std_finished(new_session, ques_id_list)
    if msg:
        return BaseResponse(code=response_utils.task_not_finished, msg=msg)

    # 将定标状态改为评分中
    new_session.query(QuesAiMark).filter(QuesAiMark.ques_id.in_(ques_id_list)).update({
        QuesAiMark.mark_state: 2,
        QuesAiMark.running_state: 2,
        QuesAiMark.start_time: format_now_time(),
        QuesAiMark.end_time: None
    })

    new_session.query(StuAnswer).filter(StuAnswer.ques_id.in_(ques_id_list)).update({
        StuAnswer.set_std_score: None
    })
    new_session.commit()

    # 开启线程处理任务
    threading.Thread(target=start_ai_mark_main, args=(ques_info, rule_detail_dict)).start()

    return BaseResponse(msg="已开始 AI 评分")


@ai_mark_router.post(path="/create_grade", response_model=BaseResponse, summary="生成考试评分成绩")
async def create_grade(query: CreateGradeReq, user: Any = Depends(get_current_user),
                       new_session: Session = Depends(session_depend)):
    """
    生成考试评分成绩
    """
    logger.info(f"{user['username']} 生成考试评分成绩")
    project_id, subject_id, paper_id, stu_secret_num, grade_type = query.model_dump().values()

    # 判断是抽卷还是抽参
    is_paper = new_session.query(ExamPaper).count()

    if is_paper:
        result, msg = create_paper_grade(new_session, project_id, subject_id, paper_id, stu_secret_num, grade_type,
                                         user)
    else:
        result, msg = create_params_grade(new_session, project_id, subject_id, paper_id, stu_secret_num, grade_type,
                                          user)

    if result:
        return BaseResponse(msg="生成成绩成功")
    return BaseResponse(code=response_utils.server_error, msg=msg)


@ai_mark_router.post(path="/create_ai_grade_process", response_model=BaseResponse,
                     summary="获取生成成绩进度")
async def create_ai_grade_process(user: Any = Depends(get_current_user),
                                  new_session: Session = Depends(session_depend)):
    """
    获取生成成绩进度
    """
    logger.info(f"{user['username']} 获取生成成绩进度")
    record_info = new_session.query(CreateGradeRecord.success_count, CreateGradeRecord.total_count,
                                    CreateGradeRecord.progress) \
        .order_by(CreateGradeRecord.created_time.desc()).first()
    if record_info:
        success_count, total_count, progress = record_info
        data = {
            "success_count": success_count,
            "total_count": total_count,
            "progress": progress
        }
        return BaseResponse(data=data)
    return BaseResponse(data={})


@ai_mark_router.post(path="/get_ai_grade_list", response_model=BaseResponse, summary="获取考生成绩列表")
async def get_ai_grade_list(query: GetGradeListReq, user: Any = Depends(get_current_user),
                            new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考生成绩列表")
    current_page, page_size, project_id, subject_id, paper_id, stu_secret_num, score_range, query_score_type, export_type, grade_type = query.model_dump().values()

    allow_exam_num, condition = ai_grade_query_condition(new_session, project_id, subject_id, paper_id, stu_secret_num,
                                                         score_range, query_score_type)

    is_paper = new_session.query(ExamPaper).count()

    grade_data = []
    limit = current_page - 1
    offset = limit * page_size

    try:
        select_fields = [
            StuTotalGrade.grade_id, ExamStudent.stu_secret_num, StuTotalGrade.project_id, StuTotalGrade.subject_id,
            StuTotalGrade.paper_id, StuTotalGrade.total_grade, StuTotalGrade.created_time, ExamPaper.paper_name,
            Project.project_name, Subject.subject_name, ExamPaper.total_score,
            StuTotalGrade.manual_total_grade, StuTotalGrade.allow_exam_num
        ]

        total = new_session.query(StuTotalGrade.grade_id) \
            .outerjoin(ExamPaper, ExamPaper.paper_id == StuTotalGrade.paper_id) \
            .join(Project, Project.project_id == StuTotalGrade.project_id) \
            .join(Subject, Subject.subject_id == StuTotalGrade.subject_id) \
            .join(ExamStudent, ExamStudent.allow_exam_num == StuTotalGrade.allow_exam_num) \
            .where(condition).count()

        stmt = select(*select_fields) \
            .join(Project, Project.project_id == StuTotalGrade.project_id) \
            .join(Subject, Subject.subject_id == StuTotalGrade.subject_id) \
            .outerjoin(ExamPaper, ExamPaper.paper_id == StuTotalGrade.paper_id) \
            .join(ExamStudent, ExamStudent.allow_exam_num == StuTotalGrade.allow_exam_num) \
            .where(condition) \
            .order_by(StuTotalGrade.created_time.desc(), StuTotalGrade.grade_id.desc())

        if export_type == 1:
            # 导出 excel 表格
            # select_fields.append(StuTotalGrade.allow_exam_num)
            pass
        elif export_type == 2:
            return BaseResponse(code=response_utils.params_error, msg="请调用 export_stu_grade_to_sqlite 接口")
        elif export_type == 3:
            return BaseResponse(code=response_utils.params_error, msg="export_type 参数有误")
        else:
            stmt = stmt.limit(page_size).offset(offset)

        business_data = get_business_data(new_session)

        total_score_dict = {}
        result = new_session.execute(stmt)
        for row in result:
            project_id, subject_id, total_score = row.project_id, row.subject_id, row.total_score
            if is_paper:
                total_score = float(row.total_score) if total_score is not None else None
            else:
                if project_id in total_score_dict:
                    if subject_id in total_score_dict[project_id]:
                        total_score = total_score_dict[project_id][subject_id]
                    else:
                        total_score = get_params_exam_total_score(new_session, business_data, project_id, subject_id)
                        total_score_dict[project_id][subject_id] = total_score
                else:
                    total_score = get_params_exam_total_score(new_session, business_data, project_id, subject_id)
                    total_score_dict[project_id] = {subject_id: total_score}

            grade_item = {
                "grade_id": row.grade_id,
                "stu_secret_num": row.stu_secret_num,
                "project_id": project_id,
                "subject_id": subject_id,
                "paper_id": row.paper_id,
                "paper_name": row.paper_name,
                "project_name": row.project_name,
                "subject_name": row.subject_name,
                "total_grade": float(row.total_grade) if row.total_grade is not None else None,
                "manual_grade": float(row.manual_total_grade) if row.manual_total_grade is not None else None,
                "total_score": total_score,
                "created_time": row.created_time and str(row.created_time).replace("T", " ")
            }
            if export_type == 1:
                grade_item["allow_exam_num"] = row.allow_exam_num
            grade_data.append(grade_item)

    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.info(f"获取考生成绩列表失败，{e}")
        return BaseResponse(code=response_utils.params_error, msg="获取考生成绩列表失败")
    logger.info("获取考生成绩列表成功")
    data = {
        "data": grade_data,
        "total": total
    }
    return BaseResponse(msg="获取考生成绩列表成功", data=data)


@ai_mark_router.post(path="/get_grade_detail", response_model=BaseResponse, summary="获取考生成绩详情")
async def get_ai_grade_detail(query: GetAiGradeDetailReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考生成绩详情")
    grade_id, grade_type = query.model_dump().values()

    # 获取所有试题信息
    all_ques_info = get_redis_ques_info_dict(new_session)

    paper_id = new_session.query(StuTotalGrade.paper_id).filter(StuTotalGrade.grade_id == grade_id).scalar()

    if paper_id:
        ques_order_info = new_session.query(PaperDetail.ques_id, PaperDetail.ques_order).filter(
            PaperDetail.paper_id == paper_id).all()
        ques_order_dict = {ques_id: re.sub(r'^0+', '', ques_order) for ques_id, ques_order in
                           ques_order_info} if ques_order_info else {}

        # 获取所有试题在试卷里的序号、分数
        ques_in_paper_data = get_ques_in_paper_data(new_session)
        business_data = {}
    else:
        ques_in_paper_data = {}
        ques_order_dict = {}
        business_data = get_business_data(new_session)

    # 获取题型编码对应的中文
    ques_type_trans = get_ques_type_trans(new_session)

    # 获取成绩详情信息
    grade_detail = new_session.query(StuTotalGradeDetail.paper_id, StuTotalGradeDetail.ques_id,
                                     StuTotalGradeDetail.ques_order, StuTotalGradeDetail.stu_answer,
                                     StuTotalGradeDetail.stu_score_list, StuTotalGradeDetail.stu_score,
                                     StuTotalGradeDetail.answer_parse, ExamQuestion.ques_type_code,
                                     ExamQuestion.parent_ques_id) \
        .join(ExamQuestion, ExamQuestion.ques_id == StuTotalGradeDetail.ques_id) \
        .filter(and_(StuTotalGradeDetail.grade_id == grade_id, StuTotalGradeDetail.grade_type == grade_type)) \
        .order_by(StuTotalGradeDetail.ques_order, StuTotalGradeDetail.ques_id) \
        .all()

    detail_data = []
    ques_type_dict = {}

    for paper_id, ques_id, ques_order, stu_answer, stu_score_list, stu_score, answer_parse, ques_type_code, parent_ques_id in grade_detail:
        ques_info = all_ques_info[ques_id]
        if paper_id:
            ques_score = ques_in_paper_data[paper_id][ques_id]["ques_score"]
            ques_order = ques_in_paper_data[paper_id][ques_id]["ques_order"]
            ques_order = re.sub(r'^0+', '', ques_order) if ques_order else None
        else:
            business_id = ques_info["business_ques_type_id"]
            ques_score = business_data[business_id]["ques_type_score"]
        item = {
            "ques_id": ques_id,
            "stu_answer": stu_answer.split(configs.NEW_SPLIT_FLAG) if stu_answer else [],
            "stu_score_list": stu_score_list,
            "stu_score": stu_score,
            "answer_parse": answer_parse,
            "ques_type_code": ques_type_code,
            "ques_score": ques_score,
            "ques_order": ques_order,
            "mark_result": judge_mark_result(stu_score, ques_score)
        }
        item.update(ques_info)
        if parent_ques_id:
            if "F" not in ques_type_dict:
                ques_type_dict["F"] = {}
            if parent_ques_id in ques_type_dict["F"]:
                ques_type_dict["F"][parent_ques_id].append(item)
            else:
                ques_type_dict["F"][parent_ques_id] = [item]
        else:
            if ques_type_code in ques_type_dict:
                ques_type_dict[ques_type_code].append(item)
            else:
                ques_type_dict[ques_type_code] = [item]

    # print("ques_type_dict", ques_type_dict)

    for ques_type_code, ques_type_value in ques_type_dict.items():
        ques_item = {
            "correct_count": 0,
            "not_marked_count": 0,
            "part_correct_count": 0,
            "wrong_count": 0,
            "ques_count": 0,
            "score": 0,
            "type": ques_type_trans[ques_type_code],
            "type_code": ques_type_code,
            "type_ques_score": 0,
            "type_stu_score": 0,
            "data": []
        }
        type_ques_score, type_stu_score, correct_count, wrong_count, part_correct_count, not_marked_count = 0, 0, 0, 0, 0, 0
        if ques_type_code == "F":
            for ques_id, children in ques_type_value.items():
                # ques_id, children = next(iter(ques_type_value.items()))
                ques_info = all_ques_info[ques_id]

                # 遍历小题
                f_stu_score, f_ques_score = 0, 0
                for small_ques in children:
                    f_stu_score += small_ques["stu_score"] if small_ques["stu_score"] else 0
                    f_ques_score += small_ques["ques_score"] if small_ques["ques_score"] else 0
                    mark_result = small_ques.get("mark_result")
                    if mark_result == 1:
                        correct_count += 1
                    elif mark_result == 2:
                        wrong_count += 1
                    elif mark_result == 3:
                        part_correct_count += 1
                    elif mark_result == 4:
                        not_marked_count += 1

                f_ques_data = {
                    "ques_id": ques_id,
                    "ques_order": ques_order_dict.get(ques_id),
                    "children": children,
                    "stu_score": f_stu_score,
                    "ques_score": f_ques_score,
                    "mark_result": judge_mark_result(f_stu_score, f_ques_score)
                }
                f_ques_data.update(ques_info)
                ques_item["data"].append(f_ques_data)

                type_stu_score += f_stu_score
                type_ques_score += f_ques_score

                ques_item["stu_score"] = f_stu_score
                ques_item["ques_score"] = f_ques_score
                ques_item["mark_result"] = judge_mark_result(type_stu_score, type_ques_score)

        else:
            ques_item["data"] = ques_type_value
            for small_ques in ques_type_value:
                type_stu_score += small_ques["stu_score"] if small_ques["stu_score"] else 0
                type_ques_score += small_ques["ques_score"] if small_ques["ques_score"] else 0
                mark_result = small_ques.get("mark_result")
                if mark_result == 1:
                    correct_count += 1
                elif mark_result == 2:
                    wrong_count += 1
                elif mark_result == 3:
                    part_correct_count += 1
                elif mark_result == 4:
                    not_marked_count += 1

        type_stu_score = round_half_up(type_stu_score, 2)

        ques_item["type_ques_score"], ques_item["type_stu_score"], ques_item["correct_count"], \
            ques_item["wrong_count"], ques_item["part_correct_count"], ques_item["not_marked_count"] = \
            type_ques_score, type_stu_score, correct_count, wrong_count, part_correct_count, not_marked_count

        detail_data.append(ques_item)
    return BaseResponse(data=detail_data)


@ai_mark_router.post(path="/clear_stu_mark_record", response_model=BaseResponse, summary="清除指定考生人工阅卷评分记录")
async def get_ai_grade_detail(query: ClearStuMarkRecordReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 清除指定考生人工阅卷评分记录")
    project_id, subject_id, paper_id, stu_secret_num, score_range, stu_secret_num_list = query.model_dump().values()

    try:
        if not stu_secret_num_list:
            result, msg, stu_secret_num_list = get_clear_stu_data(project_id, subject_id, paper_id, stu_secret_num, score_range, 2, 1, 2, user)
            if not result:
                return BaseResponse(code=response_utils.server_error, msg="清除指定考生人工阅卷评分记录失败")
            if not stu_secret_num_list:
                return BaseResponse(code=response_utils.server_error, msg="查询不到相应的考生数据")

        condition = ManualDistributeAnswer.stu_secret_num.in_(stu_secret_num_list)

        distri_answer_id_list, distri_answer_id_set, task_dict = [], set(), {}

        try:
            manual_info = new_session.query(ManualDistributeAnswer.distri_answer_id, ManualDistributeAnswer.m_read_task_id,
                                            ManualDistributeAnswer.ques_id).filter(condition).all()
            if manual_info:
                for distri_answer_id, task_id, ques_id in manual_info:
                    distri_answer_id_set.add(distri_answer_id)
                    if task_id not in task_dict:
                        task_dict[task_id] = ques_id
                distri_answer_id_list = list(distri_answer_id_set)

            task_id_list = list(task_dict.keys())

            project_id_list, subject_id_list, task_record_dict = [], [], {}
            if task_id_list:
                task_info = new_session.query(ManualReadTask.m_read_task_id, ManualReadTask.project_id, ManualReadTask.subject_id, ManualReadTask.expert_read_record_id).filter(ManualReadTask.m_read_task_id.in_(task_id_list)).all()
                for task_id, project_id, subject_id, record_id in task_info:
                    project_id_list.append(project_id)
                    subject_id_list.append(subject_id)
                    task_record_dict[task_id] = record_id

            logger.info("清空最终成绩和评析")
            new_session.query(ManualDistributeAnswer).filter(condition).update({
                ManualDistributeAnswer.final_mark_score: None,
                ManualDistributeAnswer.quality_count: 0,
                ManualDistributeAnswer.answer_parse: {},
                ManualDistributeAnswer.updated_time: None
            })

            logger.info("清空专家评分数据")
            new_session.query(ManualMark).filter(and_(ManualMark.m_read_task_id.in_(task_id_list),
                                                      ManualMark.distri_answer_id.in_(distri_answer_id_list))).delete()

            logger.info("清空仲裁和质检数据")
            new_session.query(ManualArbitrateQuality).filter(and_(ManualArbitrateQuality.m_read_task_id.in_(task_id_list),
                                                                  ManualArbitrateQuality.distri_answer_id.in_(
                                                                      distri_answer_id_list))).delete()

            logger.info("清空成绩数据")
            exam_num_info = new_session.query(ExamStudent.allow_exam_num) \
                .join(StudentSubject, StudentSubject.allow_exam_num == ExamStudent.allow_exam_num) \
                .filter(and_(ExamStudent.project_id.in_(project_id_list), StudentSubject.subject_id.in_(subject_id_list),
                             ExamStudent.stu_secret_num.in_(stu_secret_num_list))).all()
            exam_num_list = [i[0] for i in exam_num_info] if exam_num_info else []
            new_session.query(StuTotalGrade).filter(StuTotalGrade.allow_exam_num.in_(exam_num_list)).update({
                StuTotalGrade.manual_a_grade: None,
                StuTotalGrade.manual_b_grade: None,
                StuTotalGrade.manual_c_grade: None,
                StuTotalGrade.manual_d_grade: None,
                StuTotalGrade.manual_e_grade: None,
                StuTotalGrade.manual_f_grade: None,
                StuTotalGrade.manual_total_grade: None,
                StuTotalGrade.u_user_id: user["user_id"]
            })
            new_session.query(StuTotalGradeDetail).filter(and_(StuTotalGradeDetail.allow_exam_num.in_(exam_num_list), StuTotalGradeDetail.grade_type == 2)).delete()

            logger.info("清空阅卷历史记录")
            new_session.query(ManualMarkHistory).filter(
                and_(ManualMarkHistory.m_read_task_id.in_(task_id_list),
                     ManualMarkHistory.distri_answer_id.in_(distri_answer_id_list))).delete()

            new_session.commit()
        except Exception as e:
            new_session.rollback()
            msg = f"清除指定考生人工阅卷评分记录失败1：{e}"
            logger.error(msg)
            logger.error(traceback.format_exc())
            return BaseResponse(code=response_utils.server_error, msg="清除指定考生人工阅卷评分记录失败")

        logger.info("重新计算阅卷任务进度")
        for task_id, record_id in task_record_dict.items():
            distri_info = new_session.query(ManualDistributeAnswer.final_mark_score).filter(ManualDistributeAnswer.m_read_task_id == task_id).all()
            total_count, success_count = 0, 0
            for i in distri_info:
                total_count += 1
                if i[0] is not None:
                    success_count += 1

            progress = round_half_up(success_count / total_count * 100, 2)
            new_session.query(TaskExecuteRecord).filter(TaskExecuteRecord.record_id == record_id).update({
                TaskExecuteRecord.success_count: success_count,
                TaskExecuteRecord.total_count: total_count,
                TaskExecuteRecord.progress: progress
            })
            new_session.commit()
    except Exception as e:
        new_session.rollback()
        msg = f"清除指定考生人工阅卷评分记录失败2：{e}"
        logger.error(msg)
        logger.error(traceback.format_exc())
        return BaseResponse(code=response_utils.server_error, msg="清除指定考生人工阅卷评分记录失败")

    logger.info("清除指定考生人工阅卷评分记录成功")
    return BaseResponse(msg="清除指定考生人工阅卷评分记录成功")

@ai_mark_router.post(path="/calculate_img_similarity", response_model=BaseResponse, summary="绘图题作答图片相似度分析")
async def calculate_img_similarity(user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    """
    根据ques_id获取考生作答图片信息并计算相似度
    """
    logger.info(f"{user['username']} 获取作答图片信息并计算相似度")

    try:
        # 查询所有有效的试题ID
        exam_ques_ids = new_session.query(ExamQuestion.ques_id).all()
        valid_ques_ids = [q.ques_id for q in exam_ques_ids]
        
        # 查询所有考生作答图片信息
        results = new_session.query(StuAnswer.answer_id, StuAnswer.answer_image_path, StuAnswer.ques_id).filter(
            StuAnswer.ques_id.in_(valid_ques_ids)
        ).all()
        
        if not results:
            return BaseResponse(code=response_utils.no_field, msg="未找到图片数据")
            
        # 按试题ID分组处理
        ques_groups = {}
        for ans_id, img_path, ques_id in results:
            if img_path:
                if ques_id not in ques_groups:
                    ques_groups[ques_id] = []
                ques_groups[ques_id].append((ans_id, img_path))
        
        all_results = {}
        for current_ques_id, group in ques_groups.items():
            # 构建当前ques_id的图片字典
            images = {}
            for _, (ans_id, img_path) in enumerate(group):
                try:
                    # 读取图片文件并转换为base64
                    with open(img_path, "rb") as image_file:
                        encoded_string = base64.b64encode(image_file.read()).decode("utf-8")
                    # 获取文件扩展名
                    _, ext = os.path.splitext(img_path)
                    # 构建标准的data URI
                    data_uri = f"data:image/{ext[1:]};base64,{encoded_string}"
                    images[ans_id] = data_uri
                except Exception as e:
                    logger.error(f"图片文件读取失败: ans_id={ans_id}, path={img_path}, error={str(e)}")
                    continue
            
            if images:  # 确保有有效图片才调用接口
                # 构造请求参数
                payload = {
                    "images": images,
                    "similarity_threshold": 0.85,
                    "enable_preprocessing": True
                }
                
                # 调用图片相似度分析接口
                similarity_url = "http://192.168.0.200:7862/calculate_image_similarity"
                response = requests.post(similarity_url, json=payload)
                
                # 记录单个ques_id的结果
                if response.status_code == 200:
                    result_data = response.json()
                    all_results[current_ques_id] = result_data
                    
                    # 存储相似度结果到数据库
                    try:
                        # 获取当前时间作为记录时间

                        # 处理重复对
                        duplicate_pairs = result_data.get("data", {}).get("duplicate_pairs", [])
                        for pair in duplicate_pairs:
                            image1_id = pair.get("image1_id")
                            image2_id = pair.get("image2_id")
                            similarity = pair.get("similarity")
                            
                            # 创建数据库记录
                            similarity_record = StuAnswerSimilarity(
                                ans_similarity_id=configs.snow_worker.get_id(),
                                answer_id=image1_id,
                                answer_id_to=image2_id,
                                similarity=similarity,
                            )
                            new_session.add(similarity_record)
                        
                        # 提交事务
                        new_session.commit()
                        
                    except Exception as db_error:
                        new_session.rollback()
                        logger.error(f"数据库存储失败: {str(db_error)}")
                        logger.error(traceback.format_exc())
                        
                else:
                    logger.error(f"调用相似度接口失败: {response.text}")
                    all_results[current_ques_id] = {"error": "图片相似度分析失败", "detail": response.text}
        
        if not all_results:
            return BaseResponse(code=response_utils.no_field, msg="未找到有效图片数据")
            
        return BaseResponse(data=all_results)
        
    except Exception as e:
        logger.error(f"处理图片相似度分析失败: {str(e)}")
        logger.error(traceback.print_exc())
        return BaseResponse(code=response_utils.server_error, msg="图片相似度分析失败")
