#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 apps.human_statistics.services.py 模块的测试脚本
"""

import sys
import os
from datetime import datetime, date

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from apps.human_statistics.services import (
    calculate_reviewed_count,
    calculate_average_score,
    calculate_cumulative_average_score,
    calculate_max_score,
    calculate_min_score,
    calculate_arbitration_count,
    calculate_workload,
    calculate_score_distribution,
    record_spy_paper_score,  # 导入新添加的间谍卷函数
    calculate_average_marking_speed
)
from apps.human_statistics.models import HumanStatisticsPerson

# 测试数据库配置
TEST_DATABASE_URL = "mysql+pymysql://root:qweasdzxc888@192.168.1.234:3306/intelligence_read_paper"

def setup_test_database():
    """设置测试数据库"""
    try:
        # 创建引擎
        engine = create_engine(TEST_DATABASE_URL, echo=False)
        
        # 创建 Session 工厂
        SessionLocal = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))
        
        return SessionLocal
    except Exception as e:
        print(f"设置测试数据库失败: {e}")
        return None

# 设置数据库会话
SessionLocal = setup_test_database()
if SessionLocal:
    new_session = SessionLocal()
else:
    print("无法设置数据库，退出测试")
    exit(1)

# 测试参数
test_user_id = "test_user_123"
test_task_id = "test_task_123"
test_round_id = "test_round_123"
# test_date = date(2025, 8, 7)
date_range = "2025-08-08"
# total_score = 100.0

print("开始测试 apps.human_statistics.services 模块")
print("=" * 80)

# # 测试 parse_date_range 函数
# print("\n测试 parse_date_range 函数...")
# result1 = parse_date_range(None)
# print(f"None 输入: {result1}")
#
# result2 = parse_date_range("2025-08-07至2025-08-07")
# print(f"有效日期范围: {result2}")
#
# result3 = parse_date_range("invalid-date-range")
# print(f"无效日期范围: {result3}")

# 测试 calculate_reviewed_count 函数
# print("\n测试 calculate_reviewed_count 函数...")
# result1 = calculate_reviewed_count(new_session, test_user_id, test_task_id, test_round_id, "2025-08-08至2025-08-08")
# print(f"第一次调用: {result1}")
# #
# result2 = calculate_reviewed_count(new_session, test_user_id, test_task_id, test_round_id, "2025-08-08至2025-08-08")
# print(f"第二次调用: {result2}")
#
# # 检查已阅量记录
# record = new_session.query(HumanStatisticsPerson).filter(
#     HumanStatisticsPerson.user_id == test_user_id,
#     HumanStatisticsPerson.task_id == test_task_id,
#     HumanStatisticsPerson.round_id == test_round_id,
#     HumanStatisticsPerson.statistics_type == 4
# ).first()
# if record:
#     print(f"数据库中的已阅量记录: {record.statistics_result_1}")
# #
# 测试 calculate_average_score 函数
# print("\n测试 calculate_average_score 函数...")
# result1 = calculate_average_score(new_session, test_user_id, test_task_id, test_round_id, 85.5, "2025-08-08至2025-08-08")
# print(f"第一次调用 (分数: 85.5): {result1}")

# result2 = calculate_average_score(new_session, test_user_id, test_task_id, test_round_id, 90.0, "2025-08-08至2025-08-08")
# print(f"第二次调用 (分数: 90.0): {result2}")
#
# # 检查平均分记录
# record = new_session.query(HumanStatisticsPerson).filter(
#     HumanStatisticsPerson.user_id == test_user_id,
#     HumanStatisticsPerson.task_id == test_task_id,
#     HumanStatisticsPerson.round_id == test_round_id,
#     HumanStatisticsPerson.statistics_type == 1
# ).first()
# if record:
#     print(f"数据库中的平均分记录: {record.statistics_result_1}")

# 测试 calculate_cumulative_average_score 函数
# print("\n测试 calculate_cumulative_average_score 函数...")
# result1 = calculate_cumulative_average_score(new_session, test_user_id, test_task_id, test_round_id, 80.0)
# print(f"第一次调用 (分数: 80.0): {result1}")
#
# result2 = calculate_cumulative_average_score(new_session, test_user_id, test_task_id, test_round_id, 90.0)
# print(f"第二次调用 (分数: 90.0): {result2}")
#
# # 检查累计平均分记录
# record = new_session.query(HumanStatisticsPerson).filter(
#     HumanStatisticsPerson.user_id == test_user_id,
#     HumanStatisticsPerson.task_id == test_task_id,
#     HumanStatisticsPerson.round_id == test_round_id,
#     HumanStatisticsPerson.statistics_type == 14
# ).first()
# if record:
#     print(f"数据库中的累计平均分记录: {record.statistics_result_1}")

# # 测试 calculate_max_score 函数
# print("\n测试 calculate_max_score 函数...")
# result1 = calculate_max_score(new_session, test_user_id, test_task_id, test_round_id, 85.0, date_range)
# print(f"第一次调用 (分数: 85.0): {result1}")
#
# result2 = calculate_max_score(new_session, test_user_id, test_task_id, test_round_id, 95.0, date_range)
# print(f"第二次调用 (分数: 95.0): {result2}")
#
# result3 = calculate_max_score(new_session, test_user_id, test_task_id, test_round_id, 80.0, date_range)
# print(f"第三次调用 (分数: 80.0): {result3}")
#
# # 检查最高分记录
# record = new_session.query(HumanStatisticsPerson).filter(
#     HumanStatisticsPerson.user_id == test_user_id,
#     HumanStatisticsPerson.task_id == test_task_id,
#     HumanStatisticsPerson.round_id == test_round_id,
#     HumanStatisticsPerson.statistics_type == 2
# ).first()
# if record:
#     print(f"数据库中的最高分记录: {record.statistics_result_1}")
#
# # 测试 calculate_min_score 函数
# print("\n测试 calculate_min_score 函数...")
# result1 = calculate_min_score(new_session, test_user_id, test_task_id, test_round_id, 85.0, date_range)
# print(f"第一次调用 (分数: 85.0): {result1}")
#
# result2 = calculate_min_score(new_session, test_user_id, test_task_id, test_round_id, 75.0, date_range)
# print(f"第二次调用 (分数: 75.0): {result2}")
#
# result3 = calculate_min_score(new_session, test_user_id, test_task_id, test_round_id, 90.0, date_range)
# print(f"第三次调用 (分数: 90.0): {result3}")
#
# # 检查最低分记录
# record = new_session.query(HumanStatisticsPerson).filter(
#     HumanStatisticsPerson.user_id == test_user_id,
#     HumanStatisticsPerson.task_id == test_task_id,
#     HumanStatisticsPerson.round_id == test_round_id,
#     HumanStatisticsPerson.statistics_type == 3
# ).first()
# if record:
#     print(f"数据库中的最低分记录: {record.statistics_result_1}")

# # 测试 calculate_arbitration_count 函数
# print("\n测试 calculate_arbitration_count 函数...")
# result1 = calculate_arbitration_count(new_session, test_user_id, test_task_id, test_round_id, 0, date_range)
# print(f"仲裁成功: {result1}")
#
# result2 = calculate_arbitration_count(new_session, test_user_id, test_task_id, test_round_id, 1,date_range)
# print(f"仲裁失败: {result2}")
#
# # 检查仲裁记录
# total_record = new_session.query(HumanStatisticsPerson).filter(
#     HumanStatisticsPerson.user_id == test_user_id,
#     HumanStatisticsPerson.task_id == test_task_id,
#     HumanStatisticsPerson.round_id == test_round_id,
#     HumanStatisticsPerson.statistics_type == 7
# ).first()
#
# failed_record = new_session.query(HumanStatisticsPerson).filter(
#     HumanStatisticsPerson.user_id == test_user_id,
#     HumanStatisticsPerson.task_id == test_task_id,
#     HumanStatisticsPerson.round_id == test_round_id,
#     HumanStatisticsPerson.statistics_type == 8
# ).first()
#
# if total_record and failed_record:
#     print(f"数据库中的仲裁总量记录: {total_record.statistics_result_1}")
#     print(f"数据库中的仲裁失败记录: {failed_record.statistics_result_1}")

# # 测试 calculate_workload 函数
# print("\n测试 calculate_workload 函数...")
# result1 = calculate_workload(new_session, test_user_id, test_task_id, test_round_id, "2025-08-08 18:00:00", date_range)
# print(f"8号18点统计: {result1}")
#
# result2 = calculate_workload(new_session, test_user_id, test_task_id, test_round_id , date_range)
# print(f"8号18点再次统计: {result2}")
#
# result3 = calculate_workload(new_session, test_user_id, test_task_id, test_round_id, "2025-08-07 10:30:00", date_range)
# print(f"7号10点统计: {result3}")
# #
# # 检查工作量记录
# from datetime import date
# print("\n检查8号的工作量记录:")
# record_8 = new_session.query(HumanStatisticsPerson).filter(
#     HumanStatisticsPerson.user_id == test_user_id,
#     HumanStatisticsPerson.task_id == test_task_id,
#     HumanStatisticsPerson.round_id == test_round_id,
#     HumanStatisticsPerson.statistics_type == 11,
#     HumanStatisticsPerson.date == date(2025, 8, 8)
# ).first()
# if record_8:
#     print(f"8号工作量记录: {record_8.statistics_result_1}")
#     print(f"8号详细数据: {record_8.statistics_result_2}")
# else:
#     print("8号没有找到工作量记录")
#
# print("\n检查7号的工作量记录:")
# record_7 = new_session.query(HumanStatisticsPerson).filter(
#     HumanStatisticsPerson.user_id == test_user_id,
#     HumanStatisticsPerson.task_id == test_task_id,
#     HumanStatisticsPerson.round_id == test_round_id,
#     HumanStatisticsPerson.statistics_type == 11,
#     HumanStatisticsPerson.date == date(2025, 8, 7)
# ).first()
# if record_7:
#     print(f"7号工作量记录: {record_7.statistics_result_1}")
#     print(f"7号详细数据: {record_7.statistics_result_2}")
# else:
#     print("7号没有找到工作量记录")

# 测试 calculate_score_distribution 函数
print("\n测试 calculate_score_distribution 函数...")
result1 = calculate_score_distribution(new_session, test_user_id, test_task_id, test_round_id, 5.0, 5, date_range)
print(f"第一次调用 (分数: 5.0): {result1}")

result2 = calculate_score_distribution(new_session, test_user_id, test_task_id, test_round_id, 1.0, 5, date_range)
print(f"第二次调用 (分数: 1.0): {result2}")

# 检查分数分布记录
record = new_session.query(HumanStatisticsPerson).filter(
    HumanStatisticsPerson.user_id == test_user_id,
    HumanStatisticsPerson.task_id == test_task_id,
    HumanStatisticsPerson.round_id == test_round_id,
    HumanStatisticsPerson.statistics_type == 13
).first()
if record:
    print(f"数据库中的分数分布记录: {record.statistics_result_1}")

print("\n" + "=" * 80)
print("所有测试完成!")

# # 测试 record_spy_paper_score 函数
# print("\n测试 record_spy_paper_score 函数...")
#
# # 第一次调用 - 没有上次评分
# print("\n第一次调用 - 没有上次评分:")
# result1 = record_spy_paper_score(
#     new_session=new_session,
#     user_id=test_user_id,
#     task_id=test_task_id,
#     round_id=test_round_id,
#     current_score=85.5
# )
# print(f"第一次调用结果: {result1}")
#
# # 第二次调用 - 有上次评分
# print("\n第二次调用 - 有上次评分:")
# result2 = record_spy_paper_score(
#     new_session=new_session,
#     user_id=test_user_id,
#     task_id=test_task_id,
#     round_id=test_round_id,
#     current_score=90.0,
#     last_score=85.5
# )
# print(f"第二次调用结果: {result2}")
#
# # 第三次调用 - 评分下降
# print("\n第三次调用 - 评分下降:")
# result3 = record_spy_paper_score(
#     new_session=new_session,
#     user_id=test_user_id,
#     task_id=test_task_id,
#     round_id=test_round_id,
#     current_score=88.0,
#     last_score=90.0
# )
# print(f"第三次调用结果: {result3}")
#
# # 第四次调用 - 评分不变
# print("\n第四次调用 - 评分不变:")
# result4 = record_spy_paper_score(
#     new_session=new_session,
#     user_id=test_user_id,
#     task_id=test_task_id,
#     round_id=test_round_id,
#     current_score=88.0,
#     last_score=88.0
# )
# print(f"第四次调用结果: {result4}")
#
# # 检查间谍卷评分记录
# print("\n检查数据库中的间谍卷评分记录:")
# spy_record = new_session.query(HumanStatisticsPerson).filter(
#     HumanStatisticsPerson.user_id == test_user_id,
#     HumanStatisticsPerson.task_id == test_task_id,
#     HumanStatisticsPerson.round_id == test_round_id,
#     HumanStatisticsPerson.statistics_type == 12  # 12表示间谍卷评分
# ).first()

# result4 = calculate_average_marking_speed(
#     new_session=new_session,
#     user_id=test_user_id,
#     task_id=test_task_id,
#     round_id=test_round_id,
#     seconds_per_paper=50
# )
# print(f"第四次调用结果: {result4}")




# # 关闭数据库会话
# new_session.close()
