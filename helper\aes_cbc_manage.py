import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes


class MyAesCbc:
    def __init__(self, key: str):
        # 生成随机的初始化向量 (IV)
        self.iv = get_random_bytes(AES.block_size)
        # 密钥必须为8或8的倍数位
        self.key = key.encode('utf-8')
        self.mode = AES.MODE_CBC

    def encrypt(self, plain_text: str) -> bytes:
        cryptor = AES.new(self.key, self.mode, self.iv)
        # # 对明文进行填充并加密
        encrypted_text = cryptor.encrypt(pad(plain_text.encode('utf-8'), AES.block_size))
        return self.iv + encrypted_text

    def decrypt(self, iv_encrypted_text: bytes) -> str:
        # 从密文中提取 IV
        iv = iv_encrypted_text[:AES.block_size]
        # 提取实际的密文
        encrypted_text = iv_encrypted_text[AES.block_size:]
        cryptor = AES.new(self.key, self.mode, iv)
        # 解密并去除填充
        plain_text = unpad(cryptor.decrypt(encrypted_text), AES.block_size)
        return plain_text.decode('utf-8')


if __name__ == '__main__':
    # 加密
    key = "CF164ABA152747BD"
    e = MyAesCbc(key=key).encrypt("hello AES")
    print('e', e)
    a = base64.b64encode(e)
    print('a', a)
    b = str(a, encoding="utf-8")
    print('b', b)
    # 解密
    c = base64.b64decode(b)
    print('c', c)
    d = MyAesCbc(key=key).decrypt(c)
    print('d', d)
