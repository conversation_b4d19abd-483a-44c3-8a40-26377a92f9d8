import threading
import multiprocessing
import webbrowser
import time
import sys
import os

import uvicorn

from apps import create_app
from apps.grade_manage.services import calculate_stu_subject_grade, update_human_repeat_task_pass_count
from apps.human_official_mark.mark_redis_services import start_human_work_flow_schedule, start_human_assign_schedule
from factory_apps.schduler.scheduler_tasks import launch_human_scheduler
from factory_apps.redis_db.redis_service_manager import ensure_redis_running, stop_redis
from settings import configs
from settings.logger import logger

app = create_app()


def delay_open_browser(delay=None):
    if not delay:
        delay = configs.DELAY_SECOND
    time.sleep(delay)
    ip = configs.BACKEND_IP
    url = f"http://{ip}:{configs.PORT}/index.html"
    webbrowser.open(url)


def startup_redis_service():
    """启动Redis服务"""
    logger.info("正在启动Redis服务...")
    success, message = ensure_redis_running()
    if success:
        logger.info(f"Redis服务启动成功: {message}")
        return True
    else:
        logger.error(f"Redis服务启动失败: {message}")
        logger.error("程序将退出，请检查Redis配置或手动启动Redis服务")
        return False


def shutdown_handler():
    """程序关闭处理器"""
    logger.info("正在关闭Redis服务...")
    stop_redis()
    logger.info("程序已退出")


if __name__ == "__main__":
    try:
        if "_MEI" in configs.ROOT_PATH:
            # 防止打包后运行时无限调用子程序
            multiprocessing.freeze_support()

        # 首先启动Redis服务
        if not startup_redis_service():
            logger.error("Redis服务启动失败，程序退出")
            sys.exit(1)

        # 注册程序退出处理器
        import atexit
        atexit.register(shutdown_handler)

        if "_MEI" in configs.ROOT_PATH:
            # 开启线程自动打开浏览器
            threading.Thread(target=delay_open_browser).start()

        # 自启运行任务
        # threading.Thread(target=auto_execute_task).start()
        threading.Thread(target=launch_human_scheduler, args=(
            start_human_work_flow_schedule, configs.HUMAN_SCHEDULE_INTERVAL), daemon=True).start()
        threading.Thread(target=launch_human_scheduler, args=(
            start_human_assign_schedule, configs.HUMAN_SCHEDULE_INTERVAL), daemon=True).start()
        threading.Thread(target=launch_human_scheduler, args=(
            calculate_stu_subject_grade, configs.Calculate_Score_Interval), daemon=True).start()
        threading.Thread(target=launch_human_scheduler, args=(
            update_human_repeat_task_pass_count, configs.calculate_stu_subject_grade_interval), daemon=True).start()

        logger.info("正在启动主程序服务...")
        # 启动后端服务
        uvicorn.run(app="main:app", host=configs.HOST,
                    port=configs.PORT, workers=configs.WORKERS, reload=False)

    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在关闭程序...")
        shutdown_handler()
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序启动失败: {e}")
        shutdown_handler()
        sys.exit(1)
