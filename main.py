import threading
import multiprocessing
import webbrowser
import time

import uvicorn

from apps import create_app
from apps.grade_manage.services import calculate_stu_subject_grade, update_human_repeat_task_pass_count
from apps.human_official_mark.mark_redis_services import start_human_work_flow_schedule, start_human_assign_schedule
from factory_apps.schduler.scheduler_tasks import launch_human_scheduler
from settings import configs

app = create_app()


def delay_open_browser(delay=None):
    if not delay: 
        delay = configs.DELAY_SECOND
    time.sleep(delay)
    ip = configs.BACKEND_IP
    url = f"http://{ip}:{configs.PORT}/index.html"
    webbrowser.open(url)


if __name__ == "__main__":
    if "_MEI" in configs.ROOT_PATH:
        # 防止打包后运行时无限调用子程序
        multiprocessing.freeze_support()

        # 开启线程自动打开浏览器
        threading.Thread(target=delay_open_browser).start()

    # 自启运行任务
    # threading.Thread(target=auto_execute_task).start()
    threading.Thread(target=launch_human_scheduler, args=(start_human_work_flow_schedule, configs.HUMAN_SCHEDULE_INTERVAL), daemon=True).start()
    threading.Thread(target=launch_human_scheduler, args=(start_human_assign_schedule, configs.HUMAN_SCHEDULE_INTERVAL), daemon=True).start()
    threading.Thread(target=launch_human_scheduler, args=(calculate_stu_subject_grade, configs.Calculate_Score_Interval), daemon=True).start()
    threading.Thread(target=launch_human_scheduler, args=(update_human_repeat_task_pass_count, configs.calculate_stu_subject_grade_interval), daemon=True).start()
    # 启动后端服务
    uvicorn.run(app="main:app", host=configs.HOST, port=configs.PORT, workers=configs.WORKERS, reload=False)
