import datetime
from sqlalchemy import Column, String, Integer, Float, JSON, Text, DateTime, DECIMAL, PrimaryKeyConstraint
from sqlalchemy.orm import declarative_base

# 声明基类
Base = declarative_base()


class DateTimeBaseMixin:
    __abstract__ = True
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")


class HumanRepeatTaskRound(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_repeat_task_round"
    __table_args__ = {"comment": "复评任务轮次表"}
    repeat_task_round_id = Column(String(50), primary_key=True, comment="复评任务轮次id")
    task_name = Column(String(100), nullable=False, comment="人工阅卷任务名称")
    round_count = Column(Integer, comment="轮次")
    launch_time = Column(DateTime(timezone=True), default=None, comment="任务轮次发起时间")
    end_time = Column(DateTime(timezone=True), default=None, comment="任务轮次结束时间")
    task_type = Column(Integer, comment="任务类型，1 表示正评任务，2 表示试评任务，3 表示复评任务;4 技术验收；5 管理验收")
    # project_id = Column(String(50), comment="项目id")
    # subject_id = Column(String(50), comment="科目id")
    # exam_session = Column(Integer, comment="场次")
    # paper_id = Column(String(30), comment="试卷id")
    # ques_type_code = Column(String(1), comment="阅卷题型简码")
    # business_type_name = Column(String(30), comment="业务题型名称")
    # ques_order = Column(String(12), comment="试题序号")
    # ques_id = Column(String(50), nullable=False, comment="试题id")
    # ques_code = Column(String(30), nullable=False, comment="试题编号")
    # business_id = Column(String(50), comment="业务题型id")
    score_type = Column(Integer, comment="评分使用轮次：0 单轮次；1 所有轮次")
    select_round_count = Column(Integer, comment="选取轮次")
    select_score_type = Column(Integer, comment="取分方式：0 最高分；1 最低分")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class HumanRepeatTask(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_repeat_task"
    __table_args__ = {"comment": "复评任务表"}
    repeat_task_id = Column(String(50), primary_key=True, comment="复评任务id")
    repeat_task_round_id = Column(String(50), nullable=False, comment="复评任务轮次id")
    project_id = Column(String(50), comment="项目id")
    subject_id = Column(String(50), comment="科目id")
    round_count = Column(Integer, comment="轮次")
    task_type = Column(Integer, comment="任务类型，1 表示正评任务，2 表示试评任务，3 表示复评任务;4 技术验收；5 管理验收")
    # exam_session = Column(Integer, comment="场次")
    score_threshold = Column(DECIMAL(10, 2), comment="分差阈值")
    exam_count = Column(Integer, comment="实际考试数量")
    repeat_task_count = Column(Integer, comment="复评量(已复评量)")
    pass_count = Column(Integer, comment="通过量")
    bias_count = Column(Integer, comment="阅卷偏差允许份数")
    example_count = Column(Integer, comment="抽取数量")
    task_state = Column(Integer, default=1, comment="任务状态，1 为未发起，2 为正在进行中，3 为已完成，4 为已暂停，5 为已结束;验收：1 未抽取；2 待验收；3 验收中；4 已完成")
    verify_result = Column(Integer, comment="验收结构：0 验收通过；1验收不通过")
    verify_time = Column(DateTime(timezone=True), comment="验收时间")
    c_user_id = Column(String(50), comment="创建人id")
    repeat_user_id_list = Column(JSON, default=[], comment="复评人id列表")
    u_user_id = Column(String(50), comment="更新人id")


class HumanRepeatRoundDetail(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_repeat_round_detail"
    __table_args__ = {"comment": "复评轮次详情表"}
    repeat_round_detail_id = Column(String(50), primary_key=True, comment="复评轮次详情表id")
    repeat_task_id = Column(String(50), comment="复评任务id")
    stu_secret_num = Column(String(30), comment="考生密号")
    stu_state = Column(Integer, default=1, comment="任务状态，1 未分配，2 已分配，3 为已评分")
    repeat_user_id = Column(String(50), comment="复评员id")
    last_score = Column(DECIMAL(10, 2), comment="整卷评分(正评、复评)")  # 两位小数
    stu_score = Column(DECIMAL(10, 2), comment="整卷评分(复评、验收)")  # 两位小数
    c_user_id = Column(String(50), comment="创建人id")
    u_user_id = Column(String(50), comment="更新人id")
    
    
class HumanRepeatAnswerScore(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_repeat_answer_socre"
    repeat_answer_socre_id = Column(String(50), primary_key=True, comment="每人作答信息分配id")
    repeat_round_detail_id = Column(String(50), comment="复评轮次详情表id")
    # round_id = Column(String(50), comment="轮次id")
    # user_id = Column(String(50), comment="评卷员id")
    # group_id = Column(String(50), comment="阅卷小组id")
    ques_code = Column(String(50), comment="试题编号")
    mark_score = Column(DECIMAL(10, 2), comment="试题评分分数")
    # stu_secret_num = Column(String(30), index=True, comment="考生密号")
    answer_id = Column(String(50), comment="作答id")
    # is_answer_marked = Column(Integer, default=0, comment="该作答是否已评，0 未评，1 已评")
    mark_point_score_list = Column(JSON, comment="评分点分数列表")
    cost_time = Column(Integer, comment="评分耗时（秒）")
    mark_remark = Column(JSON, comment="前端标记数据")
    # small_score = Column(JSON, comment="小题分数")
    mark_time = Column(DateTime(timezone=True), comment="评分时间")

    __table_args__ = ({"comment": "复评每题得分"})   