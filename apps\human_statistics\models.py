from sqlalchemy import Column, String, Integer, Float, JSON, Text, DateTime, DECIMAL, PrimaryKeyConstraint, Boolean
from sqlalchemy.orm import declarative_base

from apps.models.models import DateTimeBaseMixin

# 声明基类
Base = declarative_base()


class HumanStatisticsPerson(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_statistics_person"
    __table_args__ = {"comment": "统计-个人"}
    statistics_person_id = Column(String(50), primary_key=True, comment="统计-个人id")
    project_id = Column(String(50), comment="资格id")
    subject_id = Column(String(50), comment="科目id")
    ques_group_id = Column(String(50), comment="题组id")
    group_id = Column(String(50), comment="小组id")
    user_id = Column(String(50), comment="评卷员id")
    task_id = Column(String(50), comment="人工阅卷任务id")
    round_count = Column(Integer, comment="轮次")
    task_type = Column(Integer, comment="任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务")
    date = Column(DateTime, comment="统计日期")
    statistics_type = Column(Integer, comment="统计类型:1 平均分，2 最高分，3 最低分，4 已阅量，5 有效评卷量(未进仲裁)，6 仲裁成功量，7 仲裁量，8 仲裁失败量，9 仲裁率"
                                              " 10 工作时间，11 工作量（每小时统计一次），12 间谍卷评分，13 分数分布，14 累计平均分，15 最长速度，16 最短速度")
    statistics_result_1 = Column(DECIMAL(11, 2), comment="统计结果")
    statistics_result_2 = Column(JSON, comment="分数分布、间谍卷分数、工作量、累计平均分")


class HumanStatisticsSmallGroup(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_statistics_small_group"
    __table_args__ = {"comment": "统计-小组"}
    statistics_group_id = Column(String(50), primary_key=True, comment="统计-小组id")
    project_id = Column(String(50), comment="资格id")
    subject_id = Column(String(50), comment="科目id")
    ques_group_id = Column(String(50), comment="题组id")
    group_id = Column(String(50), comment="小组id")
    task_id = Column(String(50), comment="人工阅卷任务id")
    round_count = Column(Integer, comment="轮次")
    task_type = Column(Integer, comment="任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务")
    date = Column(DateTime, comment="统计日期")
    statistics_type = Column(Integer, comment="统计类型:1 平均分，2 最高分，3 最低分，4 已阅量，5 有效评卷量(未进仲裁)，6 需仲裁数量，7 仲裁量，8 仲裁失败量，9 仲裁率，10 工作时间，11 仲裁成功量"
                                              "13 分数分布，14 累计平均分，15 最长速度，16 最短速度")
    statistics_result_1 = Column(DECIMAL(11, 2), comment="统计结果")
    statistics_result_2 = Column(JSON, comment="分数分布、累计平均分")



class HumanStatisticsQuestGroup(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_statistics_quest_group"
    __table_args__ = {"comment": "统计-题组"}
    statistics_ques_group_id = Column(String(50), primary_key=True, comment="统计-题组id")
    project_id = Column(String(50), comment="资格id")
    subject_id = Column(String(50), comment="科目id")
    ques_group_id = Column(String(50), comment="题组id")
    task_id = Column(String(50), comment="人工阅卷任务id")
    round_count = Column(Integer, comment="轮次")
    date = Column(DateTime, comment="统计日期")
    statistics_type = Column(Integer, comment="统计类型")
    statistics_result_1 = Column(DECIMAL(11, 2), comment="统计结果")
    statistics_result_2 = Column(JSON, comment="分数分布、累计平均分")


class HumanStatisticsSubject(Base, DateTimeBaseMixin):
    __tablename__ = "t_human_statistics_subject"
    __table_args__ = {"comment": "统计-科目"}
    statistics_subject_id = Column(String(50), primary_key=True, comment="统计-汇总id")
    project_id = Column(String(50), comment="资格id")
    subject_id = Column(String(50), comment="科目id")
    task_id = Column(String(50), comment="人工阅卷任务id")
    round_count = Column(Integer, comment="轮次")
    date = Column(DateTime, comment="统计日期")
    statistics_type = Column(Integer, comment="统计类型")
    statistics_result_1 = Column(DECIMAL(11, 2), comment="统计结果")
    statistics_result_2 = Column(JSON, comment="分数分布、累计平均分")
