import logging
from apps.human_prepare.models import HumanPaperSample


def get_selected_data_flag(new_session, paper_sample_id):
    """
    每次评分结束修改抽样任务
    """
    try:
        sample = new_session.query(HumanPaperSample).filter_by(id=paper_sample_id).first()
        if not sample:
            logging.warning(f"Paper sample {paper_sample_id} not found")
            return False

        sample.score_count += 1
        if sample.score_count == sample.sample_count:
            sample.sample_state = 2

        new_session.commit()
        return True

    except Exception as e:
        new_session.rollback()
        logging.error(f"Error updating paper sample {paper_sample_id}: {str(e)}")
        return False


def split_into_n_parts(m, n):
    """
    把列表 lst 平均分成 n 份
    """
    # m = len(lst)
    q, r = divmod(m, n)  # 商和余数
    parts = []
    for i in range(n):
        size = q + (0 if i <= r else 1)  # 前 r 份少 1 个
        parts.append(size)
    return parts


# # 示例
# data = list(range(1, 11))  # 10个数
# res = split_into_n_parts(data, 3)
# print(res)
