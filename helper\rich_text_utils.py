import os.path
import warnings
from bs4 import BeautifulSoup, MarkupResemblesLocatorWarning

from settings import configs
from utils.utils import create_file_base64

# 取消 BeautifulSoup 的 MarkupResemblesLocatorWarning 警告
warnings.filterwarnings("ignore", category=MarkupResemblesLocatorWarning)


def parse_html_entities(html):
    """
    解析 html 的文本和 图片的 src 路径
    """
    soup = BeautifulSoup(html, "html.parser")
    text = soup.text
    if text:
        text = text.strip("\n").strip(" ")
    img_tags = soup.find_all("img")
    img_base64_src_list = []
    for img in img_tags:
        src = img.get("src")
        if src and ("jpeg" in src or "jpg" in src or "png" in src or "webp" in src or "gif" in src):
            img_base64_src_list.append(src)
    return text, img_base64_src_list


def parse_images_id_base64_dict(html):
    """
    解析 html 的图片和图片id形成字典，图片id 为键，base64 为值
    返回格式：
    {
        "image_id": {
            "base64": "xxx",
            "position": 1  # 图片在HTML中的出现顺序
        }
    }
    """
    soup = BeautifulSoup(html, "html.parser")
    img_tags = soup.find_all("img")
    img_base64_dict = {}
    for index, img in enumerate(img_tags):
        src = img.get("src")
        if src and (".jpeg" in src or ".jpg" in src or ".png" in src or ".webp" in src or ".gif" in src):
            img_path = os.path.join(configs.PROJECT_PATH, src)
            # 转化为 base64
            img_id = os.path.basename(src)
            img_base64_dict[img_id] = create_file_base64(img_path)
        elif "data:image" in src:
            img_base64_dict[f"{index}"] = src
    return img_base64_dict
