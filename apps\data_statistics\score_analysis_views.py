from typing import List, Any, Optional
from decimal import Decimal

from fastapi import APIRouter, Depends
from apps.data_statistics.schemas import ScoreAnalysisReq, QuestionScoreReq, MarkProgressReq, QualityAnalysisReq, MarkProgressReq
from sqlalchemy import func, and_, case
from sqlalchemy.orm import Session
from settings import logger
from apps.data_statistics.services import get_curr_user_task_list
from apps.models.models import StuTotalGrade, StuTotalGradeDetail, ManualReadTask, ManualReadTaskGroup, ManualGroupUser, \
    ManualDistributeAnswer, ManualMark
from apps.base.schemas import BaseResponse
from apps.users.services import get_current_user
from factory_apps.mysql_db.databases import session_depend
from utils.utils import round_half_up

score_analysis_router = APIRouter()


@score_analysis_router.post(path="/question_average_score", response_model=BaseResponse, summary="评卷员监控：各题平均分、最高分、最低分统计")
async def get_question_average_score(
        request: QuestionScoreReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 获取评卷员监控：各题平均分、最高分、最低分统计")
    m_read_task_id = request.m_read_task_id
    grade_type = request.grade_type
    ques_id = request.ques_id

    # 验证用户权限
    curr_user_id = user["user_id"]
    task_id_list = get_curr_user_task_list(new_session, curr_user_id, "3", only_id=False, show_not_launch=False)

    # 基础查询
    query_base = new_session.query(
        StuTotalGradeDetail.ques_id,
        StuTotalGradeDetail.stu_score,
        func.count(StuTotalGradeDetail.detail_id).label('answer_count'),
        func.sun(StuTotalGradeDetail.stu_score).label('total_score'),
        func.max(StuTotalGradeDetail.stu_score).label('max_score'),
        func.min(StuTotalGradeDetail.stu_score).label('min_score')
    ).filter(
        StuTotalGradeDetail.grade_type == grade_type        #筛选，后面根据调用的表格，选择参数进行筛选，不可以为空，但是可以省略
    )

    # 获取任务相关的试卷ID
    task_info = new_session.query(ManualReadTask.paper_id).filter(
        ManualReadTask.m_read_task_id == m_read_task_id
    ).first()

    if not task_info:
        return BaseResponse(code=404, msg="未找到对应的阅卷任务", data=[])

    paper_id = task_info.paper_id
    query_base = query_base.filter(StuTotalGradeDetail.paper_id == paper_id)

    # 分组统计
    results = query_base.group_by(
        StuTotalGradeDetail.ques_id
    ).all()

    question_scores = []
    for ques_id, total_score, answer_count, max_score, min_score in results:
        if answer_count > 0:
            avg_score = round_half_up(float(Decimal(str(total_score)) / answer_count), 2)
            question_scores.append({
                "ques_id": ques_id,
                "average_score": avg_score,
                "total_students": answer_count,
                "total_score": float(total_score),
                "max_score": float(max_score) if max_score else 0,
                "min_score": float(min_score) if min_score else 0
            })

    data = {
        "average_score": avg_score,
        "max_score": max_score,
        "min_score": min_score,
    }
    logger.info("获取评卷员监控：各题平均分、最高分、最低分统计成功")
    return BaseResponse(code=200, msg="success", data=data)


@score_analysis_router.post(path="/paper_overall_average_score", response_model=BaseResponse,
                            summary="试卷整体平均分统计")
async def get_paper_overall_average_score(
        request: ScoreAnalysisReq,
        user: dict = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 获取试卷整体平均分统计")
    m_read_task_id = request.m_read_task_id
    grade_type = request.grade_type

    # 验证用户权限
    user_task_ids = get_curr_user_task_list(new_session, user['user_id'])
    if m_read_task_id not in user_task_ids:
        return BaseResponse(code=403, message="无权限访问此任务数据")

        # 获取任务相关的试卷ID
        task_info = new_session.query(ManualReadTask.paper_id).filter(
            ManualReadTask.m_read_task_id == m_read_task_id
        ).first()

        if not task_info:
            return BaseResponse(code=404, message="未找到对应的阅卷任务", data={})

        paper_id = task_info.paper_id

        # 查询学生总成绩
        results = new_session.query(
            StuTotalGrade.allow_exam_num,
            StuTotalGrade.total_grade,
            StuTotalGrade.manual_total_grade
        ).filter(
            StuTotalGrade.paper_id == paper_id,
            StuTotalGrade.grade_type == grade_type
        ).all()

        if not results:
            return BaseResponse(code=200, message="暂无数据", data={
                "average_score": 0,
                "total_students": 0,
                "score_distribution": {}
            })

        # 计算平均分
        total_students = len(results)
        total_scores = 0
        score_distribution = {}

        for allow_exam_num, total_grade, manual_total_grade in results:
            if grade_type == 1:
                score = float(total_grade) if total_grade else 0
            else:
                score = float(manual_total_grade) if manual_total_grade else 0

            total_scores += score

            # 分数分布统计（按整数分数段）
            score_range = int(score)
            if score_range in score_distribution:
                score_distribution[score_range] += 1
            else:
                score_distribution[score_range] = 1

        average_score = round_half_up(total_scores / total_students, 2) if total_students > 0 else 0

        # 排序分数分布
        sorted_distribution = dict(sorted(score_distribution.items()))

        return BaseResponse(code=200, message="success", data={
            "average_score": average_score,
            "total_students": total_students,
            "total_score": round_half_up(total_scores, 2),
            "score_distribution": sorted_distribution
        })

#
# @score_analysis_router.post(path="/question_score_detail", response_model=BaseResponse, summary="题目得分详情统计")
# async def get_question_score_detail(request: QuestionScoreReq,
#                                     user: Any = Depends(get_current_user),
#                                     new_session: Session = Depends(session_depend)):
#     logger.info(f"{user['username']} 获取题目得分详情统计")
#     m_read_task_id = request.m_read_task_id
#     grade_type = request.grade_type
#     ques_id = request.ques_id
#
#     if not ques_id:
#         return BaseResponse(code=400, message="请指定题目ID", data=[])
#
#     # 验证用户权限
#     user_task_ids = get_curr_user_task_list(new_session, user['user_id'])
#     if m_read_task_id not in user_task_ids:
#         return BaseResponse(code=403, message="无权限访问此任务数据")
#
#         # 获取任务相关的试卷ID
#         task_info = new_session.query(ManualReadTask.paper_id).filter(
#             ManualReadTask.m_read_task_id == m_read_task_id
#         ).first()
#
#         if not task_info:
#             return BaseResponse(code=404, message="未找到对应的阅卷任务", data=[])
#
#         paper_id = task_info.paper_id
#
#         # 查询题目得分详情
#         results = new_session.query(
#             StuTotalGradeDetail.stu_score,
#             func.count(StuTotalGradeDetail.detail_id).label('student_count')
#         ).filter(
#             StuTotalGradeDetail.paper_id == paper_id,
#             StuTotalGradeDetail.ques_id == ques_id,
#             StuTotalGradeDetail.grade_type == grade_type
#         ).group_by(
#             StuTotalGradeDetail.stu_score
#         ).all()
#
#         score_detail = []
#         for score, count in results:
#             score_detail.append({
#                 "score": float(score),
#                 "student_count": count
#             })
#
#         # 计算平均分
#         total_students = sum(item['student_count'] for item in score_detail)
#         total_score = sum(item['score'] * item['student_count'] for item in score_detail)
#         average_score = round_half_up(total_score / total_students, 2) if total_students > 0 else 0
#
#         return BaseResponse(code=200, message="success", data={
#             "ques_id": ques_id,
#             "average_score": average_score,
#             "total_students": total_students,
#             "score_distribution": score_detail
#         })
#
#
# @score_analysis_router.post(path="/mark_progress", response_model=BaseResponse, summary="阅卷进度统计")
# async def get_mark_progress(request: MarkProgressReq,
#                             user: Any = Depends(get_current_user),
#                             new_session: Session = Depends(session_depend)):
#     logger.info(f"{user['username']} 获取阅卷进度统计")
#     m_read_task_id = request.m_read_task_id
#
#     # 验证用户权限
#     user_task_ids = get_curr_user_task_list(new_session, user['user_id'])
#     if m_read_task_id not in user_task_ids:
#         return BaseResponse(code=403, message="无权限访问此任务数据")
#
#         # 获取阅卷总量（已分配的学生作答）
#         total_count = new_session.query(ManualDistributeAnswer).filter(
#             ManualDistributeAnswer.m_read_task_id == m_read_task_id
#         ).count()
#
#         # 获取已分配卷量
#         assigned_count = new_session.query(ManualDistributeAnswer).filter(
#             ManualDistributeAnswer.m_read_task_id == m_read_task_id,
#             ManualDistributeAnswer.expert_user_id.isnot(None)
#         ).count()
#
#         # 获取已阅卷量（已评分的）
#         marked_count = new_session.query(ManualMark).join(
#             ManualDistributeAnswer, ManualMark.distri_answer_id == ManualDistributeAnswer.id
#         ).filter(
#             ManualDistributeAnswer.m_read_task_id == m_read_task_id,
#             ManualMark.expert_mark_score.isnot(None)
#         ).distinct().count()
#
#         # 待阅卷量
#         pending_count = total_count - marked_count
#
#         result = {
#             "total_count": total_count,
#             "assigned_count": assigned_count,
#             "marked_count": marked_count,
#             "pending_count": max(0, pending_count)
#         }
#
#         return BaseResponse(code=200, message="获取阅卷进度成功", data=result)
#
#
# @score_analysis_router.post(path="/quality_analysis", response_model=BaseResponse, summary="试卷质量分析")
# async def get_quality_analysis(request: QualityAnalysisReq,
#                                user: Any = Depends(get_current_user),
#                                new_session: Session = Depends(session_depend)):
#     logger.info(f"{user['username']} 获取试卷质量分析")
#     m_read_task_id = request.m_read_task_id
#     pass_score = request.pass_score
#     full_score = request.full_score
#
#     # 验证用户权限
#     user_task_ids = get_curr_user_task_list(new_session, user['user_id'])
#     if m_read_task_id not in user_task_ids:
#         return BaseResponse(code=403, message="无权限访问此任务数据")
#
#         # 获取所有学生的总分
#         student_scores = new_session.query(StuTotalGrade.total_score).filter(
#             StuTotalGrade.m_read_task_id == m_read_task_id,
#             StuTotalGrade.total_score.isnot(None)
#         ).all()
#
#         if not student_scores:
#             return BaseResponse(code=404, message="未找到学生成绩数据")
#
#         scores = [float(score[0]) for score in student_scores]
#         total_students = len(scores)
#
#         # 计算各项指标
#         # 及格率
#         pass_count = sum(1 for score in scores if score >= pass_score)
#         pass_rate = round_half_up((pass_count / total_students) * 100, 2)
#
#         # 满分率
#         full_score_count = sum(1 for score in scores if score >= full_score * 0.99)  # 允许99%以上视为满分
#         full_score_rate = round_half_up((full_score_count / total_students) * 100, 2)
#
#         # 零分率
#         zero_score_count = sum(1 for score in scores if score <= 0)
#         zero_score_rate = round_half_up((zero_score_count / total_students) * 100, 2)
#
#         # 难度系数（平均分/满分）
#         avg_score = sum(scores) / total_students
#         difficulty = round_half_up((avg_score / full_score) * 100, 2)
#
#         # 区分度（高分组与低分组的平均分差异）
#         sorted_scores = sorted(scores)
#         high_group = sorted_scores[int(total_students * 0.73):]  # 前27%
#         low_group = sorted_scores[:int(total_students * 0.27)]  # 后27%
#
#         if high_group and low_group:
#             high_avg = sum(high_group) / len(high_group)
#             low_avg = sum(low_group) / len(low_group)
#             discrimination = round_half_up(((high_avg - low_avg) / full_score) * 100, 2)
#         else:
#             discrimination = 0
#
#         # 信度（使用分半信度估算）
#         # 将学生随机分成两组，计算两组平均分的相关系数
#         import random
#         random.shuffle(scores)
#         half = total_students // 2
#         group1 = scores[:half]
#         group2 = scores[half:half * 2] if half * 2 <= total_students else scores[half:]
#
#         if len(group1) == len(group2) and len(group1) > 1:
#             mean1 = sum(group1) / len(group1)
#             mean2 = sum(group2) / len(group2)
#
#             # 计算皮尔逊相关系数
#             def pearson_correlation(x, y):
#                 n = len(x)
#                 sum_x = sum(x)
#                 sum_y = sum(y)
#                 sum_xy = sum(x[i] * y[i] for i in range(n))
#                 sum_x2 = sum(x[i] ** 2 for i in range(n))
#                 sum_y2 = sum(y[i] ** 2 for i in range(n))
#
#                 numerator = n * sum_xy - sum_x * sum_y
#                 denominator = ((n * sum_x2 - sum_x ** 2) * (n * sum_y2 - sum_y ** 2)) ** 0.5
#
#                 if denominator == 0:
#                     return 0
#                 return numerator / denominator
#
#             reliability = round_half_up(pearson_correlation(group1, group2), 3)
#             # 使用斯皮尔曼-布朗公式修正
#             reliability = round_half_up((2 * reliability) / (1 + reliability), 3)
#         else:
#             reliability = 0
#
#         result = {
#             "pass_rate": pass_rate,
#             "full_score_rate": full_score_rate,
#             "zero_score_rate": zero_score_rate,
#             "difficulty": difficulty,
#             "discrimination": discrimination,
#             "reliability": reliability,
#             "total_students": total_students,
#             "average_score": round_half_up(avg_score, 2),
#             "max_score": max(scores),
#             "min_score": min(scores)
#         }
#
#         return BaseResponse(code=200, message="获取质量分析成功", data=result)
#
#
