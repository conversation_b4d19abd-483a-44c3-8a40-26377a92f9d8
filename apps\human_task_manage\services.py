import json
import math
import traceback

from sqlalchemy import and_, select, func, text

from apps.base.human_global_cache import get_round_group_member
from apps.base.services import request_api
from apps.human_mark_group.models import HumanGroupMember, HumanMarkGroup
from apps.human_official_mark.services import get_stu_answer_info
from apps.human_task_manage.models import HumanReadTask, HumanReadTaskRound, HumanRoundDistriAnswer, HumanPersonDistriAnswer
from apps.human_try_mark.models import HumanTryMarkResult
from apps.models.models import Subject, ExamQuestion, StuAnswer
from apps.ques_manage.services import get_ques_id_list
from apps.read_paper.common_services import toggle_partition
from factory_apps import session_depend, redis_session
from settings import configs, logger


def subject_query_condition(project_id, subject_id):
    project_query = Subject.project_id == project_id if project_id else True
    subject_query = Subject.subject_id == subject_id if subject_id else True

    condition = and_(project_query, subject_query)
    return condition


def exist_task_info(new_session, ques_code_list: list):
    all_ques_code_list = []
    task_type_dict = {}
    ques_task_dict = {}
    task_info = new_session.query(HumanReadTask.task_id, HumanReadTask.ques_code, HumanReadTask.task_type).filter(HumanReadTask.ques_code.in_(ques_code_list)).all()
    if task_info:
        for task_id, ques_code, task_type in task_info:
            all_ques_code_list.append(ques_code)
            if task_type in ques_task_dict:
                ques_task_dict[task_type][ques_code] = task_id
            else:
                ques_task_dict[task_type] = {ques_code: task_id}

            if task_type in task_type_dict:
                task_type_dict[task_type].append(ques_code)
            else:
                task_type_dict[task_type] = [ques_code]
    return all_ques_code_list, task_type_dict, ques_task_dict


def filter_human_read_task(task_type, project_id, subject_id, paper_id, task_name, business_type_name, ques_order, ques_code, round_state_list, round_count):
    task_type_query = HumanReadTask.task_type == task_type if task_type else True
    project_query = HumanReadTask.project_id == project_id if project_id else True
    subject_query = HumanReadTask.subject_id == subject_id if subject_id else True
    paper_query = HumanReadTask.paper_id == paper_id if paper_id else True
    task_query = HumanReadTask.task_name == task_name if task_name else True
    ques_order_query = HumanReadTask.ques_order == ques_order if ques_order else True
    ques_code_query = HumanReadTask.ques_code == ques_code if ques_code else True
    type_name_query = HumanReadTask.business_type_name == business_type_name if business_type_name else True
    round_state_query = HumanReadTaskRound.round_state.in_(round_state_list) if round_state_list else True
    round_count_query = HumanReadTaskRound.round_count == round_count if round_count else True

    condition = and_(task_type_query, project_query, subject_query, paper_query, task_query, ques_order_query, ques_code_query, type_name_query, round_state_query, round_count_query)
    return condition


def get_human_mark_round_info(task_type, task_id_list, round_id_list):
    """
    获取人工阅卷任务列表
    """
    req_data = {
        "page_size": -1,
        "task_type": task_type,
        "task_id_list": task_id_list,
        "round_id_list": round_id_list
    }
    token = configs.GLOBAL_TOKEN
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/official_mark/get_mark_task_list"
    res, msg = request_api(url, "POST", req_data, token, "获取人工阅卷任务列表")
    return res, msg


def check_has_mark_point(new_session, round_data):
    # 包含简答题需判断试题是否添加了评分点，没有添加不允许发起任务
    ques_code_list = [i["ques_code"] for i in round_data]
    no_points_ques_code = new_session.execute(select(ExamQuestion.ques_code).where(and_(ExamQuestion.ques_code.in_(ques_code_list), ExamQuestion.ques_type_code == "E"))).scalars().all()
    no_points_ques_code = list(set(no_points_ques_code))
    if no_points_ques_code:
        return False, f"试题 {'，'.join(no_points_ques_code)} 暂无评分标准，不允许发起任务"
    return True, None


def save_round_distri_answer(new_session, task_type: int, round_data: list):
    """
    将需要评分的作答存起来
    """
    # save_round_id_list = []
    fail_task_list = []
    # 保存需要评分的 answer_id，正评保存所有，试评根据 try_mark_ques_num 随机抽
    for item in round_data:
        task_id, round_id, ques_code, task_name, try_mark_ques_num = item["task_id"], item["round_id"], item["ques_code"], item["task_name"], item["try_mark_ques_num"]

        try:
            # 创建分区
            # if round_id not in save_round_id_list:
            #     toggle_partition(["t_human_round_distri_answer", "t_human_person_distri_answer"], round_id)
            #     save_round_id_list.append(round_id)

            ques_id_list = get_ques_id_list(new_session, ques_code)

            if task_type == 1:
                # 正评保存所有
                source_query = new_session.query(StuAnswer.answer_id, StuAnswer.stu_secret_num, StuAnswer.ques_id) \
                    .filter(StuAnswer.ques_id.in_(ques_id_list)).all()

                destination_objects = []
                for answer_item in source_query:
                    answer_id, ques_id, stu_secret_num = answer_item.answer_id, answer_item.ques_id, answer_item.stu_secret_num
                    new_item = HumanRoundDistriAnswer(distri_id=configs.snow_worker.get_id(), round_id=round_id, stu_secret_num=stu_secret_num,
                                                      ques_code=ques_code, ques_id=ques_id, answer_id=answer_id, is_distri=0, quality_state=0, is_again_mark=0)
                    destination_objects.append(new_item)
            else:
                # 试评根据 try_mark_ques_num 随机抽
                new_session.execute(text("SET SESSION group_concat_max_len = 9999999;"))
                source_query = new_session.query(StuAnswer.stu_secret_num, func.group_concat(StuAnswer.ques_id), func.group_concat(StuAnswer.answer_id)) \
                    .filter(and_(StuAnswer.ques_id.in_(ques_id_list), StuAnswer.is_do == 1)).group_by(StuAnswer.stu_secret_num).order_by(func.random()).limit(try_mark_ques_num).all()

                destination_objects = []
                for stu_secret_num, ques_id_str, answer_id_str in source_query:
                    for ques_id, answer_id in zip(ques_id_str.split(","), answer_id_str.split(",")):
                        item = HumanRoundDistriAnswer(distri_id=configs.snow_worker.get_id(), round_id=round_id, stu_secret_num=stu_secret_num,
                                                      ques_code=ques_code, ques_id=ques_id, answer_id=answer_id, is_distri=0, quality_state=0, is_again_mark=0)
                        destination_objects.append(item)

            # 分批插入
            destination_length = len(destination_objects)
            batch_size = 2000
            for i in range(0, destination_length, batch_size):
                new_session.bulk_save_objects(destination_objects[i:i + batch_size])
                new_session.commit()
        except:
            fail_task_list.append(task_name)
            logger.error(traceback.format_exc())
            continue
    return fail_task_list


def divide_stu_answer_to_groups(group_id_list, stu_answer_list: list):
    """
    按小组分配考生作答
    """
    # 创建一个包含空列表的小组列表
    num_groups = len(group_id_list)
    groups_exam_num = [[] for _ in range(num_groups)]

    # 计算每组应该分配的考生数目
    for i, item in enumerate(stu_answer_list):
        # 使用余数循环分配考生
        group_index = i % num_groups
        groups_exam_num[group_index].append(item)

    groups_stu_answer_dict = {}
    for group_id, stu_answer_list_item in zip(group_id_list, groups_exam_num):
        groups_stu_answer_dict[group_id] = stu_answer_list_item

    return groups_stu_answer_dict


def get_group_reviewer_load(group_id, round_id):
    r = next(redis_session())
    reviewer_load = r.get(f"reviewer_load_{group_id}")
    if reviewer_load:
        reviewer_load = json.loads(reviewer_load)
    else:
        # 缓存获取不到就从数据库获取
        reviewer_load = {}
        new_session = next(session_depend())
        reviewer_load_info = new_session.query(HumanGroupMember.user_id, func.count(HumanPersonDistriAnswer.stu_secret_num.distinct())) \
            .join(HumanPersonDistriAnswer, HumanPersonDistriAnswer.user_id == HumanGroupMember.user_id) \
            .filter(and_(HumanPersonDistriAnswer.round_id == round_id, HumanPersonDistriAnswer.is_answer_marked == 0, HumanGroupMember.group_id == group_id)) \
            .group_by(HumanGroupMember.user_id).all()
        for user_id, stu_count in reviewer_load_info:
            reviewer_load[user_id] = stu_count

        group_member_dict = get_round_group_member(new_session, round_id)
        for member_id in group_member_dict[group_id]:
            if member_id not in reviewer_load:
                reviewer_load[member_id] = 0

        r.set(f"reviewer_load_{group_id}", json.dumps(reviewer_load))
    return reviewer_load


def set_group_reviewer_load(group_id, reviewer_load):
    redis = next(redis_session())
    redis.set(f"reviewer_load_{group_id}", reviewer_load)
    return True


def assign_stu_answer_to_reviewers(is_first: bool, round_id: str, task_type: int, group_id: str, all_reviewer_list: list, reviewer_num: int, stu_answer_list: list):
    """
        根据考生数据，将每个考生分配给 reviewer_num 个负载最小的评阅员。

        参数:
        is_first: 是否是第一批
        all_reviewer_list (list): 所有评阅员 ID 的列表。
        reviewer_num (int): 每个考生需要被多少个评阅员评分。
        stu_answer_list (list of dicts): 每个元素是一个字典，键是考生密号，值是 answer_id 列表。

        返回:
        dict: {评阅员ID: [(考生密号, answer_id列表), ...], ...}
    """

    # 初始化作答分配结果
    assign_answer = {r: [] for r in all_reviewer_list}

    if task_type == 1:
        # 正评
        if is_first:
            # 初始化负载
            reviewer_load = {r: 0 for r in all_reviewer_list}
        else:
            # 从缓存或者数据库获取数据
            reviewer_load = get_group_reviewer_load(group_id, round_id)

        # 处理每个批次的数据
        for stu_answer_item in stu_answer_list:
            for student_id, answer_ids in stu_answer_item.items():
                # 根据当前负载排序，选择负载最小的 reviewer_num 个评阅员
                sorted_reviewers = sorted(all_reviewer_list, key=lambda r: reviewer_load[r])
                selected_reviewers = sorted_reviewers[:reviewer_num]

                # 分配任务
                for reviewer in selected_reviewers:
                    # 更新负载
                    reviewer_load[reviewer] += 1
                    for answer_id in answer_ids:
                        assign_answer[reviewer].append((student_id, answer_id))
    else:
        # 试评
        for stu_answer_item in stu_answer_list:
            for student_id, answer_ids in stu_answer_item.items():
                # 分配任务
                for reviewer in all_reviewer_list:
                    for answer_id in answer_ids:
                        assign_answer[reviewer].append((student_id, answer_id))

    return assign_answer


def get_insert_reviewer_answer(new_session, is_first, round_id, task_type, group_stu_answer_dict, reviewer_num, group_reviewer_dict, ques_code):
    insert_person_answer_list = []
    # 给评阅员分配 answer_id
    stu_answer_dict = get_stu_answer_info(new_session, ques_code)
    for group_id, stu_answer_list in group_stu_answer_dict.items():
        all_reviewer_list = group_reviewer_dict[group_id]
        assign_result = assign_stu_answer_to_reviewers(is_first, round_id, task_type, group_id, all_reviewer_list, reviewer_num, stu_answer_list)
        for reviewer, answer_items in assign_result.items():
            for stu_secret_num, answer_id in answer_items:
                ques_id = stu_answer_dict[answer_id]["ques_id"]
                reviewer_answer = HumanPersonDistriAnswer(person_distri_id=configs.snow_worker.get_id(), round_id=round_id, group_id=group_id, ques_code=ques_code,
                                                          ques_id=ques_id, user_id=reviewer, stu_secret_num=stu_secret_num, answer_id=answer_id)
                insert_person_answer_list.append(reviewer_answer)
    return insert_person_answer_list


def check_round_group_member(new_session, round_data, is_check=True):
    """
    获取并检查任务轮次小组信息
    """
    # 每个轮次各个小组对应的 评阅员id 列表
    round_group_dict = {}
    for item in round_data:
        # 获取轮次信息
        round_id, task_name, process_id, group_id_list, group_name_list, reviewer_num, task_type, round_count = item["round_id"], item["task_name"], item["process_id"], item["group_id_list"], item["group_name_list"], item["reviewer_num"], item["task_type"], item["round_count"]

        condition = and_(HumanGroupMember.group_id.in_(group_id_list), HumanGroupMember.member_role == 2)

        # 获取小组评阅员（不包含小组长）
        reviewer_info = new_session.query(HumanGroupMember.group_id, func.group_concat(HumanGroupMember.user_id), HumanMarkGroup.group_name) \
            .join(HumanMarkGroup, HumanMarkGroup.group_id == HumanGroupMember.group_id) \
            .filter(condition) \
            .group_by(HumanGroupMember.group_id).all()

        if not reviewer_info:
            return False, round_group_dict, f"{'，'.join(group_name_list)} 小组未分配评阅员"

        no_pass_user_id_list = []
        if task_type == 2 and round_count != 1:
            # 试评第二轮开始需要排除合格和已转为正评的评阅员
            no_pass_user_info = new_session.query(HumanTryMarkResult.user_id).filter(and_(HumanTryMarkResult.round_id == round_id, HumanTryMarkResult.is_office_mark != 1, HumanTryMarkResult.try_mark_result != 1))
            if not no_pass_user_info:
                return False, round_group_dict, f"{task_name} 该试评任务已无未及格评阅员，无需再次发起任务"

            no_pass_user_id_list = [i.user_id for i in no_pass_user_info]

        group_reviewer_dict = {}
        for group_id, user_id_str, group_name in reviewer_info:
            if not user_id_str:
                return False, round_group_dict, f"{group_name} 小组未分配评阅员"
            reviewer_id_list = user_id_str.split(",")
            if task_type == 2 and round_count != 1:
                reviewer_id_list = [user_id for user_id in reviewer_id_list if user_id in no_pass_user_id_list]
            group_reviewer_dict[group_id] = reviewer_id_list

        if is_check:
            for i in group_reviewer_dict:
                reviewer_num = len(group_reviewer_dict[i])
                if reviewer_num < reviewer_num:
                    return False, round_group_dict, "小组评阅员小于当前评分模式所需数量"

        round_group_dict[round_id] = group_reviewer_dict

    return True, round_group_dict, None


def parse_single_round_data(item):
    return (
        item["task_id"], item["task_name"], item["project_id"], item["subject_id"], item["paper_id"], item["task_type"], item["business_type_name"],
        item["ques_id"], item["ques_code"], item["round_id"], item["round_count"], item["process_id"], item["round_state"],
        item["fetch_score_way"], item["fetch_score_scope"], item["fetch_score_option"], item["arbitrate_threshold_type"],
        item["arbitrate_threshold"], item["arbitrate_score_diff"], item["deviation_threshold_type"], item["arbitrate_deviation"],
        item["mark_score_step"], item["ques_type_score"], item["ques_type_code"], item["group_id_list"], item["reviewer_num"]
    )


def distribute_human_task_data(round_data: list, round_group_dict: dict, is_first=False, assign_group_id_list=[]):
    """
    分配任务轮次数据
    """
    new_session = next(session_depend())
    for item in round_data:
        try:
            # 获取轮次信息
            (task_id, task_name, project_id, subject_id, paper_id, task_type, business_type_name, ques_id, ques_code, round_id, round_count, process_id,
             round_state, fetch_score_way, fetch_score_scope, fetch_score_option, arbitrate_threshold_type, arbitrate_threshold, arbitrate_score_diff,
             deviation_threshold_type, arbitrate_deviation, mark_score_step, ques_type_score, ques_type_code, group_id_list, reviewer_num) = parse_single_round_data(item)

            # 小组对应的 评阅员id 列表
            group_reviewer_dict = round_group_dict[round_id]

            condition = and_(HumanRoundDistriAnswer.round_id == round_id, HumanRoundDistriAnswer.is_distri == 0)

            stmt = select(HumanRoundDistriAnswer.stu_secret_num, func.group_concat(HumanRoundDistriAnswer.answer_id)) \
                .filter(condition) \
                .group_by(HumanRoundDistriAnswer.stu_secret_num) \
                .order_by(HumanRoundDistriAnswer.stu_secret_num)

            if task_type == 1:
                # 正评
                # 获取分批分配的比例，按照比例获取考生数量
                rate = configs.GROUP_DIVIDE_RATIO
                stu_count = new_session.query(HumanRoundDistriAnswer.stu_secret_num.distinct()).filter(condition).count()

                batch_stu_count = math.ceil(stu_count * rate)
                stmt = stmt.limit(batch_stu_count)

            batch_stu_answer_info = new_session.execute(stmt).all()

            batch_stu_secret_num_list, batch_stu_answer_list = [], []
            for stu_secret_num, answer_id_str in batch_stu_answer_info:
                batch_stu_secret_num_list.append(stu_secret_num)
                batch_stu_answer_list.append({stu_secret_num: answer_id_str.split(",")})

            # 每个组每批分到的考生和作答信息
            if is_first:
                group_stu_answer_dict = divide_stu_answer_to_groups(group_id_list, batch_stu_answer_list)
            else:
                if task_type == 1 and batch_stu_answer_list:
                    logger.info(f"为 {round_id} 轮次自动分配作答数据")
                    group_stu_answer_dict = divide_stu_answer_to_groups(assign_group_id_list, batch_stu_answer_list)
                    logger.info(f"为 {round_id} 轮次自动分配 {len(batch_stu_answer_list)} 个考生的作答数据成功")
                else:
                    return True

            # 每个评阅员每批分到的考生和作答信息
            insert_person_answer_list = get_insert_reviewer_answer(new_session, is_first, round_id, task_type, group_stu_answer_dict, reviewer_num, group_reviewer_dict, ques_code)

            if insert_person_answer_list:
                new_session.add_all(insert_person_answer_list)
                new_session.query(HumanRoundDistriAnswer) \
                    .filter(and_(HumanRoundDistriAnswer.round_id == round_id, HumanRoundDistriAnswer.stu_secret_num.in_(batch_stu_secret_num_list))) \
                    .update({HumanRoundDistriAnswer.is_distri: 1})
                new_session.commit()
        except:
            new_session.rollback()
            logger.error(traceback.format_exc())
            return False
    return True


def get_round_count(new_session, round_id: str):
    r = next(redis_session())
    round_count = r.get(f"round_count_{round_id}")
    if round_count:
        return round_count
    round_count = new_session.query(HumanReadTaskRound.round_count).filter(HumanReadTaskRound.round_id == round_id).scalar()
    r.set(f"round_count_{round_id}", round_count)
    return int(round_count) if round_count else None
