from sqlalchemy import and_, func
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime
import json
import traceback

from apps.models.models import UserRole, Project, Subject, UserInfo
from settings import configs, logger

from apps.human_statistics.models import HumanStatisticsPerson

"""
每有一个批阅完成 调用calculate_all_statistics函数
每有一次间谍卷，调用record_spy_paper_score
每有一次两人批阅完成，调用calculate_effective_review_count
每有一次仲裁，调用calculate_arbitration_count
"""


def parse_date(date_str: str = None) -> datetime.date:
    """
    解析单个日期
    :param date_str: 日期字符串，格式：yyyy-mm-dd，未传入时间则选择当前系统时间
    :return: 解析后的日期对象，如果没有日期或格式错误则返回当前系统日期
    """
    if date_str is None:
        # 默认为当前系统时间
        return datetime.now().date()
    else:
        # 解析单个日期
        try:
            return datetime.strptime(date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            # 如果格式错误，使用当前系统时间
            return datetime.now().date()


def calculate_reviewed_count(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                             group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                             record_date: datetime.date = None) -> Optional[int]:
    """
    计算已阅量
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 已阅量，如果没有数据则返回None，操作成功返回True
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()

    # 使用单个日期作为查询条件
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        count = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 4
        ).filter(*date_filter).first()

        if not count or count.statistics_result_1 is None:
            # 如果没有数据，创建一条记录
            stats_person = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=4,
                statistics_result_1=1,
                date=record_date
            )
            new_session.add(stats_person)
            new_session.commit()
            return None
        else:
            count = int(count.statistics_result_1) + 1
            # 更新数据库中的已阅量
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 4
            ).filter(*date_filter).update({
                HumanStatisticsPerson.statistics_result_1: count
            })

            new_session.commit()
            return True
    except Exception as e:
        logger.info(f"计算已阅量失败：{e}")
        traceback.print_exc()
        return False


def calculate_average_score(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                            group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                            expert_mark_score: float, record_date: datetime.date = None) -> bool:
    """
    在更新平均分前，请更新 4 已阅量

    计算平均分
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param expert_mark_score: 专家评分
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 操作是否成功
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()

    # 使用单个日期作为查询条件
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        # 先读取已阅量
        reviewed_count = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 4
        ).filter(*date_filter).first()

        # 查询指定用户、任务、轮次的平均分
        average_score_record = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 1  # 1表示平均分统计类型
        ).filter(*date_filter).first()

        if not average_score_record or average_score_record.statistics_result_1 is None:
            # 如果没有数据，创建一条记录，并将当前的expert_mark_score写入（作为平均分）
            stats_person = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=1,
                statistics_result_1=expert_mark_score,
                date=record_date
            )
            new_session.add(stats_person)
        else:
            # 计算平均分
            if not reviewed_count or reviewed_count.statistics_result_1 is None:
                print("已阅量为0")
            else:
                average_score = float(average_score_record.statistics_result_1) + \
                                (expert_mark_score - float(average_score_record.statistics_result_1)) / float(
                    reviewed_count.statistics_result_1)

            # 更新数据库中的平均分
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 1
            ).filter(*date_filter).update({
                HumanStatisticsPerson.statistics_result_1: round(average_score, 2)
            })

        new_session.commit()
        return True
    except Exception as e:
        logger.info(f"计算平均分失败：{e}")
        traceback.print_exc()
        return False


def calculate_cumulative_average_score(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                                       group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                                       expert_mark_score: float, record_date: datetime.date = None) -> bool:
    """
    计算累计平均分，每5次调用更新一次数据库

    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param expert_mark_score: 专家评分
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 操作是否成功
    """
    # 使用函数属性作为计数器
    # 使用函数属性作为计数器，但基于task_id, round_count,task_type, subject_id, group_id创建唯一键
    counter_key = f"{task_id}_{round_count}_{subject_id}_{group_id}"

    if not hasattr(calculate_cumulative_average_score, "reviewed_counts"):
        calculate_cumulative_average_score.reviewed_counts = {}

    if not hasattr(calculate_cumulative_average_score, "pending_updates"):
        calculate_cumulative_average_score.pending_updates = {}

    # 初始化或增加特定参数组合的计数器
    if counter_key not in calculate_cumulative_average_score.reviewed_counts:
        calculate_cumulative_average_score.reviewed_counts[counter_key] = 0

    calculate_cumulative_average_score.reviewed_counts[counter_key] += 1
    current_reviewed_count = calculate_cumulative_average_score.reviewed_counts[counter_key]

    print(f"当前已阅量（内部计数器）[{counter_key}]: {current_reviewed_count}")

    # 处理时间参数
    if record_date is None:
        record_date = datetime.now().date()

    # 获取当前时间
    current_time = datetime.now()
    time_str = current_time.strftime("%H:%M:%S")
    date_str = record_date.strftime("%Y-%m-%d")

    try:
        # 查询指定用户、任务、轮次的累计平均分记录（统计类型 14）
        cumulative_average_record = new_session.query(
            HumanStatisticsPerson
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 14,  # 14表示累计平均分统计类型
            HumanStatisticsPerson.date == record_date  # 添加日期条件，确保唯一性
        ).first()

        # 初始化或获取待更新数据
        if counter_key not in calculate_cumulative_average_score.pending_updates:
            if not cumulative_average_record or cumulative_average_record.statistics_result_1 is None:
                # 如果没有数据，初始化为当前专家评分
                cumulative_average = expert_mark_score
                history_data = {
                    "data": [
                        {
                            "date": date_str,
                            "time": time_str,
                            "average_value": round(cumulative_average, 2)
                        }
                    ]
                }
            else:
                # 如果有数据，使用当前数据库值
                cumulative_average = float(cumulative_average_record.statistics_result_1)
                history_data = {}
                if cumulative_average_record.statistics_result_2:
                    try:
                        history_data = json.loads(cumulative_average_record.statistics_result_2)
                    except json.JSONDecodeError:
                        history_data = {"data": []}

                # 确保data列表存在
                if "data" not in history_data:
                    history_data["data"] = []
        else:
            # 使用待更新数据
            cumulative_average = calculate_cumulative_average_score.pending_updates[counter_key]["average"]
            history_data = calculate_cumulative_average_score.pending_updates[counter_key]["history"]

        # 计算新的累计平均分
        cumulative_average = cumulative_average + (expert_mark_score - cumulative_average) / current_reviewed_count

        # 更新JSON历史记录
        new_data_entry = {
            "date": date_str,
            "time": time_str,
            "average_value": round(cumulative_average, 2)
        }

        history_data["data"].append(new_data_entry)

        # 保存到待更新数据
        calculate_cumulative_average_score.pending_updates[counter_key] = {
            "average": cumulative_average,
            "history": history_data
        }

        # 每5次调用更新一次数据库
        if current_reviewed_count % 5 == 0:
            # 写入当前的累计平均分
            new_data_entry = {
                "date": date_str,
                "time": time_str,
                "average_value": round(cumulative_average, 2)
            }

            # 查询记录是否存在
            existing_record = new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 14,
                HumanStatisticsPerson.date == record_date
            ).first()

            if existing_record:
                # 如果记录存在，更新现有记录
                # 加载现有历史数据
                existing_history = {}
                if existing_record.statistics_result_2:
                    try:
                        existing_history = json.loads(existing_record.statistics_result_2)
                    except json.JSONDecodeError:
                        existing_history = {"data": []}

                # 确保data列表存在
                if "data" not in existing_history:
                    existing_history["data"] = []

                # 追加新数据
                existing_history["data"].append(new_data_entry)

                # 更新数据库
                new_session.query(
                    HumanStatisticsPerson
                ).filter(
                    HumanStatisticsPerson.project_id == project_id,
                    HumanStatisticsPerson.subject_id == subject_id,
                    HumanStatisticsPerson.ques_group_id == ques_group_id,
                    HumanStatisticsPerson.group_id == group_id,
                    HumanStatisticsPerson.user_id == user_id,
                    HumanStatisticsPerson.task_id == task_id,
                    HumanStatisticsPerson.round_count == round_count,
                    HumanStatisticsPerson.task_type == task_type,
                    HumanStatisticsPerson.statistics_type == 14,
                    HumanStatisticsPerson.date == record_date
                ).update({
                    HumanStatisticsPerson.statistics_result_1: round(cumulative_average, 2),
                    HumanStatisticsPerson.statistics_result_2: json.dumps(existing_history, ensure_ascii=False)
                })
            else:
                # 如果记录不存在，创建新记录
                new_history_data = {
                    "data": [new_data_entry]
                }

                stats_person = HumanStatisticsPerson(
                    statistics_person_id=configs.snow_worker.get_id(),
                    project_id=project_id,
                    subject_id=subject_id,
                    ques_group_id=ques_group_id,
                    group_id=group_id,
                    user_id=user_id,
                    task_id=task_id,
                    round_count=round_count,
                    task_type=task_type,
                    statistics_type=14,
                    statistics_result_1=round(cumulative_average, 2),
                    statistics_result_2=json.dumps(new_history_data, ensure_ascii=False),
                    date=record_date
                )
                new_session.add(stats_person)

            print(f"已更新数据库，累计平均分为: {round(cumulative_average, 2)}")

        new_session.commit()
        return True
    except Exception as e:
        logger.info(f"计算累计平均分失败：{e}")
        traceback.print_exc()
        return False


def calculate_max_score(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                        group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                        expert_mark_score: float, record_date: datetime.date = None) -> Optional[bool]:
    """
    计算最高分
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param expert_mark_score: 专家评分
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 最高分，操作成功返回True
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()

    # 使用单个日期作为查询条件
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        max_score = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 2
        ).filter(*date_filter).first()

        if not max_score or max_score.statistics_result_1 is None:
            # 如果没有数据，创建一条记录
            stats_person = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=2,
                statistics_result_1=expert_mark_score,
                date=record_date
            )
            new_session.add(stats_person)
            new_session.commit()
            return None
        else:
            # 对比数据库中的最大值与当前专家评分，选择较大的值
            current_max = float(max_score.statistics_result_1)
            new_max = max(current_max, expert_mark_score)

            # 更新数据库中的最大分
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 2  # 2表示最高分统计类型
            ).update({
                HumanStatisticsPerson.statistics_result_1: round(new_max, 2)
            })

            new_session.commit()
            return True
    except Exception as e:
        logger.info(f"计算最高分失败：{e}")
        traceback.print_exc()
        return False


def calculate_min_score(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                        group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                        expert_mark_score: float, record_date: datetime.date = None) -> Optional[bool]:
    """
    计算最低分
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param expert_mark_score: 专家评分
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 最低分，操作成功返回True
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()

    # 使用单个日期作为查询条件
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        min_score = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 3
        ).filter(*date_filter).first()

        if not min_score or min_score.statistics_result_1 is None:
            # 如果没有数据，创建一条记录
            stats_person = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=3,
                statistics_result_1=expert_mark_score,
                date=record_date
            )
            new_session.add(stats_person)
            new_session.commit()
            return None
        else:
            # 对比数据库中的最低分与当前专家评分，选择较小的值
            current_min = float(min_score.statistics_result_1)
            new_min = min(current_min, expert_mark_score)

            # 更新数据库中的最低分
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 3
            ).update({
                HumanStatisticsPerson.statistics_result_1: round(new_min, 2)
            })

            new_session.commit()
            return True
    except Exception as e:
        logger.info(f"计算最低分失败：{e}")
        traceback.print_exc()
        return False


def calculate_workload(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                       group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                       record_date: datetime.date = None) -> bool:
    """
    计算工作量，统一记录到小时
    
    该函数会记录用户的工作量，包括总工作量按小时分布的情况。
    每次调用都会增加总工作量计数，并更新对应小时区间的计数。
    
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 操作是否成功
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()

    # 解析传入的时间字符串
    try:
        # 尝试解析 "2023-08-08 08:30:00" 格式
        parsed_time = datetime.strptime(record_date.strip(), '%Y-%m-%d %H:%M:%S')
    except ValueError:
        try:
            # 尝试解析其他可能的格式
            parsed_time = datetime.strptime(record_date.strip(), '%Y-%m-%d %H:%M')
        except ValueError:
            # 如果解析失败，使用当前时间
            parsed_time = datetime.now()

    # 从解析的时间中提取日期和小时
    record_date = parsed_time.date()
    hour = parsed_time.hour
    hour_range = f"{hour}-{hour + 1}"

    # 使用单个日期作为查询条件
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        # 查询指定用户、任务、轮次、日期的工作量
        workload_record = new_session.query(
            HumanStatisticsPerson
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 11,  # 11表示工作量统计类型
            HumanStatisticsPerson.date == record_date
        ).filter(*date_filter).first()

        if not workload_record:
            # 如果没有数据，创建一条记录
            workload_data = {
                "time": record_date.strftime('%Y-%m-%d'),
                "hourly_distribution": {
                    hour_range: 1
                }
            }

            stats_person = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=11,
                statistics_result_1=1,  # 总工作量
                statistics_result_2=json.dumps(workload_data, ensure_ascii=False),
                date=record_date
            )
            new_session.add(stats_person)
        else:
            # 如果已有记录，更新工作量数据
            workload_data = {}
            if workload_record.statistics_result_2:
                workload_data = json.loads(workload_record.statistics_result_2)

            # 更新总工作量 - 每次调用都加1
            current_total = workload_data.get("total_workload", 0) if "total_workload" in workload_data else (workload_record.statistics_result_1 or 0)
            workload_data["total_workload"] = current_total + 1

            # 确保时间字段存在
            workload_data["time"] = record_date.strftime('%Y-%m-%d')
            workload_data["hourly_distribution"] = workload_data.get("hourly_distribution", {})

            # 更新小时分布
            if hour_range in workload_data["hourly_distribution"]:
                workload_data["hourly_distribution"][hour_range] += 1
            else:
                workload_data["hourly_distribution"][hour_range] = 1

            # 对小时分布进行排序
            workload_data["hourly_distribution"] = dict(sorted(
                workload_data["hourly_distribution"].items(),
                key=lambda x: int(x[0].split('-')[0])
            ))

            # 使用总工作量而不是小时分布总和
            total_workload = workload_data["total_workload"]

            # 更新数据库中的工作量数据
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 11,
                HumanStatisticsPerson.date == record_date
            ).update({
                HumanStatisticsPerson.statistics_result_1: total_workload,
                HumanStatisticsPerson.statistics_result_2: json.dumps(workload_data, ensure_ascii=False)
            })

        new_session.commit()
        return True
    except Exception as e:
        logger.info(f"计算工作量失败：{e}")
        traceback.print_exc()
        return False


def calculate_score_distribution(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                                 group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                                 expert_mark_score: float, total_score: float,
                                 record_date: datetime.date = None) -> bool:
    """
    计算小题分的分数分布
    
    根据总分自动确定分数区间大小：
    - 总分 > 50：区间大小为10分
    - 总分 >= 30：区间大小为5分
    - 总分 >= 10：区间大小为3分
    - 总分 < 10：区间大小为1分
    
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param expert_mark_score: 小题分
    :param total_score: 总分
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 操作是否成功
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()

    # 使用单个日期作为查询条件
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        # 确定分数区间 - 根据总分确定区间大小
        if total_score > 50:
            interval = 10
        elif total_score >= 30:
            interval = 5
        elif total_score >= 10:
            interval = 3
        else:
            interval = 1

        # 查询现有的分数分布记录（按日期精确匹配）
        distribution_record = new_session.query(
            HumanStatisticsPerson
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 13,
            HumanStatisticsPerson.date == record_date
        ).first()

        if expert_mark_score is not None:
            # 计算区间
            if interval == 1:
                range_key = str(int(expert_mark_score))
            else:
                range_start = int((expert_mark_score - 1) // interval) * interval + 1
                range_end = range_start + interval - 1

                if range_end > total_score:
                    range_key = f"{range_start}分及以上"
                else:
                    range_key = f"{range_start}-{range_end}"

            # 如果没有现有记录，创建新记录并初始化所有区间
            if not distribution_record:
                initialized_distribution = {}

                # 生成所有可能的区间
                if interval == 1:
                    if total_score >= 1:
                        for i in range(1, int(total_score) + 1):
                            initialized_distribution[str(i)] = 0
                    else:
                        initialized_distribution["1"] = 0
                else:
                    current_start = 1
                    while True:
                        current_end = current_start + interval - 1
                        if current_end + interval > total_score:
                            key = f"{int(current_start)}分及以上"
                            initialized_distribution[key] = 0
                            break
                        key = f"{current_start}-{current_end}"
                        initialized_distribution[key] = 0
                        current_start = current_end + 1

                # 初始化当前区间计数
                initialized_distribution[range_key] = 1
                # 创建记录
                new_distribution = HumanStatisticsPerson(
                    statistics_person_id=configs.snow_worker.get_id(),
                    project_id=project_id,
                    subject_id=subject_id,
                    ques_group_id=ques_group_id,
                    group_id=group_id,
                    user_id=user_id,
                    task_id=task_id,
                    round_count=round_count,
                    task_type=task_type,
                    statistics_type=13,
                    statistics_result_1=len(initialized_distribution),  # 分布区间数量
                    statistics_result_2=json.dumps(dict(sorted(
                        initialized_distribution.items(),
                        key=lambda x: (int(x[0].split('-')[0].split('分')[0]), 0) if '分' not in x[0] else (float('inf'), x[0])
                    )), ensure_ascii=False),
                    date=record_date
                )
                new_session.add(new_distribution)
            else:
                # 如果有现有记录，加载现有分布
                try:
                    current_distribution = json.loads(distribution_record.statistics_result_2)
                except (json.JSONDecodeError, TypeError):
                    current_distribution = {}

                # 更新对应区间的计数
                current_distribution[range_key] = current_distribution.get(range_key, 0) + 1

                new_session.query(
                    HumanStatisticsPerson
                ).filter(
                    HumanStatisticsPerson.project_id == project_id,
                    HumanStatisticsPerson.subject_id == subject_id,
                    HumanStatisticsPerson.ques_group_id == ques_group_id,
                    HumanStatisticsPerson.group_id == group_id,
                    HumanStatisticsPerson.user_id == user_id,
                    HumanStatisticsPerson.task_id == task_id,
                    HumanStatisticsPerson.round_count == round_count,
                    HumanStatisticsPerson.task_type == task_type,
                    HumanStatisticsPerson.statistics_type == 13,
                    HumanStatisticsPerson.date == record_date
                ).update({
                    HumanStatisticsPerson.statistics_result_2: json.dumps(current_distribution, ensure_ascii=False)
                })

        new_session.commit()
        return True
    except Exception as e:
        logger.info(f"计算分数分布失败：{e}")
        traceback.print_exc()
        return False


def calculate_work_time(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                        group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                        seconds_per_paper: float, record_date: datetime.date = None) -> bool:
    """
    计算工作时间（累计秒数）
    
    该函数会累加用户在指定任务和轮次上的工作时间，以秒为单位。
    每次调用都会在现有时间基础上增加传入的秒数。
    
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param seconds_per_paper: 每份试卷所需的秒数
    :return: 操作是否成功
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        # 查询指定用户、任务、轮次的工作时间记录（统计类型 10）
        work_time_record = new_session.query(
            HumanStatisticsPerson
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 10  #
        ).filter(*date_filter).first()

        if not work_time_record:
            # 如果没有数据，创建一条记录，初始化为传入的秒数
            total_seconds = seconds_per_paper

            stats_person = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=10,
                statistics_result_1=round(total_seconds, 2),  # 累计秒数
                date=datetime.now().date()
            )
            new_session.add(stats_person)
        else:
            # 如果已有记录，累加工作时间
            current_seconds = float(
                work_time_record.statistics_result_1) if work_time_record.statistics_result_1 is not None else 0
            total_seconds = current_seconds + seconds_per_paper

            # 更新数据库中的工作时间
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 10
            ).filter(*date_filter).update({
                HumanStatisticsPerson.statistics_result_1: round(total_seconds, 2)
            })

        new_session.commit()
        return True
    except Exception as e:
        logger.info(f"计算工作时间失败：{e}")
        traceback.print_exc()
        return False



def calculate_max_speed(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                        group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                        seconds_per_paper: float, record_date: datetime.date = None) -> Optional[bool]:
    """
    计算最低分
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param seconds_per_paper: 每份试卷所需的秒数
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 操作成功返回True
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()

    # 使用单个日期作为查询条件
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        max_speed = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 15
        ).filter(*date_filter).first()

        if not max_speed or max_speed.statistics_result_1 is None:
            # 如果没有数据，创建一条记录
            stats_person = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=15,
                statistics_result_1=seconds_per_paper,
                date=record_date
            )
            new_session.add(stats_person)
            new_session.commit()
            return None
        else:
            current_max = float(max_speed.statistics_result_1)
            new_max = max(current_max, seconds_per_paper)

            # 更新数据库中的最低分
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 15
            ).update({
                HumanStatisticsPerson.statistics_result_1: round(new_max, 2)
            })

            new_session.commit()
            return True
    except Exception as e:
        logger.info(f"计算最长速度失败：{e}")
        traceback.print_exc()
        return False


def calculate_min_speed(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                        group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                        seconds_per_paper: float, record_date: datetime.date = None) -> Optional[bool]:
    """
    计算最低分
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param seconds_per_paper: 每份试卷所需的秒数
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 操作成功返回True
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()

    # 使用单个日期作为查询条件
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        min_speed = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 16
        ).filter(*date_filter).first()

        if not min_speed or min_speed.statistics_result_1 is None:
            # 如果没有数据，创建一条记录
            stats_person = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=16,
                statistics_result_1=seconds_per_paper,
                date=record_date
            )
            new_session.add(stats_person)
            new_session.commit()
            return None
        else:
            current_min = float(min_speed.statistics_result_1)
            new_min = min(current_min, seconds_per_paper)

            # 更新数据库中的最低分
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 16
            ).update({
                HumanStatisticsPerson.statistics_result_1: round(new_min, 2)
            })

            new_session.commit()
            return True
    except Exception as e:
        logger.info(f"计算最短速度失败：{e}")
        traceback.print_exc()
        return False



def calculate_all_statistics(params: Dict[str, Any]) -> bool:
    """
    计算所有统计数据，按照指定顺序执行
    
    该函数会按照以下顺序计算统计数据：
    1. 更新已阅量 (type=4)
    2. 更新工作时间 (type=10)，如果提供了seconds_per_paper参数
    3. 更新工作量 (type=11)，如果提供了mark_time参数
    4. 计算平均分 (type=1)，如果提供了expert_mark_score参数
    5. 计算最高分 (type=2)，如果提供了expert_mark_score参数
    6. 计算最低分 (type=3)，如果提供了expert_mark_score参数
    7. 计算累计平均分 (type=14)，如果提供了expert_mark_score参数
    8. 计算分数分布 (type=13)，如果同时提供了expert_mark_score和total_score参数
    9. 计算最长速度(type=15)，如果提供了seconds_per_paper参数
    10. 计算最短速度(type=16)，如果提供了seconds_per_paper参数
    
    :param params: 包含所有参数的字典，需要包含以下键：
                  - new_session: 数据库会话
                  - project_id: 资格ID
                  - subject_id: 科目ID
                  - ques_group_id: 题组ID
                  - group_id: 小组ID
                  - user_id: 用户ID
                  - task_id: 任务ID
                  - round_count: 轮次
                  - task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
                  - expert_mark_score: 专家评分 (可选)
                  - mark_time: 标记时间 (可选)
                  - seconds_per_paper: 每份试卷所需的秒数 (可选)
                  - total_score: 总分 (可选)
                  - record_date: 记录日期 (可选)
    :return: 操作是否成功
    """
    try:
        # 提取参数
        new_session = params.get('new_session')
        project_id = params.get('project_id')
        subject_id = params.get('subject_id')
        ques_group_id = params.get('ques_group_id')
        group_id = params.get('group_id')
        user_id = params.get('user_id')
        task_id = params.get('task_id')
        round_count = params.get('round_count')
        task_type = params.get('task_type')
        expert_mark_score = params.get('expert_mark_score')
        mark_time = params.get('mark_time')
        seconds_per_paper = params.get('seconds_per_paper')
        total_score = params.get('total_score')
        record_date = params.get('record_date')

        # 首先更新4已阅量、10工作时间、11工作量
        # 更新已阅量
        calculate_reviewed_count(new_session, project_id, subject_id, ques_group_id, group_id, user_id, task_id, round_count, task_type, record_date)

        # 更新工作时间
        if seconds_per_paper is not None:
            calculate_work_time(new_session, project_id, subject_id, ques_group_id, group_id, user_id, task_id, round_count, task_type, seconds_per_paper, record_date)
            calculate_max_speed(new_session, project_id, subject_id, ques_group_id, group_id, user_id, task_id, round_count, task_type, seconds_per_paper, record_date)
            calculate_min_speed(new_session, project_id, subject_id, ques_group_id, group_id, user_id, task_id, round_count, task_type, seconds_per_paper, record_date)

        # 更新工作量
        if mark_time is not None:
            calculate_workload(new_session, project_id, subject_id, ques_group_id, group_id, user_id, task_id, round_count, task_type, mark_time, record_date)

        # 按照type顺序更新其他统计
        if expert_mark_score is not None:
            # 平均分 (type=1)
            calculate_average_score(new_session, project_id, subject_id, ques_group_id, group_id, user_id, task_id, round_count, task_type, expert_mark_score, record_date)

            # 最高分 (type=2)
            calculate_max_score(new_session, project_id, subject_id, ques_group_id, group_id, user_id, task_id, round_count, task_type, expert_mark_score, record_date)

            # 最低分 (type=3)
            calculate_min_score(new_session, project_id, subject_id, ques_group_id, group_id, user_id, task_id, round_count, task_type, expert_mark_score, record_date)

            # 累计平均分 (type=14)
            calculate_cumulative_average_score(new_session, project_id, subject_id, ques_group_id, group_id, user_id, task_id, round_count, task_type, expert_mark_score, record_date)

            # 分数分布 (type=13)
            if total_score is not None:
                calculate_score_distribution(new_session, project_id, subject_id, ques_group_id, group_id, user_id, task_id, round_count, task_type, expert_mark_score, total_score, record_date)

        return True
    except Exception as e:
        logger.info(f"计算所有统计数据失败：{e}")
        traceback.print_exc()
        return False


def record_spy_paper_score(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                           group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                           current_score: float, last_score: float = None) -> bool:
    """
    记录间谍卷评分情况
    
    该函数会记录间谍卷的评分历史，包括每次评分、与前一次评分的差异等信息。
    每次调用都会在历史记录中添加一条新的评分记录。
    
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param current_score: 本次评分
    :param last_score: 上次评分，如果没有则为None
    :return: 操作是否成功
    """
    try:
        # 查询是否已有间谍卷评分记录
        spy_paper_record = new_session.query(
            HumanStatisticsPerson
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 12  # 12表示间谍卷评分统计类型
        ).first()

        if not spy_paper_record:
            # 如果没有数据，创建一条记录，差异设为0
            score_record = {
                "round": "第1次",
                "current_score": round(current_score, 2),
                "last_score": None,
                "difference": 0  # 第一次记录差异为0
            }

            stats_person = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=12,
                statistics_result_1=current_score,  # 最新评分
                statistics_result_2=json.dumps([score_record], ensure_ascii=False),
                date=datetime.now().date()
            )
            new_session.add(stats_person)
        else:
            # 如果已有记录，计算差异并更新评分历史
            difference = current_score - (last_score if last_score is not None else 0)

            # 更新评分历史
            score_history = []
            if spy_paper_record.statistics_result_2:
                try:
                    score_history = json.loads(spy_paper_record.statistics_result_2)
                except json.JSONDecodeError:
                    score_history = []

            # 确定是第几次评分
            record_count = len(score_history) + 1
            round_label = f"第{record_count}次"

            # 创建评分记录
            score_record = {
                "round": round_label,
                "current_score": round(current_score, 2),
                "last_score": round(last_score, 2) if last_score is not None else None,
                "difference": round(difference, 2)
            }

            # 添加新的评分记录
            score_history.append(score_record)

            # 更新数据库中的间谍卷评分记录
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 12
            ).update({
                HumanStatisticsPerson.statistics_result_1: current_score,
                HumanStatisticsPerson.statistics_result_2: json.dumps(score_history, ensure_ascii=False)
            })

        new_session.commit()
        return True
    except Exception as e:
        logger.info(f"记录间谍卷评分失败：{e}")
        traceback.print_exc()
        return False


def calculate_effective_review_count(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                                     group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                                     record_date: datetime.date = None) -> bool:
    """
    计算有效评卷量
    
    该函数会统计用户的有效评卷次数，每次调用都会增加计数。
    有效评卷量用于评估用户的工作质量和效率。
    
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 操作是否成功
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()

    # 使用单个日期作为查询条件
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        # 查询指定用户、任务、轮次的有效评卷量
        effective_count = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 5  # 5表示有效评卷量统计类型
        ).filter(*date_filter).first()

        if not effective_count or effective_count.statistics_result_1 is None:
            # 如果没有数据，创建一条记录
            stats_person = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=5,
                statistics_result_1=1,
                date=record_date
            )
            new_session.add(stats_person)
            new_session.commit()
            return None
        else:
            count = int(effective_count.statistics_result_1) + 1
            # 更新数据库中的有效评卷量
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 5
            ).filter(*date_filter).update({
                HumanStatisticsPerson.statistics_result_1: count
            })

            new_session.commit()
            return True
    except Exception as e:
        logger.info(f"计算有效评卷量失败：{e}")
        traceback.print_exc()
        return False


def calculate_arbitration_count(new_session: Session, project_id: str, subject_id: str, ques_group_id: str,
                                group_id: str, user_id: str, task_id: str, round_count: str, task_type: int,
                                arbitration_result: int, record_date: datetime.date = None) -> bool:
    """
    计算仲裁量、仲裁失败数量和仲裁率
    
    该函数会更新四个统计数据：
    1. 仲裁成功数量 (type=6)：只有当arbitration_result为0时才增加1
    2. 仲裁总量 (type=7)：每次调用都会增加1
    3. 仲裁失败数量 (type=8)：只有当arbitration_result为1时才增加1
    4. 仲裁率 (type=9)：仲裁总量与已阅量的比率
    
    :param new_session: 数据库会话
    :param project_id: 资格ID
    :param subject_id: 科目ID
    :param ques_group_id: 题组ID
    :param group_id: 小组ID
    :param user_id: 用户ID
    :param task_id: 任务ID
    :param round_count: 轮次
    :param task_type: 任务类型，1 表示正评任务，2 表示试评任务，3 表示培训任务
    :param arbitration_result: 仲裁结果，0表示仲裁成功，1表示仲裁失败（必传）
    :param record_date: 记录日期，如果未传入则使用当前系统时间
    :return: 操作是否成功
    """
    # 如果未传入日期，使用当前系统时间
    if record_date is None:
        record_date = datetime.now().date()

    # 使用单个日期作为查询条件
    date_filter = [HumanStatisticsPerson.date == record_date]

    try:
        # 查询指定用户、任务、轮次的仲裁总量
        total_arbitration = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 7
        ).filter(*date_filter).first()

        # 查询指定用户、任务、轮次的仲裁失败数量
        failed_arbitration = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 8
        ).filter(*date_filter).first()

        # 查询指定用户、任务、轮次的已阅量 (用于计算仲裁率)
        reviewed_count = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 4  # 4表示已阅量统计类型
        ).filter(*date_filter).first()

        # 更新仲裁总量
        if not total_arbitration or total_arbitration.statistics_result_1 is None:
            # 如果没有数据，创建一条记录，仲裁量初始化为1
            total_count = 1
            stats_person_total = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=7,
                statistics_result_1=total_count,
                date=datetime.now().date()
            )
            new_session.add(stats_person_total)
        else:
            # 如果已有记录，仲裁量加1
            total_count = int(total_arbitration.statistics_result_1) + 1
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 7
            ).filter(*date_filter).update({
                HumanStatisticsPerson.statistics_result_1: total_count
            })

        # 更新仲裁失败数量
        if arbitration_result == 1:
            # 只有当仲裁结果为失败时才更新失败数量
            if not failed_arbitration or failed_arbitration.statistics_result_1 is None:
                # 如果没有数据，创建一条记录，失败数量初始化为1
                failed_count = 1
                stats_person_failed = HumanStatisticsPerson(
                    statistics_person_id=configs.snow_worker.get_id(),
                    project_id=project_id,
                    subject_id=subject_id,
                    ques_group_id=ques_group_id,
                    group_id=group_id,
                    user_id=user_id,
                    task_id=task_id,
                    round_count=round_count,
                    task_type=task_type,
                    statistics_type=8,
                    statistics_result_1=failed_count,
                    date=datetime.now().date()
                )
                new_session.add(stats_person_failed)
            else:
                # 如果已有记录，失败数量加1
                failed_count = int(failed_arbitration.statistics_result_1) + 1
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 8
            ).filter(*date_filter).update({
                HumanStatisticsPerson.statistics_result_1: failed_count
            })
        else:
            # 如果仲裁结果不是失败（成功或None），保持失败数量不变
            failed_count = failed_arbitration.statistics_result_1 if failed_arbitration and failed_arbitration.statistics_result_1 is not None else 0

        # 仲裁成功数量（statistics_type=6），当 arbitration_result 为 0 时加 1
        if arbitration_result == 0:
            success_record = new_session.query(HumanStatisticsPerson.statistics_result_1).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 6
            ).filter(*date_filter).first()
            if not success_record or success_record.statistics_result_1 is None:
                success_count = 1
                stats_person_success = HumanStatisticsPerson(
                    statistics_person_id=configs.snow_worker.get_id(),
                    project_id=project_id,
                    subject_id=subject_id,
                    ques_group_id=ques_group_id,
                    group_id=group_id,
                    user_id=user_id,
                    task_id=task_id,
                    round_count=round_count,
                    task_type=task_type,
                    statistics_type=6,
                    statistics_result_1=success_count,
                    date=datetime.now().date()
                )
                new_session.add(stats_person_success)
            else:
                success_count = int(success_record.statistics_result_1) + 1
                new_session.query(HumanStatisticsPerson).filter(
                    HumanStatisticsPerson.project_id == project_id,
                    HumanStatisticsPerson.subject_id == subject_id,
                    HumanStatisticsPerson.ques_group_id == ques_group_id,
                    HumanStatisticsPerson.group_id == group_id,
                    HumanStatisticsPerson.user_id == user_id,
                    HumanStatisticsPerson.task_id == task_id,
                    HumanStatisticsPerson.round_count == round_count,
                    HumanStatisticsPerson.task_type == task_type,
                    HumanStatisticsPerson.statistics_type == 6
                ).filter(*date_filter).update({
                    HumanStatisticsPerson.statistics_result_1: success_count
                })

        # 计算仲裁率
        reviewed_count_val = reviewed_count.statistics_result_1 if reviewed_count and reviewed_count.statistics_result_1 is not None else 0
        arbitration_rate = (total_count / reviewed_count_val * 100) if reviewed_count_val > 0 else 0

        # 更新仲裁率
        # 先查询是否已有仲裁率记录
        arbitration_rate_record = new_session.query(
            HumanStatisticsPerson.statistics_result_1
        ).filter(
            HumanStatisticsPerson.project_id == project_id,
            HumanStatisticsPerson.subject_id == subject_id,
            HumanStatisticsPerson.ques_group_id == ques_group_id,
            HumanStatisticsPerson.group_id == group_id,
            HumanStatisticsPerson.user_id == user_id,
            HumanStatisticsPerson.task_id == task_id,
            HumanStatisticsPerson.round_count == round_count,
            HumanStatisticsPerson.task_type == task_type,
            HumanStatisticsPerson.statistics_type == 9
        ).filter(*date_filter).first()

        if not arbitration_rate_record:
            # 如果没有数据，创建一条记录
            stats_person_rate = HumanStatisticsPerson(
                statistics_person_id=configs.snow_worker.get_id(),
                project_id=project_id,
                subject_id=subject_id,
                ques_group_id=ques_group_id,
                group_id=group_id,
                user_id=user_id,
                task_id=task_id,
                round_count=round_count,
                task_type=task_type,
                statistics_type=9,
                statistics_result_1=round(arbitration_rate, 2),
                date=datetime.now().date()
            )
            new_session.add(stats_person_rate)
        else:
            # 如果已有记录，更新仲裁率
            new_session.query(
                HumanStatisticsPerson
            ).filter(
                HumanStatisticsPerson.project_id == project_id,
                HumanStatisticsPerson.subject_id == subject_id,
                HumanStatisticsPerson.ques_group_id == ques_group_id,
                HumanStatisticsPerson.group_id == group_id,
                HumanStatisticsPerson.user_id == user_id,
                HumanStatisticsPerson.task_id == task_id,
                HumanStatisticsPerson.round_count == round_count,
                HumanStatisticsPerson.task_type == task_type,
                HumanStatisticsPerson.statistics_type == 9
            ).filter(*date_filter).update({
                HumanStatisticsPerson.statistics_result_1: round(arbitration_rate, 2)
            })

        new_session.commit()
        return True
    except Exception as e:
        logger.info(f"计算仲裁量失败：{e}")
        traceback.print_exc()
        return False

def get_human_statistics_condition(project_ids, subject_ids):
    """
    构建人员统计数据查询条件
    """
    # 确保project_ids和subject_ids不为None
    if project_ids is None:
        project_ids = []
    if subject_ids is None:
        subject_ids = []
        
    project_query = Project.project_id.in_(project_ids) if project_ids else True
    subject_query = Subject.subject_id.in_(subject_ids) if subject_ids else True

    # 使用and_组合所有条件
    return and_(project_query, subject_query)
