from typing import Optional, List, Literal
from pydantic import BaseModel, Field
from apps.base.schemas import PaginationModel


class GetQuesListReq(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    paper_id: Optional[str] = Field(None, description="试卷id")
    ques_type_code_list: list = Field([], description="试题类型简码列表")
    bs_ques_type_name: Optional[str] = Field(None, description="业务试题类型简码")
    ques_code: Optional[str] = Field(None, description="试题编号")
    ques_name: Optional[str] = Field(None, description="试题名称")
    used_count_list: list = Field([], description="使用次数")
    has_mark_point: Optional[bool] = Field(None, description="是否有评分标准")
    business_id_list: list = Field([], description="业务题型id 列表")
    ques_desc: Optional[str] = Field(None, description="试题描述")
    task_type: Literal[None, 1, 2] = Field(None, description="任务类型，用于区分 人工阅卷分配状态 是属于什么类型的任务，1 表示正评，2 表示试评")
    distribute_state: Optional[int] = Field(None, description="人工阅卷分配状态，None 表示全部试题，1 表示未分配，2 表示已分配")


class GetQuesDetailReq(BaseModel):
    ques_code: str = Field(..., description="试题编号")
    ques_id: Optional[list[str]] = Field(None, description="试题ID列表")

class GetQuesDetailListReq(BaseModel):
    ques_code_list: list[str] = Field(..., description="试题编号列表")


class GetAdjoinQuesDetailReq(GetQuesListReq):
    curr_ques_code: str = Field(..., description="当前试题编号")
    adjoin_type: Literal[1, 2] = Field(..., description="1 表示上一题，2 表示下一题")


class SuppleImagesReq(BaseModel):
    ques_code_list: list = Field([], description="试题编号列表")
    is_auto_launch: bool = Field(False, comment="非前端参数，区分是否是系统自启任务")
    ques_desc_info: list = Field([], description="包含素材字段的试题信息列表")


class EditSmallQuesBaseReq(BaseModel):
    """小小题基础请求模型"""
    ques_id: str = Field(..., description="小小题id")
    ques_code: str = Field(..., description="试题编码")
    ques_choices: list = Field([], description="小小题选项")
    standard_answer: Optional[list] = Field(None, description="参考答案")
    standard_answer_html: Optional[list] = Field(None, description="参考答案html")
    standard_choices_code: Optional[list] = Field(None, description="参考答案代号")
    ques_type_code: str = Field(..., description="基础题型简码,A:单选,B:判断,C:多选,D:填空,E:简答,F:组合")
    standard_parse: Optional[str] = Field(None, description="参考答案解析")
    e_mark_rule: Optional[str] = Field(None, description="小小题评分规则")
    ques_score: Optional[float] = Field(None, description="小小题分数")
    ques_mark_point: Optional[list] = Field(None, description="评分要点")
    small_ques_num: Optional[str] = Field(None, description="小小题序号")
    small_ques_int: Optional[str] = Field(None, description="小小题int序号")

class EditSubQuesReq(BaseModel):
    ques_id: str = Field(..., description="试题id")
    ques_code: str = Field(..., description="试题编码")
    ques_choices: list = Field([], description="试题选项")
    weight: Optional[list] = Field([], description="权重列表")
    d_out_of_order_group: Optional[str] = Field(None, description="填空题不区分顺序分组字段")
    standard_answer: Optional[list] = Field(None, description="参考答案")
    standard_answer_html: Optional[list] = Field(None, description="参考答案html")
    standard_parse: Optional[str] = Field(None, description="参考答案解析")
    e_mark_rule: Optional[str] = Field(None, description="简答题评分规则")
    ques_type_code: str = Field(..., description="基础题型简码,A:单选,B:判断,C:多选,D:填空,E:简答,F:组合")
    ques_score: Optional[float] = Field(None, description="小题分数")
    ques_mark_point: Optional[list] = Field(None, description="评分要点")
    small_ques_num: Optional[str] = Field(None, description="小题序号")
    small_ques_int: Optional[str] = Field(None, description="小题序号")
    children: List[EditSmallQuesBaseReq] = Field([], description="小小题列表")

class EditQuesReq(BaseModel):
    ques_id: str = Field(..., description="试题id")
    ques_code: str = Field(..., description="试题编码")
    ques_choices: list = Field([], description="试题选项")
    weight: Optional[list] = Field([], description="权重列表")
    d_out_of_order_group: Optional[str] = Field(None, description="填空题不区分顺序分组字段")
    standard_answer: Optional[list] = Field(None, description="参考答案")
    standard_answer_html: Optional[list] = Field(None, description="参考答案html")
    standard_parse: Optional[str] = Field(None, description="参考答案解析")
    e_mark_rule: Optional[str] = Field(None, description="简答题评分规则")
    ques_score: Optional[float] = Field(None, description="大题分数")
    ques_mark_point: Optional[list] = Field(None, description="评分要点")
    ques_name: Optional[str] = Field(None, description="试题名称")
    children: List[EditSubQuesReq] = Field([], description="组合题的小题")

class CreateSmallQues(BaseModel):
    small_ques_num: str = Field(..., description="小小题序号")
    small_ques_int: str = Field(..., description="小小题排序序号")
    ques_choices: list = Field([], description="试题选项")
    ques_type_code: str = Field(..., description="基础题型简码,A:单选,B:判断,C:多选,D:填空,E:简答")
    ques_score: float = Field(..., description="小小题分数")
    standard_answer: Optional[list] = Field(None, description="参考答案")
    standard_answer_html: Optional[list] = Field(None, description="参考答案html")
    standard_parse: Optional[str] = Field(None, description="参考答案解析")
    e_mark_rule: Optional[str] = Field(None, description="简答题评分规则")
    ques_mark_point: Optional[list] = Field(None, description="评分要点")

class CreateSubQues(BaseModel):
    small_ques_num: str = Field(..., description="小题序号")
    ques_choices: list = Field([], description="试题选项")
    ques_type_code: str = Field(..., description="基础题型简码,A:单选,B:判断,C:多选,D:填空,E:简答,F:组合")
    ques_score: float = Field(..., description="小题分数")
    standard_answer: Optional[list] = Field(None, description="参考答案")
    standard_answer_html: Optional[list] = Field(None, description="参考答案html")
    standard_parse: Optional[str] = Field(None, description="参考答案解析")
    e_mark_rule: Optional[str] = Field(None, description="简答题评分规则")
    ques_mark_point: Optional[list] = Field(None, description="评分要点")
    children: List[CreateSmallQues] = Field([], description="小小题")

class GetCreateQuestionReq(BaseModel):
    project_id: str = Field(..., description="资格id")
    subject_id: str = Field(..., description="科目id")
    business_type_id: str = Field(..., description="业务题型id")
    ques_type_code: Optional[str] = Field(..., description="基础题型简码,A:单选,B:判断,C:多选,D:填空,E:简答,F:组合")
    ques_code: str = Field(..., description="试题编码")
    ques_score: float = Field(..., description="试题分数")
    ques_name: str = Field(..., description="试题名称")
    ques_choices: list = Field([], description="试题选项")
    standard_answer: Optional[list] = Field(None, description="参考答案")
    standard_answer_html: Optional[list] = Field(None, description="参考答案html")
    standard_parse: Optional[str] = Field(None, description="参考答案解析")
    e_mark_rule: Optional[str] = Field(None, description="简答题评分规则")
    ques_mark_point: Optional[list] = Field(None, description="评分要点")
    children: List[CreateSubQues] = Field([], description="组合题的小题")


class GenerateMPReq(BaseModel):
    subject_name: str = Field(..., description="科目名称")
    ques_id: str = Field(..., description="试题id")
    ques_code: str = Field(..., description="试题编号")
    small_ques_num: int = Field(..., description="小题编号")
    generate_num: Optional[int] = Field(None, description="生成评分标准数量")


class GetAllBusinessQuesTypeReq(BaseModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    is_remove_duplicate: bool = Field(False, description="是否去重")


class GetCommonErrorAnalysisReq(BaseModel):
    ques_id :str = Field(...,description="试题id")

class GetQualityQuesReq(BaseModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    is_quality: Literal[0, 1] = Field(1, description="是否用于质检筛选的数据，0 否，1 是")