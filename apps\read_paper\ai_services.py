import re

import requests
import copy
from typing import Optional, Union

from apps.read_paper.common_services import is_all_punctuation
from settings import logger

from helper.response_utils import ai_can_not_connect, ai_can_not_connect_code, \
    ai_not_response_code, ai_not_response, ai_score_ambiguity
from settings import configs
from utils.utils import sum_with_precision

# file_lock = threading.Lock()


def ai_fill_blank_score(ques_id: str, subject: str, ques_type: str, ques_desc: str, std_answer: list, stu_answer: list,
                        rule: int, mark_count: int, std_score: list):
    """ 按题判分 """
    # data = {
    #     "ques_id": 123456,
    #     "subject": "生物",
    #     "ques_type": "填空题",
    #     "ques_desc": "薇甘菊被称为“植物杀手”，它与红树植物属于______关系。被薇甘菊覆盖的红树植物枯萎的原因可能是______。",
    #     "std_answer": ["竞争", "光照不足，影响红树植物正常的光合作用"],
    #     "stu_answer": ["竞争", "营养不足"],
    #     "rule": 1,  # 评分规则：1 表示宽松，2 表示适中，3 表示严格
    #     "mark_count": 2,  # 评分轮次，宽松取最高分，适中取平均分，严格取最低分
    #     "std_score": [1.00, 1.00]  # 每个空对应的分数
    # }

    data = {
        "ques_id": ques_id,
        "subject": subject,
        "ques_type": ques_type,
        "ques_desc": ques_desc,
        "std_answer": std_answer,
        "stu_answer": stu_answer,
        "rule": rule,  # 评分规则：1 表示宽松，2 表示适中，3 表示严格
        "mark_count": mark_count,  # 评分轮次，宽松取最高分，适中取平均分，严格取最低分
        "std_score": std_score  # 每个空对应的分数，例如 [1.00, 1.00]
    }

    res = requests.post(configs.SPACE_MARK_URL, json=data)
    res_code = res.status_code
    if res_code != 200:
        logger.error(f"AI返回错误码{res_code}")
        return None
    result = res.json()
    # result = {
    #     "ques_id": "123456",
    #     "ai_score": [1.00, 0.00],  # 空格分数
    #     "ai_reason": "xxxx",  # 评分解析
    #     "cost_time": 5.00,  # 解析耗时，秒为单位
    #     "pending_review": 1  # 待人工判断，0,1
    # }
    logger.info(f"AI返回信息：{result}")
    return result


def is_format_fill_blank(std_answer, stu_answer, std_score):
    """
    检查填空题的数据是否已格式化
    :param std_answer: 标准答案
    :param stu_answer: 考生答案
    :param std_score: 标准分数
    :return:
    """
    # if not (isinstance(std_answer, list) and isinstance(stu_answer, list) and isinstance(std_score, list)):
    #     logger.error("标准答案、考生答案、标准分数格式必须为列表")
    #     return False
    if not (len(std_answer) == len(stu_answer) and len(std_answer) == len(std_score)):
        logger.error("标准答案、考生答案、标准分数长度必须一致")
        return False
    return True


def fill_blank_score_main(ques_id: str, subject: str, ques_desc: str, ques_type: str, std_answer: list, std_score: list,
                          stu_answer: list, rule: int, mark_count: int):
    """按题判断分数主流程"""
    stu_score = copy.deepcopy(std_score)
    ai_reason = None
    # 校验格式
    if not is_format_fill_blank(std_answer, stu_answer, std_score):
        return False, [], ai_reason
    if all(not i or i.isspace() for i in stu_answer):  # 考生答案全部为空，0 分
        logger.info("考生答案全部为空，0 分")
        stu_score = [0.00 for _ in range(len(std_answer))]
    else:
        need_ai_parse_index = []  # 需要AI判分的答案的索引列表
        # 考生所有答案和标准答案完全一致，不需要处理；不完全一致，需要处理
        if '-'.join(std_answer) != '-'.join(stu_answer):
            for answer_index, (std_answer_item, stu_answer_item) in enumerate(zip(std_answer, stu_answer)):
                # answer_index 作为答案列表和分数列表的索引
                # 答案为空或者空字符串
                if not stu_answer_item:
                    logger.info("单个答案为空或者空字符串")
                    stu_score[answer_index] = 0.00
                else:
                    # 答案和标准答案一致不需要处理，不一致发送给模型判断分数
                    if std_answer_item != stu_answer_item:
                        logger.info(
                            f"标准答案：{std_answer_item}，考生答案：{stu_answer_item}，不一致，存储该答案索引：{answer_index}")
                        need_ai_parse_index.append(answer_index)
            logger.info(f"发送给模型进行判分")
            ai_res = ai_fill_blank_score(ques_id, subject, ques_type, ques_desc, std_answer, stu_answer,
                                         rule, mark_count, std_score)
            ai_score = ai_res.get("ai_score")
            ai_reason = ai_res.get("ai_reason")
            if isinstance(ai_score, list) and len(ai_score) == len(std_score):
                for i in need_ai_parse_index:
                    stu_score[i] = ai_score[i]
            else:
                logger.error(f"AI返回分数格式错误：{ai_score}")
                return False, [], ai_reason
    logger.info(f"题目最终得分{stu_score}")
    return True, stu_score, ai_reason


def ai_subjective_single_space_score(ques_id: str, subject: str, ques_type: str, ques_desc: str, std_answer: str,
                                     stu_answer: str, rule: int, mark_count: int, std_score: float, e_mark_rule: str,
                                     mark_point: list, ques_material: Optional[str] = None):
    data = {
        "ques_id": ques_id,
        "subject": subject,
        "ques_type": ques_type,
        "ques_desc": ques_desc,
        "std_answer": std_answer,
        "stu_answer": stu_answer,
        "rule": rule,  # 评分规则：1 表示宽松，2 表示适中，3 表示严格
        "mark_count": mark_count,  # 评分轮次，宽松取最高分，适中取平均分，严格取最低分
        "std_score": std_score,  # 空对应的分数
        "e_mark_rule": e_mark_rule,
        "mark_point": mark_point,
        "ques_material": ques_material
    }

    logger.info(f"发送给 AI 参数：{data}")
    try:
        res = requests.post(configs.SPACE_MARK_URL, json=data)
    except:
        return ai_can_not_connect_code, None
    res_code = res.status_code
    if res_code != 200:
        logger.error(f"AI 返回错误码 {res_code}")
        return res_code, None
    result = res.json()
    # result = {
    #     "ques_id": 123456,
    #     "ai_score": 1.00,  # 单个空格分数
    #     "ai_reason": "xxxx",  # 评分解析
    #     "cost_time": 5.00  # 解析耗时，秒为单位
    # }
    logger.info(f"AI 返回数据：{result}")
    return res_code, result


def ai_subjective_all_space_score(same_answer_group_id: str, ques_id: str, subject: str, ques_type: str, ques_desc: str,
                                  std_answer: list, stu_answer: list, rule: int, mark_count: int, std_score: list,
                                  e_mark_rule: str, mark_point: list, ques_material: Optional[str] = None,
                                  ques_images_dict: Optional[dict] = None, is_multiple: int = 1):
    data = {
        "same_answer_group_id": same_answer_group_id,
        "ques_id": ques_id,
        "subject": subject,
        "ques_type": ques_type,
        "ques_desc": ques_desc,
        "std_answer": std_answer,
        "stu_answer": stu_answer,
        "rule": rule,  # 评分规则：1 表示宽松，2 表示适中，3 表示严格
        "mark_count": mark_count,  # 评分轮次，宽松取最高分，适中取平均分，严格取最低分
        "std_score": std_score,  # 空对应的分数
        "e_mark_rule": e_mark_rule,
        "mark_point": mark_point,
        "ques_material": ques_material.replace("\n", "") if ques_material else None,
        "ques_images_dict": ques_images_dict,
        "is_multiple": is_multiple
    }

    logger_data = copy.deepcopy(data)
    # logger_data["ques_images_dict"]["img_base64_list"] = []

    logger.info(f"发送给 AI 参数：{logger_data}")
    try:
        res = requests.post(configs.SET_STD_URL, json=data, timeout=(5, configs.AI_TIMEOUT_SECOND))
    except requests.exceptions.RequestException:
        return ai_not_response_code, None
    except:
        return ai_can_not_connect_code, None
    res_code = res.status_code

    if res_code != 200:
        logger.error(f"AI 返回错误码 {res_code}")
        return res_code, None
    result = res.json()
    logger.info(f"AI 返回数据：{result}")
    return res_code, result


def format_ques_desc(desc: str, std_answer: list, space_num: int):
    """
    将试题描述拼接成只有单个空的，其余空用标准答案补齐
    :param desc: 试题描述
    :param std_answer: 标准答案列表
    :param space_num: 空号
    :return: 格式化的试题描述
    """
    # desc = "珊瑚虫与海洋中的微小生物属于______关系。从生态系统的组成成分看，珊瑚虫属于______者。"
    # std_answer = ["捕食", "消费"]
    # space_num = 3
    index = space_num - 1
    std_punctuation = r"______"
    format_desc = re.sub(r"_+", std_punctuation, desc)
    format_desc_list = re.split(rf"({std_punctuation})", format_desc)
    punctuation_index_list = [index for index, value in enumerate(format_desc_list) if value == std_punctuation]
    for list_index, punctuation_index in enumerate(punctuation_index_list):
        if list_index != index:
            format_desc_list[punctuation_index] = std_answer[list_index]
    text = "".join(format_desc_list)
    logger.info(f"格式化后的试题描述：{text}")
    return text


def check_ai_data_format(code, ai_res, ques_type, ques_score, mark_point_score):
    if code != 200:
        if code == ai_can_not_connect_code:
            logger.error(ai_can_not_connect)
            return False, ai_can_not_connect, None, None, None, []
        elif code == ai_not_response_code:
            logger.error(ai_not_response)
            return False, ai_not_response, None, None, None, []
        logger.error(f"AI 异常，错误码：{code}")
        return False, f"AI 异常，错误码：{code}", None, None, None, []
    try:
        ai_score = ai_res["ai_score"]
        ai_reason = ai_res["ai_reason"]
        pending_review = ai_res["pending_review"]
        ai_single_score_list = ai_res.get("ai_single_score", [])
    except KeyError:
        logger.error("AI 返回格式错误")
        return False, "AI 返回格式错误", None, None, None, []
    except TypeError:
        logger.error("AI 返回参数缺失")
        return False, "AI 返回参数缺失", None, None, None, []
    if type(ai_score) != float:
        logger.error("AI 返回参数类型错误， ai_score 不是浮点数")
        return False, "AI 返回参数类型错误， ai_score 不是浮点数", None, None, None, []
    if type(ai_reason) != list:
        logger.error("AI 返回参数类型错误， ai_reason 不是列表")
        return False, "AI 返回参数类型错误， ai_reason 不是列表", None, None, None, []
    if ai_single_score_list and type(ai_single_score_list) != list:
        logger.error("AI 返回参数类型错误， ai_single_score_list 不是列表")
        return False, "AI 返回参数类型错误， ai_single_score_list 不是列表", None, None, None, []
    if type(pending_review) != int:
        logger.error("AI 返回参数类型错误， pending_review 不是整数")
        return False, "AI 返回参数类型错误， pending_review 不是整数", None, None, None, []
    if ques_type == "E":
        if len(ai_single_score_list) != len(mark_point_score):
            logger.error("AI 单点评分标准分数长度不匹配")
            return False, ai_score_ambiguity, None, ai_reason, None, []
        # for i, j in zip(ai_single_score_list, mark_point_score):
        #     if i > j:
        #         logger.error("AI 单点评分标准分数存在歧义")
        #         return False, ai_score_ambiguity, None, ai_reason, None, []
        if ai_score > ques_score:
            logger.info("AI 分数超过试题总分，将分数改为试题总分")
            ai_score = ques_score
    else:
        if pending_review not in [0, 1]:
            logger.error("AI 返回参数类型错误， pending_review 不为 0 或 1")
            return False, "AI 返回参数类型错误， pending_review 不为 0 或 1", None, None, None, []
    return True, None, ai_score, ai_reason, pending_review, ai_single_score_list


def judge_mark_result(stu_score, std_score):
    """
    判断评分结果
    1 表示正确，2 表示错误，3 表示部分正确，4 表示未评分， 5 表示分数有误
    """
    if stu_score is None:
        mark_result = 4
    elif stu_score <= 0:
        mark_result = 2
    elif 0 < stu_score < std_score:
        mark_result = 3
    elif stu_score == std_score:
        mark_result = 1
    else:
        mark_result = 5
    return mark_result


def subjective_mark_score_main(same_answer_group_id: str, ques_id: str, subject: str, ques_desc: str, ques_type: str,
                               std_answer: list, std_score: list, stu_answer: Union[str, list], rule: int,
                               mark_count: int, space_num: Optional[int], e_mark_rule: str, mark_point: Optional[list],
                               ooo_ques_type: bool = False, mode: Optional[int] = None,
                               ques_material: Optional[str] = None, out_of_order_group=None,
                               ques_images_dict: Optional[dict] = None):
    """
    主观题判分主流程
    :return: 是否成功
             分数
             评分评析
             每个空的评分分数列表
             评分状态：1 表示未评分，2 表示评分成功，3 表示评分失败，4 表示作答答案待人工判断, 5 表示正在评分中
             评分结果：1 表示正确，2 表示错误，3 表示部分正确，4 表示未知
             出错信息
    """
    if out_of_order_group is None:
        out_of_order_group = []
    logger.info(f"考生答案 {stu_answer}")
    if not stu_answer or not any(stu_answer):
        # 101 表示 考生未作答，得0分
        return True, 0.00, "考生未作答，得0分", [], 2, 2, "考生未作答，得0分"

    if is_all_punctuation(stu_answer):
        # 102 表示 考生作答不符合规范，得0分
        return True, 0.00, "考生作答不符合规范，得0分", [], 2, 2, "考生作答不符合规范，得0分"

    if mode == 2:
        # 填空题答案乱序题型按整题评分
        compare_score = sum_with_precision(std_score)

        # 考生答案与参考答案一样得满分
        if std_answer == stu_answer:
            return True, compare_score, "考生答案与参考答案一致，得满分", std_score, 2, 1, 100

        if ooo_ques_type:
            # 填空题可乱序
            flag = True
            for group in out_of_order_group:
                std_answer_item = [std_answer[i - 1] for i in group]
                stu_answer_item = [stu_answer[i - 1] for i in group]
                if sorted(std_answer_item, key=lambda x: x) != sorted(stu_answer_item, key=lambda x: x):
                    flag = False
            if flag:
                # 100 表示 考生答案与参考答案一致，得满分
                return True, compare_score, "考生答案与参考答案一致，得满分", std_score, 2, 1, 100
        if ques_type == "D" and len(std_answer) < 2:
            is_multiple = 0
            std_answer = ",".join(std_answer)
            std_score = ",".join(std_score)
            if isinstance(stu_answer, list):
                stu_answer = ",".join(stu_answer)
        else:
            is_multiple = 1
            if not isinstance(stu_answer, list):
                stu_answer = [stu_answer]
        # 发送给 AI 判断
        code, ai_res = ai_subjective_all_space_score(same_answer_group_id, ques_id, subject, ques_type, ques_desc,
                                                     std_answer, stu_answer, rule, mark_count, std_score, e_mark_rule,
                                                     mark_point, ques_material, ques_images_dict, is_multiple)
    else:
        # space_num 校验合不合法
        if not space_num:
            logger.error("space_num 必须为大于 0 的且小于 std_score 长度的正整数")
            return False, None, None, [], 3, 4, "space_num 必须为大于 0 的且小于 std_score 长度的正整数"
        index = space_num - 1
        if index < 0 or index > len(std_score):
            logger.error("space_num 必须为大于 0 的且小于 std_score 长度的正整数")
            return False, None, None, [], 3, 4, "space_num 必须为大于 0 的且小于 std_score 长度的正整数"
        # 取出 space_num 对应的填空分数

        single_std_score = float(std_score[index])
        compare_score = single_std_score

        # 取出 space_num 对应的填空参考答案
        if std_answer:
            single_std_answer = std_answer[index]
        else:
            single_std_answer = None

        if ooo_ques_type and mode == 1:
            # 填空题答案乱序题型按单空评分
            if stu_answer in std_answer:
                # 100 表示 考生答案与参考答案一致，得满分
                return True, single_std_score, "考生答案与参考答案一致，得满分", [], 2, 1, 100
        else:
            if single_std_answer == stu_answer:
                # 100 表示 考生答案与参考答案一致，得满分
                return True, single_std_score, "考生答案与参考答案一致，得满分", [], 2, 1, 100

        # 格式化试题描述
        single_space_desc = format_ques_desc(ques_desc, std_answer, space_num)
        code, ai_res = ai_subjective_single_space_score(ques_id, subject, ques_type, single_space_desc,
                                                        single_std_answer, stu_answer, rule, mark_count,
                                                        single_std_score, e_mark_rule, mark_point, ques_material)

    mark_point_score = [i["score"] for i in mark_point]
    flag, msg, ai_score, ai_reason, pending_review, ai_single_score_list = check_ai_data_format(code, ai_res, ques_type, compare_score, mark_point_score)
    if not flag or code != 200 or ai_score is None:
        if code == ai_can_not_connect_code:
            msg = ai_can_not_connect
        elif code == ai_not_response_code:
            msg = ai_not_response
        return False, None, None, [], 3, 6, msg
    if pending_review == 1:
        mark_state = 4
    else:
        mark_state = 2

    mark_result = judge_mark_result(ai_score, compare_score)
    if mark_result == 5:
        # ai_score_ambiguity_code 表示 作答分数大于试题分数
        return False, ai_score, ai_reason, [], 3, mark_result, ai_score_ambiguity

    return True, ai_score, ai_reason, ai_single_score_list, mark_state, mark_result, None


if __name__ == '__main__':
    # ai_fill_blank_score()

    # ques_id, subject, ques_type = 123456789, "生物", "填空题"
    # ques_desc = "薇甘菊被称为“植物杀手”，它与红树植物属于______关系。被薇甘菊覆盖的红树植物枯萎的原因可能是______。"
    # std_answer = ["竞争", "光照不足，影响红树植物正常的光合作用"]
    # std_score = [1, 1]
    # # stu_answer = ["竞争", "光照不足，影响红树植物正常的光合作用"]
    # stu_answer = ["竞争", "营养不足"]
    # # stu_answer = ["寄生", "营养不足"]
    # # stu_answer = ["", "营养不足"]
    # # stu_answer = ["竞争1", ""]
    # # stu_answer = ["", ""]

    # ques_desc = "隔离膜的作用相当于______壁和毛细血管壁。气体进入人工心肺机后，通过隔离膜完成与血液的气体交换，排出气体的含氧量______。"
    # std_answer = ["肺泡", "减少"]
    # std_score = [1, 1]
    # stu_answer = ["动脉", "减少"]
    #
    # rule, mark_count = 1, 2
    # fill_blank_score_main(ques_id, subject, ques_desc, ques_type, std_answer, std_score, stu_answer, rule, mark_count)
    # print(all(stu_answer))

    # ai_fill_single_space_score()

    same_answer_group_id, ques_id, subject, ques_type = "123456", "123456789", "生物", "D"
    ques_desc = "薇甘菊被称为“植物杀手”，它与红树植物属于______关系。被薇甘菊覆盖的红树植物枯萎的原因可能是______。"
    std_answer = ["竞争", "光照不足，影响红树植物正常的光合作用"]
    std_score = [1.00, 1.00]
    # stu_answer = "竞争"
    stu_answer = "寄生"
    # stu_answer = "营养不足"
    rule, mark_count = 1, 2

    subjective_mark_score_main(same_answer_group_id, ques_id, subject, ques_desc, ques_type, std_answer, std_score,
                               stu_answer, rule, mark_count, 1, "", [], False, 2)

    # ques_id, subject, ques_type = 123456789, "生物", "E"
    # ques_desc = ("植物的CO2补偿点是指由于CO2的限制，光合速率与呼吸速率相等时环境中的CO 2浓度，已知甲种植物的CO2补偿点大于乙种植物的，"
    #              "回答问题：将正常生长的甲、乙两种植物放置在同一密闭小室中，适宜条件下照光培养，培养后发现两种植物的光合速率都降低，"
    #              "原因是_________________")
    # std_answer = ["植物在光下光合作用吸收CO2的量大于呼吸作用释放CO2的量，使密闭小室中CO2浓度降低，光合速率也随之降低。"]
    # std_score = [10.00]
    # stu_answer = "光合作用因为CO2浓度受到限制"
    # rule, mark_count = 1, 1
    # mark_point = [{"point": "提及光合作用吸收CO2的量大于呼吸作用释放CO2的量（5分）", "score": "5"}, {"point": "提及密闭小室中CO2浓度降低（5分）", "score": "5"}]
    #
    # result = subjective_mark_score_main(same_answer_group_id, ques_id, subject, ques_desc, ques_type, std_answer, std_score, stu_answer, rule,
    #                                     mark_count, 1, mark_point)
    # print(result)
