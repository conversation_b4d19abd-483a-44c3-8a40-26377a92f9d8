import logging
from datetime import datetime

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger


def launch_human_scheduler(func, seconds=30):
    # 配置定时任务
    logging.getLogger('apscheduler').setLevel(logging.WARNING)
    scheduler = BackgroundScheduler(timezone="Asia/Shanghai")
    scheduler.add_job(
        func,
        trigger=IntervalTrigger(seconds=seconds),
        # next_run_time=datetime.now()
    )
    scheduler.start()

