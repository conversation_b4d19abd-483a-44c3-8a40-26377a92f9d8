import os
import re
import threading
import traceback
from urllib.parse import quote

from fastapi import APIRouter, Depends
from sqlalchemy import select, func, and_
from starlette.responses import StreamingResponse

from apps.ai_set_std.schemas import GetSetStdQuesReq, StartSetStdReq, QuesSetStdDetailReq, AgainSetStdReq, \
    ManualSetStdReq, ExcSetStdReq, GetExcSetStdProgressReq, GetScoreParseReq, SetStdIdReq
from apps.ai_set_std.set_std_services import get_set_std_ques_condition, trans_set_std_state, get_set_std_process, \
    get_set_std_detail_condition, trans_mark_state_as_set_std, get_ques_list_set_std_state, \
    get_again_set_std_detail, check_ai_terminal_alive, get_set_std_error_num, create_ques_set_std_data, \
    supp_stu_count_data, get_paper_detail_by_ques_id, check_has_std_answer, get_has_answer_ques_id_list, \
    get_ques_set_std_info
from apps.ai_set_std.set_std_thread_services import start_set_std_main, update_ques_set_std_state, check_can_pause, \
    cancel_remark_stu_answer_group, check_can_cancel, remark_init_stu_answer_group, auto_pause_ai_set_std, \
    check_task_execute_state, restore_backup_set_std_data
from apps.base.global_cache import set_redis_ques_set_std_state, get_redis_ques_info_dict
from apps.base.services import update_monitor_cal, get_import_db_monitor_info
from apps.operation_ques.op_manual_mark_services import file_iterator
from helper import response_utils
from settings import logger, configs
from typing import Any
from sqlalchemy.orm import Session, aliased

from apps.users.services import get_current_user
from apps.models.models import QuesSetStdState, ExamQuestion, Project, PaperDetail, ExamPaper, Subject, UserInfo, QuesType, \
    SameStuAnswerGroup, StuTotalGradeDetail, StuTotalGrade, BusinessQuesType, TaskExecuteRecord, ScoreParse
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from utils.create_excel import create_grade_excel
from utils.time_func import format_now_time
from utils.utils import sum_with_precision

set_std_router = APIRouter()


@set_std_router.post(path="/get_ques_set_std_list", response_model=BaseResponse, summary="获取试题定标列表")
async def get_ques_set_std_list(query: GetSetStdQuesReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    只展示主观题
    """
    logger.info(f"{user['username']} 获取试题定标列表")
    current_page, page_size, project_id, subject_id, paper_id, ques_type_code_list, ques_order, small_ques_num, ques_code, set_std_state_list = query.model_dump().values()
    curr_user_id = user['user_id']

    # 判断是否需要补充数据
    monitor_id, monitor_type = get_import_db_monitor_info(new_session)
    if monitor_id:
        index = 2
        if monitor_type[index] == "1":
            logger.info("补充考生人数")
            threading.Thread(target=supp_stu_count_data, args=(monitor_id, monitor_type, index)).start()

        index = 3
        if monitor_type[index] == "1":
            update_monitor_cal(new_session, monitor_id, monitor_type, index, "2")
            logger.info("生成试题定标数据")
            create_ques_set_std_data(new_session, curr_user_id)
            update_monitor_cal(new_session, monitor_id, monitor_type, index, "3")

    # 查询有考生作答信息的试题
    has_answer_ques_id_list = get_has_answer_ques_id_list(new_session, is_reload=True)
    set_std_ques_data = []
    limit = current_page - 1
    offset = limit * page_size
    # 拼凑查询条件
    is_paper = new_session.query(ExamPaper).count()
    filter_condition = get_set_std_ques_condition(project_id, subject_id, paper_id, ques_type_code_list, ques_code,
                                                  None, set_std_state_list, [], ques_order, small_ques_num)
    condition = and_(filter_condition, ExamQuestion.ques_type_code.in_(["D", "E"]),
                     ExamQuestion.ques_id.in_(has_answer_ques_id_list))

    if is_paper:
        state_stmt = select(ExamQuestion.ques_id, Subject.subject_name, ExamQuestion.small_ques_num,
                            func.group_concat(ExamPaper.paper_name).label("paper_name"),
                            QuesType.ques_type_name, ExamQuestion.ques_code, UserInfo.name,
                            func.ifnull(QuesSetStdState.set_std_state, 1).label("set_std_state"),
                            QuesSetStdState.set_std_time, QuesSetStdState.set_std_end_time, PaperDetail.ques_order,
                            PaperDetail.paper_id, Subject.subject_id) \
            .join(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
            .join(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
            .join(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
            .join(Project, Project.project_id == ExamPaper.project_id) \
            .join(Subject, Subject.subject_id == ExamPaper.subject_id) \
            .join(QuesSetStdState, QuesSetStdState.ques_id == ExamQuestion.ques_id) \
            .join(UserInfo, UserInfo.user_id == QuesSetStdState.c_user_id) \
            .where(condition) \
            .group_by(ExamQuestion.ques_id, Subject.subject_name, ExamQuestion.small_ques_num, QuesType.ques_type_name,
                      ExamQuestion.ques_code, UserInfo.name, QuesSetStdState.set_std_state,
                      QuesSetStdState.set_std_time,
                      QuesSetStdState.set_std_end_time, PaperDetail.ques_order, PaperDetail.paper_id,
                      Subject.subject_id) \
            .order_by(PaperDetail.ques_order)

        if page_size != -1:
            total = new_session.query(ExamQuestion) \
                .join(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
                .join(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
                .join(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
                .join(Project, Project.project_id == ExamPaper.project_id) \
                .join(Subject, Subject.subject_id == ExamPaper.subject_id) \
                .join(QuesSetStdState, QuesSetStdState.ques_id == ExamQuestion.ques_id) \
                .join(UserInfo, UserInfo.user_id == QuesSetStdState.c_user_id) \
                .where(condition).count()
            state_stmt = state_stmt.limit(page_size).offset(offset)
    else:
        parent_exam_ques = aliased(ExamQuestion, name="parent_exam_ques")
        state_stmt = select(ExamQuestion.ques_id, Subject.subject_name, ExamQuestion.small_ques_num,
                            QuesType.ques_type_name, ExamQuestion.ques_code, UserInfo.name,
                            func.ifnull(QuesSetStdState.set_std_state, 1).label("set_std_state"),
                            QuesSetStdState.set_std_time, QuesSetStdState.set_std_end_time,
                            BusinessQuesType.subject_id) \
            .join(parent_exam_ques, parent_exam_ques.ques_id == ExamQuestion.parent_ques_id) \
            .join(BusinessQuesType, BusinessQuesType.business_ques_type_id == parent_exam_ques.business_ques_type_id) \
            .join(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
            .join(Project, Project.project_id == BusinessQuesType.project_id) \
            .join(Subject, Subject.subject_id == BusinessQuesType.subject_id) \
            .join(QuesSetStdState, QuesSetStdState.ques_id == ExamQuestion.ques_id) \
            .join(UserInfo, UserInfo.user_id == QuesSetStdState.c_user_id) \
            .where(condition) \
            .order_by(ExamQuestion.ques_id)

    # .join(parent_exam_ques, parent_exam_ques.ques_id == ExamQuestion.parent_ques_id)\

        if page_size != -1:
            total = new_session.query(ExamQuestion) \
                .join(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
                .join(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
                .join(Project, Project.project_id == BusinessQuesType.project_id) \
                .join(Subject, Subject.subject_id == BusinessQuesType.subject_id) \
                .join(QuesSetStdState, QuesSetStdState.ques_id == ExamQuestion.ques_id) \
                .join(UserInfo, UserInfo.user_id == QuesSetStdState.c_user_id) \
                .where(condition).count()

            state_stmt = state_stmt.limit(page_size).offset(offset)

    try:
        result = list(new_session.execute(state_stmt))
        print(result)
        ques_id_state_dict = {}
        query_ques_id_list = []
        for i in result:
            ques_id_state_dict[i.ques_id] = i.set_std_state
            query_ques_id_list.append(i.ques_id)

        process_dict, finish_ques_id_list = get_set_std_process(new_session, ques_id_state_dict)

        # 前端是否刷新列表参数，0:不刷新，1:刷新
        is_refresh = 0

        # 异常数量
        error_num_dict = get_set_std_error_num(new_session, query_ques_id_list)

        all_ques_info = get_redis_ques_info_dict(new_session,True)

        for row in result:
            ques_id = row.ques_id
            progress_count = "0/0"
            progress = 0
            process_item = process_dict.get(ques_id)
            if process_item:
                if process_item.get("total_count"):
                    progress_count = f"{process_dict[ques_id]['finish_count']}/{process_dict[ques_id]['total_count']}"
                    progress = process_item["progress"]

            ques_info = all_ques_info[ques_id]

            set_std_state = 3 if ques_id in finish_ques_id_list else row.set_std_state
            set_std_item = {
                "ques_id": ques_id,
                "subject_name": row.subject_name,#ques_info["subject_name"],
                "paper_id": row.paper_id if is_paper else None,
                "paper_name": row.paper_name if is_paper else None,
                "ques_type_name": row.ques_type_name,
                "ques_code": row.ques_code,
                "ques_order": (re.sub(r'^0+', '', row.ques_order) if row.ques_order else None) if is_paper else None,
                "small_ques_num": row.small_ques_num,
                "set_std_state": set_std_state,
                "set_std_state_str": trans_set_std_state(set_std_state),
                "progress_count": progress_count,
                "progress": progress,
                "error_num": error_num_dict.get(ques_id, 0),
                "set_std_start_time": row.set_std_time and str(row.set_std_time).replace("T", " "),
                "set_std_end_time": row.set_std_end_time and str(row.set_std_end_time).replace("T", " "),
                "c_name": row.name
            }
            set_std_ques_data.append(set_std_item)

            if set_std_state == 2:
                is_refresh = 1

    except Exception as e:
        logger.error(f"获取试题定标列表失败，{e}")
        logger.error(traceback.format_exc())
        return BaseResponse(code=response_utils.server_error, msg="获取试题定标列表失败")

    logger.info("获取试题定标列表成功")
    data = {
        "data": set_std_ques_data,
        "total": total if page_size != -1 else len(set_std_ques_data),
        "is_refresh": is_refresh
    }
    return BaseResponse(msg="获取试题定标列表成功", data=data)


@set_std_router.post(path="/start_set_std", response_model=BaseResponse, summary="开始定标")
async def start_set_std(query: StartSetStdReq, user: Any = Depends(get_current_user),
                        new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 开始定标")

    if not check_ai_terminal_alive():
        return BaseResponse(code=response_utils.terminal_error, msg="AI 服务连接失败，请联系管理员")

    ques_id_list = query.ques_id_list

    # 检查填空题是否有参考答案，简答题是否有得分点
    result, msg = check_has_std_answer(new_session, ques_id_list)
    if not result:
        return BaseResponse(code=response_utils.no_field, msg=msg)

    ques_set_std_info = new_session.query(QuesSetStdState.ques_id, ExamQuestion.ques_code) \
        .join(ExamQuestion, ExamQuestion.ques_id == QuesSetStdState.ques_id) \
        .filter(and_(QuesSetStdState.ques_id.in_(ques_id_list), QuesSetStdState.set_std_state == 2)).all()
    if ques_set_std_info:
        ques_code_list = [i.ques_code for i in ques_set_std_info]
        return BaseResponse(code=response_utils.params_error,
                            msg=f"试题编号为：{'，'.join(ques_code_list)} 的试题正在定标中，请勿重复操作")
    # 检查状态并修改任务状态
    _, launch_ques_id_list = check_task_execute_state(new_session, ques_id_list, False)
    print("launch_ques_id_list", launch_ques_id_list)
    # 开启线程处理任务
    threading.Thread(target=start_set_std_main,
                     args=(ques_id_list, False, False, [], [], None, launch_ques_id_list)).start()
    return BaseResponse(msg="已开始定标")


@set_std_router.post(path="/pause_set_std", response_model=BaseResponse, summary="暂停定标")
async def pause_set_std(query: StartSetStdReq, user: Any = Depends(get_current_user),
                        new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 暂停定标")
    ques_id_list, is_auto_trigger, _ = query.model_dump().values()
    ques_state_data = get_ques_list_set_std_state(new_session, ques_id_list)
    for ques in ques_state_data:
        state_id, ques_id, running_state, pause_state = ques["state_id"], ques["ques_id"], ques["running_state"], ques[
            "pause_state"]
        msg = check_can_pause(running_state)

        if msg:
            if not is_auto_trigger:
                return BaseResponse(code=response_utils.operation_repeat, msg=msg)
            else:
                continue
        # 记录暂停时的任务及其状态并将定标状态改为已暂停
        update_ques_set_std_state(new_session, ques_id, 4, 4, running_state)
    return BaseResponse(msg="暂停定标成功")


@set_std_router.post(path="/cancel_set_std", response_model=BaseResponse, summary="取消定标")
async def cancel_set_std(query: StartSetStdReq, user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 取消定标")
    ques_id_list, is_auto_trigger, _ = query.model_dump().values()
    ques_state_data = get_ques_list_set_std_state(new_session, ques_id_list)
    for ques in ques_state_data:
        state_id, ques_id, running_state, pause_state, set_std_state = ques["state_id"], ques["ques_id"], ques[
            "running_state"], ques["pause_state"], ques["set_std_state"]
        msg = check_can_cancel(running_state)

        if msg:
            if not is_auto_trigger:
                return BaseResponse(code=response_utils.operation_repeat, msg=msg)
            else:
                continue

        # 将定标状态改为取消中
        update_ques_set_std_state(new_session, ques_id, 5, 5)
        # 取消待评分数据标记
        result = cancel_remark_stu_answer_group(new_session, ques_id)
        if not result:
            update_ques_set_std_state(new_session, ques_id, running_state, set_std_state)
            return BaseResponse(code=response_utils.server_error, msg="取消定标失败")
        # 将定标状态改为未定标
        new_session.query(QuesSetStdState).filter(QuesSetStdState.ques_id == ques_id).update({
            QuesSetStdState.running_state: 0,
            QuesSetStdState.set_std_state: 1,
            QuesSetStdState.set_std_time: None,
            QuesSetStdState.set_std_end_time: None
        })
        new_session.commit()
    return BaseResponse(msg="取消定标成功")


@set_std_router.post(path="/continue_set_std", response_model=BaseResponse, summary="继续定标")
async def continue_set_std(query: StartSetStdReq, user: Any = Depends(get_current_user),
                           new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 继续定标")

    if not check_ai_terminal_alive():
        return BaseResponse(code=response_utils.terminal_error, msg="AI 服务连接失败，请联系管理员")

    ques_id_list, is_auto_trigger, is_auto_launch = query.model_dump().values()

    # 当前运行状态
    ques_state_data = get_ques_list_set_std_state(new_session, ques_id_list)
    # update_data = []
    for ques in ques_state_data:
        state_id, ques_id, running_state, set_std_state, pause_state = ques["state_id"], ques["ques_id"], \
            ques["running_state"], ques["set_std_state"], ques["pause_state"]

        if not is_auto_trigger:
            mark_priority_exist = new_session.query(SameStuAnswerGroup).filter(
                SameStuAnswerGroup.mark_priority == 1).first()
            if mark_priority_exist:
                return BaseResponse(code=response_utils.permission_deny, msg="存在重新定标的数据，暂时无法继续定标")
        # 检查当前状态是否为暂停
        # if running_state != 4:
        #     if not is_auto_trigger:
        #         return BaseResponse(code=response_utils.permission_deny, msg="该任务当前状态不是暂停，无法继续定标")
        #     else:
        #         if is_auto_launch:
        #             pass
        #         else:
        #             continue

        # 运行状态，0 表示初始状态，1 表示正在获取数据中，2 表示正在标记中，3 表示评分中，4 表示暂停，5 表示正在取消中，6 表示已取消，7 表示已完成
        if pause_state in [0, 1, 2]:
            # 直接发起任务
            skip_remark = False
        elif pause_state == 3:
            # 跳过标记，发起任务
            skip_remark = True
        else:
            if is_auto_launch:
                skip_remark = True
            else:
                continue

        # 记录暂停时的任务及其状态并将定标状态改为定标中
        _, launch_ques_id = check_task_execute_state(new_session, ques_id_list)
        threading.Thread(target=start_set_std_main,
                         args=([ques_id], skip_remark, False, [], [], None, launch_ques_id)).start()
    return BaseResponse(msg="继续定标成功")


@set_std_router.post(path="/ques_set_std_detail", response_model=BaseResponse, summary="获取试题定标详情列表")
async def ques_set_std_detail(query: QuesSetStdDetailReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取试题定标详情")
    current_page, page_size, ques_id, same_answer_group_id, answer_cluster, mark_state, stu_score, search_time, small_ques_order = query.model_dump().values()
    filter_condition = get_set_std_detail_condition(same_answer_group_id, answer_cluster, mark_state, stu_score,
                                                    search_time, small_ques_order)

    # 获取试题序号和试题分数


    row_mark_state = mark_state
    condition = and_(SameStuAnswerGroup.ques_id == ques_id, filter_condition)

    detail_data = []
    total = 0
    detail_stmt = select(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.paper_id,
                         SameStuAnswerGroup.small_ques_order, SameStuAnswerGroup.stu_answer,
                         SameStuAnswerGroup.stu_count, SameStuAnswerGroup.ai_score, SameStuAnswerGroup.ai_answer_parse,
                         SameStuAnswerGroup.mark_fail_reason, SameStuAnswerGroup.mark_state,
                         SameStuAnswerGroup.updated_time).where(condition)
    if page_size != -1:
        limit = current_page - 1
        offset = limit * page_size
        total = new_session.query(SameStuAnswerGroup).filter(condition).count()
        detail_stmt = detail_stmt \
            .order_by(SameStuAnswerGroup.updated_time.desc(), SameStuAnswerGroup.same_answer_group_id) \
            .limit(page_size).offset(offset)

    try:
        all_ques_info = get_redis_ques_info_dict(new_session)
        result = new_session.execute(detail_stmt)
        for row in result:
            paper_id, mark_state, small_ques_order = row.paper_id, row.mark_state, row.small_ques_order

            ques_info = all_ques_info[ques_id]
            ques_score = ques_info["ques_score"]
            ai_score = None
            if mark_state != 3 and row.ai_score is not None:
                ai_score = float(row.ai_score)

            detail_item = {
                "same_answer_group_id": row.same_answer_group_id,
                "ques_id": ques_id,
                "answer_cluster": row.stu_answer,
                "stu_count": row.stu_count,
                "ai_score": ai_score,
                "ai_answer_parse": row.ai_answer_parse if mark_state not in [3, 4] else None,
                "ques_score": ques_score,
                "mark_fail_reason": row.mark_fail_reason,
                "mark_state": mark_state,
                "mark_state_str": trans_mark_state_as_set_std(mark_state) if row_mark_state != 4 else "重新定标",
                "updated_time": row.updated_time and str(row.updated_time).replace("T", " ")
            }
            detail_data.append(detail_item)
    except Exception as e:
        logger.error(f"获取试题定标详情失败，{e}")
        logger.error(traceback.format_exc())
        return BaseResponse(code=response_utils.server_error, msg="获取试题定标详情失败")

    logger.info("获取试题定标详情成功")
    data = {
        "data": detail_data,
        "total": total if page_size != -1 else len(detail_data)
    }
    return BaseResponse(msg="获取试题定标详情成功", data=data)


@set_std_router.post(path="/again_ai_set_std", response_model=BaseResponse, summary="定标详情重新 AI 定标")
async def again_ai_set_std(query: AgainSetStdReq, user: Any = Depends(get_current_user),
                           new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 发起重新 AI 定标")

    if not check_ai_terminal_alive():
        return BaseResponse(code=response_utils.terminal_error, msg="AI 服务连接失败，请联系管理员")

    _, _, ques_id, same_answer_group_id, answer_cluster, mark_state, stu_score, search_time, small_ques_order, select_group_id_list = query.model_dump().values()
    if select_group_id_list:
        logger.info(f"{user['username']} 获取勾选的数据")
        priority_group_id_list = select_group_id_list
    else:
        logger.info(f"{user['username']} 根据查询条件获取重新定标的数据")
        res, msg = get_again_set_std_detail(ques_id, same_answer_group_id, answer_cluster, mark_state, stu_score,
                                            search_time, small_ques_order, user)
        if res == 0:
            return BaseResponse(code=response_utils.server_error, msg=msg)
        again_set_std_list = res["data"]
        if not again_set_std_list:
            return BaseResponse(code=response_utils.no_field, msg="未找到重新AI定标的数据")
        priority_group_id_list = [i["same_answer_group_id"] for i in again_set_std_list]

    # 先将试题定标的任务暂停
    # 获取正在运行中的任务的 ques_id
    running_ques_info = new_session.query(QuesSetStdState.ques_id).filter(
        QuesSetStdState.running_state.in_([1, 2, 3])).all()
    running_ques_id = [i[0] for i in running_ques_info]
    # 暂停
    auto_pause_ai_set_std(running_ques_id, user)

    # 标记重新定标数据
    remark_init_stu_answer_group(new_session, ques_id, True, priority_group_id_list, True)

    # 启动重新定标线程
    token = f"Bearer {user['token']}"
    threading.Thread(target=start_set_std_main,
                     args=([ques_id], True, True, priority_group_id_list, running_ques_id, token)).start()
    # todo: 重新定标完成删除备份
    return BaseResponse(msg="已发起重新 AI 定标")


@set_std_router.post(path="/cancel_again_ai_set_std", response_model=BaseResponse,
                     summary="取消定标详情里的重新 AI 定标")
async def cancel_again_ai_set_std(query: SetStdIdReq, user: Any = Depends(get_current_user),
                                  new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 取消定标详情里的重新 AI 定标")
    # todo: 退出重新评分线程
    # 将备份还原并取消标记
    ques_id = query.ques_id
    restore_backup_set_std_data(new_session, ques_id)
    return BaseResponse(msg="取消重新定标成功")


@set_std_router.post(path="/manual_set_std", response_model=BaseResponse, summary="人工定标")
async def manual_set_std(query: ManualSetStdReq, user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 人工定标")

    paper_id, ques_id, group_id_list, manual_score, manual_answer_parse = query.model_dump().values()

    if paper_id:
        ques_score_list = new_session.query(PaperDetail.ques_score_list).filter(
            and_(PaperDetail.paper_id == paper_id, PaperDetail.ques_id == ques_id)).scalar()
        ques_score = sum_with_precision(ques_score_list)
    else:
        ques_score = get_redis_ques_info_dict(new_session)[ques_id]["ques_type_score"]

    if manual_score > ques_score:
        return BaseResponse(code=response_utils.params_error, msg=f"定标分数不能大于试题分数：{ques_score}")

    new_session.query(SameStuAnswerGroup).filter(and_(SameStuAnswerGroup.ques_id == ques_id,
                                                      SameStuAnswerGroup.same_answer_group_in.in_(
                                                          group_id_list))).update(
        {
            SameStuAnswerGroup.running_state: 3,
            SameStuAnswerGroup.mark_state: 2,
            SameStuAnswerGroup.ai_score: manual_score,
            SameStuAnswerGroup.ai_answer_parse: manual_answer_parse,
            SameStuAnswerGroup.ai_mark_time: format_now_time()
        })
    new_session.commit()
    return BaseResponse(msg="人工定标成功")


@set_std_router.post(path="/export_stu_detail", response_model=BaseResponse, summary="临时导出考生作答及评分")
async def export_stu_detail(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 临时导出考生作答及评分")

    all_ques_info = get_redis_ques_info_dict(new_session)

    # 获取试题序号和试题分数
    result, msg, paper_detail_dict = get_paper_detail_by_ques_id(new_session)
    if not result:
        return BaseResponse(code=response_utils.no_field, msg=msg)

    detail_info = new_session.query(StuTotalGrade.paper_id, StuTotalGradeDetail.ques_id, StuTotalGradeDetail.stu_answer,
                                    StuTotalGradeDetail.stu_score, StuTotalGradeDetail.answer_parse,
                                    StuTotalGrade.allow_exam_num) \
        .join(StuTotalGrade, StuTotalGrade.grade_id == StuTotalGradeDetail.grade_id) \
        .order_by(StuTotalGrade.allow_exam_num).all()

    data = []
    if detail_info:
        for paper_id, ques_id, stu_answer, stu_score, answer_parse, allow_exam_num in detail_info:
            answer_parse_text = ""
            if answer_parse and answer_parse != [None]:
                answer_parse_list = ["\n".join(i) for i in answer_parse]
                answer_parse_length = len(answer_parse_list)

                for index, value in enumerate(answer_parse_list):
                    if answer_parse_length >= 2:
                        answer_parse_text += f"({index + 1}){value}"
                        if index == 0:
                            answer_parse_text += "\n"
                    else:
                        answer_parse_text += value

            ques_order = paper_detail_dict[paper_id][ques_id]["ques_order"]

            ques_info = all_ques_info[ques_id]

            ques_type_code = ques_info["ques_type_code"]

            if ques_type_code == "D":
                if stu_answer is not None and configs.NEW_SPLIT_FLAG in stu_answer:
                    stu_answer = "".join([f"({index + 1}){value}" for index, value in
                                          enumerate(stu_answer.split(configs.NEW_SPLIT_FLAG))])
            else:
                stu_answer = stu_answer.replace(configs.NEW_SPLIT_FLAG, "") if stu_answer is not None else None

            # print("allow_exam_num", allow_exam_num)
            item = [
                allow_exam_num,
                ques_order,
                stu_answer,
                float(stu_score) if stu_score is not None else None,
                answer_parse_text
            ]
            data.append(item)

        title_dict = {
            "A1": "准考证号",
            "B1": "题号",
            "C1": "作答答案",
            "D1": "作答得分",
            "E1": "作答评析"
        }

        file_path = os.path.join(configs.PROJECT_PATH, f"server_static/export_file/cluster")
        os.makedirs(file_path, exist_ok=True)
        filename = f"ai_mark_{format_now_time('%Y_%m_%d_%H_%M_%S')}.xlsx"
        absolute_path = os.path.join(file_path, filename)
        flag = create_grade_excel(title_dict, data, absolute_path)
        if not flag:
            return BaseResponse(code=response_utils.server_error, msg="生成文件失败")

        # 传统编码（兼容旧系统）
        encoded_filename_legacy = quote(filename.encode('ISO-8859-1'))
        # UTF-8编码（RFC 5987，兼容新系统）
        encoded_filename_utf8 = quote(filename, safe='')

        headers = {
            "Content-Disposition": (
                f"attachment; "
                f"filename=\"{encoded_filename_legacy}\"; "
                f"filename*=UTF-8''{encoded_filename_utf8}"
            )
        }

        return StreamingResponse(
            file_iterator(absolute_path),
            media_type="application/octet-stream",
            headers=headers
        )


@set_std_router.post(path="/exc_set_std", response_model=BaseResponse, summary="批量处理异常定标的数据")
async def exc_set_std(query: ExcSetStdReq, user: Any = Depends(get_current_user),
                      new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 批量处理异常定标的数据")
    _, _, project_id, subject_id, paper_id, ques_type_code_list, ques_order, small_ques_num, ques_code, set_std_state_list, ques_id_list = query.model_dump().values()

    if not ques_id_list:
        # 根据筛选条件获取异常数据
        res, msg = get_ques_set_std_info(project_id, subject_id, paper_id, ques_type_code_list, ques_order,
                                         small_ques_num, ques_code, set_std_state_list, user)
        if res == 0:
            logger.info("获取AI定标的试题列表失败")
            return BaseResponse(code=response_utils.server_error, msg=msg)

        set_std_data = res["data"]
        if not set_std_data:
            return BaseResponse(code=response_utils.no_field, msg="所选试题暂无异常数据")

        ques_id_list = [i["ques_id"] for i in set_std_data if i["set_std_state"] != 1]

    mark_priority_count = new_session.query(SameStuAnswerGroup.same_answer_group_id) \
        .filter(and_(SameStuAnswerGroup.ques_id.in_(ques_id_list), SameStuAnswerGroup.mark_priority == 1)).count()
    if mark_priority_count:
        return BaseResponse(code=response_utils.no_field, msg="所选试题存在正在定标的数据，请稍后重试")

    group_info = new_session.query(SameStuAnswerGroup.same_answer_group_id).filter(
        and_(SameStuAnswerGroup.ques_id.in_(ques_id_list), SameStuAnswerGroup.mark_state.in_([3, 4]))).all()
    if not group_info:
        return BaseResponse(code=response_utils.no_field, msg="暂无异常数据")

    # 暂停正在运行的任务
    running_ques_info = new_session.query(QuesSetStdState.ques_id).filter(
        QuesSetStdState.running_state.in_([1, 2, 3])).all()
    running_ques_id = [i[0] for i in running_ques_info]
    auto_pause_ai_set_std(running_ques_id, user)

    group_id_list = [i[0] for i in group_info]
    total_count = len(group_id_list)

    # 创建进度条
    record_id = configs.snow_worker.get_id()
    new_record = TaskExecuteRecord(record_id=record_id, record_type=2, progress=0, success_count=0,
                                   total_count=total_count, extra_data=ques_id_list)
    new_session.add(new_record)

    # 标记重新定标数据
    for ques_id in ques_id_list:
        remark_init_stu_answer_group(new_session, ques_id, True, group_id_list)

    # 启动重新定标线程
    token = f"Bearer {user['token']}"
    threading.Thread(target=start_set_std_main,
                     args=(ques_id_list, True, True, group_id_list, running_ques_id, token)).start()
    data = {"record_id": record_id}
    return BaseResponse(msg="开始处理异常定标的数据", data=data)


@set_std_router.post(path="/get_exc_set_std_progress", response_model=BaseResponse,
                     summary="获取批量处理异常定标的数据进度")
async def get_exc_set_std_progress(query: GetExcSetStdProgressReq, user: Any = Depends(get_current_user),
                                   new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取批量处理异常定标的数据进度")
    record_id = query.record_id
    # 获取 ques_id_list
    if record_id:
        condition = TaskExecuteRecord.record_id == record_id
    else:
        condition = TaskExecuteRecord.record_type == 2
    record_info = new_session.query(TaskExecuteRecord.total_count, TaskExecuteRecord.extra_data) \
        .filter(condition).order_by(TaskExecuteRecord.created_time.desc()).first()
    if not record_info:
        return BaseResponse(data={})
    total_count, ques_id_list = record_info
    # 获取 ques_id_list 里 mark_priority = 1 的数量
    remain_count = new_session.query(SameStuAnswerGroup.same_answer_group_id) \
        .filter(and_(SameStuAnswerGroup.ques_id.in_(ques_id_list), SameStuAnswerGroup.mark_priority == 1)).count()
    # 计算进度
    success_count = total_count - remain_count
    success_count = success_count if success_count > 0 else 0
    progress = round(success_count / total_count * 100, 2) if total_count else 0
    data = {
        "success_count": success_count,
        "total_count": total_count,
        "progress": progress
    }
    return BaseResponse(msg="获取批量处理异常定标的数据进度成功", data=data)


@set_std_router.post(path="/get_score_parse", response_model=BaseResponse, summary="获取分数对应的评析")
async def get_score_parse(query: GetScoreParseReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取分数对应的评析")
    parse_type = query.parse_type
    parse_text = new_session.query(ScoreParse.parse_text).filter(ScoreParse.parse_type == parse_type).scalar()
    data = {"parse_text": parse_text}
    return BaseResponse(msg="获取分数对应的评析成功", data=data)
