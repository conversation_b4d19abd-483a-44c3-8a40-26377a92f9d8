from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import List, Dict, Any
from datetime import datetime

from apps.models.models import Subject, Project, StuAnswer, ExamStudent, ExamQuestion
from apps.grade_manage.models import HumanStudentSubjectGrade,HumanStudentSubjectGradeDetail

def get_verification_results_by_category(db: Session, subject_id: str) -> Dict[str, Any]:
    """
    按核验分类categories_info获取核验结果列表，并在查询的过程中更新考生试题作答的verification_status值
    使用StuAnswer、ExamQuestion与Subject表进行核验
    """
    # 定义四种核验分类及其表示信息
    categories_info = {
        "no_answer_no_score": {
            "name": "有作答无成绩",
            "description": "有作答但没有成绩记录的题目"
        },
        "answer_no_score": {
            "name": "有成绩无作答",
            "description": "有成绩记录但没有作答记录的题目"
        },
        "score_exceeds_max": {
            "name": "考生试题得分超过满分或出现负分",
            "description": "单题得分超过该题满分或出现负分的记录"
        },
        "total_score_exceeds_max": {
            "name": "考生的科目总得分超过科目满分或出现负分",
            "description": "考生的科目总得分超过科目满分或出现负分的记录"
        }
    }
    
    result_data = {}
    
    # 初始化各类别结果
    for category_key in categories_info.keys():
        result_data[category_key] = {
            "name": categories_info[category_key]["name"],
            "description": categories_info[category_key]["description"],
            "data": [],
            "error": ""
        }
    
    # 1. 查询"有作答无成绩"的题目
    # 从HumanStudentSubjectGradeDetail表中获取正确的考生得分
    no_answer_no_score_query = db.query(StuAnswer, ExamQuestion, HumanStudentSubjectGradeDetail).join(
        ExamQuestion, StuAnswer.ques_id == ExamQuestion.ques_id
    ).outerjoin(
        HumanStudentSubjectGradeDetail, 
        (StuAnswer.answer_id == HumanStudentSubjectGradeDetail.answer_id) & 
        (StuAnswer.ques_id == HumanStudentSubjectGradeDetail.ques_id)
    ).filter(
        StuAnswer.is_do == 1,  # 有作答
        HumanStudentSubjectGradeDetail.stu_score == None,  # 没有成绩记录
        # StuAnswer.verification_status != 2,  # 未被标记为核验不通过
        StuAnswer.subject_id == subject_id
    ).order_by(StuAnswer.stu_secret_num)
    
    no_answer_no_score_items = no_answer_no_score_query.all()
    # 获取科目名称
    subject_name = ""
    subject = db.query(Subject).filter(Subject.subject_id == subject_id).first()
    if subject:
        subject_name = subject.subject_name
    for item in no_answer_no_score_items:
        stu_answer, exam_question, grade_detail = item
        # 处理可能为None的grade_detail
        stu_score = None
        if grade_detail is not None:
            stu_score = grade_detail.stu_score
        result_data["no_answer_no_score"]["data"].append({
            "stu_secret_num": stu_answer.stu_secret_num, # 考生密号
            "subject_name": subject_name, # 科目名称
            "ques_id": stu_answer.ques_id, # 试题编号
            "is_answer": "是" if stu_answer.is_do == 1 else "否", # 是否作答
            "stu_score": stu_score, # 考生得分
        })
        # 更新verification_status为2（核验不通过）
        db.query(StuAnswer).filter(StuAnswer.answer_id == stu_answer.answer_id, StuAnswer.ques_id == stu_answer.ques_id).update({
            StuAnswer.verification_status: 2
        })
    
    # 2. 查询"有成绩无作答"的题目
    # 从HumanStudentSubjectGradeDetail表中获取正确的考生得分
    answer_no_score_query = db.query(StuAnswer, ExamQuestion, HumanStudentSubjectGradeDetail).join(
        ExamQuestion, StuAnswer.ques_id == ExamQuestion.ques_id
    ).outerjoin(
        HumanStudentSubjectGradeDetail, 
        (StuAnswer.answer_id == HumanStudentSubjectGradeDetail.answer_id) & 
        (StuAnswer.ques_id == HumanStudentSubjectGradeDetail.ques_id)
    ).filter(
        StuAnswer.is_do == 0,  # 没有作答
        HumanStudentSubjectGradeDetail.stu_score != None,  # 没有成绩记录
        HumanStudentSubjectGradeDetail.stu_score > float(0),
        # StuAnswer.verification_status != 2,  # 未被标记为核验不通过
        StuAnswer.subject_id == subject_id
    ).order_by(StuAnswer.stu_secret_num)
    
    answer_no_score_items = answer_no_score_query.all()
    for item in answer_no_score_items:
        stu_answer, exam_question, grade_detail = item
        # 处理可能为None的grade_detail
        stu_score = None
        if grade_detail is not None:
            stu_score = grade_detail.stu_score
        result_data["answer_no_score"]["data"].append({
            "stu_secret_num": stu_answer.stu_secret_num, # 考生密号
            "subject_name": subject_name, # 科目名称，需要从Subject表获取
            "ques_id": stu_answer.ques_id, # 试题编号
            "is_answer": "是" if stu_answer.is_do == 1 else "否", # 是否作答
            "stu_score": stu_score, # 考生得分
        })
        # 更新verification_status为2（核验不通过）
        db.query(StuAnswer).filter(StuAnswer.answer_id == stu_answer.answer_id, StuAnswer.ques_id == stu_answer.ques_id).update({
            StuAnswer.verification_status: 2
        })
    
    # 3. 查询"考生试题得分超过满分或出现负分"的题目
    # 从HumanStudentSubjectGradeDetail表中获取正确的考生得分
    score_exceeds_max_query = db.query(StuAnswer, ExamQuestion, HumanStudentSubjectGradeDetail).join(
        ExamQuestion, StuAnswer.ques_id == ExamQuestion.ques_id
    ).outerjoin(
        HumanStudentSubjectGradeDetail, 
        (StuAnswer.answer_id == HumanStudentSubjectGradeDetail.answer_id) & 
        (StuAnswer.ques_id == HumanStudentSubjectGradeDetail.ques_id)
    ).filter(
        HumanStudentSubjectGradeDetail.stu_score != None,  # 有成绩记录
        # StuAnswer.verification_status != 2,  # 未被标记为核验不通过
        StuAnswer.subject_id == subject_id
    ).order_by(StuAnswer.stu_secret_num)
    
    score_exceeds_max_items = score_exceeds_max_query.all()
    # 筛选得分超过满分或负分的记录
    filtered_score_exceeds_max = []
    for item in score_exceeds_max_items:
        stu_answer, exam_question, grade_detail = item
        if (grade_detail.stu_score is not None and
            exam_question.ques_score is not None and
            (grade_detail.stu_score > exam_question.ques_score or grade_detail.stu_score < 0)):
            filtered_score_exceeds_max.append(item)
            # 更新verification_status为2（核验不通过）
            db.query(StuAnswer).filter(StuAnswer.answer_id == stu_answer.answer_id, StuAnswer.ques_id == stu_answer.ques_id).update({
                StuAnswer.verification_status: 2
            })
    
    for item in filtered_score_exceeds_max:
        stu_answer, exam_question, grade_detail = item
        # 处理可能为None的grade_detail
        stu_score = None
        error_msg = ""
        if grade_detail is not None:
            stu_score = grade_detail.stu_score
            # 根据得分情况设置error信息
            if grade_detail.stu_score > exam_question.ques_score:
                error_msg = "试题得分超过试题总分"
            elif grade_detail.stu_score < 0:
                error_msg = "试题得分为负分"
        result_data["score_exceeds_max"]["data"].append({
            "stu_secret_num": stu_answer.stu_secret_num,
            "subject_name": subject_name,
            "ques_id": stu_answer.ques_id,
            "ques_score": exam_question.ques_score,
            "stu_score": grade_detail.stu_score,
            "error": error_msg
        })
        # 更新verification_status为2（核验不通过）
        db.query(StuAnswer).filter(StuAnswer.answer_id == stu_answer.answer_id, StuAnswer.ques_id == stu_answer.ques_id).update({
            StuAnswer.verification_status: 2
        })
    
    # 4. 查询"考生的科目总得分超过科目满分或出现负分"的题目
    # 从HumanStudentSubjectGrade表中获取考生科目总分
    total_score_query = db.query(
        HumanStudentSubjectGrade.stu_secret_num,
        HumanStudentSubjectGrade.score.label("total_score")
    ).filter(
        HumanStudentSubjectGrade.score != None,
        # HumanStudentSubjectGrade.verification_status != 2,
        HumanStudentSubjectGrade.subject_id == subject_id
    ).order_by(HumanStudentSubjectGrade.stu_secret_num)
    total_scores = total_score_query.all()
    
    # 获取科目总分
    subject_total_scores = {}
    if subject_id:
        subject = db.query(Subject).filter(Subject.subject_id == subject_id).first()
        if subject and subject.subject_total_score:
            subject_total_scores[subject_id] = subject.subject_total_score
    else:
        subjects = db.query(Subject).all()
        for subject in subjects:
            if subject.subject_total_score:
                subject_total_scores[subject.subject_id] = subject.subject_total_score
                
    # 筛选总分超过满分或负分的考生
    filtered_total_score_exceeds_max = []
    for stu_secret_num, total_score in total_scores:
        # 获取科目ID
        subject_id_for_exam = db.query(HumanStudentSubjectGrade.subject_id).filter(HumanStudentSubjectGrade.stu_secret_num == stu_secret_num).first()
        if subject_id_for_exam and subject_id_for_exam[0] in subject_total_scores:
            subject_total_score = subject_total_scores[subject_id_for_exam[0]]
            if total_score > subject_total_score or total_score < 0:
                # 获取该考生的所有作答记录
                exam_answers = db.query(StuAnswer).filter(StuAnswer.stu_secret_num == stu_secret_num).all()
                for answer in exam_answers:
                    # 更新verification_status为2（核验不通过）
                    db.query(StuAnswer).filter(StuAnswer.answer_id == answer.answer_id, StuAnswer.ques_id == answer.ques_id).update({
                        StuAnswer.verification_status: 2
                    })
                filtered_total_score_exceeds_max.append((stu_secret_num, total_score, subject_id_for_exam[0], subject_total_score))
    
    for stu_secret_num, total_score, subject_id, subject_total_score in filtered_total_score_exceeds_max:
        # 根据总分情况设置error信息
        error_msg = ""
        if total_score > subject_total_score:
            error_msg = "试卷得分超过试卷总分"
        elif total_score < 0:
            error_msg = "试卷得分为负分"
        result_data["total_score_exceeds_max"]["data"].append({
            "stu_secret_num": stu_secret_num,
            "subject_name": subject_name,
            "subject_id":subject_id,
            "subject_score":subject_total_score,
            "stu_score": total_score,
            "error": error_msg
        })
        # 更新该考生所有作答记录的verification_status为2（核验不通过）
        db.query(StuAnswer).filter(StuAnswer.stu_secret_num == stu_secret_num).update({
            StuAnswer.verification_status: 2
        })
    
    # 提交所有更新
    db.commit()
    
    # 更新所有未被标记为核验不通过的考生作答的verification_status为1（核验通过）
    # 这一步确保通过核验的考生作答状态被正确设置
    db.query(StuAnswer).filter(
        StuAnswer.subject_id == subject_id,
        StuAnswer.verification_status != 2 # 不是核验不通过的记录
    ).update({
        StuAnswer.verification_status: 1
    })
    
    # 提交StuAnswer表的更新
    db.commit()
    
    # 更新Subject表的verification_status状态
    # 情况一：科目核验未通过 - 当前科目下只要存在一个核验未通过的考生作答（StuAnswer.verification_status值为2），则Subject.verification_status为2
    unverified_count = db.query(StuAnswer).filter(
        StuAnswer.subject_id == subject_id,
        StuAnswer.verification_status == 2
    ).count()
    
    if unverified_count > 0:
        # 存在核验不通过的记录，科目核验不通过
        db.query(Subject).filter(Subject.subject_id == subject_id).update({
            Subject.verification_status: 2
        })
    else:
        # 情况二：科目核验通过 - 当前科目下所有StuAnswer.verification_status值为1，则更新Subject.verification_status为1
        all_verified_count = db.query(StuAnswer).filter(
            StuAnswer.subject_id == subject_id,
            StuAnswer.verification_status == 1
        ).count()
        
        total_count = db.query(StuAnswer).filter(
            StuAnswer.subject_id == subject_id
        ).count()
        
        if all_verified_count == total_count and total_count > 0:
            # 所有记录都已核验通过
            db.query(Subject).filter(Subject.subject_id == subject_id).update({
                Subject.verification_status: 1
            })
    
    # 提交Subject表的更新
    db.commit()
    
    # 为每个分类设置error信息
    if result_data["no_answer_no_score"]["data"]:
        result_data["no_answer_no_score"]["error"] = f"{len(result_data['no_answer_no_score']['data'])}个考生有作答无成绩"
    if result_data["answer_no_score"]["data"]:
        result_data["answer_no_score"]["error"] = f"{len(result_data['answer_no_score']['data'])}个考生有成绩无作答"
    if result_data["score_exceeds_max"]["data"]:
        result_data["score_exceeds_max"]["error"] = _generate_score_exceeds_max_error(result_data["score_exceeds_max"]["data"])
    if result_data["total_score_exceeds_max"]["data"]:
        result_data["total_score_exceeds_max"]["error"] = _generate_total_score_exceeds_max_error(result_data["total_score_exceeds_max"]["data"])
    
    # 将error信息添加到结果中
    error_messages = []
    if result_data["no_answer_no_score"]["error"]:
        error_messages.append(result_data["no_answer_no_score"]["error"])
    if result_data["answer_no_score"]["error"]:
        error_messages.append(result_data["answer_no_score"]["error"])
    if result_data["score_exceeds_max"]["error"]:
        error_messages.append(result_data["score_exceeds_max"]["error"])
    if result_data["total_score_exceeds_max"]["error"]:
        error_messages.append(result_data["total_score_exceeds_max"]["error"])
    result_data["error"] = "；".join(error_messages) if error_messages else ""
    
    return result_data




def get_verify_result_list(db: Session, project_id: str = None, subject_id: str = None, 
                          verification_status: int = -1, page: int = 1, page_size: int = 10) -> Dict[str, Any]:
    """
    获取资格科目核验状态列表
    根据查询的subject_id（可选），verification_status（可选）查询，Subject.verification_status 状态 但是要考虑分页
    """
    from sqlalchemy import func, case
    
    # 先查询所有符合条件的科目
    subject_query = db.query(
        Subject.subject_id,
        Subject.subject_name,
        Subject.project_id,
        Project.project_name,
        Subject.verification_status
    ).join(
        Project, Subject.project_id == Project.project_id
    )
    
    # 添加过滤条件
    if project_id:
        subject_query = subject_query.filter(Subject.project_id == project_id)
    if subject_id:
        subject_query = subject_query.filter(Subject.subject_id == subject_id)
    if verification_status != -1:
        subject_query = subject_query.filter(Subject.verification_status == verification_status)
    
    # 获取总记录数
    total_count = subject_query.count()
    
    # 分页处理
    offset = (page - 1) * page_size
    paginated_results = subject_query.offset(offset).limit(page_size).all()
    
    # 构建返回结果
    results_list = []
    for row in paginated_results:
        results_list.append({
            "project_id": row.project_id,
            "project_name": row.project_name,
            "subject_id": row.subject_id,
            "subject_name": row.subject_name,
            "verification_status": row.verification_status
        })

    return {
        "data": results_list,
        "total": total_count
    }


def _generate_score_exceeds_max_error(score_exceeds_max_list: List[Dict]) -> str:
    """
    生成试题得分超过满分的错误描述
    """
    # 统计超过满分和负分的考生数量
    exceed_max_count = 0
    negative_score_count = 0
    for item in score_exceeds_max_list:
        # 注意：在score_exceeds_max_list中，我们使用的是ques_score字段
        if item.get("stu_score", 0) > 0 and item.get("stu_score", 0) > item.get("ques_score", 0):
            exceed_max_count += 1
        elif item.get("stu_score", 0) < 0:
            negative_score_count += 1
    # 生成描述信息
    description_parts = []
    if exceed_max_count > 0:
        description_parts.append(f"{exceed_max_count}个考生作答出现试题得分超过试题总分情况")
    if negative_score_count > 0:
        description_parts.append(f"有{negative_score_count}个考生作答出现试题得分为负分的情况")
    return "，".join(description_parts) if description_parts else ""


def _generate_total_score_exceeds_max_error(total_score_exceeds_max_list: List[Dict]) -> str:
    """
    生成试卷总分超过满分的错误描述
    """
    # 统计超过满分和负分的考生数量
    exceed_max_count = 0
    negative_score_count = 0
    for item in total_score_exceeds_max_list:
        if item.get("stu_score", 0) > 0 and item.get("stu_score", 0) > item.get("subject_total_score", 0):
            exceed_max_count += 1
        elif item.get("stu_score", 0) < 0:
            negative_score_count += 1
    # 生成描述信息
    description_parts = []
    if exceed_max_count > 0:
        description_parts.append(f"{exceed_max_count}个考生作答出现试卷得分超过试卷总分情况")
    if negative_score_count > 0:
        description_parts.append(f"有{negative_score_count}个考生作答出现试卷得分为负分的情况")
    return "，".join(description_parts) if description_parts else ""
