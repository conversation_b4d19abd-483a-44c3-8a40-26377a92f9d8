from pydantic import BaseModel, Field
from typing import Literal, Optional

from apps.base.schemas import PaginationModel


class SingleQuesReq(BaseModel):
    paper_id: Optional[str] = Field(None, title="试卷 id")  # 设置为选填以支持抽参
    ques_id: str = Field(..., title="试题 id")


class AnswerClusterReq(PaginationModel):
    paper_id: str = Field(..., title="试卷 id")
    ques_id: str = Field(..., title="试题 id")
    ques_type_code: Literal["D", "E"] = Field(..., title="试题类型")
    space_num: Optional[str] = Field(None, title="填空题的空号")
    # 查询条件
    stu_answer: Optional[str] = Field(None, title="聚类类别")
    set_std_state: int = Field(1, title="定标状态，1 表示未定标，2 表示已定标")
    stu_count: Optional[dict] = Field({}, title="人数")
    ai_score: Optional[dict] = Field({}, title="AI 评分")
    manual_score: Optional[dict] = Field({}, title="人工评分")
    answer_percentage: Optional[dict] = Field({}, title="占比率")
    manual_ai_diff_score: Optional[dict] = Field({}, title="人机分差")
    # 查询条件类型为 dict，参数说明
    # {
    #     "type": "1",  # 1 表示小于，2 表示大于，3 表示等于
    #     "value": value  # type 为 1 和 2 时，value 为浮点数，type 为 3 时，value 为两个浮点数组成的列表
    # }


class ClusterSetStdReq(BaseModel):
    ques_id: str = Field(..., title="试题 id")
    same_answer_group_id_list: list = Field(..., title="聚类类别 id 列表")
    set_std_score_type: int = Field(..., title="定标分数类型：None 表示未定标，1 表示AI分数，2 表示专家分数")
    set_std_score: float = Field(..., title="定标分数")
    profession_parse: Optional[list] = Field(None, title="定标解析")


class GetClusterParseReq(BaseModel):
    ques_id: str = Field(..., title="试题 id")
    same_answer_group_id: str = Field(..., title="聚类类别 id")

