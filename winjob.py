"""
统一启动脚本
按顺序启动Redis、主程序和定时任务程序
"""
import os
import sys
import time
import threading
import subprocess
import signal
import multiprocessing
from pathlib import Path
from settings import configs
from settings.logger import logger
from factory_apps.redis_db.redis_service_manager import ensure_redis_running, stop_redis


class UnifiedLauncher:
    """统一启动器"""
    
    def __init__(self):
        self.main_process = None
        self.schedule_process = None
        self.redis_started = False
        
        # 可执行文件路径
        if "_MEI" in configs.ROOT_PATH:
            # 打包后的路径
            self.main_exe_path = os.path.join(sys._MEIPASS, "卓帆电子化考试阅卷管理系统V1.0.exe")
            self.schedule_exe_path = os.path.join(sys._MEIPASS, "定时任务V1.0.exe")
        else:
            # 开发环境路径
            self.main_exe_path = "main.py"
            self.schedule_exe_path = "schedule_main.py"
    
    def start_redis(self):
        """启动Redis服务"""
        logger.info("统一启动器正在启动Redis服务...")
        success, message = ensure_redis_running()
        if success:
            logger.info(f"Redis服务启动成功: {message}")
            self.redis_started = True
            return True
        else:
            logger.error(f"Redis服务启动失败: {message}")
            return False
    
    def start_main_program(self):
        """启动主程序"""
        try:
            logger.info("正在启动主程序...")
            
            if "_MEI" in configs.ROOT_PATH:
                # 打包后直接运行exe
                if os.path.exists(self.main_exe_path):
                    self.main_process = subprocess.Popen(
                        [self.main_exe_path],
                        creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                    )
                else:
                    logger.error(f"主程序可执行文件不存在: {self.main_exe_path}")
                    return False
            else:
                # 开发环境运行Python脚本
                self.main_process = subprocess.Popen(
                    [sys.executable, self.main_exe_path],
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
            
            logger.info("主程序启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动主程序失败: {e}")
            return False
    
    def start_schedule_program(self):
        """启动定时任务程序"""
        try:
            logger.info("正在启动定时任务程序...")
            
            # 等待主程序完全启动
            time.sleep(10)
            
            if "_MEI" in configs.ROOT_PATH:
                # 打包后直接运行exe
                if os.path.exists(self.schedule_exe_path):
                    self.schedule_process = subprocess.Popen(
                        [self.schedule_exe_path],
                        creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                    )
                else:
                    logger.error(f"定时任务可执行文件不存在: {self.schedule_exe_path}")
                    return False
            else:
                # 开发环境运行Python脚本
                self.schedule_process = subprocess.Popen(
                    [sys.executable, self.schedule_exe_path],
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
            
            logger.info("定时任务程序启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动定时任务程序失败: {e}")
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        while True:
            try:
                # 检查主程序状态
                if self.main_process and self.main_process.poll() is not None:
                    logger.warning("主程序意外退出")
                    break
                
                # 检查定时任务程序状态
                if self.schedule_process and self.schedule_process.poll() is not None:
                    logger.warning("定时任务程序意外退出")
                    # 可以选择重启定时任务程序
                    # self.start_schedule_program()
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"监控进程时出错: {e}")
                break
    
    def shutdown(self):
        """关闭所有服务"""
        logger.info("正在关闭所有服务...")
        
        # 关闭定时任务程序
        if self.schedule_process:
            try:
                if self.schedule_process.poll() is None:
                    self.schedule_process.terminate()
                    self.schedule_process.wait(timeout=10)
                    logger.info("定时任务程序已关闭")
            except Exception as e:
                logger.error(f"关闭定时任务程序时出错: {e}")
                try:
                    self.schedule_process.kill()
                except:
                    pass
        
        # 关闭主程序
        if self.main_process:
            try:
                if self.main_process.poll() is None:
                    self.main_process.terminate()
                    self.main_process.wait(timeout=10)
                    logger.info("主程序已关闭")
            except Exception as e:
                logger.error(f"关闭主程序时出错: {e}")
                try:
                    self.main_process.kill()
                except:
                    pass
        
        # 关闭Redis服务
        if self.redis_started:
            try:
                stop_redis()
                logger.info("Redis服务已关闭")
            except Exception as e:
                logger.error(f"关闭Redis服务时出错: {e}")
        
        logger.info("所有服务已关闭")
    
    def run(self):
        """运行统一启动器"""
        try:
            logger.info("=== 卓帆智能阅卷系统统一启动器 ===")
            
            # 1. 启动Redis服务
            if not self.start_redis():
                logger.error("Redis服务启动失败，程序退出")
                return False
            
            # 2. 启动主程序
            if not self.start_main_program():
                logger.error("主程序启动失败，程序退出")
                self.shutdown()
                return False
            
            # 3. 启动定时任务程序
            if not self.start_schedule_program():
                logger.warning("定时任务程序启动失败，但主程序将继续运行")
            
            # 4. 监控进程状态
            logger.info("所有服务启动完成，开始监控...")
            self.monitor_processes()
            
            return True
            
        except KeyboardInterrupt:
            logger.info("接收到中断信号，正在关闭...")
            self.shutdown()
            return True
        except Exception as e:
            logger.error(f"统一启动器运行失败: {e}")
            self.shutdown()
            return False


def main():
    """主函数"""
    if "_MEI" in configs.ROOT_PATH:
        # 防止打包后运行时无限调用子程序
        multiprocessing.freeze_support()
    
    launcher = UnifiedLauncher()
    
    # 注册退出处理器
    import atexit
    atexit.register(launcher.shutdown)
    
    # 信号处理
    def signal_handler(signum, frame):
        logger.info(f"接收到信号 {signum}，正在关闭...")
        launcher.shutdown()
        sys.exit(0)
    
    if os.name != 'nt':  # 非Windows系统
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
    
    # 运行启动器
    success = launcher.run()
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
