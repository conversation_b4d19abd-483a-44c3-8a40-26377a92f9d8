"""
人工阅卷任务数据统计定时任务处理入口文件
"""
import multiprocessing
import threading
import uvicorn
import os
import signal
from apps import create_schedule_app
from settings import configs
from factory_apps.schduler.scheduler_tasks import launch_human_scheduler
from apps.data_statistics_schedule.services import query_and_insert_person_statistics,query_and_insert_group_statistics

pid = os.getpid()
app = create_schedule_app()


if __name__ == "__main__":
    if "_MEI" in configs.ROOT_PATH:
        # 防止打包后运行时无限调用子程序
        multiprocessing.freeze_support()
    threading.Thread(target=launch_human_scheduler, args=(query_and_insert_person_statistics, configs.Calculate_statistics_Interval), daemon=True).start()
    threading.Thread(target=launch_human_scheduler, args=(query_and_insert_group_statistics, configs.Calculate_statistics_Interval), daemon=True).start()
    # 启动后端服务
    uvicorn.run(app="schedule_main:app", host="0.0.0.0", port=configs.SCHEDULE_PORT, workers=configs.SCHEDULE_WORKERS, reload=False)
