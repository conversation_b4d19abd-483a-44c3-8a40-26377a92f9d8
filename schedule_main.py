"""
人工阅卷任务数据统计定时任务处理入口文件
"""
import multiprocessing
import threading
import uvicorn
import os
import signal
import sys
import time
from apps import create_schedule_app
from settings import configs
from settings.logger import logger
from factory_apps.schduler.scheduler_tasks import launch_human_scheduler
from factory_apps.redis_db.redis_service_manager import ensure_redis_running, stop_redis
from apps.data_statistics_schedule.services import query_and_insert_person_statistics, query_and_insert_group_statistics

pid = os.getpid()
app = create_schedule_app()


def startup_redis_service_for_schedule():
    """为定时任务启动Redis服务"""
    logger.info("定时任务程序正在检查Redis服务...")

    # 等待一段时间，让主程序先启动Redis
    time.sleep(5)

    success, message = ensure_redis_running()
    if success:
        logger.info(f"定时任务程序Redis连接成功: {message}")
        return True
    else:
        logger.error(f"定时任务程序Redis连接失败: {message}")
        return False


def shutdown_handler_schedule():
    """定时任务程序关闭处理器"""
    logger.info("定时任务程序正在退出...")


if __name__ == "__main__":
    try:
        if "_MEI" in configs.ROOT_PATH:
            # 防止打包后运行时无限调用子程序
            multiprocessing.freeze_support()

        # 检查Redis服务是否可用
        if not startup_redis_service_for_schedule():
            logger.error("定时任务程序无法连接到Redis服务，程序退出")
            sys.exit(1)

        # 注册程序退出处理器
        import atexit
        atexit.register(shutdown_handler_schedule)

        threading.Thread(target=launch_human_scheduler, args=(
            query_and_insert_person_statistics, configs.Calculate_statistics_Interval), daemon=True).start()
        threading.Thread(target=launch_human_scheduler, args=(
            query_and_insert_group_statistics, configs.Calculate_statistics_Interval), daemon=True).start()

        logger.info("正在启动定时任务服务...")
        # 启动后端服务
        uvicorn.run(app="schedule_main:app", host="0.0.0.0",
                    port=configs.SCHEDULE_PORT, workers=configs.SCHEDULE_WORKERS, reload=False)

    except KeyboardInterrupt:
        logger.info("定时任务程序接收到中断信号，正在关闭...")
        shutdown_handler_schedule()
        sys.exit(0)
    except Exception as e:
        logger.error(f"定时任务程序启动失败: {e}")
        shutdown_handler_schedule()
        sys.exit(1)
