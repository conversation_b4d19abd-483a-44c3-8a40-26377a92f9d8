from typing import Optional, Literal, List

from pydantic import Field, BaseModel

from apps.base.schemas import PaginationModel


class BaseRoundIdListReq(BaseModel):
    round_id_list: List[str] = Field(..., description="轮次id列表")


class MarkStuListReq(PaginationModel):
    round_id: str = Field(..., description="轮次id")
    ques_code: str = Field(..., description="试题编号")
    stu_type: Literal[0, 1] = Field(0, description="0 表示未评考生，1 表示已评考生")


class GetMyOfficialTaskListReq(PaginationModel):
    task_type: Literal[1, 2] = Field(..., description="任务类型")
    task_name: Optional[str] = Field(None, description="任务名称")
    round_state_list: list = Field([], description="任务状态")


class ReviewerMarkReq(BaseModel):
    answer_id: str = Field(..., description="作答id")
    mark_point_score_list: list = Field([], description="评分标准得分")
    mark_score: Optional[float] = Field(None, description="评分分数")


class ReviewerMarkStuReq(BaseModel):
    round_id: str = Field(..., description="轮次id")
    mark_type: Literal[1, 2, 3] = Field(1, description="评分类型，1 表示正常评分，2 表示重评，3 表示回评卷回评")
    cost_time: int = Field(..., description="评分耗时（秒）")
    mark_info: List[ReviewerMarkReq] = Field(..., description="评分数据")
    is_again_mark: int = Field(0, description="非 0 表示质检退回重评次数")


class GetMarkRecordReq(PaginationModel):
    subject_id: Optional[str] = Field(None, description="科目id")
    task_type: Optional[int] = Field(None, description="任务类型：1 表示正评，2 表示试评，3 表示复评")
    round_count: Optional[int] = Field(None, description="轮次")
    ques_code: Optional[str] = Field(None, description="试题编号")
    user_id: Optional[str] = Field(None, description="评阅员id")
    stu_secret_num: Optional[str] = Field(None, description="考生密号")
    start_time: Optional[str] = Field(None, description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")
    mark_score_range: Optional[list] = Field(None, description="评分分数")


class QuesCodeItem(BaseModel):
    ques_code: str = Field(..., description="试题编号")


class AnswerIdListItem(BaseModel):
    answer_id_list: list = Field(..., description="作答id列表")
