import traceback

from fastapi import APIRouter, Depends
from sqlalchemy import exists, and_
from settings import logger
from typing import Any

from sqlalchemy.orm import Session

from apps.models.models import SysModule, SysSubModule, SysFuncPoint
from apps.sys_manage.schemas import DeleteModuleReq, UpdateFuncPointReq, CreateFuncPointReq, UpdateModuleReq, \
    CreateModuleReq, DeleteFuncPointReq
from apps.sys_manage.services import get_all_sys_info, update_module_rank_when_create, update_module_rank_when_update, \
    update_module_rank_when_delete
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from apps.users.services import get_current_user
from settings import configs
from utils.utils import create_timestamp_id

sys_module_router = APIRouter()


@sys_module_router.get(path="/get_sys_module", response_model=BaseResponse, summary="获取系统模块列表")
async def get_sys_module(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取系统模块列表")
    final_module_data = get_all_sys_info(new_session)
    data = {"data": final_module_data}

    return BaseResponse(msg="获取系统模块列表成功", data=data)


@sys_module_router.post(path="/create_module", response_model=BaseResponse, summary="创建模块")
async def create_sys_module(query: CreateModuleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建模块")
    curr_user_id = user.get("user_id")
    flag, module_id, rank, module_name, web_path, icon, show_link = query.model_dump().values()

    if web_path and not web_path.startswith("/"):
        return BaseResponse(code=response_utils.fields_exist, msg=f"模块路径 {web_path} 格式错误，必须以 / 开头")

    if flag == 1:
        is_exist = new_session.query(exists().where(SysModule.module_name == module_name)).scalar()
        if is_exist:
            return BaseResponse(code=response_utils.fields_exist, msg=f"模块名 {module_name} 已存在")

        update_module_rank_when_create(new_session, flag, rank)
        new_sys_module = SysModule(module_id=configs.snow_worker.get_id(), rank=rank,
                                   module_name=module_name, web_path=web_path,
                                   icon=icon, show_link=show_link, module_flag=create_timestamp_id(),
                                   c_user_id=curr_user_id)
    elif flag == 2:
        is_exist = new_session.query(exists().where(SysModule.module_id == module_id)).scalar()
        if not is_exist:
            return BaseResponse(code=response_utils.no_field, msg=f"父模块不存在")

        name_is_exist = (new_session.query(exists().where(
            and_(SysModule.module_id == module_id, SysSubModule.sub_module_name == module_name))).scalar())
        if name_is_exist:
            return BaseResponse(code=response_utils.fields_exist, msg=f"子模块名 {module_name} 已存在该目录下")

        update_module_rank_when_create(new_session, flag, rank)
        new_sys_module = SysSubModule(sub_module_id=configs.snow_worker.get_id(), rank=rank,
                                      module_id=module_id,
                                      sub_module_name=module_name, sub_web_path=web_path,
                                      sub_icon=icon, sub_show_link=show_link,
                                      sub_module_flag=create_timestamp_id(), c_user_id=curr_user_id)
    elif flag == 3:
        is_exist = new_session.query(exists().where(SysSubModule.module_id == module_id)).scalar()
        if not is_exist:
            return BaseResponse(code=response_utils.no_field, msg=f"上一级对应的模块 {module_id} 不存在")

        name_is_exist = new_session.query(exists().where(
            and_(SysFuncPoint.module_id == module_id,
                 SysFuncPoint.func_point_name == module_name))).scalar()
        if name_is_exist:
            return BaseResponse(code=response_utils.fields_exist, msg=f"功能点 {query.module_name} 已存在该目录下")

        update_module_rank_when_create(new_session, flag, rank)
        new_sys_module = SysFuncPoint(func_point_id=configs.snow_worker.get_id(), func_point_flag=create_timestamp_id(),
                                      rank=rank, module_id=module_id,
                                      func_point_name=module_name, func_point_path=web_path,
                                      func_point_icon=icon, c_user_id=curr_user_id)
    else:
        return BaseResponse(code=response_utils.params_error, msg="参数有误")
    try:
        new_session.add(new_sys_module)
        new_session.commit()
        logger.info("创建模块成功")
        return BaseResponse(msg=f"创建模块成功")
    except Exception as e:
        logger.error(f"创建模块失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"创建模块失败")


@sys_module_router.post(path="/update_module", response_model=BaseResponse, summary="编辑模块")
async def update_module(query: UpdateModuleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    flag, module_id, rank, module_name, web_path, icon, show_link = query.model_dump().values()
    logger.info(f"{user['username']} 编辑模块")
    curr_user_id = user.get("user_id")

    if web_path and not web_path.startswith("/"):
        return BaseResponse(code=response_utils.fields_exist, msg=f"模块路径 {web_path} 格式错误，必须以 / 开头")

    try:
        if flag == 1:
            is_exist = new_session.query(exists().where(SysModule.module_id == module_id)).scalar()
            if not is_exist:
                return BaseResponse(code=response_utils.fields_exist, msg=f"模块名 {module_name} 不存在")

            update_module_rank_when_update(new_session, flag, module_id, rank)
            new_session.query(SysModule).filter(SysModule.module_id == module_id).update({
                SysModule.rank: rank,
                SysModule.module_name: module_name,
                SysModule.web_path: web_path,
                SysModule.icon: icon,
                SysModule.show_link: show_link,
                SysModule.u_user_id: curr_user_id
            })
        elif flag == 2:
            is_exist = new_session.query(exists().where(SysSubModule.sub_module_id == module_id)).scalar()
            if not is_exist:
                return BaseResponse(code=response_utils.fields_exist, msg=f"子模块名 {module_name} 不存在")

            update_module_rank_when_update(new_session, flag, module_id, rank)
            new_session.query(SysSubModule).filter(SysSubModule.sub_module_id == module_id).update({
                SysSubModule.rank: rank,
                SysSubModule.sub_module_name: module_name,
                SysSubModule.sub_web_path: web_path,
                SysSubModule.sub_icon: icon,
                SysSubModule.sub_show_link: show_link,
                SysSubModule.u_user_id: curr_user_id
            })
        else:
            return BaseResponse(code=response_utils.params_error, msg="参数有误")
        new_session.commit()
        logger.info("编辑模块成功")
        return BaseResponse(msg="编辑模块成功")
    except Exception as e:
        logger.error(f"编辑模块失败，{e}")
        new_session.rollback()
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg="编辑模块失败")


@sys_module_router.post(path="/delete_module", response_model=BaseResponse, summary="删除模块信息")
async def delete_module(query: DeleteModuleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    flag, module_id = query.flag, query.module_id
    logger.info(f"{user['username']} 删除模块信息，id 为 {module_id}")

    if flag == 1:
        is_exist = new_session.query(exists().where(SysModule.module_id == module_id)).scalar()
        if not is_exist:
            return BaseResponse(code=response_utils.fields_exist, msg=f"模块 {module_id} 不存在")
    elif flag == 2:
        is_exist = new_session.query(exists().where(SysSubModule.sub_module_id == module_id)).scalar()
        if not is_exist:
            return BaseResponse(code=response_utils.fields_exist, msg=f"模块 {module_id} 不存在")

    try:
        update_module_rank_when_delete(new_session, flag, module_id)
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除模块信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除模块信息失败")
    logger.info("删除模块信息成功")
    return BaseResponse(msg="删除模块信息成功")


@sys_module_router.post(path="/create_func_point", response_model=BaseResponse, summary="创建功能点")
async def create_func_point(query: CreateFuncPointReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建功能点")
    parent_module_flag, func_point_name, func_point_rank, func_point_value = query.model_dump().values()

    is_module_exist = new_session.query(exists().where(SysModule.module_flag == parent_module_flag)).scalar()
    is_sub_module_exist = new_session.query(
        exists().where(SysSubModule.sub_module_flag == parent_module_flag)).scalar()
    if not is_module_exist and not is_sub_module_exist:
        return BaseResponse(code=response_utils.no_field, msg="上一级对应的模块不存在")

    update_module_rank_when_create(new_session, 3, func_point_rank)
    name_is_exist = new_session.query(exists().where(
        and_(SysFuncPoint.parent_module_flag == parent_module_flag,
             SysFuncPoint.func_point_name == func_point_name))).scalar()
    if name_is_exist:
        return BaseResponse(code=response_utils.fields_exist, msg=f"功能点 {func_point_name} 已存在该目录下")

    new_func_point = SysFuncPoint(func_point_id=configs.snow_worker.get_id(), func_point_flag=create_timestamp_id(),
                                  rank=func_point_rank, parent_module_flag=parent_module_flag,
                                  func_point_name=func_point_name, func_point_value=func_point_value,
                                  c_user_id=user.get("user_id"))

    try:
        new_session.add(new_func_point)
        new_session.commit()
        logger.info("创建功能点成功")
        return BaseResponse(msg=f"创建功能点成功")
    except Exception as e:
        logger.error(f"创建功能点失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"创建功能点失败")


@sys_module_router.post(path="/update_func_point", response_model=BaseResponse, summary="编辑功能点")
async def update_func_point(query: UpdateFuncPointReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 编辑功能点")
    func_point_id, func_point_rank, func_point_name, func_point_value = query.model_dump().values()

    is_exist = new_session.query(exists().where(SysFuncPoint.func_point_id == func_point_id)).scalar()
    if not is_exist:
        return BaseResponse(code=response_utils.fields_exist, msg=f"该功能点不存在")

    try:
        update_module_rank_when_update(new_session, 3, func_point_id, func_point_rank)
        new_session.query(SysFuncPoint).filter(SysFuncPoint.func_point_id == func_point_id).update({
            SysFuncPoint.rank: func_point_rank,
            SysFuncPoint.func_point_name: func_point_name,
            SysFuncPoint.func_point_value: func_point_value,
            SysFuncPoint.u_user_id: user.get("user_id")
        })
        new_session.commit()
        logger.info("编辑功能点成功")
        return BaseResponse(msg="编辑功能点成功")
    except Exception as e:
        logger.error(f"编辑功能点失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="编辑功能点失败")


@sys_module_router.post(path="/delete_func_point", response_model=BaseResponse, summary="删除功能点")
async def delete_func_point(query: DeleteFuncPointReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    func_point_id = query.func_point_id
    logger.info(f"{user['username']} 删除功能点信息，id 为 {func_point_id}")

    is_exist = new_session.query(exists().where(SysFuncPoint.func_point_id == func_point_id)).scalar()
    if not is_exist:
        return BaseResponse(code=response_utils.fields_exist, msg=f"功能点 {func_point_id} 不存在")
    try:
        update_module_rank_when_delete(new_session, 3, func_point_id)
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除功能点失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除功能点失败")
    logger.info("删除功能点成功")
    return BaseResponse(msg="删除功能点成功")


@sys_module_router.post(path="/get_sys_version", response_model=BaseResponse, summary="获取系统版本号")
async def delete_func_point(user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 获取系统版本号")

    data = {
        "data": configs.SYS_VERSION
    }
    return BaseResponse(msg="获取系统版本号成功", data=data)
