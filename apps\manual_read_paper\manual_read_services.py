import traceback
import random
from collections import defaultdict
from decimal import Decimal
from typing import Optional, Literal

from settings import logger
from sqlalchemy import and_, func

from apps.base.global_cache import get_small_ques_count, set_single_task_state, get_redis_ques_info_dict
from apps.models.models import ManualGroupUser, ManualDistributeAnswer, ManualMark, ManualReadTask, \
    WorkFlowMainProcessInstance, ManualArbitrateQuality, ExamStudent, StudentSubject, StuAnswer, TaskExecuteRecord, \
    PaperDetail, ManualMarkHistory, UserInfo, WorkFlowMainProcess
from factory_apps import session_depend
from utils.time_func import format_now_time
from utils.utils import count_decimal_places, round_half_up, sum_with_precision
from settings import configs


def get_mark_count(new_session, m_read_task_id, distri_answer_id, history_type):
    mark_count = new_session.query(func.coalesce(func.max(ManualMarkHistory.mark_count), 1)) \
        .filter(and_(ManualMarkHistory.m_read_task_id == m_read_task_id,
                     ManualMarkHistory.distri_answer_id == distri_answer_id,
                     ManualMarkHistory.history_type == history_type)).scalar()
    return mark_count


def create_manual_history(history_id, m_read_task_id, distri_answer_id, history_type, mark_score, mark_suggestion,
                          mark_user_id, manual_aq_id=None, step_score_list=None):
    if step_score_list is None:
        step_score_list = []
    new_session = next(session_depend())
    try:
        if not history_id:
            history_id = configs.snow_worker.get_id()
        # real_mark_count = get_mark_count(new_session, m_read_task_id, distri_answer_id, history_type)
        manual_history = ManualMarkHistory(history_id=history_id, m_read_task_id=m_read_task_id,
                                           distri_answer_id=distri_answer_id, history_type=history_type,
                                           mark_count=1, mark_score=mark_score, op_step_score=step_score_list,
                                           mark_suggestion=mark_suggestion, mark_user_id=mark_user_id)
        new_session.add(manual_history)

        if manual_aq_id:
            new_session.query(ManualArbitrateQuality).filter(ManualArbitrateQuality.manual_aq_id == manual_aq_id) \
                .update({
                ManualArbitrateQuality.history_id: history_id
            })
        new_session.commit()
    except Exception as e:
        if "1062" in str(e):
            logger.warning("id 主键重复，重新生成 id")
            new_session.rollback()
            history_id = configs.snow_worker.get_id()
            logger.info(f"重新生成 history_id: {history_id}")
            create_manual_history(history_id, m_read_task_id, distri_answer_id, history_type, mark_score,
                                  mark_suggestion, mark_user_id, manual_aq_id, step_score_list)
        else:
            logger.error(e)
            return False


def update_manual_mark_history(history_id, mark_score, mark_suggestion):
    """
    更新人工评分历史记录
    """
    new_session = next(session_depend())
    new_session.query(ManualMarkHistory).filter(ManualMarkHistory.history_id == history_id).update({
        ManualMarkHistory.mark_score: mark_score,
        ManualMarkHistory.mark_suggestion: mark_suggestion
    })
    new_session.commit()


def update_quality_history(history_id, history_type, quality_num, quality_suggestion):
    """
    更新人工质检历史记录
    """
    new_session = next(session_depend())
    new_session.query(ManualMarkHistory).filter(ManualMarkHistory.history_id == history_id).update({
        ManualMarkHistory.history_type: history_type,
        ManualMarkHistory.mark_score: quality_num,
        ManualMarkHistory.mark_suggestion: quality_suggestion
    })
    new_session.commit()


def translate_history_type(history_type):
    """
    将历史记录类型数字转化为中文
    1 表示专家评分，2 表示专家重评，3 表示专家质检返评，4 表示仲裁，5 表示重新仲裁，6 表示质检，7 表示重新质检
    """
    history_type_str = None
    if history_type == 1:
        history_type_str = "专家评分"
    elif history_type == 2:
        history_type_str = "专家重评"
    elif history_type == 3:
        history_type_str = "专家质检返评"
    elif history_type == 4:
        history_type_str = "仲裁"
    elif history_type == 5:
        history_type_str = "重新仲裁"
    elif history_type == 6:
        history_type_str = "质检"
    elif history_type == 7:
        history_type_str = "重新质检"
    return history_type_str


def get_curr_state(state: Optional[int]):
    if state == 1:
        curr_state = "专家评分完成"
    elif state == 2:
        curr_state = "待仲裁"
    elif state == 3:
        curr_state = "仲裁完成"
    elif state == 4:
        curr_state = "待质检"
    elif state == 5:
        curr_state = "质检通过"
    elif state == 6:
        curr_state = "待质检返评"
    elif state == 7:
        curr_state = "已质检返评"
    elif state == 8:
        curr_state = "评分完成"
    else:
        curr_state = "未知"
    return curr_state


def get_student_count(new_session, project_id: str, subject_id: str, paper_id: Optional[str] = None):
    """
    根据项目id和科目id获取考生数量
    """
    if paper_id:
        condition = and_(ExamStudent.project_id == project_id, StudentSubject.subject_id == subject_id,
                         StuAnswer.project_id == project_id, StuAnswer.subject_id == subject_id,
                         StuAnswer.paper_id == paper_id)
    else:
        condition = and_(ExamStudent.project_id == project_id, StudentSubject.subject_id == subject_id,
                         StuAnswer.project_id == project_id, StuAnswer.subject_id == subject_id)

    stu_count = new_session.query(ExamStudent.stu_id.distinct()) \
        .join(StudentSubject, StudentSubject.allow_exam_num == ExamStudent.allow_exam_num) \
        .join(StuAnswer, StuAnswer.allow_exam_num == ExamStudent.allow_exam_num) \
        .filter(condition).count()
    return stu_count


def get_user_manual_group_id(user_id, role_id):
    """
    根据用户id和角色id获取用户的人工阅卷小组
    :param user_id:
    :param role_id:
    :return:
    """
    new_session = next(session_depend())
    group_info = new_session.query(ManualGroupUser.manual_group_id).filter(
        and_(ManualGroupUser.user_id == user_id, ManualGroupUser.role_id == role_id)).all()
    if not group_info:
        group_id = None
    else:
        group_id = [i[0] for i in group_info]
    return group_id


def get_arbitrated_distri_answer_id(new_session, m_read_task_id, group_id, user_id_list):
    """
    获取已经仲裁的数据
    """
    condition = and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id,
                     ManualDistributeAnswer.manual_group_id == group_id,
                     ManualArbitrateQuality.aq_type == 1,
                     ManualArbitrateQuality.aq_state == 2,
                     ManualArbitrateQuality.aq_user_id.in_(user_id_list),
                     ManualArbitrateQuality.aq_mark_score.isnot(None))

    arbitrated_info = new_session.query(ManualDistributeAnswer.distri_answer_id) \
        .join(ManualMark, ManualDistributeAnswer.distri_answer_id == ManualMark.distri_answer_id) \
        .outerjoin(ManualArbitrateQuality,
                   ManualArbitrateQuality.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
        .filter(condition).all()

    if not arbitrated_info:
        return []

    arbitrated_distri_answer_id = list(set([arbitrated[0] for arbitrated in arbitrated_info]))
    return arbitrated_distri_answer_id


def get_quality_distri_answer_id(new_session, m_read_task_id, group_id, user_id_list):
    """
    获取已经质检的数据
    """
    condition = and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id,
                     ManualArbitrateQuality.aq_type == 2,
                     ManualArbitrateQuality.aq_state == 4,
                     ManualArbitrateQuality.aq_user_id.in_(user_id_list),
                     ManualDistributeAnswer.manual_group_id == group_id)

    yet_quality_info = new_session.query(ManualArbitrateQuality.distri_answer_id) \
        .join(ManualDistributeAnswer,
              ManualDistributeAnswer.distri_answer_id == ManualArbitrateQuality.distri_answer_id).filter(
        condition).all()

    yet_quality_answer_id_list = [quality[0] for quality in yet_quality_info] if yet_quality_info else []
    return yet_quality_answer_id_list


def get_marked_distri_answer_data(new_session, m_read_task_id, group_id, user_id_list, distri_answer_id=None):
    """
    获取已经评分的数据
    """
    if distri_answer_id:
        condition = and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id,
                         ManualDistributeAnswer.manual_group_id == group_id,
                         ManualMark.mark_person_id.in_(user_id_list),
                         ManualMark.distri_answer_id == distri_answer_id)
    else:
        condition = and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id,
                         ManualDistributeAnswer.manual_group_id == group_id,
                         ManualMark.mark_person_id.in_(user_id_list),
                         ManualMark.mark_state != 6)
    marked_info = new_session.query(ManualMark.manual_mark_id, ManualMark.distri_answer_id, ManualMark.mark_person_id,
                                    ManualMark.expert_mark_score, ManualDistributeAnswer.paper_id,
                                    ManualDistributeAnswer.ques_code, ManualDistributeAnswer.stu_answer_id,
                                    ManualDistributeAnswer.stu_secret_num, ManualDistributeAnswer.stu_answer,
                                    ManualMark.mark_state, ManualMark.op_step_score) \
        .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualMark.distri_answer_id) \
        .filter(condition).all()

    if not marked_info:
        return [], []

    marked_data = []
    marked_distri_answer_id = []
    for marked in marked_info:
        distri_answer_id = marked.distri_answer_id
        marked_distri_answer_id.append(distri_answer_id)
        marked_item = {
            "manual_mark_id": marked.manual_mark_id,
            "distri_answer_id": distri_answer_id,
            "mark_person_id": marked.mark_person_id,
            "expert_mark_score": marked.expert_mark_score,
            "paper_id": marked.paper_id,
            "ques_code": marked.ques_code,
            "stu_answer_id": marked.stu_answer_id,
            "stu_secret_num": marked.stu_secret_num,
            "stu_answer": marked.stu_answer,
            "mark_state": marked.mark_state,
            "op_step_score": marked.op_step_score
        }
        marked_data.append(marked_item)
    return marked_distri_answer_id, marked_data


def get_f_marked_distri_answer_data(new_session, m_read_task_id: str, group_id: str, ques_code: str,
                                    user_id_list: list):
    """
    获取组合题已经评分的数据id
    """
    condition = and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id,
                     ManualDistributeAnswer.manual_group_id == group_id,
                     ManualDistributeAnswer.ques_code == ques_code,
                     ManualMark.mark_person_id.in_(user_id_list),
                     ManualMark.mark_state != 6)
    marked_info = new_session.query(ManualMark.distri_answer_id, ManualDistributeAnswer.stu_secret_num) \
        .join(ManualDistributeAnswer, ManualDistributeAnswer.distri_answer_id == ManualMark.distri_answer_id) \
        .filter(condition).all()

    if not marked_info:
        return []

    # 统计同一个考生评分数据的个数
    marked_distri_answer_id_list, stu_secret_num_list, marked_distri_secret_num_dict = [], [], {}
    for marked in marked_info:
        marked_distri_answer_id, stu_secret_num = marked
        if stu_secret_num not in stu_secret_num_list:
            stu_secret_num_list.append(stu_secret_num)
        if stu_secret_num in marked_distri_secret_num_dict:
            marked_distri_secret_num_dict[stu_secret_num].append(marked_distri_answer_id)
        else:
            marked_distri_secret_num_dict[stu_secret_num] = [marked_distri_answer_id]

    # 同一个考生评分数据的个数大于等于该组合题的小题个数才算该组合题全部评过分
    small_ques_count = get_small_ques_count(ques_code)
    for marked in marked_info:
        marked_distri_answer_id, stu_secret_num = marked
        if len(marked_distri_secret_num_dict[stu_secret_num]) >= small_ques_count:
            marked_distri_answer_id_list.append(marked_distri_answer_id)

    return marked_distri_answer_id_list


def get_f_quality_distri_answer_id(new_session, m_read_task_id, group_id, ques_code, user_id_list):
    """
    获取组合题已经质检的数据
    """
    condition = and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id,
                     ManualDistributeAnswer.manual_group_id == group_id,
                     ManualArbitrateQuality.aq_type == 2,
                     ManualArbitrateQuality.aq_state == 4,
                     ManualArbitrateQuality.aq_user_id.in_(user_id_list))

    quality_info = new_session.query(ManualDistributeAnswer.distri_answer_id) \
        .join(ManualMark, ManualDistributeAnswer.distri_answer_id == ManualMark.distri_answer_id) \
        .outerjoin(ManualArbitrateQuality,
                   ManualArbitrateQuality.distri_answer_id == ManualDistributeAnswer.distri_answer_id) \
        .filter(condition).all()

    if not quality_info:
        return []

    # 统计同一个考生评分数据的个数
    marked_distri_answer_id_list, stu_secret_num_list, marked_distri_secret_num_dict = [], [], {}
    for marked in quality_info:
        marked_distri_answer_id, stu_secret_num = marked
        if stu_secret_num not in stu_secret_num_list:
            stu_secret_num_list.append(stu_secret_num)
        if stu_secret_num in marked_distri_secret_num_dict:
            marked_distri_secret_num_dict[stu_secret_num].append(marked_distri_answer_id)
        else:
            marked_distri_secret_num_dict[stu_secret_num] = [marked_distri_answer_id]

    # 同一个考生评分数据的个数大于等于该组合题的小题个数才算该组合题全部评过分
    small_ques_count = get_small_ques_count(ques_code)
    for marked in quality_info:
        marked_distri_answer_id, stu_secret_num = marked
        if len(marked_distri_secret_num_dict[stu_secret_num]) >= small_ques_count:
            marked_distri_answer_id_list.append(marked_distri_answer_id)

    # quality_distri_answer_id = list(set([quality[0] for quality in quality_info]))
    return marked_distri_answer_id_list


def sort_distri_answer(new_session, m_read_task_id, ques_id, group_id, curr_user_id, distri_info):
    """
    已经被其他专家评完分的非组合题显示在前面
    """
    if not distri_info:
        return distri_info
    # 查询同个小组的专家
    other_marked_distri_answer_id = []
    sample_group_expert = new_session.query(ManualGroupUser.user_id).filter(
        and_(ManualGroupUser.manual_group_id == group_id, ManualGroupUser.role_id == "3",
             ManualGroupUser.user_id != curr_user_id)).all()
    if sample_group_expert:
        sample_group_expert_id_list = [i[0] for i in sample_group_expert]
        # 查询出被其他组员评过分的distri_answer_id
        other_marked_distri_answer_id, _ = get_marked_distri_answer_data(new_session, m_read_task_id, group_id,
                                                                         sample_group_expert_id_list)
        # print("other_marked_distri_answer_id", other_marked_distri_answer_id)
        if other_marked_distri_answer_id:
            sorted_distri_info = sorted(distri_info,
                                        key=lambda x: x["distri_answer_id"] not in other_marked_distri_answer_id)
            sorted_distri_info = sorted(sorted_distri_info,
                                        key=lambda x: x["mark_state"] != 6)
            return sorted_distri_info
    return distri_info


def sort_f_distri_answer(new_session, m_read_task_id, ques_code, group_id, curr_user_id, distri_info):
    """
    已经被其他专家评完分的组合题显示在前面
    """
    if not distri_info:
        return distri_info
    other_marked_distri_answer_id = []
    # 查询同个小组的专家
    sample_group_expert = new_session.query(ManualGroupUser.user_id).filter(
        and_(ManualGroupUser.manual_group_id == group_id, ManualGroupUser.role_id == "3",
             ManualGroupUser.user_id != curr_user_id)).all()
    if sample_group_expert:
        sample_group_expert_id_list = [i[0] for i in sample_group_expert]
        # 查询出被其他同组专家评过分的distri_answer_id
        other_marked_distri_answer_id = get_f_marked_distri_answer_data(new_session, m_read_task_id, group_id,
                                                                        ques_code, sample_group_expert_id_list)
        if other_marked_distri_answer_id:
            sorted_distri_info = sorted(distri_info,
                                        key=lambda x: all(
                                            i not in other_marked_distri_answer_id for i in x["distri_answer_id_list"]))
            return sorted_distri_info
    return distri_info


def save_distri_answer_data(new_session, task_id, distri_info, ques_id, parent_ques_id, has_group=True):
    """
    保存考生的作答信息
    """
    total_count = len(distri_info)
    batch_size = 500
    total_batches = (total_count + batch_size - 1) // batch_size  # 计算总批次
    for batch_num in range(total_batches):
        logger.info(f"正在处理第 {batch_num + 1} 批考生作答数据")
        start_idx = batch_num * batch_size
        end_idx = min((batch_num + 1) * batch_size, total_count)
        batch_data = distri_info[start_idx: end_idx]
        distri_answer_list = []
        for item in batch_data:
            stu_answer = item["stu_answer"].split(configs.NEW_SPLIT_FLAG) if item["stu_answer"] else []
            new_distri_answer = ManualDistributeAnswer(distri_answer_id=configs.snow_worker.get_id(),
                                                       stu_answer_id=item["answer_id"], m_read_task_id=task_id,
                                                       manual_group_id=item["group_id"],
                                                       stu_secret_num=item["stu_secret_num"],
                                                       paper_id=item["paper_id"], ques_code=item["ques_code"],
                                                       stu_answer=stu_answer, op_file=item["op_file"],
                                                       ques_id=ques_id, parent_ques_id=parent_ques_id,
                                                       ques_type_code=item["ques_type_code"])
            distri_answer_list.append(new_distri_answer)

        new_session.add_all(distri_answer_list)
        new_session.commit()
        logger.info(f"处理第 {batch_num + 1} 批考生作答数据成功")
    logger.info("保存作答信息成功")
    return True


def compare_diff_scores_with_threshold(score_list, threshold_score, compare_type=None):
    """
    对比该组专家的评分差值是否有大于阈值分数的，有返回 True，无返回 False
    专家人数 > 2 时，仲裁分差取值方式，1 表示平均值，2 表示最大偏差，3 表示最小偏差
    """
    if compare_type == 1:
        # 平均分差与仲裁阈值相比较
        differences = []
        for i in range(len(score_list)):
            for j in range(i + 1, len(score_list)):
                # 获取两个评分字典的评分差值
                differences.append(abs(score_list[i]["expert_mark_score"] - score_list[j]["expert_mark_score"]))
        if differences:
            average_diff = float(sum([Decimal(str(i)) for i in differences]) / len(score_list))
            if average_diff > threshold_score:
                return True
        return False
    elif compare_type == 2:
        # 最大分差与仲裁阈值相比较
        marked_data = sorted(score_list, key=lambda x: x["expert_mark_score"])
        score_diff = abs(marked_data[0]["expert_mark_score"] - marked_data[-1]["expert_mark_score"])
        if score_diff > threshold_score:
            return True
        return False
    elif compare_type == 3:
        # 最小分差与仲裁阈值相比较
        # 初始化一个无穷大的值
        min_diff = float("inf")
        for i in range(len(score_list)):
            for j in range(i + 1, len(score_list)):
                # 获取两个评分字典的评分差值
                score_diff = abs(score_list[i]["expert_mark_score"] - score_list[j]["expert_mark_score"])
                if score_diff < min_diff:
                    min_diff = score_diff
        if min_diff > threshold_score:
            return True
        return False
    else:
        # 遍历评分列表中的每一个评分字典
        for i in range(len(score_list)):
            for j in range(i + 1, len(score_list)):
                # 获取两个评分字典的评分差值
                score_diff = float(abs(score_list[i]["expert_mark_score"] - score_list[j]["expert_mark_score"]))
                # 如果评分差值大于等于阈值
                if score_diff > threshold_score:
                    return True
        return False


def is_sign_quality_data(percentage: int):
    """
    根据质检抽样概率决定是否分配质检数据
    返回：bool
    """
    if not percentage:
        return False

    probability = percentage / 100
    true_count = int(100 * probability)
    sample_list = [True for _ in range(true_count)] + [False for _ in range(100 - true_count)]
    is_quality = random.choice(sample_list)

    return is_quality


def launch_arbitrate(new_session, distri_answer_id: str, expert_data: list, manual_mark_id_list: list,
                     m_read_task_id: str):
    """
    发起仲裁
    """
    try:
        aq_data = {
            "expert_data": expert_data,
            "arbitrate_data": None
        }
        new_arbitrate_item = ManualArbitrateQuality(manual_aq_id=configs.snow_worker.get_id(),
                                                    distri_answer_id=distri_answer_id, aq_type=1, aq_count=1,
                                                    aq_data=aq_data, aq_state=1, can_aq_again=1,
                                                    m_read_task_id=m_read_task_id)
        new_session.add(new_arbitrate_item)
        new_session.query(ManualMark).filter(ManualMark.manual_mark_id.in_(manual_mark_id_list)).update(
            {
                ManualMark.mark_state: 2,
                ManualMark.can_expert_mark_again: 0,
                ManualMark.not_mark_again_reason: "专家评分超过阈值，已发送给阅卷组长仲裁，无法重评"
            })
        new_session.commit()
        logger.info("发起仲裁成功")
        return True
    except Exception as e:
        traceback.print_exc()
        logger.error(f"发起仲裁失败，{e}")
        new_session.rollback()
        return False


def launch_quality(new_session, distri_answer_id: str, expert_data: list, arbitrator_data: Optional[list],
                   aq_count: int, manual_mark_id_list: list, m_read_task_id: str):
    """
    发起质检
    """
    aq_data = {
        "expert_data": expert_data,
        "arbitrate_data": arbitrator_data
    }

    try:
        new_quality_item = ManualArbitrateQuality(manual_aq_id=configs.snow_worker.get_id(),
                                                  distri_answer_id=distri_answer_id, aq_type=2, aq_count=aq_count,
                                                  aq_data=aq_data, aq_state=3, can_aq_again=1,
                                                  m_read_task_id=m_read_task_id)
        new_session.add(new_quality_item)
        new_session.query(ManualMark).filter(ManualMark.manual_mark_id.in_(manual_mark_id_list)).update(
            {
                ManualMark.mark_state: 4,
                ManualMark.can_expert_mark_again: 0,
                ManualMark.not_mark_again_reason: "已发送给质检人员质检，无法重评"
            })

        quality_count = new_session.query(ManualDistributeAnswer.quality_count).filter(
            ManualDistributeAnswer.distri_answer_id == distri_answer_id).scalar()
        if not quality_count:
            quality_count = 0
        new_session.query(ManualDistributeAnswer).filter(
            ManualDistributeAnswer.distri_answer_id == distri_answer_id).update({
            ManualDistributeAnswer.quality_count: quality_count + 1
        })
        new_session.commit()
        logger.info(f"{distri_answer_id} 发起质检成功")
        return True
    except Exception as e:
        traceback.print_exc()
        new_session.rollback()
        logger.error(f"{distri_answer_id} 发起质检失败，{e}")
        return False


def get_target_node_by_source(new_session, process_id: str, source_node: str, condition=True):
    """
    通过来源节点获取下一级节点的信息
    """
    total_condition = and_(WorkFlowMainProcessInstance.parent_process_id == process_id,
                           WorkFlowMainProcessInstance.instance_ele_type == source_node, condition)
    target_instance_info = new_session.query(WorkFlowMainProcessInstance.instance_target_id,
                                             WorkFlowMainProcessInstance.instance_target_percentage) \
        .filter(total_condition).first()
    return target_instance_info


def get_process_id_by_name(new_session, process_name: str):
    """
    通过名字获取 process_id
    """
    condition = and_(WorkFlowMainProcess.process_name == process_name, WorkFlowMainProcess.is_builtin == 1)
    process_id = new_session.query(WorkFlowMainProcess.process_id).filter(condition).scalar()
    return process_id



def get_all_ele_type_by_process(new_session, process_id: str):
    """
    通过流程id获取所有节点类型
    """
    ele_type_info = new_session.query(WorkFlowMainProcessInstance.instance_ele_type) \
        .filter(WorkFlowMainProcessInstance.parent_process_id == process_id).all()
    if not ele_type_info:
        return []
    return [i[0] for i in ele_type_info]


def update_return_quality_state(new_session, distri_answer_id: str, manual_aq_id: str):
    """
    小组里的所有专家都已经进行了质检返评，更新质检数据状态为 已质检返评
    """
    new_session.query(ManualArbitrateQuality).filter(ManualArbitrateQuality.manual_aq_id == manual_aq_id).update({
        ManualArbitrateQuality.aq_state: 4,
        ManualArbitrateQuality.aq_result: 3
    })
    new_session.commit()


def calculate_score_by_fetch_way(fetch_score_way, fetch_score_option, fetch_score_scope, mark_score_list):
    """
    根据 取分方式、取分选项、取分范围 计算分数
    """
    sorted_score_list = [Decimal(str(i)) for i in sorted(mark_score_list)]
    score_list_length = len(sorted_score_list)
    if fetch_score_way == 1:
        final_score = float(sorted_score_list[-1])
    elif fetch_score_way == 2:
        final_score = float(sorted_score_list[0])
    elif fetch_score_way == 3:
        average_score = sum(sorted_score_list) / score_list_length
        if score_list_length >= 3:
            # 小组内专家超过2且为平均分取值时增加全分组、高分组、中分组、低分组，
            # 可选择根据全分组、高分组、中分组、低分组中的任一组的平均分进行最终评分计算方式
            if fetch_score_scope == 1:
                pass
            elif fetch_score_scope == 2:
                high_score_list = sorted_score_list[-2:]
                average_score = sum(high_score_list) / len(high_score_list)
            elif fetch_score_scope == 3:
                if score_list_length % 2 == 0:
                    middle_index = score_list_length / 2
                    middle_score_list = sorted_score_list[middle_index - 1: middle_index + 1]
                    average_score = sum(middle_score_list) / len(middle_score_list)
                else:
                    middle_index = int((score_list_length - 1) / 2)
                    average_score = sorted_score_list[middle_index]
            elif fetch_score_scope == 4:
                low_score_list = sorted_score_list[: 2]
                average_score = sum(low_score_list) / len(low_score_list)
        decimal_count = count_decimal_places(average_score)
        if decimal_count > 2:
            if fetch_score_option == 1:
                final_score = float(f"{average_score:.2f}")
            elif fetch_score_option == 2:
                final_score = float(f"{average_score:.2f}") + 0.01
            elif fetch_score_option == 3:
                final_score = round_half_up(average_score, 2)
            else:
                final_score = None
        else:
            final_score = average_score
    elif fetch_score_way == 4 and score_list_length >= 3:
        middle_score_list = sorted_score_list[1: -1]
        final_score = sum(middle_score_list) / len(middle_score_list)
    else:
        final_score = None
    return final_score


fetch_way_dict = {
    1: "取最高分",
    2: "取最低分",
    3: "取平均分",
    4: "去除最高和最低分取平均分",
}


def statistic_final_score(new_session, m_read_task_id: str, distri_answer_id: str, fetch_score_way: Literal[1, 2, 3, 4],
                          fetch_score_option: Literal[1, 2, 3], fetch_score_scope: Literal[1, 2, 3],
                          is_get_mark_detail: bool = False):
    """
    统计人工阅卷最后的分数
    fetch_score_way: 1 为取最高分，2 为取最低分，3 为取平均分, 4 为去除最高和最低分取平均分
    """
    final_score, is_show_arbitrate = None, True
    mark_detail_dict, mark_process = {}, []
    mark_info = new_session.query(ManualMark.expert_mark_score, ManualMark.mark_parse, ManualMark.expert_mark_time,
                                  ManualMark.mark_person_id) \
        .filter(
        and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.distri_answer_id == distri_answer_id)).all()
    if mark_info:
        mark_score_list = []
        last_mark_time = None
        for expert_mark_score, mark_parse, expert_mark_time, expert_id in mark_info:
            if is_get_mark_detail:
                mark_parse_item = {
                    "type": 1,  # 1 表示专家，2 表示仲裁
                    "user_id": expert_id,
                    "score": float(expert_mark_score) if expert_mark_score is not None else None,
                    "parse": mark_parse,
                    "time": expert_mark_time and str(expert_mark_time).replace("T", " ")
                }
                mark_process.append(mark_parse_item)
            # 获取专家的评分分数
            mark_score_list.append(expert_mark_score)
            # 获取专家最后评分的时间
            if not last_mark_time:
                last_mark_time = expert_mark_time
            else:
                last_mark_time = expert_mark_time if expert_mark_time > last_mark_time else last_mark_time

        mark_score_list_length = len(mark_score_list)
        if mark_score_list_length == 1:
            final_score = mark_score_list[0]
        else:
            # 有多个分数
            # 判断是否仲裁过，仲裁过拿仲裁的分数
            arbitration_info = new_session.query(ManualArbitrateQuality.aq_mark_score,
                                                 ManualArbitrateQuality.aq_suggestion,
                                                 ManualArbitrateQuality.created_time,
                                                 ManualArbitrateQuality.updated_time,
                                                 ManualArbitrateQuality.aq_user_id) \
                .filter(and_(ManualArbitrateQuality.m_read_task_id == m_read_task_id,
                             ManualArbitrateQuality.distri_answer_id == distri_answer_id,
                             ManualArbitrateQuality.aq_type == 1, ManualArbitrateQuality.aq_mark_score.isnot(None))) \
                .order_by(ManualArbitrateQuality.updated_time.desc()).first()
            if arbitration_info:
                arbitration_score, aq_suggestion, created_time, updated_time, aq_user_id = arbitration_info
                arbitration_time = updated_time if updated_time else created_time
                # 仲裁时间大于专家最后评分时间，最终分数取仲裁分数，反之取专家分数
                if arbitration_time >= last_mark_time:
                    final_score = arbitration_score
                    if is_get_mark_detail:
                        mark_parse_item = {
                            "type": 2,  # 1 表示专家，2 表示仲裁，3 表示质检
                            "user_id": aq_user_id,
                            "score": float(arbitration_score) if arbitration_score is not None else None,
                            "parse": aq_suggestion,
                            "time": arbitration_time and str(arbitration_time).replace("T", " ")
                        }
                        mark_process.append(mark_parse_item)
                else:
                    is_show_arbitrate = False
                    final_score = calculate_score_by_fetch_way(fetch_score_way, fetch_score_option, fetch_score_scope,
                                                               mark_score_list)
            else:
                is_show_arbitrate = False
                final_score = calculate_score_by_fetch_way(fetch_score_way, fetch_score_option, fetch_score_scope,
                                                           mark_score_list)
        if is_get_mark_detail:
            mark_detail_dict = {
                "mark_process": mark_process,
                "fetch_way": fetch_way_dict.get(fetch_score_way) if fetch_score_way else None,
                "final_score": float(final_score) if final_score is not None else None
            }
    return final_score, is_show_arbitrate, mark_detail_dict


def update_manual_task_progress(new_session, m_read_task_id, read_record_id, is_check_finish_state=False,
                                distri_answer_id=None):
    """
    更新人工阅卷进度
    """
    success_count, total_count = 0, 0
    distri_info = new_session.query(ManualDistributeAnswer.final_mark_score).filter(
        ManualDistributeAnswer.m_read_task_id == m_read_task_id).all()
    for i in distri_info:
        total_count += 1
        if i[0] is not None:
            success_count += 1

    new_progress = round_half_up(success_count / total_count * 100, 2)
    end_time = format_now_time() if new_progress >= 100 else None

    if new_progress >= 100:
        new_progress = 100
        # new_session.query(ManualReadTask).filter(ManualReadTask.m_read_task_id == m_read_task_id).update({
        #     ManualReadTask.task_state: 3
        # })
        # set_single_task_state(m_read_task_id, 3)
    else:
        if is_check_finish_state:
            # new_session.query(ManualReadTask).filter(ManualReadTask.m_read_task_id == m_read_task_id).update({
            #     ManualReadTask.task_state: 2
            # })
            # set_single_task_state(m_read_task_id, 2)
            # 更新评分信息状态
            new_session.query(ManualMark).filter(
                and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.distri_answer_id == distri_answer_id)) \
                .update({
                ManualMark.mark_state: 1,
                ManualMark.expert_mark_time: format_now_time()
            })

    new_session.query(TaskExecuteRecord).filter(TaskExecuteRecord.record_id == read_record_id).update({
        TaskExecuteRecord.success_count: success_count,
        TaskExecuteRecord.progress: new_progress,
        TaskExecuteRecord.end_time: end_time
    })


def manual_process_operation(m_read_task_id, distri_answer_id, group_id, ques_id, source_node, condition=True,
                             aq_result=1, manual_aq_id=None, record_task_progress=True, is_check_finish_state=False):
    """
    根据主流程判断专家打分/仲裁/质检后需要进行的操作
    :param m_read_task_id:
    :param distri_answer_id:
    :param group_id:
    :param ques_id:
    :param source_node: 工作流来源节点
    :param condition:
    :param aq_result: 1 表示质检通过，2 表示质检不通过，待质检返评，3 表示质检不通过，已质检返评（该字段只适用于质检）
    :param manual_aq_id:
    :param record_task_progress: 是否要更新人工阅卷任务评分进度
    :param is_check_finish_state: 是否要检查人工阅卷任务状态（重评可能导致已完成的任务变成未完成）
    :return:
    """
    new_session = next(session_depend())
    sample_group_expert = new_session.query(ManualGroupUser.user_id).filter(
        and_(ManualGroupUser.manual_group_id == group_id, ManualGroupUser.role_id == "3")).all()

    # 获取的同个组的专家的id列表
    sample_group_expert_id_list = [i[0] for i in sample_group_expert]
    sample_group_expert_length = len(sample_group_expert_id_list)
    task_info = new_session.query(ManualReadTask.manual_process_id, ManualReadTask.quality_ratio,
                                  ManualReadTask.project_id, ManualReadTask.subject_id, ManualReadTask.fetch_score_way,
                                  ManualReadTask.fetch_score_option, ManualReadTask.fetch_score_scope,
                                  ManualReadTask.arbitrate_threshold_type, ManualReadTask.arbitrate_threshold,
                                  ManualReadTask.arbitrate_score_diff, ManualReadTask.expert_read_record_id,
                                  ManualReadTask.paper_id, ManualReadTask.ques_type_code).filter(
        ManualReadTask.m_read_task_id == m_read_task_id).first()
    (process_id, quality_ratio, project_id, subject_id, fetch_score_way, fetch_score_option, fetch_score_scope,
     arbitrate_threshold_type, arbitrate_threshold, arbitrate_score_diff, expert_read_record_id, paper_id,
     ques_type_code) = task_info

    target_instance_info = get_target_node_by_source(new_session, process_id, source_node, condition)
    if not target_instance_info:
        return

    # 获取下一步操作指向的id和专家评分阈值
    threshold_score = None
    target_id, target_percentage = target_instance_info
    if arbitrate_threshold_type == 1 and arbitrate_threshold:
        target_percentage = arbitrate_threshold
    elif arbitrate_threshold_type == 2 and arbitrate_threshold:
        threshold_score = arbitrate_threshold

    target_type_info = new_session.query(WorkFlowMainProcessInstance.instance_ele_type).filter(
        WorkFlowMainProcessInstance.instance_self_id == target_id).first()
    if not target_type_info:
        return
    target_type = target_type_info[0]

    # 查询出被其他同组专家评过分的评分信息
    _, marked_data = get_marked_distri_answer_data(new_session, m_read_task_id, group_id, sample_group_expert_id_list,
                                                   distri_answer_id)

    # 如果这个组的专家还没有全部对这道题打分就直接退出
    if len(marked_data) < sample_group_expert_length:
        return

    # 生成专家的评分数据
    expert_data = []
    manual_mark_id_list = []

    # 判断质检的数据专家是否已全部返评
    quality_return_marked = False
    if manual_aq_id:
        yet_return_user_id = new_session.query(ManualArbitrateQuality.yet_return_user_id) \
            .filter(ManualArbitrateQuality.manual_aq_id == manual_aq_id).scalar()
        if yet_return_user_id and len(set(yet_return_user_id)) == sample_group_expert_length:
            quality_return_marked = True

    for i in marked_data:
        manual_mark_item = {
            "manual_mark_id": i["manual_mark_id"],
            "mark_person_id": i["mark_person_id"],
            "mark_score": float(i["expert_mark_score"]) if i["expert_mark_score"] is not None else None,
            "op_step_score": i["op_step_score"]
        }
        expert_data.append(manual_mark_item)
        manual_mark_id_list.append(i["manual_mark_id"])

    all_ele_type = get_all_ele_type_by_process(new_session, process_id)
    if "bpmn:qualityNode" in all_ele_type:
        if quality_return_marked:
            logger.info("小组里的所有专家都已经进行了质检返评，更新质检数据状态为 已质检返评")
            update_return_quality_state(new_session, distri_answer_id, manual_aq_id)

    if target_type == "bpmn:scoreDetection":
        # 仲裁前的 判分检测
        if manual_aq_id and not quality_return_marked:
            logger.info("质检数据未全部完成返评，不发起仲裁")
            return
        # 判断专家评分是否超过阈值
        if not threshold_score:
            # 获取这道题的分数
            paper_id = marked_data[0]["paper_id"]
            if paper_id:
                ques_info = new_session.query(PaperDetail.ques_score_list).filter(
                    and_(PaperDetail.paper_id == paper_id, PaperDetail.ques_id == ques_id)).first()
                ques_score = ques_info[0]
                total_score = sum_with_precision(ques_score)
            else:
                total_score = get_redis_ques_info_dict(new_session)[ques_id]["ques_type_score"]
            # 获取阈值分数
            threshold_score = float(Decimal(total_score) * (Decimal(target_percentage) / 100))
        # 专家人数 > 2 时，仲裁的分差值取最大偏差/最小偏差/平均值
        if sample_group_expert_length <= 2:
            result = compare_diff_scores_with_threshold(marked_data, threshold_score)
        else:
            if arbitrate_score_diff:
                result = compare_diff_scores_with_threshold(marked_data, threshold_score, arbitrate_score_diff)
            else:
                result = compare_diff_scores_with_threshold(marked_data, threshold_score)
        if result:
            logger.info(f"专家判分超过阈值分数：{threshold_score}，{distri_answer_id} 发起仲裁，并禁止专家重评")
            condition = WorkFlowMainProcessInstance.instance_target_logic == 2
        else:
            condition = WorkFlowMainProcessInstance.instance_target_logic == 1
        new_session.commit()
        manual_process_operation(m_read_task_id, distri_answer_id, group_id, ques_id, target_type, condition)

    elif target_type == "bpmn:arbitratorNode":
        # 仲裁
        launch_arbitrate(new_session, distri_answer_id, expert_data, manual_mark_id_list, m_read_task_id)

    elif target_type == "bpmn:isQuality":
        # 判断是否质检
        is_quality = is_sign_quality_data(quality_ratio)

        if is_quality:
            aq_result = new_session.query(ManualArbitrateQuality.aq_result).filter(
                ManualArbitrateQuality.manual_aq_id == manual_aq_id).scalar()
            if aq_result == 2 and not quality_return_marked:
                logger.info(f"{distri_answer_id} 质检数据未全部完成返评，不发起质检")
                condition = WorkFlowMainProcessInstance.instance_target_logic == 2
            else:
                logger.info(f"{distri_answer_id} 发起质检，并禁止专家重评")
                condition = WorkFlowMainProcessInstance.instance_target_logic == 1
            new_session.commit()
            manual_process_operation(m_read_task_id, distri_answer_id, group_id, ques_id, target_type, condition, 1,
                                     manual_aq_id)
        else:
            logger.info(f"{distri_answer_id} 不发起质检，并禁止专家重评")
            condition = WorkFlowMainProcessInstance.instance_target_logic == 2
            new_session.commit()
            manual_process_operation(m_read_task_id, distri_answer_id, group_id, ques_id, target_type, condition)

    elif target_type == "bpmn:qualityNode":
        # 发起质检
        arbitrator_data = None
        # 如果有仲裁，获取仲裁的数据
        if manual_aq_id:
            arbitrator_info = new_session.query(ManualArbitrateQuality.aq_mark_score, ManualArbitrateQuality.aq_user_id,
                                                ManualArbitrateQuality.op_step_score) \
                .filter(ManualArbitrateQuality.manual_aq_id == manual_aq_id).first()
            if arbitrator_info:
                arbitrator_data = [{
                    "manual_mark_id": manual_mark_id_list,
                    "mark_score": float(arbitrator_info[0]) if arbitrator_info[0] is not None else None,
                    "mark_person_id": arbitrator_info[1]
                }]
                if ques_type_code == "G":
                    arbitrator_data[0]["op_step_score"] = arbitrator_info[2]
            # 将该条仲裁信息改为不可重评
            new_session.query(ManualArbitrateQuality).filter(
                ManualArbitrateQuality.manual_aq_id == manual_aq_id).update({
                ManualArbitrateQuality.can_aq_again: 0
            })

        new_session.commit()

        launch_quality(new_session, distri_answer_id, expert_data, arbitrator_data, 1, manual_mark_id_list,
                       m_read_task_id)

    elif target_type == "bpmn:qualityInspectionScore":
        # 质检判分
        condition = WorkFlowMainProcessInstance.instance_target_logic == aq_result
        new_session.commit()
        manual_process_operation(m_read_task_id, distri_answer_id, group_id, ques_id,
                                 source_node=target_type, condition=condition)

    elif target_type == "bpmn:userTask":
        # 质检不通过返回给专家评分
        logger.info(f"{distri_answer_id} 质检不通过返回给专家评分")

    elif target_type == "bpmn:endEvent":
        # 阅卷流程结束
        logger.info(f"{distri_answer_id} 阅卷流程结束，保存最终分数")
        # 将题目最终分数保存起来
        final_score, _, mark_process = statistic_final_score(new_session, m_read_task_id, distri_answer_id,
                                                             fetch_score_way, fetch_score_option, fetch_score_scope, True)

        update_data = {ManualDistributeAnswer.final_mark_score: final_score}
        if mark_process:
            update_data[ManualDistributeAnswer.answer_parse] = mark_process

        new_session.query(ManualDistributeAnswer).filter(and_(
            ManualDistributeAnswer.m_read_task_id == m_read_task_id,
            ManualDistributeAnswer.distri_answer_id == distri_answer_id)) \
            .update(update_data)

        if record_task_progress:
            logger.info("更新任务进度")
            update_manual_task_progress(new_session, m_read_task_id, expert_read_record_id, is_check_finish_state,
                                        distri_answer_id)
        # 更新评分信息状态
        new_session.query(ManualMark) \
            .filter(and_(ManualMark.m_read_task_id == m_read_task_id, ManualMark.distri_answer_id == distri_answer_id)) \
            .update({
            ManualMark.mark_state: 8,
            ManualMark.final_mark_time: format_now_time()
        })
        new_session.commit()
        logger.info(f"{distri_answer_id} 保存最终分数成功")


def get_chinese_fetch_way(fetch_score_way):
    # 1 为取最高分，2 为取最低分，3 为取平均分
    if fetch_score_way == 1:
        chinese_fetch_way = "取最高分"
    elif fetch_score_way == 2:
        chinese_fetch_way = "取最低分"
    elif fetch_score_way == 3:
        chinese_fetch_way = "取平均分"
    else:
        chinese_fetch_way = None
    return chinese_fetch_way


def get_chinese_fetch_option(fetch_score_option):
    if fetch_score_option == 1:
        chinese_fetch_option = "向下舍入"
    elif fetch_score_option == 2:
        chinese_fetch_option = "向上舍入"
    elif fetch_score_option == 3:
        chinese_fetch_option = "四舍五入"
    else:
        chinese_fetch_option = None
    return chinese_fetch_option


def manual_mark_record_instance(manual_mark_id, distri_answer_id, m_read_task_id, ques_id, curr_user_id,
                                expert_mark_score, mark_parse, op_step_score=None):
    if op_step_score is None:
        op_step_score = []
    new_manual_mark = ManualMark(manual_mark_id=manual_mark_id, distri_answer_id=distri_answer_id,
                                 m_read_task_id=m_read_task_id, ques_id=ques_id, mark_person_id=curr_user_id,
                                 mark_state=1, mark_count=1, expert_mark_score=expert_mark_score, mark_parse=mark_parse,
                                 op_step_score=op_step_score, final_mark_time=format_now_time(),
                                 expert_mark_time=format_now_time())
    return new_manual_mark


def add_manual_mark_record(new_session, distri_answer_id, m_read_task_id, ques_id, curr_user_id, expert_mark_score,
                           mark_parse, manual_mark_id=None, op_step_score=None):
    if op_step_score is None:
        op_step_score = []
    try:
        if not manual_mark_id:
            manual_mark_id = configs.snow_worker.get_id()
        new_manual_mark = manual_mark_record_instance(manual_mark_id, distri_answer_id, m_read_task_id, ques_id,
                                                      curr_user_id, expert_mark_score, mark_parse, op_step_score)
        new_session.add(new_manual_mark)
        new_session.commit()
        return True

    except Exception as e:
        if "1062" in str(e):
            logger.warning("id 主键重复，重新生成 id")
            new_session.rollback()
            manual_mark_id = configs.snow_worker.get_id()
            logger.info(f"重新生成 manual_mark_id: {manual_mark_id}")
            add_manual_mark_record(new_session, distri_answer_id, m_read_task_id, ques_id, curr_user_id,
                                   expert_mark_score, mark_parse, manual_mark_id, op_step_score)
        else:
            logger.error(e)
            return False


def update_manual_mark_record(new_session, expert_mark_score, mark_parse, manual_mark_id):
    mark_count = new_session.query(ManualMark.mark_count).filter(ManualMark.manual_mark_id == manual_mark_id).scalar()
    new_session.query(ManualMark).filter(ManualMark.manual_mark_id == manual_mark_id).update({
        ManualMark.expert_mark_score: expert_mark_score,
        ManualMark.mark_parse: mark_parse,
        ManualMark.mark_count: mark_count + 1,
        ManualMark.final_mark_time: format_now_time()
    })
    new_session.commit()


def get_manual_type(role_id: str):
    """
    1 表示阅卷任务、2 表示仲裁任务、3 表示质检任务
    """
    if role_id == "3":
        manual_type = 1
    elif role_id == "4":
        manual_type = 2
    elif role_id == "5":
        manual_type = 3
    else:
        manual_type = 0
    return manual_type


def has_final_mark_score(new_session, distri_answer_id):
    has_final_mark_score_count = new_session.query(ManualDistributeAnswer.distri_answer_id).filter(
        and_(ManualDistributeAnswer.distri_answer_id == distri_answer_id,
             ManualDistributeAnswer.final_mark_score.isnot(None))).count()
    if has_final_mark_score_count:
        return True
    return False


def get_last_aq_suggestion(aq_suggestion_concat, aq_updated_time_concat):
    """
    获取更新时间最大的对应索引的质检意见
    """
    quality_suggestion_list = [i for i in aq_suggestion_concat.split(",") if
                               i is not None] if aq_suggestion_concat is not None else aq_suggestion_concat
    quality_updated_time_list = [j for j in aq_updated_time_concat.split(",") if
                                 j is not None] if aq_updated_time_concat is not None else aq_updated_time_concat
    if quality_suggestion_list and quality_updated_time_list:
        try:
            quality_suggestion = quality_suggestion_list[
                quality_updated_time_list.index(max(quality_updated_time_list))]
        except IndexError:
            quality_suggestion = quality_updated_time_list[0]
    else:
        quality_suggestion = None
    return quality_suggestion
