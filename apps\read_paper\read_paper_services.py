import copy
import re
import time
import traceback
from decimal import Decimal
from itertools import permutations
from typing import Optional
import concurrent.futures

from sqlalchemy import select, and_, func
from settings import logger
from apps.models.models import StuAnswer, MarkRuleDetail, ExamPaper, ExamQuestion, Subject, QuesAnswerMark, \
    SameStuAnswerGroup, PaperDetail
from apps.read_paper.ai_services import subjective_mark_score_main, judge_mark_result
from apps.read_paper.common_services import struct_ques_with_img, clean_ques_text
from factory_apps.mysql_db.databases import session_depend
from helper.response_utils import ai_score_ambiguity, ai_score_ambiguity_code, ai_can_not_connect_code, \
    ai_can_not_connect
from settings import configs
from utils.utils import round_half_up, sum_with_precision


def type_a_and_b_mark(rule_detail, standard_answer, stu_answer, ques_score):
    """
    单选题和判断题判分逻辑
    :param rule_detail:
    :param standard_answer:
    :param stu_answer:
    :param ques_score:
    :return: 是否成功，评分，答案评析，评分状态，评分结果，出错原因
    """
    try:
        if stu_answer is None:
            mark_score = 0
            answer_parse = "考生未作答，得0分"
            mark_result = 2
        else:
            mark_type = rule_detail["type"]
            if "".join(standard_answer) == stu_answer:
                mark_score = ques_score
                mark_result = 1
                answer_parse = "考生答案与参考答案一致，得满分。"
            else:
                mark_result = 2
                if mark_type == 1:
                    mark_score = 0
                    answer_parse = "考生答案与参考答案不一致，不得分。"
                else:
                    mark_score = -ques_score
                    answer_parse = f"考生答案与参考答案不一致，扣{ques_score}分。"
        mark_state = 2
        return True, mark_score, answer_parse, mark_state, mark_result, None
    except:
        logger.error(traceback.format_exc())
        return False, None, None, 3, 6, "评分失败"


def type_c_mark(rule_detail: dict, standard_answer: list, stu_answer: Optional[str], ques_score: float):
    """
    多选题判分逻辑
    :param rule_detail:
    :param standard_answer:
    :param stu_answer:
    :param ques_score:
    :return: 是否成功，评分，答案评析，评分状态，评分结果，出错原因
    """
    # stu_answer = "A"
    # mark_type = 6
    # rule_detail["score"] = 1.5
    try:
        has_correct_answer = ["".join(perm) for r in range(1, len(standard_answer) + 1) for perm in
                              permutations(standard_answer, r)]
        # logger.info(f"少选、完全匹配选项列表: {has_correct_answer}")

        all_correct_answer_list = []
        for answer in has_correct_answer:
            if len(answer) == len(standard_answer):
                all_correct_answer_list.append(answer)
        # logger.info(f"完全匹配选项列表: {all_correct_answer_list}")

        mark_type = rule_detail["type"]

        if stu_answer in all_correct_answer_list:
            logger.info("完全匹配得满分")
            mark_score = ques_score
            mark_result = 1
            answer_parse = "考生答案与参考答案一致，得满分。"
        elif stu_answer not in has_correct_answer:
            # logger.info("错选")
            mark_result = 2
            if mark_type == 5:
                logger.info("错选扣全分")
                mark_score = -ques_score
                answer_parse = f"考生答案与参考答案不一致，扣{ques_score}分。"
            else:
                logger.info("错选不得分")
                mark_score = 0
                answer_parse = "考生答案与参考答案不一致，不得分。"
        else:
            logger.info("少选")
            mark_result = 3
            if mark_type == 1:
                logger.info("少选不得分")
                mark_score = 0
                answer_parse = "考生答案少选，不得分。"
            elif mark_type == 2:
                logger.info("少选得比例分")
                single_score = ques_score / len(standard_answer)
                mark_score = round_half_up(Decimal(str(single_score)) * len(stu_answer), 2)
                answer_parse = "考生答案少选，得比例分。"
            elif mark_type == 3:
                logger.info("少选得一半分")
                mark_score = round_half_up(ques_score / 2, 2)
                answer_parse = f"考生答案少选，得一半分，{mark_score}分。"
            elif mark_type == 4:
                logger.info("少选得指定分")
                mark_score = rule_detail["score"]
                answer_parse = f"考生答案少选，得指定分，{mark_score}分。"
            elif mark_type == 5:
                logger.info("少选扣全分")
                mark_score = -ques_score
                answer_parse = f"考生答案少选，扣{ques_score}分。"
            elif mark_type == 6:
                logger.info("少选扣指定分")
                mark_score = -rule_detail["score"]
                answer_parse = f"考生答案少选，扣{rule_detail['score']}分。"
            else:
                logger.info("类型不合法")
                mark_score = None
                answer_parse = None
        mark_state = 2
        return True, mark_score, answer_parse, mark_state, mark_result, None
    except:
        logger.error(traceback.format_exc())
        return False, None, None, 3, 4, "评分失败"


def get_no_mark_item(answer_id: Optional[str] = None, subject_condition=None):
    """获取未评分的作答信息"""
    new_session = next(session_depend())
    if answer_id:
        total_condition = StuAnswer.answer_id == answer_id
    elif subject_condition is not None:
        total_condition = and_(subject_condition, StuAnswer.mark_state == 1)
    else:
        total_condition = StuAnswer.mark_state == 1
    answer_stmt = select(StuAnswer, ExamPaper, ExamQuestion, PaperDetail, Subject.subject_name) \
        .join(ExamPaper, ExamPaper.paper_code == StuAnswer.paper_code) \
        .join(PaperDetail, PaperDetail.ques_id == StuAnswer.ques_id) \
        .join(ExamQuestion, ExamQuestion.ques_id == StuAnswer.ques_id) \
        .join(Subject, Subject.subject_id == StuAnswer.subject_id) \
        .where(total_condition).limit(1)
    result = new_session.execute(answer_stmt)
    finally_result = result.first()
    if not finally_result:
        return None, None, None, None, None
    else:
        answer, paper, ques, paper_detail, subject_name = finally_result
        return answer, paper, ques, paper_detail, subject_name


def get_no_mark_group(new_session, subject_condition=None):
    """获取未评分的作答分组信息"""
    all_answer_id_list, paper_code_list = [], []
    if subject_condition is not None:
        # total_condition = and_(subject_condition, SameStuAnswerGroup.mark_state == 1)
        # 放开未评分的条件限制
        total_condition = subject_condition
    else:
        # total_condition = SameStuAnswerGroup.mark_state == 1
        # 放开未评分的条件限制
        total_condition = True
    group_info = new_session.query(SameStuAnswerGroup.same_answer_group_id, PaperDetail.ques_score_list,
                                   func.group_concat(StuAnswer.answer_id), PaperDetail.paper_code) \
        .join(StuAnswer, StuAnswer.same_answer_group_id == SameStuAnswerGroup.same_answer_group_id) \
        .join(Subject, Subject.subject_id == SameStuAnswerGroup.subject_id) \
        .join(PaperDetail, PaperDetail.ques_id == SameStuAnswerGroup.ques_id) \
        .filter(total_condition) \
        .group_by(StuAnswer.same_answer_group_id, PaperDetail.ques_score_list, PaperDetail.paper_code) \
        .all()
    if not group_info:
        return all_answer_id_list

    for row in group_info:
        answer_id_list = [i for i in row[2].split(",")]
        ques_score_list = row[1]
        ques_score_list_length = len(ques_score_list)
        answer_id_by_ques_list = [answer_id_list[i: i + ques_score_list_length] for i in
                                  range(0, len(answer_id_list), ques_score_list_length)]
        all_answer_id_list.append(answer_id_by_ques_list)
        if row[3] not in paper_code_list:
            paper_code_list.append(row[3])

    return all_answer_id_list, paper_code_list


def get_all_no_mark_item_id(subject_condition=None):
    """
    获取未评分的作答信息的总数，用于计算进度
    """
    new_session = next(session_depend())
    if subject_condition is not None:
        total_condition = and_(subject_condition, StuAnswer.mark_state == 1)
    else:
        total_condition = StuAnswer.mark_state == 1
    await_mark_info = new_session.query(StuAnswer.answer_id) \
        .join(ExamPaper, ExamPaper.paper_code == StuAnswer.paper_code) \
        .join(ExamQuestion, ExamQuestion.ques_code == StuAnswer.ques_code) \
        .join(Subject, Subject.subject_id == StuAnswer.subject_id) \
        .filter(total_condition).all()
    if not await_mark_info:
        return []
    await_mark_item_id = [i[0] for i in await_mark_info]
    logger.info(f"待智能阅卷id列表，{await_mark_item_id}")
    return await_mark_item_id


def retry_read_paper(new_session, retry_count, same_answer_group_id, answer_id_item, execute_user_id, ooo_type):
    """
    AI 评分失败进行重评
    """
    new_session.query(SameStuAnswerGroup).filter(SameStuAnswerGroup.same_answer_group_id == same_answer_group_id) \
        .update({SameStuAnswerGroup.retry_count: retry_count + 1})
    new_session.commit()
    logger.info("AI 评分失败，进行重评")
    if ooo_type:
        ooo_mark_thread_control([answer_id_item], execute_user_id)
    else:
        start_read_paper([answer_id_item], None, None, False, 1, True)


def batch_update_mark_options(paper_code, ques_code, stu_answer, mark_state, mark_result, mark_score,
                              answer_parse: list, small_ques_order, profession_mark=False,
                              profession_parse=None, modify_reason=None, u_user_id=None, marked_answer_id=None,
                              same_answer_group_id=None):
    """批量修改得分和评分状态"""
    new_session = next(session_depend())

    try:
        if not profession_mark:
            update_dict = {
                StuAnswer.mark_state: mark_state,
                StuAnswer.mark_result: mark_result,
                StuAnswer.stu_score: mark_score,
                StuAnswer.answer_parse: answer_parse,
                StuAnswer.mark_fail_reason: None,
            }
        else:
            update_dict = {
                StuAnswer.mark_state: mark_state,
                StuAnswer.mark_result: mark_result,
                StuAnswer.stu_score: mark_score,
                StuAnswer.profession_parse: profession_parse,
                StuAnswer.modify_reason: modify_reason,
                StuAnswer.u_user_id: u_user_id
            }
        if not marked_answer_id and not same_answer_group_id:
            # 批量
            new_session.query(StuAnswer).filter(StuAnswer.paper_code == paper_code,
                                                StuAnswer.ques_code == ques_code,
                                                StuAnswer.stu_answer == stu_answer,
                                                StuAnswer.small_ques_order == small_ques_order
                                                ).update(update_dict)
            new_session.commit()
        elif not marked_answer_id and same_answer_group_id:
            # 同组
            new_session.query(StuAnswer).filter(StuAnswer.paper_code == paper_code,
                                                StuAnswer.ques_code == ques_code,
                                                StuAnswer.stu_answer == stu_answer,
                                                StuAnswer.small_ques_order == small_ques_order,
                                                StuAnswer.same_answer_group_id == same_answer_group_id,
                                                StuAnswer.mark_state != 2).update(update_dict)
            new_session.commit()

            max_small_ques_order = new_session.query(func.max(StuAnswer.small_ques_order)).filter(
                StuAnswer.same_answer_group_id == same_answer_group_id).scalar()
            if max_small_ques_order == small_ques_order:
                marked_info = new_session.query(StuAnswer.stu_score, StuAnswer.answer_parse).filter(
                    StuAnswer.same_answer_group_id == same_answer_group_id).order_by(StuAnswer.small_ques_order).all()
                stu_score_list, answer_parse_list = [], []
                whole_ques_score = 0
                if marked_info:
                    for i in marked_info:
                        whole_ques_score += i[0]
                        stu_score_list.append(str(i[0]))
                        answer_parse_list.append(i[1])
                    new_session.query(SameStuAnswerGroup).filter(
                        SameStuAnswerGroup.same_answer_group_id == same_answer_group_id).update({
                        SameStuAnswerGroup.mark_state: mark_state,
                        SameStuAnswerGroup.mark_result: mark_result,
                        SameStuAnswerGroup.stu_score: float(f"{configs.NEW_SPLIT_FLAG}".join(stu_score_list)),
                        SameStuAnswerGroup.answer_parse: f"{configs.NEW_SPLIT_FLAG}".join(
                            [parse[0] for parse in answer_parse_list]),
                        SameStuAnswerGroup.modify_reason: modify_reason,
                        SameStuAnswerGroup.u_user_id: u_user_id
                    })
                    new_session.commit()
        else:
            # 单个
            new_session.query(StuAnswer).filter(StuAnswer.answer_id == marked_answer_id).update(update_dict)
            new_session.commit()
        return True
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.error(f"批量修改得分和评分状态失败，{e}")
        return False


def handle_fail_item(wait_mark_id, error_msg, same_answer_group_id, manual_ai_data=None):
    """处理评分失败的情况"""
    try:
        if manual_ai_data:
            ai_distri_answer_id = manual_ai_data.get("ai_distri_answer_id")
            execute_user_id = manual_ai_data.get("execute_user_id")
            update_ai_distri_mark(ai_distri_answer_id, None, error_msg, execute_user_id)
        else:
            new_session = next(session_depend())
            # retry_info = new_session.query(SameStuAnswerGroup.retry_count).filter(
            #     SameStuAnswerGroup.same_answer_group_id == same_answer_group_id).first()
            # retry_count = retry_info.retry_count
            # if error_msg == ai_score_ambiguity_code and retry_count == 0:
            #     retry_read_paper(new_session, retry_count, same_answer_group_id, wait_mark_id, None,
            #                      False)
            # else:
            if error_msg == ai_score_ambiguity_code:
                error_msg = ai_score_ambiguity
                mark_state = 4
                mark_result = 5
            else:
                mark_state = 3
                mark_result = 6
            new_session.query(StuAnswer).filter(StuAnswer.answer_id == wait_mark_id).update({
                StuAnswer.mark_state: mark_state,
                StuAnswer.mark_result: mark_result,
                StuAnswer.mark_fail_reason: error_msg
            })
            new_session.query(SameStuAnswerGroup).filter(
                SameStuAnswerGroup.same_answer_group_id == same_answer_group_id).update({
                SameStuAnswerGroup.mark_state: mark_state,
                SameStuAnswerGroup.mark_result: 5,
                SameStuAnswerGroup.modify_reason: error_msg,
            })
            new_session.commit()
    except Exception as e:
        logger.error(f"处理评分失败的情况失败，{e}")


def update_ooo_single_answer_score(wait_mark_id, mark_score, answer_parse, mark_result, execute_user_id):
    """
    更新可乱序填空题单个空分数
    """
    new_session = next(session_depend())
    new_session.query(StuAnswer).filter(StuAnswer.answer_id == wait_mark_id).update({
        StuAnswer.stu_score: mark_score,
        StuAnswer.answer_parse: answer_parse,
        StuAnswer.mark_state: 2,
        StuAnswer.mark_result: mark_result,
        StuAnswer.u_user_id: execute_user_id
    })
    new_session.commit()


def create_logger_template(same_answer_group_id, ques_id, ques_type_code, ques_desc, ques_score, ques_choices,
                           standard_answer, small_ques_order, stu_answer, e_mark_rule, mark_point):
    log_template = f"""
{"-" * 50}
分组id： {same_answer_group_id}
试题id： {ques_id}
试题类型：{ques_type_code}
试题描述：{ques_desc}
试题分数：{ques_score}
试题选项：{ques_choices}
试题答案：{standard_answer}
小题号：{small_ques_order}
考生答案：{stu_answer}
评分规则：{e_mark_rule}
评分点：{mark_point}
{"-" * 50}
"""
    logger.info(log_template)


def add_new_ques_answer_mark(paper_code, ques_id, ques_code, subject_id, ques_type_code, small_ques_order, stu_answer,
                             mark_score, pending_review, answer_parse, mark_result, single_score_list=None):
    """
    将填空题的阅卷结果存入答案集
    """
    if single_score_list is None:
        single_score_list = []
    if ques_type_code == "D":
        new_session = next(session_depend())
        logger.info("将阅卷结果存入答案集")
        new_mark = QuesAnswerMark(mark_id=configs.snow_worker.get_id(), paper_code=paper_code, ques_id=ques_id,
                                  ques_code=ques_code, subject_id=subject_id, ques_type_code=ques_type_code,
                                  small_ques_order=str(small_ques_order), stu_answer=stu_answer,
                                  single_score_list=single_score_list, stu_score=str(mark_score),
                                  pending_review=pending_review, answer_parse=answer_parse, mark_result=mark_result)
        new_session.add(new_mark)
        new_session.commit()


def handle_read_result(flag, paper_code, ques_id, ques_code, subject_id, ques_type_code, small_ques_order, stu_answer,
                       mark_score, pending_review, answer_parse, mark_state, mark_result, wait_mark_id,
                       same_answer_group_id, error_msg, need_add_blank_mark_record=False, manual_ai_data=None,
                       ai_type=1, ooo_ques_type=False, is_mark_same_answer=False):
    """阅卷结果处理"""
    if type(answer_parse) != list:
        if answer_parse:
            answer_parse = [answer_parse]
        else:
            answer_parse = []

    if flag:
        logger.info(f"得分：{mark_score}，评析：{answer_parse}")
        if manual_ai_data:
            ai_distri_answer_id = manual_ai_data.get("ai_distri_answer_id")
            execute_user_id = manual_ai_data.get("execute_user_id")
            if ai_distri_answer_id:
                logger.info("保存人工阅卷 AI 评分结果")
                if ai_type == 1:
                    update_ai_distri_mark(ai_distri_answer_id, mark_score, answer_parse[0], execute_user_id)
                elif ai_type == 2:
                    update_ai_distri_arbitrate(ai_distri_answer_id, mark_score, answer_parse[0], execute_user_id)
                elif ai_type == 3:
                    update_ai_distri_quality(ai_distri_answer_id, mark_score, answer_parse[0], execute_user_id)
            else:
                # 智能阅卷可乱序填空题保存评分结果
                update_ooo_single_answer_score(wait_mark_id, mark_score, answer_parse, mark_result, execute_user_id)
        else:
            if is_mark_same_answer:
                logger.info(f"更新同组id {same_answer_group_id} 的评分")
                batch_update_mark_options(paper_code, ques_code, stu_answer, mark_state, mark_result, mark_score,
                                          answer_parse, small_ques_order, same_answer_group_id=same_answer_group_id)
            else:
                if not ooo_ques_type:
                    logger.info("不是填空题答案可乱序题型，批量修改得分和评分状态")
                    batch_update_mark_options(paper_code, ques_code, stu_answer, mark_state, mark_result, mark_score,
                                              answer_parse, small_ques_order)
                else:
                    logger.info("是填空题答案可乱序题型，只修改本空的得分和评分状态")
                    batch_update_mark_options(paper_code, ques_code, stu_answer, mark_state, mark_result, mark_score,
                                              answer_parse, small_ques_order, marked_answer_id=wait_mark_id)
        if need_add_blank_mark_record:
            add_new_ques_answer_mark(paper_code, ques_id, ques_code, subject_id, ques_type_code, small_ques_order,
                                     stu_answer, mark_score, pending_review, answer_parse, mark_result)
    else:
        # 评分失败处理逻辑
        handle_fail_item(wait_mark_id, error_msg, same_answer_group_id, manual_ai_data)


def get_answer_mark_info(same_answer_group_id, paper_code, ques_id, small_ques_order, stu_answer):
    """
    先到 答案集 里查看有无该答案
    """
    new_session = next(session_depend())
    mark_info = new_session.query(QuesAnswerMark.stu_score, QuesAnswerMark.single_score_list,
                                  QuesAnswerMark.answer_parse, QuesAnswerMark.pending_review,
                                  QuesAnswerMark.mark_result).filter(
        and_(QuesAnswerMark.paper_code == paper_code, QuesAnswerMark.ques_code == ques_id,
             QuesAnswerMark.small_ques_order == small_ques_order, QuesAnswerMark.stu_answer == stu_answer)).first()
    if mark_info:
        logger.info(f"答案集存在分组id为 {same_answer_group_id} 的答案评分结果，直接获取该结果")
        return mark_info
    return None


def handle_subjective_thread(ques_id, paper_code, ques_code, subject_id, subject_name, ques_desc,
                             ques_type_code, standard_answer, ques_score_list, stu_answer, small_ques_order,
                             e_mark_rule, mark_point, mark_rule_detail, wait_mark_id, same_answer_group_id,
                             manual_ai_data, ai_type, ooo_ques_type, is_mark_same_answer, ques_material,
                             ques_images_dict):
    if ques_type_code == "D" and ai_type == 1:
        # 填空题先到 答案集 里查看有无该答案，有则直接获取，没有发给大模型，获得结果后新增答案集
        mark_info = get_answer_mark_info(same_answer_group_id, paper_code, ques_id, small_ques_order, stu_answer)
        if mark_info:
            logger.info("答案集存在该题答案评分结果，直接获取该结果")
            marked_score, _, marked_parse, pending_review, mark_result = mark_info
            handle_read_result(True, paper_code, ques_id, ques_code, subject_id, ques_type_code, small_ques_order,
                               stu_answer, marked_score, pending_review, marked_parse, 2, mark_result,
                               wait_mark_id, same_answer_group_id, None, False,
                               manual_ai_data, ai_type, ooo_ques_type, is_mark_same_answer)
            return

    rule = mark_rule_detail["type"]
    mark_count = mark_rule_detail["count"]
    flag, mark_score, answer_parse, _, mark_state, mark_result, error_msg = subjective_mark_score_main(
        same_answer_group_id, ques_id, subject_name, ques_desc, ques_type_code, standard_answer,
        ques_score_list, stu_answer, rule, mark_count, small_ques_order, e_mark_rule, mark_point, ooo_ques_type,
        ques_material=ques_material, ques_images_dict=ques_images_dict)
    if error_msg:
        need_add_blank_mark_record = False
    else:
        need_add_blank_mark_record = True

    handle_read_result(flag, paper_code, ques_id, ques_code, subject_id, ques_type_code, small_ques_order, stu_answer,
                       mark_score, 0, answer_parse, mark_state, mark_result,
                       wait_mark_id, same_answer_group_id, error_msg, need_add_blank_mark_record, manual_ai_data,
                       ai_type, ooo_ques_type, is_mark_same_answer)
    return True


def ooo_duplicate_answer_handle(new_session, ooo_group_index, paper_code, ques_code, stu_answer, allow_exam_num,
                                wait_mark_id, manual_ai_data=False):
    """
    检查是否存在填空题乱序的题型，有则进行答案可乱序填空题处理
    """
    if ooo_group_index != 0:
        ooo_answer_info = new_session.query(StuAnswer.answer_id).filter(
            and_(StuAnswer.paper_code == paper_code, StuAnswer.ques_code == ques_code,
                 StuAnswer.ques_type_code == "D", StuAnswer.ooo_group_index == ooo_group_index,
                 StuAnswer.stu_answer == stu_answer, StuAnswer.allow_exam_num == allow_exam_num)).all()

        if ooo_answer_info and len(ooo_answer_info) > 1:
            sample_answer_id_list = [i[0] for i in ooo_answer_info]
            sample_answer_id_list.remove(wait_mark_id)
            if not manual_ai_data:
                new_session.query(StuAnswer).filter(StuAnswer.answer_id.in_(sample_answer_id_list)).update({
                    StuAnswer.mark_state: 2,
                    StuAnswer.stu_score: 0,
                    # StuAnswer.ooo_mark_score: 0,
                    StuAnswer.mark_result: 2,
                    StuAnswer.answer_parse: ["答案重复，不得分"]
                })
            return sample_answer_id_list
    return []


def get_next_execute_answer(answer_id_index, answer_id_list, subject_condition):
    if answer_id_list:
        # 查询下一个answer_id的作答信息
        if answer_id_index >= len(answer_id_list):
            answer, paper, ques, paper_detail, subject_name = None, None, None, None, None
        else:
            answer_id = answer_id_list[answer_id_index]
            answer, paper, ques, paper_detail, subject_name = get_no_mark_item(answer_id=answer_id)
    else:
        # 查询下一个未评分的作答信息
        if subject_condition is not None:
            answer, paper, ques, paper_detail, subject_name = get_no_mark_item(subject_condition=subject_condition)
        else:
            answer, paper, ques, paper_detail, subject_name = get_no_mark_item()
    return answer, paper, ques, paper_detail, subject_name


def handle_not_subjective_ques(new_session, same_answer_group_id, paper_code, ques_id, ques_code,
                               ques_type_code, mark_rule_detail, standard_answer, stu_answer, ques_score,
                               is_ai_manual=False):
    """
    新版客观题智能评分
    """
    if ques_type_code == "A" or ques_type_code == "B":
        flag, mark_score, answer_parse, mark_state, mark_result, error_msg = type_a_and_b_mark(
            mark_rule_detail, standard_answer, stu_answer, ques_score)

    else:
        flag, mark_score, answer_parse, mark_state, mark_result, error_msg = type_c_mark(
            mark_rule_detail, standard_answer, stu_answer, ques_score)

    if not is_ai_manual:
        if type(answer_parse) != list:
            if answer_parse:
                answer_parse = [answer_parse]
            else:
                answer_parse = []

        update_dict = {
            StuAnswer.mark_state: mark_state,
            StuAnswer.mark_result: mark_result,
            StuAnswer.stu_score: mark_score,
            StuAnswer.answer_parse: answer_parse,
            StuAnswer.mark_fail_reason: None
        }

        new_session.query(StuAnswer).filter(
            StuAnswer.ques_id == ques_id,
            StuAnswer.paper_code == paper_code,
            StuAnswer.ques_code == ques_code,
            StuAnswer.stu_answer == stu_answer,
            StuAnswer.same_answer_group_id == same_answer_group_id,
            StuAnswer.mark_state != 2).update(update_dict)

    return flag, ques_score, same_answer_group_id, mark_score, answer_parse, error_msg


def get_ai_mark_score(same_answer_id_item: list, curr_user_id: str):
    """
    智能阅卷，并统计该题最终得分
    现版本全部改为 按题评分
    """
    new_session = next(session_depend())
    # 按题评分
    flag, ques_score, same_answer_group_id, stu_score, answer_parse, error_msg = ooo_all_space_mark(new_session,
                                                                                                    same_answer_id_item,
                                                                                                    curr_user_id)

    # retry_count = new_session.query(SameStuAnswerGroup.retry_count).filter(
    #     SameStuAnswerGroup.same_answer_group_id == same_answer_group_id).scalar()
    #
    # if error_msg == ai_score_ambiguity_code and retry_count == 0:
    #     retry_read_paper(new_session, retry_count, same_answer_group_id, same_answer_id_item, curr_user_id, True)
    # else:
    if flag:
        real_mark_result = judge_mark_result(stu_score, ques_score)
        mark_state = 2
        answer_parse = f"{configs.NEW_SPLIT_FLAG}".join(answer_parse)

        new_session.query(SameStuAnswerGroup).filter(
            SameStuAnswerGroup.same_answer_group_id == same_answer_group_id).update({
            SameStuAnswerGroup.mark_state: mark_state,
            SameStuAnswerGroup.mark_result: real_mark_result,
            SameStuAnswerGroup.stu_score: stu_score if stu_score is not None else None,
            SameStuAnswerGroup.answer_parse: answer_parse,
            SameStuAnswerGroup.mark_fail_reason: None,
            SameStuAnswerGroup.u_user_id: curr_user_id
        })
        new_session.commit()
        return True
    else:
        mark_state = 3
        mark_result = 6
        if error_msg == ai_score_ambiguity_code:
            error_msg = ai_score_ambiguity
            mark_state = 4
            mark_result = 5
        new_session.query(SameStuAnswerGroup).filter(
            SameStuAnswerGroup.same_answer_group_id == same_answer_group_id).update({
            SameStuAnswerGroup.mark_state: mark_state,
            SameStuAnswerGroup.mark_result: mark_result,
            SameStuAnswerGroup.stu_score: None,
            SameStuAnswerGroup.answer_parse: None,
            SameStuAnswerGroup.mark_fail_reason: error_msg,
            SameStuAnswerGroup.u_user_id: curr_user_id
        })
        new_session.commit()
        return False


def start_read_paper(answer_id_list: Optional[list] = None, subject_condition=None, manual_ai_data=None,
                     await_process=False, ai_type=1, is_mark_same_answer=False):
    """
    智能阅卷主流程
    :param answer_id_list: 作答答案 id 列表
    :param subject_condition: 科目过滤条件
    :param manual_ai_data: 人员阅卷里的 AI 助手传过来的数据
    :param await_process: False 不等待，开启线程；True 等待，不开启线程
    :param ai_type: 1 表示 AI 评分，2 表示 AI 仲裁，3 表示 AI 质检
    :param is_mark_same_answer: 是否将同组的作答一起评分
    :return:
    """
    answer_id_index = 0
    all_sample_answer_id_list = []
    if answer_id_list:
        answer_id = answer_id_list[0]
        answer, paper, ques, paper_detail, subject_name = get_no_mark_item(answer_id=answer_id)
    else:
        answer, paper, ques, paper_detail, subject_name = get_no_mark_item(subject_condition=subject_condition)

    new_session = next(session_depend())
    # 创建ThreadPoolExecutor对象，并限制线程数量
    futures_list = []
    wait_mark_id_list = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=configs.AI_REQ_THREAD_COUNT) as executor:
        while answer:
            wait_mark_id = answer.answer_id
            wait_mark_id_list.append(wait_mark_id)
            stu_answer = answer.stu_answer
            small_ques_order = answer.small_ques_order
            paper_code = paper.paper_code
            subject_id = answer.subject_id
            ques_code = answer.ques_code
            ques_type_code = answer.ques_type_code
            e_mark_rule = ques.e_mark_rule
            allow_exam_num = answer.allow_exam_num
            ooo_group_index = answer.ooo_group_index
            same_answer_group_id = answer.same_answer_group_id
            ooo_ques_type = False
            if ooo_group_index != 0:
                ooo_ques_type = True

            # 如果该答案属于填空题答案可乱序的题型，且该答案被判定为重复答案
            if wait_mark_id in all_sample_answer_id_list:
                if manual_ai_data:
                    # 如果是人工阅卷里的评分 AI
                    handle_read_result(True, paper_code, ques_id, ques_code, subject_id, ques_type_code,
                                       small_ques_order,
                                       stu_answer, 0, 0, "答案重复，不得分", 2, 2,
                                       wait_mark_id, same_answer_group_id, None, False, manual_ai_data, ai_type,
                                       ooo_ques_type, is_mark_same_answer)
                    answer_id_index += 1
                    answer, paper, ques, paper_detail, subject_name = get_next_execute_answer(answer_id_index,
                                                                                              answer_id_list,
                                                                                              subject_condition)
                    if not answer:
                        break
                    continue
                else:
                    continue

            # 检查是否存在填空题乱序的题型，有则进行答案可乱序填空题处理
            sample_answer_id_list = ooo_duplicate_answer_handle(new_session, ooo_group_index, paper_code, ques_code,
                                                                stu_answer, allow_exam_num, wait_mark_id,
                                                                manual_ai_data)
            if sample_answer_id_list:
                all_sample_answer_id_list.extend(sample_answer_id_list)

            if not stu_answer:
                # 答案为空时直接判分
                handle_read_result(True, paper_code, ques_id, ques_code, subject_id, ques_type_code, small_ques_order,
                                   stu_answer, 0, 0, "考生未作答，得0分", 2, 2,
                                   wait_mark_id, same_answer_group_id, None, False, manual_ai_data, ai_type,
                                   ooo_ques_type, is_mark_same_answer)
            else:
                # 获取评分规则
                rule_id = paper.mark_rule_id

                rule_detail = new_session.query(MarkRuleDetail) \
                    .filter(MarkRuleDetail.parent_rule_id == rule_id,
                            MarkRuleDetail.ques_type_code == ques_type_code).first()

                mark_rule_detail = rule_detail.mark_rule_detail

                ques_desc = ques.ques_desc["text"]
                standard_answer = ques.standard_answer
                ques_choices = ques.ques_choices
                ques_score = answer.score
                ques_id = ques.ques_id
                mark_point = ques.ques_mark_point

                ques_desc = clean_ques_text(ques_desc)
                create_logger_template(same_answer_group_id, ques_id, ques_type_code, ques_desc, ques_score,
                                       ques_choices, standard_answer, small_ques_order, stu_answer, e_mark_rule,
                                       mark_point)

                # 分题型阅卷
                if ques_type_code == "A" or ques_type_code == "B":
                    flag, mark_score, answer_parse, mark_state, mark_result, error_msg = type_a_and_b_mark(
                        mark_rule_detail, standard_answer, stu_answer, ques_score)

                    handle_read_result(flag, paper_code, ques_id, ques_code, subject_id, ques_type_code,
                                       small_ques_order,
                                       stu_answer, mark_score, 0, answer_parse, mark_state, mark_result,
                                       wait_mark_id, same_answer_group_id, error_msg, False, manual_ai_data, ai_type,
                                       ooo_ques_type, is_mark_same_answer)

                elif ques_type_code == "C":
                    flag, mark_score, answer_parse, mark_state, mark_result, error_msg = type_c_mark(
                        mark_rule_detail, standard_answer, stu_answer, ques_score)

                    handle_read_result(flag, paper_code, ques_id, ques_code, subject_id, ques_type_code,
                                       small_ques_order, stu_answer, mark_score, 0, answer_parse, mark_state, mark_result,
                                       wait_mark_id, same_answer_group_id, error_msg, False,
                                       manual_ai_data, ai_type, ooo_ques_type, is_mark_same_answer)

                elif ques_type_code == "D" or ques_type_code == "E":
                    """开启主观题阅卷线程"""
                    ques_score_list = ques.ques_score_list
                    # 如果有父id，说明为组合题，需要传试题材料
                    parent_ques_id = ques.parent_ques_id
                    parent_ques_desc, ques_material = None, None
                    if parent_ques_id:
                        parent_ques_desc = new_session.query(ExamQuestion.ques_desc).filter(
                            ExamQuestion.ques_id == parent_ques_id).scalar()
                        ques_material = parent_ques_desc.get("text", "") if parent_ques_desc else ""
                        ques_material = clean_ques_text(ques_material)

                    ques_images_dict = struct_ques_with_img(parent_ques_desc, ques.ques_desc)

                    args = (ques_id, paper_code, ques_code, subject_id, subject_name, ques_desc, ques_type_code,
                            standard_answer, ques_score_list, stu_answer, small_ques_order, e_mark_rule, mark_point,
                            mark_rule_detail, wait_mark_id, same_answer_group_id, manual_ai_data, ai_type,
                            ooo_ques_type, is_mark_same_answer, ques_material, ques_images_dict)
                    if await_process:
                        handle_subjective_thread(*args)
                    else:
                        # 提交任务给线程池执行
                        futures_list.append(executor.submit(handle_subjective_thread, *args))
                else:
                    logger.warning(f"暂不支持 {ques_type_code} 该题型")
                    continue

            answer_id_index += 1
            answer, paper, ques, paper_detail, subject_name = get_next_execute_answer(answer_id_index, answer_id_list,
                                                                                      subject_condition)
            if not answer:
                break

        # 捕获线程异常
        for future in concurrent.futures.as_completed(futures_list):
            try:
                result = future.result()
                logger.info(f"普通阅卷线程结果：{result}")
            except Exception as e:
                traceback.print_exc()
                logger.error(f"Exception: {e}")


def ooo_mark_thread_control(ooo_answer_id_list: list, curr_user_id: str):
    """
    智能阅卷线程控制
    """
    with concurrent.futures.ThreadPoolExecutor(max_workers=configs.AI_REQ_THREAD_COUNT) as executor:
        futures = [executor.submit(get_ai_mark_score, same_answer_id_item, curr_user_id) for
                   same_answer_id_item in ooo_answer_id_list]

        # 捕获线程异常
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                logger.info(f"智能阅卷线程结果：{result}")
            except Exception as e:
                traceback.print_exc()
                logger.info("智能阅卷线程结果：False")
                logger.error(f"Exception: {e}")


def handle_all_space_read_result(new_session, flag, subject_id, ques_id, paper_code, ques_code, ques_type_code,
                                 same_answer_id_item, small_ques_order, mark_score, stu_answer, answer_parse,
                                 mark_state, mark_result, single_score_list, same_answer_group_id, error_msg,
                                 need_add_blank_mark_record, u_user_id, ques_score_list):
    raw_answer_parse = copy.deepcopy(answer_parse)
    for index, _ in enumerate(same_answer_id_item):
        if flag:
            if ques_type_code == "D":
                if not single_score_list or (len(same_answer_id_item) != len(single_score_list)):
                    mark_state = 3
                    update_dict = {
                        StuAnswer.mark_state: mark_state,
                        StuAnswer.mark_result: 6,
                        StuAnswer.mark_fail_reason: error_msg,
                        StuAnswer.stu_score: None,
                        StuAnswer.answer_parse: [],
                        StuAnswer.u_user_id: u_user_id
                    }
                else:
                    stu_score = single_score_list[index]

                    if isinstance(raw_answer_parse, list):
                        try:
                            answer_parse = [raw_answer_parse[index]] if raw_answer_parse else []
                        except:
                            answer_parse = answer_parse
                    else:
                        answer_parse = [answer_parse]
                    update_dict = {
                        StuAnswer.mark_state: mark_state,
                        StuAnswer.mark_result: mark_result,
                        StuAnswer.stu_score: stu_score,
                        StuAnswer.answer_parse: answer_parse,
                        StuAnswer.mark_fail_reason: None,
                        StuAnswer.u_user_id: u_user_id
                    }
            else:
                stu_score = mark_score
                update_dict = {
                    StuAnswer.mark_state: mark_state,
                    StuAnswer.mark_result: mark_result,
                    StuAnswer.stu_score: stu_score,
                    StuAnswer.answer_parse: answer_parse,
                    StuAnswer.mark_fail_reason: None,
                    StuAnswer.u_user_id: u_user_id
                }
        else:
            if error_msg == ai_score_ambiguity_code:
                mark_state = 4
                mark_result = 5
                if ques_type_code == "D":
                    stu_score = single_score_list[index]
                    space_score = float(ques_score_list[index])
                    if stu_score > space_score:
                        mark_state = 4
                        mark_result = 5
                        mark_fail_reason = ai_score_ambiguity
                    else:
                        mark_fail_reason = error_msg
                else:
                    mark_fail_reason = ai_score_ambiguity
            else:
                mark_state = 3
                mark_result = 6
                mark_fail_reason = error_msg

            update_dict = {
                StuAnswer.mark_state: mark_state,
                StuAnswer.mark_result: mark_result,
                StuAnswer.mark_fail_reason: mark_fail_reason,
                StuAnswer.stu_score: None,
                StuAnswer.answer_parse: [],
                StuAnswer.u_user_id: u_user_id
            }
        if ques_type_code == "D":
            condition = and_(StuAnswer.paper_code == paper_code, StuAnswer.ques_code == ques_code,
                             StuAnswer.ques_id == ques_id, StuAnswer.same_answer_group_id == same_answer_group_id,
                             StuAnswer.small_ques_order == index + 1)
        else:
            condition = and_(StuAnswer.paper_code == paper_code, StuAnswer.ques_code == ques_code,
                             StuAnswer.ques_id == ques_id, StuAnswer.same_answer_group_id == same_answer_group_id)
        try:
            new_session.query(StuAnswer).filter(condition).update(update_dict)
        except Exception as e:
            if "Deadlock" in str(e):
                time.sleep(30)
                new_session.query(StuAnswer).filter(condition).update(update_dict)

    if mark_state == 2 and need_add_blank_mark_record and ques_type_code == "D":
        add_new_ques_answer_mark(paper_code, ques_id, ques_code, subject_id, ques_type_code, small_ques_order,
                                 stu_answer, mark_score, 0, raw_answer_parse, mark_result, single_score_list)


def ooo_all_space_mark(new_session, same_answer_id_item, curr_user_id):
    """
    新版智能阅卷评分处理逻辑
    """
    # 修改评分状态为 进行中
    new_session.query(StuAnswer).filter(StuAnswer.answer_id.in_(same_answer_id_item)).update(
        {StuAnswer.mark_state: 5})
    new_session.commit()

    answer_id = same_answer_id_item[0]
    answer, paper, ques, paper_detail, subject_name = get_no_mark_item(answer_id=answer_id)

    paper_code = paper.paper_code
    subject_id = answer.subject_id
    ques_code = answer.ques_code
    e_mark_rule = ques.e_mark_rule
    ques_id = ques.ques_id
    parent_ques_id = ques.parent_ques_id
    ques_type_code = ques.ques_type_code
    ques_choices = ques.ques_choices
    same_answer_group_id = answer.same_answer_group_id
    ques_desc = ques.ques_desc["text"]
    standard_answer = ques.standard_answer
    ques_score_list = paper_detail.ques_score_list
    mark_point = ques.ques_mark_point
    ooo_group_index = answer.ooo_group_index
    ooo_ques_type = False
    out_of_order_group = []
    if ooo_group_index != 0:
        ooo_ques_type = True
        out_of_order_group = ques.d_out_of_order_group

    mark_rule_detail = new_session.query(MarkRuleDetail.mark_rule_detail) \
        .filter(MarkRuleDetail.parent_rule_id == paper.mark_rule_id,
                MarkRuleDetail.ques_type_code == ques_type_code).first()[0]

    rule = mark_rule_detail["type"]
    mark_count = mark_rule_detail.get("count")

    if ques_type_code == "D":
        stu_answer_info = new_session.query(StuAnswer.stu_answer).filter(StuAnswer.answer_id.in_(same_answer_id_item)) \
            .order_by(StuAnswer.small_ques_order).all()
        stu_answer_list = [i[0] for i in stu_answer_info]
    else:
        stu_answer_info = new_session.query(StuAnswer.stu_answer).filter(StuAnswer.answer_id.in_(same_answer_id_item)) \
            .order_by(StuAnswer.small_ques_order).first()
        stu_answer_list = [stu_answer_info[0]]
    if not any(stu_answer_list):
        # 如果 stu_answer_list 都是 None
        stu_answer = None
    else:
        stu_answer = f"{configs.NEW_SPLIT_FLAG}".join(stu_answer_list)

    ques_score = sum_with_precision(ques_score_list)
    small_ques_order = f"{configs.NEW_SPLIT_FLAG}".join([str(i) for i in range(1, len(same_answer_id_item) + 1)])

    ques_desc = clean_ques_text(ques_desc)
    create_logger_template(same_answer_group_id, ques_id, ques_type_code, ques_desc, ques_score, ques_choices,
                           standard_answer, small_ques_order, stu_answer, e_mark_rule, mark_point)

    same_answer_id_item_length = len(same_answer_id_item)

    try:
        if ques_type_code not in ["D", "E"]:
            # 非主观题处理逻辑
            flag, ques_score, same_answer_group_id, mark_score, answer_parse, error_msg = handle_not_subjective_ques(
                new_session, same_answer_group_id, paper_code, ques_id, ques_code, ques_type_code, mark_rule_detail,
                standard_answer, stu_answer, ques_score)
            new_session.commit()
            return flag, ques_score, same_answer_group_id, mark_score, answer_parse, error_msg

        elif ques_type_code == "G":
            logger.info("调用操作题评分引擎评分")
            pass

        else:
            # 主观题处理逻辑
            if ques_type_code == "D":
                # 填空题有评分集的逻辑
                mark_info = get_answer_mark_info(same_answer_group_id, paper_code, ques_id, small_ques_order,
                                                 stu_answer)
                if mark_info:
                    # 答案集存在该题答案评分结果，直接获取该结果
                    mark_score, single_score_list, answer_parse, pending_review, mark_result = mark_info

                    handle_all_space_read_result(new_session, True, subject_id, ques_id, paper_code, ques_code,
                                                 ques_type_code, same_answer_id_item, small_ques_order, mark_score,
                                                 stu_answer, answer_parse, 2, mark_result, single_score_list,
                                                 same_answer_group_id, None, False,
                                                 curr_user_id, ques_score_list)
                    new_session.commit()
                    return True, ques_score, same_answer_group_id, mark_score, answer_parse, None

            # 如果有父id，说明为组合题，需要传试题材料
            parent_ques_desc, ques_material = None, None
            if parent_ques_id:
                parent_ques_desc = new_session.query(ExamQuestion.ques_desc).filter(
                    ExamQuestion.ques_id == parent_ques_id).scalar()
                ques_material = parent_ques_desc.get("text", "") if parent_ques_desc else ""
                ques_material = clean_ques_text(ques_material)

            ques_images_dict = struct_ques_with_img(parent_ques_desc, ques.ques_desc)

            args = [same_answer_group_id, ques_id, subject_name, ques_desc, ques_type_code, standard_answer,
                    ques_score_list, stu_answer_list, rule, mark_count, None, e_mark_rule, mark_point, ooo_ques_type, 2,
                    ques_material, out_of_order_group, ques_images_dict]
            flag, mark_score, answer_parse, single_score_list, mark_state, mark_result, error_msg = subjective_mark_score_main(
                *args)
            print(flag, mark_score, answer_parse, single_score_list, mark_state, mark_result, error_msg)

            if error_msg:
                answer_parse = [error_msg for _ in range(same_answer_id_item_length)]
                single_score_list = [0 for _ in range(same_answer_id_item_length)]

                if error_msg == 100:
                    answer_parse = ["考生答案与参考答案一致，得满分。" for _ in range(same_answer_id_item_length)]
                need_add_blank_mark_record = False
            else:
                need_add_blank_mark_record = True

            if ques_type_code == "D":
                if error_msg == ai_can_not_connect:
                    answer_parse = [error_msg for _ in range(same_answer_id_item_length)]
                elif not single_score_list or (same_answer_id_item_length != len(single_score_list)):
                    mark_score = None
                    error_msg = "AI 返回格式异常"
                    answer_parse = [error_msg for _ in range(len(same_answer_id_item))]
                else:
                    for index, single_score in enumerate(single_score_list):
                        if float(single_score) > float(ques_score_list[index]):
                            flag = False
                            mark_state = 4
                            mark_result = 5
                            mark_score = None
                            error_msg = ai_score_ambiguity
                            answer_parse = [error_msg for _ in range(same_answer_id_item_length)]
                            break

    except Exception as e:
        flag = False
        mark_state = 3
        mark_result = 6
        mark_score = None
        error_msg = "程序异常"
        answer_parse = ["程序异常" for _ in range(same_answer_id_item_length)]
        need_add_blank_mark_record = False
        single_score_list = []
        logger.error(f"程序异常：{e}")
        # traceback.print_exc()
    handle_all_space_read_result(new_session, flag, subject_id, ques_id, paper_code, ques_code, ques_type_code,
                                 same_answer_id_item, small_ques_order, mark_score, stu_answer, answer_parse,
                                 mark_state, mark_result, single_score_list, same_answer_group_id, error_msg,
                                 need_add_blank_mark_record, curr_user_id, ques_score_list)
    return flag, ques_score, same_answer_group_id, mark_score, answer_parse, error_msg


def check_mark_rule(new_session, paper_code_list):
    """
    检查要进行智能阅卷的作答信息所在的试卷是否绑定了评分规则
    """
    logger.info("检查评分规则")
    # paper_id_list, paper_id_set = [], set()
    # all_answer_id_by_ques_list = [i for sub_list in answer_id_list for i in sub_list]
    # all_answer_id_list = [i for sub_list in all_answer_id_by_ques_list for i in sub_list]
    # paper_info = new_session.query(StuAnswer.paper_id).filter(StuAnswer.answer_id.in_(all_answer_id_list)).all()
    # if paper_info:
    #     for i in paper_info:
    #         paper_id_set.add(i[0])
    # paper_id_list = list(paper_id_set)
    # 查询是否有评分规则
    mark_rule_info = new_session.query(ExamPaper.paper_name, ExamPaper.mark_rule_id).filter(
        ExamPaper.paper_code.in_(paper_code_list))
    if not mark_rule_info:
        return False, "所选作答信息的试卷暂未绑定评分规则"

    paper_name_list = []
    for item in mark_rule_info:
        paper_name, mark_rule_id = item
        if not mark_rule_id:
            paper_name_list.append(paper_name)
    if paper_name_list:
        return False, f"{'，'.join(paper_name_list)} 暂未绑定评分规则"
    logger.info("检查评分规则成功")
    return True, None


def ai_manual_all_space_mark(stu_answer_id, manual_ai_data):
    """
    新版人工阅卷 AI 角色评分处理逻辑
    """
    new_session = next(session_depend())
    answer, paper, ques, paper_detail, subject_name = get_no_mark_item(answer_id=stu_answer_id)

    paper_code = paper.paper_code
    ques_code = answer.ques_code
    e_mark_rule = ques.e_mark_rule
    ques_id = ques.ques_id
    parent_ques_id = ques.parent_ques_id
    ques_type_code = ques.ques_type_code
    ques_choices = ques.ques_choices
    same_answer_group_id = answer.same_answer_group_id
    ques_desc = ques.ques_desc["text"]
    standard_answer = ques.standard_answer
    ques_score_list = paper_detail.ques_score_list
    mark_point = ques.ques_mark_point
    ooo_group_index = answer.ooo_group_index
    ooo_ques_type = False
    out_of_order_group = []
    if ooo_group_index != 0:
        ooo_ques_type = True
        out_of_order_group = ques.d_out_of_order_group

    mark_rule_detail = new_session.query(MarkRuleDetail.mark_rule_detail) \
        .filter(MarkRuleDetail.parent_rule_id == paper.mark_rule_id,
                MarkRuleDetail.ques_type_code == ques_type_code).first()[0]

    rule = mark_rule_detail["type"]
    mark_count = mark_rule_detail.get("count")

    stu_answer_list = manual_ai_data["stu_answer"]
    stu_answer = f"{configs.NEW_SPLIT_FLAG}".join(stu_answer_list)

    ques_score = sum_with_precision(ques_score_list)
    small_ques_order = 1

    ques_desc = clean_ques_text(ques_desc)
    create_logger_template(same_answer_group_id, ques_id, ques_type_code, ques_desc, ques_score, ques_choices,
                           standard_answer, small_ques_order, stu_answer, e_mark_rule, mark_point)

    stu_answer_id_list_length = 1
    try:
        if ques_type_code in ["A", "B", "C"]:
            # 客观题处理逻辑
            flag, ques_score, same_answer_group_id, mark_score, answer_parse, error_msg = handle_not_subjective_ques(
                new_session, same_answer_group_id, paper_code, ques_id, ques_code, ques_type_code,
                mark_rule_detail, standard_answer, stu_answer, ques_score, is_ai_manual=True)
            return flag, ques_score, same_answer_group_id, mark_score, answer_parse, error_msg
        else:
            # 主观题处理逻辑
            # 如果有父id，说明为组合题，需要传试题材料
            parent_ques_desc, ques_material = None, None
            if parent_ques_id:
                parent_ques_desc = new_session.query(ExamQuestion.ques_desc).filter(
                    ExamQuestion.ques_id == parent_ques_id).scalar()
                ques_material = parent_ques_desc.get("text", "") if parent_ques_desc else ""
                ques_material = clean_ques_text(ques_material)

            ques_images_dict = struct_ques_with_img(parent_ques_desc, ques.ques_desc)

            args = [same_answer_group_id, ques_id, subject_name, ques_desc, ques_type_code, standard_answer,
                    ques_score_list, stu_answer_list, rule, mark_count, None, e_mark_rule, mark_point, ooo_ques_type, 2,
                    ques_material, out_of_order_group, ques_images_dict]
            flag, mark_score, answer_parse, single_score_list, mark_state, mark_result, error_msg = subjective_mark_score_main(
                *args)
            print(flag, mark_score, answer_parse, single_score_list, mark_state, mark_result, error_msg)

            if error_msg:
                answer_parse = [error_msg for _ in range(stu_answer_id_list_length)]
                single_score_list = [0 for _ in range(stu_answer_id_list_length)]

                if error_msg == 100:
                    answer_parse = ["考生答案与参考答案一致，得满分。" for _ in range(stu_answer_id_list_length)]

            if ques_type_code == "D":
                if error_msg == ai_can_not_connect:
                    answer_parse = [error_msg for _ in range(stu_answer_id_list_length)]
                elif not single_score_list or (stu_answer_id_list_length != len(single_score_list)):
                    mark_score = None
                    error_msg = "AI 返回格式异常"
                    answer_parse = [error_msg for _ in range(stu_answer_id_list_length)]
                else:
                    for index, single_score in enumerate(single_score_list):
                        if float(single_score) > float(ques_score_list[index]):
                            flag = False
                            mark_score = None
                            error_msg = ai_score_ambiguity
                            answer_parse = [error_msg for _ in range(stu_answer_id_list_length)]
                            break

    except Exception as e:
        flag = False
        mark_score = None
        error_msg = "程序异常"
        answer_parse = ["程序异常" for _ in range(stu_answer_id_list_length)]
        logger.error(f"程序异常：{e}")

    return flag, ques_score, same_answer_group_id, mark_score, answer_parse, error_msg


def get_paper_detail(new_session, paper_detail_dict, raw_paper_id, raw_ques_id):
    """
    通过试题所属的试卷id和试题编号获取试题序号和试题分数
    """
    is_add = False
    if raw_paper_id in paper_detail_dict:
        if raw_ques_id in paper_detail_dict[raw_paper_id]:
            detail_item = paper_detail_dict[raw_paper_id][raw_ques_id]
            ques_order, ques_score_list = detail_item["ques_order"], detail_item["ques_score_list"]
        else:
            paper_detail_info = new_session.query(PaperDetail.ques_order, PaperDetail.ques_score_list) \
                .filter(and_(PaperDetail.paper_id == raw_paper_id, PaperDetail.ques_id == raw_ques_id)).first()
            ques_order, ques_score_list = paper_detail_info
            is_add = True
    else:
        paper_detail_info = new_session.query(PaperDetail.ques_order, PaperDetail.ques_score_list) \
            .filter(and_(PaperDetail.paper_id == raw_paper_id, PaperDetail.ques_id == raw_ques_id)).first()
        ques_order, ques_score_list = paper_detail_info
        is_add = True
    ques_order = re.sub(r'^0+', '', ques_order) if ques_order else None
    return is_add, ques_order, ques_score_list
