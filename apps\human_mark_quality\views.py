"""
评分质检接口模块
"""
import json
from typing import Any

from fastapi import APIRouter, Depends
from sqlalchemy import and_, func, select, text
from sqlalchemy.orm import Session

from apps.base.human_global_cache import get_round_group_member
from apps.base.schemas import BaseResponse
from apps.human_mark_quality.schemas import QualitySampleReq, QualityMarkReq, QualityStuListReq, YetQualityStuListReq
from apps.human_mark_quality.services import create_back_again_mark_data
from apps.human_official_mark.mark_redis_services import create_human_subject_grade
from apps.human_official_mark.services import get_stu_answer_info
from apps.human_task_manage.models import HumanReadTask, HumanReadTaskRound, HumanRoundDistriAnswer, HumanPersonDistriAnswer, HumanQualityDistriAnswer
from apps.human_task_manage.services import get_round_count
from apps.models.models import Project, Subject, UserInfo, ExamQuestion
from apps.users.services import get_current_user
from helper import response_utils
from settings import logger, configs
from factory_apps import session_depend
from utils.time_func import format_now_time

human_quality_router = APIRouter()


@human_quality_router.post(path="/quality_sample", response_model=BaseResponse, summary="质检抽样")
async def quality_sample(query: QualitySampleReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 质检抽样")
    project_id, subject_id, ques_code, score_list, stu_secret_num, sample_num = query.model_dump().values()

    if score_list and len(score_list) != 2:
        return BaseResponse(code=response_utils.params_error, msg="题得分字段参数格式有误")

    condition = and_(HumanReadTask.task_type == 1, HumanReadTask.project_id == project_id, HumanReadTask.subject_id == subject_id, HumanReadTask.ques_code == ques_code)
    task_id = new_session.query(HumanReadTask.task_id).filter(condition).scalar()
    round_id = new_session.query(HumanReadTaskRound.round_id).filter(and_(HumanReadTaskRound.task_id == task_id, HumanReadTaskRound.round_count == 1)).scalar()
    stu_secret_num_query = HumanRoundDistriAnswer.stu_secret_num.ilike(f"%{stu_secret_num}%") if stu_secret_num else True

    stmt = select(HumanRoundDistriAnswer.stu_secret_num, func.sum(HumanRoundDistriAnswer.round_score).label("round_score")) \
        .where(and_(HumanRoundDistriAnswer.round_id == round_id, HumanRoundDistriAnswer.quality_state == 0, stu_secret_num_query, HumanRoundDistriAnswer.round_score.isnot(None))) \
        .group_by(HumanRoundDistriAnswer.stu_secret_num)

    # 考生得分范围
    stmt = stmt.having(func.sum(HumanRoundDistriAnswer.round_score).between(*score_list)) if score_list else stmt

    # 抽取数量
    stmt = stmt.order_by(func.random()).limit(sample_num) if sample_num else stmt

    result = new_session.execute(stmt).all()

    assign_data = [dict(row._mapping) for row in result]

    data = {
        "round_id": round_id,
        "total": len(assign_data),
        "data": assign_data
    }

    return BaseResponse(data=data, msg="质检抽样成功")


@human_quality_router.post(path="/get_no_quality_stu_list", response_model=BaseResponse, summary="获取待质检考生列表")
async def get_quality_stu_list(query: QualityStuListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取待质检考生列表")
    current_page, page_size, round_id, ques_code, stu_secret_num_list = query.model_dump().values()

    condition = and_(HumanRoundDistriAnswer.round_id == round_id, HumanRoundDistriAnswer.stu_secret_num.in_(stu_secret_num_list), HumanRoundDistriAnswer.quality_state == 0)

    total = new_session.query(HumanRoundDistriAnswer.stu_secret_num.distinct()).filter(condition).count()

    select_fields = [
        HumanRoundDistriAnswer.stu_secret_num, HumanRoundDistriAnswer.reviewer_id,
        func.group_concat(HumanRoundDistriAnswer.distri_id).label("distri_id_str"),
        func.group_concat(HumanRoundDistriAnswer.answer_id).label("answer_id_str"),
        func.sum(HumanRoundDistriAnswer.round_score).label("mark_score_sum"),
        func.group_concat(HumanRoundDistriAnswer.round_score).label("mark_score_str"),
        func.group_concat(HumanPersonDistriAnswer.person_distri_id).label("person_distri_id_str"),
        func.group_concat(HumanPersonDistriAnswer.mark_point_score_list, "-").label("mark_point_score_list_str"),
        func.group_concat(HumanPersonDistriAnswer.cost_time).label("cost_time_str"),
        func.max(HumanPersonDistriAnswer.mark_time).label("mark_time")
    ]

    stmt = select(*select_fields) \
        .join(HumanPersonDistriAnswer, and_(HumanPersonDistriAnswer.answer_id == HumanRoundDistriAnswer.answer_id, HumanPersonDistriAnswer.user_id == HumanRoundDistriAnswer.reviewer_id, HumanPersonDistriAnswer.is_again_mark == HumanRoundDistriAnswer.is_again_mark)) \
        .filter(condition) \
        .group_by(HumanRoundDistriAnswer.stu_secret_num, HumanRoundDistriAnswer.reviewer_id, HumanPersonDistriAnswer.is_again_mark) \
        .order_by(func.min(HumanRoundDistriAnswer.created_time).desc())

    new_session.execute(text("SET SESSION group_concat_max_len = 9999999;"))
    result = new_session.execute(stmt).all()

    if not result:
        return BaseResponse(data={"round_id": round_id, "total": 0, "data": []})

    stu_answer_dict = get_stu_answer_info(new_session, ques_code)

    quality_stu_list = []
    for row in result:
        answer_data = []
        distri_id_list, person_distri_id_list, answer_id_list = row.distri_id_str.split(","), row.person_distri_id_str.split(","), row.answer_id_str.split(",")
        mark_score_list, mark_point_score_list_list, cost_time_list = row.mark_score_str.split(","), row.mark_point_score_list_str.split("-,"), row.cost_time_str.split(",")
        mark_point_score_lists = [i.replace("-", "") for i in mark_point_score_list_list]

        answer_items = []
        for distri_id, answer_id, mark_score, mark_point_score_list, cost_time, person_distri_id in zip(distri_id_list, answer_id_list, mark_score_list, mark_point_score_lists, cost_time_list, person_distri_id_list):
            answer_info = stu_answer_dict[answer_id]
            answer_item = {
                "distri_id": distri_id,
                "person_distri_id": person_distri_id,
                "answer_id": answer_id,
                "ques_order": answer_info["ques_order"],
                "answer_image_path": answer_info["answer_image_path"],
                "word_count": answer_info["word_count"],
                "is_do": answer_info["is_do"],
                "mark_score": float(mark_score),
                "mark_point_score_list": json.loads(mark_point_score_list),
                "cost_time": int(cost_time)
            }
            answer_items.append(answer_item)
        # 按 ques_order 升序排序
        answer_items.sort(key=lambda x: x["ques_order"])
        answer_data.extend(answer_items)
        item = {
            "stu_secret_num": row.stu_secret_num,
            "ques_code": ques_code,
            "mark_score": row.mark_score_sum,
            "answer_data": answer_data,
            "mark_time": row.mark_time,
            "reviewer_id": row.reviewer_id
        }
        quality_stu_list.append(item)

    data = {
        "round_id": round_id,
        "total": total,
        "data": quality_stu_list
    }

    return BaseResponse(data=data, msg="获取待质检考生列表成功")


@human_quality_router.post(path="/get_yet_quality_stu_list", response_model=BaseResponse, summary="获取已质检考生列表")
async def get_quality_stu_list(query: YetQualityStuListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取已质检考生列表")
    current_page, page_size, project_id, subject_id, stu_secret_num, is_show_self = query.model_dump().values()

    curr_user_id = user.get("user_id")

    ques_name_info = new_session.query(ExamQuestion.ques_code, ExamQuestion.knowledge_show).filter(ExamQuestion.parent_ques_id.is_(None)).all()
    ques_name_dict = {row.ques_code: row.knowledge_show for row in ques_name_info} if ques_name_info else {}

    limit = current_page - 1
    offset = limit * page_size

    project_condition = HumanReadTask.project_id == project_id if project_id else True
    subject_condition = HumanReadTask.subject_id == subject_id if subject_id else True
    self_condition = HumanQualityDistriAnswer.quality_user_id == curr_user_id if is_show_self else True

    condition = and_(HumanRoundDistriAnswer.quality_state == 1, project_condition, subject_condition, self_condition)

    total = new_session.query(HumanQualityDistriAnswer.stu_secret_num.distinct()) \
        .join(HumanRoundDistriAnswer, HumanRoundDistriAnswer.distri_id == HumanQualityDistriAnswer.round_distri_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.round_id == HumanQualityDistriAnswer.round_id) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .filter(condition).count()

    select_fields = [
        HumanQualityDistriAnswer.stu_secret_num, HumanRoundDistriAnswer.reviewer_id, HumanReadTask.ques_code, Project.project_name, Subject.subject_name, UserInfo.name.label("quality_user_name"),
        func.group_concat(HumanRoundDistriAnswer.distri_id.label("distri_id")).label("distri_id_str"),
        func.group_concat(HumanRoundDistriAnswer.answer_id.label("answer_id")).label("answer_id_str"),
        func.sum(HumanRoundDistriAnswer.round_score).label("mark_score_sum"),
        func.group_concat(HumanRoundDistriAnswer.round_score).label("mark_score_str"),
        func.group_concat(HumanPersonDistriAnswer.person_distri_id).label("person_distri_id_str"),
        func.group_concat(HumanPersonDistriAnswer.mark_point_score_list, "-").label("mark_point_score_list_str"),
        func.group_concat(HumanPersonDistriAnswer.cost_time).label("cost_time_str"),
        func.max(HumanPersonDistriAnswer.mark_time).label("mark_time"),
        func.group_concat(HumanQualityDistriAnswer.quality_id).label("quality_id_str"),
        func.sum(HumanQualityDistriAnswer.quality_score).label("quality_score_sum"),
        func.max(HumanQualityDistriAnswer.quality_type).label("quality_type"),
        func.group_concat(HumanQualityDistriAnswer.quality_score).label("quality_score_str"),
        func.group_concat(HumanQualityDistriAnswer.mark_point_score_list, "-").label("quality_point_score_list_str"),
        func.max(HumanQualityDistriAnswer.quality_result).label("quality_result"),
        func.max(HumanQualityDistriAnswer.created_time).label("created_time"),
        func.max(HumanQualityDistriAnswer.updated_time).label("updated_time")
    ]

    stmt = select(*select_fields) \
        .join(HumanRoundDistriAnswer, HumanRoundDistriAnswer.distri_id == HumanQualityDistriAnswer.round_distri_id) \
        .join(HumanPersonDistriAnswer, HumanPersonDistriAnswer.person_distri_id == HumanQualityDistriAnswer.person_distri_id) \
        .join(HumanReadTaskRound, HumanReadTaskRound.round_id == HumanRoundDistriAnswer.round_id) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(UserInfo, UserInfo.user_id == HumanQualityDistriAnswer.quality_user_id) \
        .filter(condition) \
        .group_by(HumanQualityDistriAnswer.stu_secret_num, HumanRoundDistriAnswer.reviewer_id, HumanReadTask.ques_code, Project.project_name, Subject.subject_name, UserInfo.name) \
        .order_by(func.max(HumanQualityDistriAnswer.created_time).desc()) \
        .limit(page_size).offset(offset)

    new_session.execute(text("SET SESSION group_concat_max_len = 9999999;"))
    result = new_session.execute(stmt).all()

    if not result:
        return BaseResponse(data={"total": 0, "data": []})

    quality_stu_list = []
    for row in result:
        answer_data = []
        stu_answer_dict = get_stu_answer_info(new_session, row.ques_code)
        distri_id_list, answer_id_list, person_distri_id_list = row.distri_id_str.split(","), row.answer_id_str.split(","), row.person_distri_id_str.split(",")
        mark_score_list, mark_point_score_list_list, cost_time_list = row.mark_score_str.split(","), row.mark_point_score_list_str.split("-,"), row.cost_time_str.split(",")
        mark_point_score_lists = [i.replace("-", "") for i in mark_point_score_list_list]

        quality_id_list = row.quality_id_str.split(",") if row.quality_id_str else None
        quality_score_list = row.quality_score_str.split(",") if row.quality_score_str else None
        quality_point_score_list_list = row.quality_point_score_list_str.split("-,")
        quality_point_score_lists = [i.replace("-", "") for i in quality_point_score_list_list]

        index = 0
        answer_items = []
        for quality_id, distri_id, answer_id, mark_score, mark_point_score_list, cost_time, quality_point_score_list, person_distri_id in zip(quality_id_list, distri_id_list, answer_id_list, mark_score_list, mark_point_score_lists, cost_time_list, quality_point_score_lists, person_distri_id_list):
            quality_score = quality_score_list[index] if quality_score_list else None
            answer_info = stu_answer_dict[answer_id]
            answer_item = {
                "quality_id": quality_id,
                "distri_id": distri_id,
                "person_distri_id": person_distri_id,
                "answer_id": answer_id,
                "ques_order": answer_info["ques_order"],
                "answer_image_path": answer_info["answer_image_path"],
                "word_count": answer_info["word_count"],
                "is_do": answer_info["is_do"],
                "mark_score": float(mark_score),
                "mark_point_score_list": json.loads(mark_point_score_list),
                "cost_time": int(cost_time),
                "quality_score": float(quality_score) if quality_score is not None else None,
                "quality_point_score_list": json.loads(quality_point_score_list)
            }
            answer_items.append(answer_item)
            index += 1
        # 按 ques_order 升序排序
        answer_items.sort(key=lambda x: x["ques_order"])
        answer_data.extend(answer_items)
        item = {
            "stu_secret_num": row.stu_secret_num,
            "ques_code": row.ques_code,
            "knowledge_show":ques_name_dict.get(row.ques_code),
            "mark_score_sum": row.mark_score_sum,
            "answer_data": answer_data,
            "mark_time": row.mark_time,
            "quality_type": row.quality_type,
            "quality_result": row.quality_result,
            "quality_score_sum": row.quality_score_sum,
            "project_name": row.project_name,
            "subject_name": row.subject_name,
            "reviewer_id": row.reviewer_id,
            "quality_user_name": row.quality_user_name,
            "quality_time": row.updated_time if row.updated_time else row.created_time,
        }
        quality_stu_list.append(item)

    data = {
        "total": total,
        "data": quality_stu_list
    }

    return BaseResponse(data=data, msg="获取已质检考生列表成功")


@human_quality_router.post(path="/quality_mark", response_model=BaseResponse, summary="质检评分")
async def quality_mark(query: QualityMarkReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 质检评分")
    curr_user_id = user["user_id"]
    project_id, subject_id, round_id, stu_secret_num, ques_code, quality_mark_type, quality_type, mark_info, reviewer_id = query.project_id, query.subject_id, query.round_id, query.stu_secret_num, query.ques_code, query.quality_mark_type, query.quality_type, query.mark_info, query.reviewer_id

    quality_time = format_now_time()

    quality_result = 1 if quality_type == 1 else 0

    curr_group_id = None
    round_group_member = get_round_group_member(new_session, round_id)
    for group_id, member_id_list in round_group_member.items():
        if reviewer_id in member_id_list:
            curr_group_id = group_id
            break

    round_count = get_round_count(new_session, round_id)

    again_mark_count = new_session.query(func.max(HumanPersonDistriAnswer.is_again_mark)).filter(HumanPersonDistriAnswer.stu_secret_num == stu_secret_num).scalar()
    again_mark_count += 1

    if quality_mark_type == 1:
        for mark_item in mark_info:
            quality_id, distri_id, person_distri_id, answer_id, ques_id, mark_point_score_list, mark_score = mark_item.model_dump().values()
            update_item = {HumanRoundDistriAnswer.quality_state: 1}
            if quality_type == 1:
                pass
            if quality_type == 2:
                if mark_score is None:
                    return BaseResponse(code=response_utils.params_error, msg="修改提交分数不能为空")
            elif quality_type == 3:
                # 退回重评逻辑
                create_back_again_mark_data(new_session, round_id, stu_secret_num, ques_code, ques_id, answer_id, curr_group_id, reviewer_id, again_mark_count)

            condition = and_(HumanRoundDistriAnswer.round_id == round_id, HumanRoundDistriAnswer.distri_id == distri_id)
            new_session.query(HumanRoundDistriAnswer).filter(condition).update(update_item)

            # 生成质检数据
            quality_item = HumanQualityDistriAnswer(quality_id=configs.snow_worker.get_id(), round_id=round_id, round_distri_id=distri_id, person_distri_id=person_distri_id,
                                                    stu_secret_num=stu_secret_num, answer_id=answer_id, mark_point_score_list=mark_point_score_list, quality_type=quality_type,
                                                    quality_score=mark_score, quality_result=quality_result, quality_user_id=curr_user_id, created_time=quality_time)
            new_session.add(quality_item)
            if quality_type == 2:
                create_human_subject_grade(new_session, round_id, round_count, project_id, subject_id, 3, stu_secret_num, mark_score, curr_user_id, answer_id, ques_id, mark_point_score_list)
    else:
        # 重新质检，做逻辑校验，更新质检数据
        quality_id_list = [mark_item.quality_id for mark_item in mark_info if mark_item.quality_id is not None]
        if not quality_id_list:
            return BaseResponse(code=response_utils.params_error, msg="")
        raw_quality_type = new_session.query(HumanQualityDistriAnswer.quality_type).filter(HumanQualityDistriAnswer.quality_id == quality_id_list[0]).scalar()

        if raw_quality_type == 3:
            # 已退回的不允许再次质检
            return BaseResponse(code=response_utils.permission_deny, msg="该评分已退回，不允许再次质检")

        if raw_quality_type == 2:
            # 已经修改提交的只能修改提交
            if quality_type != 2:
                return BaseResponse(code=response_utils.permission_deny, msg="修改提交的评分只能进行修改提交")
            else:
                for mark_item in mark_info:
                    quality_id, distri_id, person_distri_id, answer_id, ques_id, mark_point_score_list, mark_score = mark_item.model_dump().values()
                    if mark_score is None:
                        return BaseResponse(code=response_utils.params_error, msg="修改提交分数不能为空")
                    new_session.query(HumanQualityDistriAnswer).filter(HumanQualityDistriAnswer.quality_id == quality_id).update({
                        HumanQualityDistriAnswer.quality_score: mark_score,
                        HumanQualityDistriAnswer.mark_point_score_list: mark_point_score_list
                    })
                    create_human_subject_grade(new_session, round_id, round_count, project_id, subject_id, 3, stu_secret_num, mark_score, curr_user_id, answer_id, ques_id, mark_point_score_list)

        if raw_quality_type == 1:
            # 已经通过的可以修改提交或退回重评
            if quality_type == 1:
                return BaseResponse(code=response_utils.operation_repeat, msg="该评分已通过，请勿重复操作")
            if quality_type == 2:
                # 修改提交
                for mark_item in mark_info:
                    quality_id, distri_id, person_distri_id, answer_id, ques_id, mark_point_score_list, mark_score = mark_item.model_dump().values()
                    if mark_score is None:
                        return BaseResponse(code=response_utils.params_error, msg="修改提交分数不能为空")
                    new_session.query(HumanQualityDistriAnswer).filter(HumanQualityDistriAnswer.quality_id == quality_id).update({
                        HumanQualityDistriAnswer.quality_score: mark_score,
                        HumanQualityDistriAnswer.mark_point_score_list: mark_point_score_list,
                        HumanQualityDistriAnswer.quality_result: 0
                    })
                    create_human_subject_grade(new_session, round_id, round_count, project_id, subject_id, 3, stu_secret_num, mark_score, curr_user_id, answer_id, ques_id, mark_point_score_list)
            else:
                for mark_item in mark_info:
                    # 退回重评逻辑
                    quality_id, distri_id, person_distri_id, answer_id, ques_id, mark_point_score_list, mark_score = mark_item.model_dump().values()
                    new_session.query(HumanQualityDistriAnswer).filter(HumanQualityDistriAnswer.quality_id == quality_id).update({
                        HumanQualityDistriAnswer.quality_score: mark_score,
                        HumanQualityDistriAnswer.mark_point_score_list: mark_point_score_list,
                        HumanQualityDistriAnswer.quality_result: 0
                    })
                    create_back_again_mark_data(new_session, round_id, stu_secret_num, ques_code, ques_id, answer_id, curr_group_id, reviewer_id, again_mark_count)

    new_session.commit()
    return BaseResponse(msg="质检评分成功")
