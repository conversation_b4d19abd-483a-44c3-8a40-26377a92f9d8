import json
from typing import Any, AsyncIterable, Dict, Union

import httpx

from settings.loggings import logger


async def writesnic_stream_request(
    url: str, params: Union[str, Dict[str, Any]]
) -> AsyncIterable[Dict[str, Any]]:
    try:
        pass
    except Exception as e:
        logger.error(f"流式请求出现错误，错误原因为 : {e}")
        return


# async def writesonic_stream_request2(url: str, headers, data) -> AsyncIterable[Dict[str, Any]]:
async def writesonic_stream_request2(url: str, headers, data):
    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST", url=url, headers=headers, json=data, timeout=60
        ) as response:
            async for line in response.aiter_lines():
                data = json.loads(line)
                # print(data[0]["text"])
                yield data[0]["text"]


async def writesonic_stream_article_request(url: str, headers, data):
    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST", url=url, headers=headers, json=data, timeout=60
        ) as response:
            async for item in response.aiter_lines():
                data = json.loads(item)
                yield data["data"][0]["content"]
