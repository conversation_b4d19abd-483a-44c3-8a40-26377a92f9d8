import json
import os
import mimetypes
import socket
import string

from utils.snowflake_algorithm import IdWorker


class BaseConfig:
    # 构造程序运行路径（打包后会定位到.exe生成的临时目录下）
    CURRENT_PATH = os.path.abspath(__file__)
    ROOT_PATH = os.path.dirname(os.path.dirname(CURRENT_PATH))
    if "_MEI" in ROOT_PATH:
        # 打包后运行时指定的配置文件
        setting_file = "AppSetting.json"
    else:
        # 开发环境的配置文件
        setting_file = "AppSetting.json"
    try:
        PROJECT_PATH = os.path.abspath(".")
        # 加载配置文件
        cfg_json_path = os.path.join(PROJECT_PATH, setting_file)
        with open(cfg_json_path, "r", encoding="utf-8-sig") as f:
            CFG_DATA = json.load(f)
    except FileNotFoundError:
        PROJECT_PATH = os.path.abspath("../../")
        # 加载配置文件
        cfg_json_path = os.path.join(PROJECT_PATH, setting_file)
        with open(cfg_json_path, "r", encoding="utf-8-sig") as f:
            CFG_DATA = json.load(f)

    SYSTEM_CFG = CFG_DATA["SystemConfig"]
    MODE = SYSTEM_CFG["Mode"]
    HOST = "0.0.0.0"
    PORT = SYSTEM_CFG["Port"]
    WORKERS = SYSTEM_CFG["Workers"]
    IS_SEVERAL_SERVER = SYSTEM_CFG["IsSeveralServer"]

    # 路由版本号
    VERSION = "/v1"

    # 发版版本号
    SYS_VERSION = "V1.1.50906"

    BACKEND_IP = socket.gethostbyname(socket.gethostname())
    if not BACKEND_IP:
        BACKEND_IP = "127.0.0.1"

    # token 私钥
    SECRET_KEY = "82sade5624fco4ra2d87f1i8158bov9r694rat6708f6o8k49wy6ct6td93e0206yw"
    ALGORITHM = "HS256"  # token算法
    ACCESS_TOKEN_EXPIRE_HOURS = SYSTEM_CFG["AccessTokenExpireHours"]  # token生存时间

    # 写死一个有效期为 100 年的 token 用以启动自启任务
    GLOBAL_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.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.Tzsv-52f8ST4Uf1EnVOrs8DFl9YUk15k_sQ5LLWkGtg"

    # 指定 js 文件的解析方式
    mimetypes.add_type("application/javascript", ".js")

    LAUNCH = CFG_DATA["Launch"]
    IS_AUTO_OPEN_BROWSER = (LAUNCH["IsAutoOpenBrowser"],)
    DELAY_SECOND = LAUNCH["DelaySecond"]

    # 加密私钥
    PWD_KEY = "cw28e80g57re4gs3"

    # 配置 MySQL 数据库
    DB_DATA = CFG_DATA["DbConfig"]
    MYSQL_CONFIG = {
        "HOST": DB_DATA["Host"],
        "PORT": DB_DATA["Port"],
        "USER": DB_DATA["User"],
        "PASSWORD": DB_DATA["Password"],
        "DATABASE": DB_DATA["Database"],
        "CONNECT_TIMEOUT": DB_DATA["ConnectTimeout"],  # MySQL 服务等待连接握手超时时间，单位为秒
        "MAX_CONNECTIONS": DB_DATA["MaxConnections"],  # MySQL 服务最大连接数
    }

    # 配置MySQL 数据库会话
    MYSQL_ENGINE_CONFIG = {
        "POOL_SIZE": DB_DATA["PoolSize"],  # 最小连接数
        "MAX_OVERFLOW": DB_DATA["MaxOverflow"],  # 允许连接数超过 pool_size 多少，可用于设置最大连接数
        "ECHO": False,  # 是否打印执行的 sql 语句
        "POOL_PRE_PING": DB_DATA["PoolPrePing"],  # 开启连接断开检测
        "POOL_RECYCLE": DB_DATA["PoolRecycleSecond"],  # 连接过期时间，单位为秒
    }

    # 配置 Redis 服务
    CACHE_DATA = CFG_DATA["CacheConfig"]
    REDIS_CONFIG = {"HOST": CACHE_DATA["Host"], "PORT": CACHE_DATA["Port"], "DATABASE": CACHE_DATA["Db"], "MAX_CONNECTIONS": CACHE_DATA["MaxConnections"]}

    # redis 中人工阅卷队列 key
    R_QUEUE_KEY = "human_grade_queue"
    R_HUMAN_KEY = "human_score"

    # 配置 SQLite 服务
    SQLITE_DB_PATH = os.path.join(PROJECT_PATH, "server_static/data_transfer/read_paper_grade.db")

    ques_cfg = CFG_DATA["QuesConfig"]
    HAS_QUES_DESC = ques_cfg["HasQuesDesc"]  # 是否进行 AI 预评

    ai_cfg = CFG_DATA["AiConfig"]
    AI_PREPARE_MARK = ai_cfg["AiPrepareMark"]  # 是否进行 AI 预评
    SUPPLE_IMAGES_DESC_URL = ai_cfg["AiImagesUrl"]  # 补充试题图片描述AI接口
    SET_STD_URL = ai_cfg["SetStdUrl"]  # AI定标接口
    CS_SET_STD_URL = ai_cfg["CsSetStdUrl"]  # AI定标（小小题）
    GET_MP_URL = ai_cfg["GetMPUrl"]  # AI 生成评分标准接口
    AI_REQ_THREAD_COUNT = ai_cfg["AiReqThreadCount"]  # 发送给AI的最大线程数
    AI_INIT_COUNT = ai_cfg["InitNum"]
    AI_TIMEOUT_SECOND = ai_cfg["AiTimeoutSecond"]

    # 模板文件要求分隔符
    NEW_SPLIT_FLAG = "◎☆◎"

    # 试题素材发给 AI 时的占位符
    MATERIAL_PLACEHOLDER = "☾⚝image⚝☽"

    # 人工阅卷配置
    manual_read_cfg = CFG_DATA["HumanReadConfig"]
    # 发起人工阅卷后分配任务时所取的考生数占所有考生的比例
    GROUP_DIVIDE_RATIO = manual_read_cfg["GroupDivideRatio"]
    HUMAN_SCHEDULE_INTERVAL = manual_read_cfg["HumanScheduleInterval"]
    ASSIGN_ANSWER_THRESHOLD = manual_read_cfg["AssignAnswerThreshold"]  # 分配阈值
    Calculate_Score_Interval = manual_read_cfg["CalculateScoreInterval"] #计算科目成绩表总分
    Calculate_statistics_Interval = manual_read_cfg["CalculateStatisticsInterval"]  # 更新统计表
    calculate_stu_subject_grade_interval = manual_read_cfg["calculate_stu_subject_grade_interval"] #偏差统计间隔

    # 实例化雪花算法实例，用于生成 id 等唯一标识
    snow_worker = IdWorker(SYSTEM_CFG["DataCenterId"], 14)

    # 导入相关配置
    import_cfg = CFG_DATA["ImportConfig"]
    # 单批导入考生数量
    EXAM_STU_BATCH_NUM = import_cfg["ExamStuBatchNum"]
    # 单批导入考生作答数量
    STU_ANSWER_BATCH_NUM = import_cfg["StuAnswerBatchNum"]
    # 是否要对考生准考证号加密
    ENCRYPT_EXAM_NUM = import_cfg["EncryptExamNum"]

    # 试题素材相关配置
    ques_resource_cfg = CFG_DATA["StaticConfig"]
    QUES_IMAGE_PATH = f"server_static/{ques_resource_cfg['QuesResourcePath']}/"
    image_path = os.path.join(PROJECT_PATH, QUES_IMAGE_PATH)
    os.makedirs(image_path, exist_ok=True)

    # 操作题相关配置
    op_resource_cfg = CFG_DATA["OperationConfig"]
    OP_MATERIAL_PATH = op_resource_cfg["OperationMaterialPath"]
    os.makedirs(OP_MATERIAL_PATH, exist_ok=True)

    # 操作题引擎评分
    MASTER_SERVER_URL = op_resource_cfg["MasterServerUrl"]
    # 每批标记考生操作题数量
    OP_ANSWER_MARK_BATCH_NUM = op_resource_cfg["OpAnswerMarkBatch"]
    # 操作题引擎接口
    OP_ENGINE_URL = op_resource_cfg["OpEngineUrlList"]
    # 定义 pjoStuPfNet.exe 文件路径
    OP_ENGINE_BASE_PATH = op_resource_cfg["OpEnginePath"]
    OP_ENGINE_ABSOLUTE_PATH = os.path.join(PROJECT_PATH, OP_ENGINE_BASE_PATH)

    # 操作题人工评分
    # 操作题步骤和评析分隔符
    OP_PARSE_SPLIT_FLAG = "〖○⊙●】"
    # 操作题步骤之间分隔符
    OP_STEP_SPLIT_FLAG = "U"
    # op 文件密码
    OP_PWD = op_resource_cfg["OpPwd"]
    # op 文件解压秘钥
    OP_KEY = "RV278HQA955748BE"

    # 字母列表，用于生成选择题选项
    LETTER_LIST = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X" "Y", "Z"]

    # 标点符号
    # 定义中文标点符号
    chinese_punctuation = "，。！？；：“”‘’（）《》【】、——"
    # 中英文标点符号
    ALL_PUNCTUATION = string.punctuation + chinese_punctuation

    # 与学考对接导出作答分组数据评分 zip 文件加密密码
    ZIP_PASSWORD = "zhuofan.com"

    # 登录失败限制配置
    LOGIN_FAIL_CONFIG = CFG_DATA["LoginFailConfig"]
    MAX_LOGIN_ATTEMPTS = LOGIN_FAIL_CONFIG["MaxAttempts"]  # 最大登录尝试次数
    LOGIN_LOCK_TIME = LOGIN_FAIL_CONFIG["LockTime"]  # 登录锁定时间（秒）

    EXAM_COUNT_CONFIG = [
        {"start": 26, "end": 90, "extract_count": 5, "allowed_error_count": 0},
        {"start": 91, "end": 150, "extract_count": 8, "allowed_error_count": 0},
        {"start": 151, "end": 280, "extract_count": 13, "allowed_error_count": 1},
        {"start": 281, "end": 500, "extract_count": 20, "allowed_error_count": 2},
        {"start": 501, "end": 1200, "extract_count": 32, "allowed_error_count": 3},
        {"start": 1201, "end": 3200, "extract_count": 50, "allowed_error_count": 5},
        {"start": 3201, "end": 10000, "extract_count": 80, "allowed_error_count": 7},
        {"start": 10001, "end": 35000, "extract_count": 125, "allowed_error_count": 10},
        {"start": 35001, "end": 150000, "extract_count": 200, "allowed_error_count": 14},
        {"start": 150001, "end": 500000, "extract_count": 315, "allowed_error_count": 21},
        {"start": 500001, "end": 50000000, "extract_count": 500, "allowed_error_count": 21},
    ]

    """后台定时任务服务相关配置"""
    # 后台定时任务路由版本号
    SCHEDULE_ROUTE_VERSION = "/V1"

    # 发版版本号
    SCHEDULE_VERSION = "V1.1.50906"

    SCHEDULE_CFG = CFG_DATA["ScheduleBackendCfg"]
    SCHEDULE_PORT = SCHEDULE_CFG["Port"]
    SCHEDULE_WORKERS = SCHEDULE_CFG["Workers"]

