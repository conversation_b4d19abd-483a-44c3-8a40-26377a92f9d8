import os
import traceback
import json
from typing import Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy import select, and_, func, text
from settings import logger
from pydantic import BaseModel
from sqlalchemy.orm import Session
from utils.utils import round_half_up
from apps.users.services import get_current_user
from apps.human_statistics.models import HumanStatisticsSmallGroup
from apps.human_statistics.schemas import (
GCalculateReviewedCountReq,
GCalculateAverageScoreReq,
GCalculateMaxScoreReq,
GCalculateMinScoreReq,
GCalculateMaxSpeedReq,
GCalculateMinSpeedReq,
GCalculateArbitrationCountReq,
GCalculateScoreDistributionReq,
GCalculateEffectiveReviewCountReq,
GCalculateAverageSpeedReq,
GCalculateArbitrationPieChartReq
)

from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from settings import configs
from apps.models.models import UserInfo, ManualReadTask

# 创建APIRouter
g_score_analysis_router = APIRouter()


@g_score_analysis_router.post(path="/calculate_reviewed_count", response_model=BaseResponse,
                            summary="小组页面：计算已阅量")
async def calculate_reviewed_count_api(
        query: GCalculateReviewedCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算已阅量")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件，有效评卷量 (type=5)
    filters5 = [
        HumanStatisticsSmallGroup.statistics_type == 5,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters5.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters5.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters5.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters5.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters5.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

    data5 = new_session.query(func.sum(HumanStatisticsSmallGroup.statistics_result_1)).filter(*filters5)

    # 动态构建过滤条件，需仲裁数量 (type=6)
    filters6 = [
        HumanStatisticsSmallGroup.statistics_type == 6,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters6.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters6.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters6.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters6.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters6.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

    data6 = new_session.query(func.sum(HumanStatisticsSmallGroup.statistics_result_1)).filter(*filters6)

    # 添加日期范围条件
    if start_date:
        data5 = data5.filter(HumanStatisticsSmallGroup.date >= start_date)
        data6 = data6.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        data5 = data5.filter(HumanStatisticsSmallGroup.date <= end_date)
        data6 = data6.filter(HumanStatisticsSmallGroup.date <= end_date)

    result5 = data5.scalar() or 0
    result6 = data6.scalar() or 0
    total = result5 + result6
    total_count = [{"count": int(total)}] if total > 0 else []
    logger.info(f"小组页面：计算有效评卷量，总数: {total_count}")
    return BaseResponse(data=total_count, msg="获取小组页面：有效评卷量")


@g_score_analysis_router.post(path="/calculate_average_score", response_model=BaseResponse,
                            summary="小组页面：计算平均分")
async def calculate_average_score_api(
        query: GCalculateAverageScoreReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算平均分")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件
    filters = [
        HumanStatisticsSmallGroup.statistics_type == 1,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
    
    # 查询所有符合条件的平均分记录 (statistics_type == 1)
    score_data = new_session.query(
        HumanStatisticsSmallGroup.date,
        HumanStatisticsSmallGroup.statistics_result_1
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        score_data = score_data.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        score_data = score_data.filter(HumanStatisticsSmallGroup.date <= end_date)

    score_results = score_data.all()

    # 动态构建过滤条件
    filters = [
        HumanStatisticsSmallGroup.statistics_type == 4,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
        HumanStatisticsSmallGroup.subject_id == query.subject_id,
    ]
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
    
    workload_data = new_session.query(
        HumanStatisticsSmallGroup.date,
        HumanStatisticsSmallGroup.statistics_result_1
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        workload_data = workload_data.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        workload_data = workload_data.filter(HumanStatisticsSmallGroup.date <= end_date)

    workload_results = workload_data.all()

    # 将工作量结果按日期组织成字典，方便查找
    workload_dict = {}
    for result in workload_results:
        workload_dict[result.date] = int(result.statistics_result_1) if result.statistics_result_1 else 0.0

    # 计算加权平均分：每一天的平均分 * 该天工作量求和，然后除以总工作量
    total_weighted_score = 0.0
    total_workload = 0

    for result in score_results:
        daily_avg_score = float(result.statistics_result_1) if result.statistics_result_1 else 0.0
        # 从工作量字典中获取对应日期的工作量
        daily_workload = workload_dict.get(result.date, 0.0)

        total_weighted_score += daily_avg_score * daily_workload
        total_workload += daily_workload

    # 避免除以0，并保留两位小数
    final_avg_score = round_half_up(total_weighted_score / total_workload, 2) if total_workload > 0 else 0.0

    logger.info(f"小组页面：计算平均分，结果: {final_avg_score}")
    return BaseResponse(data=[final_avg_score], msg="平均分")


@g_score_analysis_router.post(path="/calculate_max_score", response_model=BaseResponse, summary="小组页面：计算最高分")
async def calculate_max_score_api(
        query: GCalculateMaxScoreReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算最高分")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件
    filters = [
        HumanStatisticsSmallGroup.statistics_type == 2,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
    
    # 查询所有符合条件的记录
    data = new_session.query(
        func.max(HumanStatisticsSmallGroup.statistics_result_1)
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsSmallGroup.date <= end_date)

    result = data.scalar()
    logger.info(f"小组页面：计算最高分，结果: {result}")
    return BaseResponse(data=[result], msg="最高分")


@g_score_analysis_router.post(path="/calculate_min_score", response_model=BaseResponse, summary="小组页面：计算最低分")
async def calculate_min_score_api(
        query: GCalculateMinScoreReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算最低分")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件
    filters = [
        HumanStatisticsSmallGroup.statistics_type == 3,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
    
    # 查询所有符合条件的记录
    data = new_session.query(
        func.min(HumanStatisticsSmallGroup.statistics_result_1)
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsSmallGroup.date <= end_date)

    result = data.scalar()
    logger.info(f"小组页面：计算最低分，结果: {result}")
    return BaseResponse(data=[result], msg="最低分")


@g_score_analysis_router.post(path="/calculate_max_speed", response_model=BaseResponse, summary="小组页面：计算最长速度")
async def calculate_max_speed_api(
        query: GCalculateMaxSpeedReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算最长速度")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件
    filters = [
        HumanStatisticsSmallGroup.statistics_type == 15,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

    # 查询所有符合条件的记录
    data = new_session.query(
        func.max(HumanStatisticsSmallGroup.statistics_result_1)
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsSmallGroup.date <= end_date)

    result = data.scalar()
    logger.info(f"小组页面：计算最长速度，结果: {result}")
    return BaseResponse(data=[result], msg="计算最长速度")


@g_score_analysis_router.post(path="/calculate_min_speed", response_model=BaseResponse, summary="小组页面：计算最短速度")
async def calculate_min_speed_api(
        query: GCalculateMinSpeedReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算最短速度")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件
    filters = [
        HumanStatisticsSmallGroup.statistics_type == 16,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

    # 查询所有符合条件的记录
    data = new_session.query(
        func.min(HumanStatisticsSmallGroup.statistics_result_1)
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsSmallGroup.date <= end_date)

    result = data.scalar()
    logger.info(f"小组页面：计算最短速度，结果: {result}")
    return BaseResponse(data=[result], msg="最短速度")




@g_score_analysis_router.post(path="/calculate_arbitration_count", response_model=BaseResponse,
                            summary="小组页面：计算仲裁率")
async def calculate_arbitration_count_api(
        query: GCalculateArbitrationCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算仲裁率")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 检查是否为多天数据
    is_multiple_days = False
    if start_date and end_date:
        # 计算日期差
        date_diff = (end_date - start_date).days + 1
        if date_diff > 1:
            is_multiple_days = True

    # 如果没有传入日期范围参数，则使用多天数据的计算逻辑
    if not query.date_range:
        is_multiple_days = True

    if is_multiple_days:
        # 多天数据：计算仲裁量总和/已阅量总和
        # 动态构建过滤条件
        filters_arbitration = [
            HumanStatisticsSmallGroup.statistics_type == 7,
            HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
        ]
        if query.subject_id is not None:
            filters_arbitration.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
        if query.task_id:
            filters_arbitration.append(HumanStatisticsSmallGroup.task_id == query.task_id)
        if query.group_id:
            filters_arbitration.append(HumanStatisticsSmallGroup.group_id == query.group_id)
        if query.project_id:
            filters_arbitration.append(HumanStatisticsSmallGroup.project_id == query.project_id)
        if query.ques_group_id:
            filters_arbitration.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
        
        # 查询仲裁量总和 (statistics_type == 7)
        arbitration_data = new_session.query(
            func.sum(HumanStatisticsSmallGroup.statistics_result_1)
        ).filter(*filters_arbitration)
        
        # 添加日期范围条件
        if start_date:
            arbitration_data = arbitration_data.filter(HumanStatisticsSmallGroup.date >= start_date)
        if end_date:
            arbitration_data = arbitration_data.filter(HumanStatisticsSmallGroup.date <= end_date)

        arbitration_sum = arbitration_data.scalar() or 0.0
        print(arbitration_sum)
        # 动态构建过滤条件
        filters_reviewed = [
            HumanStatisticsSmallGroup.statistics_type == 4,
            HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
        ]
        if query.subject_id is not None:
            filters_reviewed.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
        if query.task_id:
            filters_reviewed.append(HumanStatisticsSmallGroup.task_id == query.task_id)
        if query.group_id:
            filters_reviewed.append(HumanStatisticsSmallGroup.group_id == query.group_id)
        if query.project_id:
            filters_reviewed.append(HumanStatisticsSmallGroup.project_id == query.project_id)
        if query.ques_group_id:
            filters_reviewed.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
        
        # 查询已阅量总和 (statistics_type == 4)
        reviewed_data = new_session.query(
            func.sum(HumanStatisticsSmallGroup.statistics_result_1)
        ).filter(*filters_reviewed)

        # 添加日期范围条件
        if start_date:
            reviewed_data = reviewed_data.filter(HumanStatisticsSmallGroup.date >= start_date)
        if end_date:
            reviewed_data = reviewed_data.filter(HumanStatisticsSmallGroup.date <= end_date)

        reviewed_sum = reviewed_data.scalar() or 0.0
        print(reviewed_sum)
        # 计算仲裁率：仲裁量/已阅量 * 100
        arbitration_rate = round((arbitration_sum / reviewed_sum * 100), 2) if reviewed_sum > 0 else 0.0
    else:
        # 单天数据：直接读取已计算好的仲裁率 (statistics_type == 9)
        data = new_session.query(
            func.sum(HumanStatisticsSmallGroup.statistics_result_1)
        ).filter(
            HumanStatisticsSmallGroup.task_id == query.task_id,
            HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
            HumanStatisticsSmallGroup.subject_id == query.subject_id,
            HumanStatisticsSmallGroup.group_id == query.group_id,
            HumanStatisticsSmallGroup.project_id == query.project_id,
            HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id,
            HumanStatisticsSmallGroup.statistics_type == 9  # 9表示仲裁率统计类型
        )

        # 添加日期范围条件
        if start_date:
            data = data.filter(HumanStatisticsSmallGroup.date >= start_date)
        if end_date:
            data = data.filter(HumanStatisticsSmallGroup.date <= end_date)

        result = data.scalar()

        # 如果结果为None，则仲裁率为0
        arbitration_rate = float(result) if result is not None else 0.0

    logger.info(f"小组页面：计算仲裁率，结果: {arbitration_rate}%")
    return BaseResponse(data=[arbitration_rate], msg="仲裁率")


@g_score_analysis_router.post(path="/calculate_score_distribution", response_model=BaseResponse,
                            summary="小组页面：计算分数分布")
async def calculate_score_distribution_api(
        query: GCalculateScoreDistributionReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算分数分布柱状图")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件
    filters = [
        HumanStatisticsSmallGroup.statistics_type == 13,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
    
    # 查询所有符合条件的记录
    data = new_session.query(
        HumanStatisticsSmallGroup.statistics_result_1,
        HumanStatisticsSmallGroup.statistics_result_2
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsSmallGroup.date <= end_date)

    results = data.all()

    # 判断是否是多天数据
    is_multiple_days = False
    if start_date and end_date:
        # 计算日期差
        date_diff = (end_date - start_date).days + 1
        if date_diff > 1:
            is_multiple_days = True

    # 如果没有传入日期范围参数，则使用多天数据的计算逻辑
    if not query.date_range:
        is_multiple_days = True

    # 格式化数据
    distribution_data = []

    if is_multiple_days:
        # 多天数据：对相同分数范围的值进行求和汇总
        distribution_sum = {}

        for result in results:
            # 解析JSON数据
            distribution_dict = {}
            if result.statistics_result_2:
                try:
                    distribution_dict = json.loads(result.statistics_result_2)
                except json.JSONDecodeError:
                    logger.warning(f"无法解析JSON数据: {result.statistics_result_2}")
                    distribution_dict = {}

            # 将字典转换为列表格式并进行求和汇总
            for score_range, count in distribution_dict.items():
                count_value = float(count) if count is not None else 0
                if score_range in distribution_sum:
                    distribution_sum[score_range] += count_value
                else:
                    distribution_sum[score_range] = count_value

        # 将汇总后的字典转换为列表格式
        for score_range, total_count in distribution_sum.items():
            distribution_item = {
                "score_range": score_range,
                "count": total_count
            }
            distribution_data.append(distribution_item)
    else:
        # 单天数据：保持原有逻辑
        for result in results:
            # 解析JSON数据
            distribution_dict = {}
            if result.statistics_result_2:
                try:
                    distribution_dict = json.loads(result.statistics_result_2)
                except json.JSONDecodeError:
                    logger.warning(f"无法解析JSON数据: {result.statistics_result_2}")
                    distribution_dict = {}

            # 将字典转换为列表格式
            for score_range, count in distribution_dict.items():
                distribution_item = {
                    "score_range": score_range,
                    "count": float(count) if count is not None else 0
                }
                distribution_data.append(distribution_item)

    # 构建返回数据
    data = {
        "legend": ["分数分布"],
        "x_data": [item["score_range"] for item in distribution_data] if distribution_data and isinstance(
            distribution_data, list) else [],
        "y_data": [item["count"] for item in distribution_data] if distribution_data and isinstance(distribution_data,
                                                                                                    list) else []
    }

    logger.info(f"小组页面：计算分数分布柱状图，结果数量: {len(distribution_data)}")
    return BaseResponse(data=data, msg="获取小组界面：分数分布柱状图")




@g_score_analysis_router.post(path="/calculate_invalid_reviewed_count", response_model=BaseResponse,
                            summary="小组页面：计算无效评卷量")
async def calculate_invalid_reviewed_count_api(
        query: GCalculateReviewedCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算无效评卷量")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件
    filters = [
        HumanStatisticsSmallGroup.statistics_type == 8,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
    
    # 查询所有符合条件的记录并计算总和
    data = new_session.query(
        func.sum(HumanStatisticsSmallGroup.statistics_result_1)
    ).filter(*filters)

    # 添加日期范围条件
    if start_date:
        data = data.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        data = data.filter(HumanStatisticsSmallGroup.date <= end_date)

    result = data.scalar()

    # 如果结果为None，则总和为0，并转换为列表格式以符合BaseResponse的data字段类型要求
    total_count = [{"count": int(result)}] if result is not None else []
    logger.info(f"小组页面：计算无效评卷量，总数: {result}")
    return BaseResponse(data=total_count, msg="获取小组页面：无效评卷量")


@g_score_analysis_router.post(path="/calculate_effective_review_count", response_model=BaseResponse,
                            summary="小组页面：计算有效评卷量")
async def calculate_effective_review_count_api(
        query: GCalculateEffectiveReviewCountReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算有效评卷量")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 动态构建过滤条件，有效评卷量 (type=5)
    filters5 = [
        HumanStatisticsSmallGroup.statistics_type == 5,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters5.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters5.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters5.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters5.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters5.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

    data5 = new_session.query(func.sum(HumanStatisticsSmallGroup.statistics_result_1)).filter(*filters5)

    # 动态构建过滤条件，仲裁成功量 (type=11)
    filters11 = [
        HumanStatisticsSmallGroup.statistics_type == 11,
        HumanStatisticsSmallGroup.round_count == query.round_count,
        HumanStatisticsSmallGroup.task_type == query.task_type,
    ]
    if query.subject_id is not None:
        filters11.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
    if query.task_id:
        filters11.append(HumanStatisticsSmallGroup.task_id == query.task_id)
    if query.group_id:
        filters11.append(HumanStatisticsSmallGroup.group_id == query.group_id)
    if query.project_id:
        filters11.append(HumanStatisticsSmallGroup.project_id == query.project_id)
    if query.ques_group_id:
        filters11.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

    data11 = new_session.query(func.sum(HumanStatisticsSmallGroup.statistics_result_1)).filter(*filters11)

    # 添加日期范围条件
    if start_date:
        data5 = data5.filter(HumanStatisticsSmallGroup.date >= start_date)
        data11 = data11.filter(HumanStatisticsSmallGroup.date >= start_date)
    if end_date:
        data5 = data5.filter(HumanStatisticsSmallGroup.date <= end_date)
        data11 = data11.filter(HumanStatisticsSmallGroup.date <= end_date)

    result5 = data5.scalar() or 0
    result11 = data11.scalar() or 0
    total = result5 + result11
    total_count = [{"count": int(total)}] if total is not None else []
    logger.info(f"小组页面：计算有效评卷量，总数: {total_count}")
    return BaseResponse(data=total_count, msg="获取小组页面：有效评卷量")


@g_score_analysis_router.post(path="/calculate_average_speed", response_model=BaseResponse,
                            summary="小组页面：计算平均速度")
async def calculate_average_speed_api(
        query: GCalculateAverageSpeedReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算平均速度")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    # 判断是单天还是多天查询
    is_single_day = False
    if start_date and end_date:
        # 计算日期差
        date_diff = (end_date - start_date).days + 1
        if date_diff == 1:
            is_single_day = True

    # 如果没有传入日期范围参数，则使用多天数据的计算逻辑
    if not query.date_range:
        is_single_day = False

    try:
        if is_single_day:
            # 单天数据：直接查询指定日期的记录
            # 动态构建过滤条件
            filters_reviewed = [
                HumanStatisticsSmallGroup.statistics_type == 4,
                HumanStatisticsSmallGroup.round_count == query.round_count,
                HumanStatisticsSmallGroup.task_type == query.task_type,
                HumanStatisticsSmallGroup.date == start_date,
            ]
            if query.subject_id is not None:
                filters_reviewed.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
            if query.task_id:
                filters_reviewed.append(HumanStatisticsSmallGroup.task_id == query.task_id)
            if query.group_id:
                filters_reviewed.append(HumanStatisticsSmallGroup.group_id == query.group_id)
            if query.project_id:
                filters_reviewed.append(HumanStatisticsSmallGroup.project_id == query.project_id)
            if query.ques_group_id:
                filters_reviewed.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

            # 查询已阅量 (statistics_type == 4)
            reviewed_count = new_session.query(
                HumanStatisticsSmallGroup.statistics_result_1
            ).filter(*filters_reviewed).first()

            # 动态构建过滤条件
            filters_work_time = [
                HumanStatisticsSmallGroup.statistics_type == 10,
                HumanStatisticsSmallGroup.round_count == query.round_count,
                HumanStatisticsSmallGroup.task_type == query.task_type,
                HumanStatisticsSmallGroup.date == start_date,
            ]
            if query.subject_id is not None:
                filters_work_time.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
                if query.task_id:
                    filters_work_time.append(HumanStatisticsSmallGroup.task_id == query.task_id)
                if query.group_id:
                    filters_work_time.append(HumanStatisticsSmallGroup.group_id == query.group_id)
                if query.project_id:
                    filters_work_time.append(HumanStatisticsSmallGroup.project_id == query.project_id)
                if query.ques_group_id:
                    filters_work_time.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

            # 查询工作时间 (statistics_type == 10)
            work_time = new_session.query(
                HumanStatisticsSmallGroup.statistics_result_1
            ).filter(*filters_work_time).first()

            reviewed_count_val = int(
                reviewed_count.statistics_result_1) if reviewed_count and reviewed_count.statistics_result_1 is not None else 0.0
            work_time_val = float(
                work_time.statistics_result_1) if work_time and work_time.statistics_result_1 is not None else 0.0
        else:
            # 多天数据：查询日期范围内所有天的数据，分别累加已阅量和工作时间
            # 动态构建过滤条件
            filters_reviewed = [
                HumanStatisticsSmallGroup.statistics_type == 4,
                HumanStatisticsSmallGroup.round_count == query.round_count,
                HumanStatisticsSmallGroup.task_type == query.task_type,
            ]
            if query.subject_id is not None:
                filters_reviewed.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
                if query.task_id:
                    filters_reviewed.append(HumanStatisticsSmallGroup.task_id == query.task_id)
                if query.group_id:
                    filters_reviewed.append(HumanStatisticsSmallGroup.group_id == query.group_id)
                if query.project_id:
                    filters_reviewed.append(HumanStatisticsSmallGroup.project_id == query.project_id)
                if query.ques_group_id:
                    filters_reviewed.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

            # 查询已阅量总和 (statistics_type == 4)
            reviewed_data = new_session.query(
                func.sum(HumanStatisticsSmallGroup.statistics_result_1)
            ).filter(*filters_reviewed)

            # 动态构建过滤条件
            filters_work_time = [
                HumanStatisticsSmallGroup.statistics_type == 10,
                HumanStatisticsSmallGroup.round_count == query.round_count,
                HumanStatisticsSmallGroup.task_type == query.task_type,
            ]
            if query.subject_id is not None:
                filters_work_time.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
                if query.task_id:
                    filters_work_time.append(HumanStatisticsSmallGroup.task_id == query.task_id)
                if query.group_id:
                    filters_work_time.append(HumanStatisticsSmallGroup.group_id == query.group_id)
                if query.project_id:
                    filters_work_time.append(HumanStatisticsSmallGroup.project_id == query.project_id)
                if query.ques_group_id:
                    filters_work_time.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)

            # 查询工作时间总和 (statistics_type == 10)
            work_time_data = new_session.query(
                func.sum(HumanStatisticsSmallGroup.statistics_result_1)
            ).filter(*filters_work_time)

            # 添加日期范围条件
            if start_date:
                reviewed_data = reviewed_data.filter(HumanStatisticsSmallGroup.date >= start_date)
                work_time_data = work_time_data.filter(HumanStatisticsSmallGroup.date >= start_date)
            if end_date:
                reviewed_data = reviewed_data.filter(HumanStatisticsSmallGroup.date <= end_date)
                work_time_data = work_time_data.filter(HumanStatisticsSmallGroup.date <= end_date)

            reviewed_count_val = float(
                reviewed_data.scalar()) if reviewed_data and reviewed_data.scalar() is not None else 0.0
            work_time_val = float(
                work_time_data.scalar()) if work_time_data and work_time_data.scalar() is not None else 0.0

        # 无论单天还是多天，都执行平均速度计算
        if work_time_val > 0:
            average_speed = reviewed_count_val / (work_time_val / 3600)
            result = round_half_up(average_speed, 2)
        else:
            # 如果工作时间为0，则返回0
            result = 0.0

        # 添加调试信息
        logger.info(f"小组页面：计算平均速度，结果: {result} 份/小时")
        return BaseResponse(data=[result], msg="平均速度（份/小时）")

    except Exception as e:
        logger.error(f"计算平均速度失败：{e}")
        traceback.print_exc()
        return BaseResponse(data=[], msg="计算平均速度失败")



@g_score_analysis_router.post(path="/calculate_arbitration_pie_chart", response_model=BaseResponse,
                            summary="小组页面：计算仲裁饼图")
async def calculate_arbitration_pie_chart_api(
        query: GCalculateArbitrationPieChartReq,
        user: Any = Depends(get_current_user),
        new_session: Session = Depends(session_depend)
):
    logger.info(f"{user['username']} 小组页面：计算仲裁饼图")

    # 处理日期范围
    start_date, end_date = None, None
    if query.date_range:
        try:
            start_date_str, end_date_str = query.date_range.split('至')
            start_date = datetime.strptime(start_date_str.strip(), '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str.strip(), '%Y-%m-%d').date()
        except ValueError:
            pass

    try:
        # 动态构建过滤条件
        filters_arbitration = [
            HumanStatisticsSmallGroup.statistics_type == 7,
            HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
        ]
        if query.subject_id is not None:
            filters_arbitration.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
        if query.task_id:
            filters_arbitration.append(HumanStatisticsSmallGroup.task_id == query.task_id)
        if query.group_id:
            filters_arbitration.append(HumanStatisticsSmallGroup.group_id == query.group_id)
        if query.project_id:
            filters_arbitration.append(HumanStatisticsSmallGroup.project_id == query.project_id)
        if query.ques_group_id:
            filters_arbitration.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
        
        # 查询仲裁量总和 (statistics_type == 7)
        arbitration_data = new_session.query(
            func.sum(HumanStatisticsSmallGroup.statistics_result_1)
        ).filter(*filters_arbitration)

        # 动态构建过滤条件
        filters_effective = [
            HumanStatisticsSmallGroup.statistics_type == 5,
            HumanStatisticsSmallGroup.round_count == query.round_count,
            HumanStatisticsSmallGroup.task_type == query.task_type,
        ]
        if query.subject_id is not None:
            filters_effective.append(HumanStatisticsSmallGroup.subject_id == query.subject_id)
        if query.task_id:
            filters_effective.append(HumanStatisticsSmallGroup.task_id == query.task_id)
        if query.group_id:
            filters_effective.append(HumanStatisticsSmallGroup.group_id == query.group_id)
        if query.project_id:
            filters_effective.append(HumanStatisticsSmallGroup.project_id == query.project_id)
        if query.ques_group_id:
            filters_effective.append(HumanStatisticsSmallGroup.ques_group_id == query.ques_group_id)
        
        # 查询有效评卷量总和 (statistics_type == 5)
        effective_data = new_session.query(
            func.sum(HumanStatisticsSmallGroup.statistics_result_1)
        ).filter(*filters_effective)

        # 添加日期范围条件
        if start_date:
            arbitration_data = arbitration_data.filter(HumanStatisticsSmallGroup.date >= start_date)
            effective_data = effective_data.filter(HumanStatisticsSmallGroup.date >= start_date)
        if end_date:
            arbitration_data = arbitration_data.filter(HumanStatisticsSmallGroup.date <= end_date)
            effective_data = effective_data.filter(HumanStatisticsSmallGroup.date <= end_date)

        # 获取查询结果
        arbitration_sum = int(arbitration_data.scalar()) if arbitration_data.scalar() is not None else 0.0
        effective_sum = int(effective_data.scalar()) if effective_data.scalar() is not None else 0.0

        # 构建返回数据
        data = {
            "quality_data": {
                "x_data": ["仲裁", "评阅通过"],
                "y_data": [
                    {"value": arbitration_sum, "name": "仲裁"},
                    {"value": effective_sum, "name": "评阅通过"}
                ]
            }
        }

        logger.info(f"小组页面：计算仲裁饼图，仲裁: {arbitration_sum}, 评阅通过: {effective_sum}")
        return BaseResponse(data=data, msg="仲裁饼图数据")

    except Exception as e:
        logger.error(f"计算仲裁饼图失败：{e}")
        traceback.print_exc()
        return BaseResponse(data={}, msg="计算仲裁饼图失败")

