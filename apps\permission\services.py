from typing import Optional, Union
from sqlalchemy import and_, select

from apps.models.models import User<PERSON><PERSON>, RoleSysModuleFlag, SysModule, SysSubModule, SysFuncPoint, UserSysModuleFlag, UserDataPermission, Project, Subject, EmptyModuleUser
from apps.sys_manage.services import get_all_sys_info

# from apps.permission.schemas import AddDataPermission
from factory_apps import session_depend
from settings import configs


def get_user_role(new_session, user_id):
    user_role_info = new_session.query(UserRole.role_id).filter(UserRole.user_id == user_id).all()
    if not user_role_info:
        role_id_list = ["6"]
    else:
        role_id_list = [i[0] for i in user_role_info]
    return role_id_list


def get_selected_module_flag(new_session, role_id: Union[None, str, list] = None, user_id: Optional[str] = None):
    """
    获取可见对应模块唯一标识
    """
    if role_id:
        if type(role_id) == str:
            selected_info = new_session.query(RoleSysModuleFlag.module_flag).filter(RoleSysModuleFlag.role_id == role_id).all()
        else:
            selected_info = new_session.query(RoleSysModuleFlag.module_flag).filter(RoleSysModuleFlag.role_id.in_(role_id)).all()
    else:
        selected_info = new_session.query(UserSysModuleFlag.module_flag).filter(UserSysModuleFlag.user_id == user_id).all()

    if not selected_info:
        selected_module_flag = []
    else:
        selected_module_flag = [i[0] for i in selected_info]
    return selected_module_flag


def get_all_module_flag():
    # 获取所有模块唯一标识
    new_session = next(session_depend())

    p_module_info = new_session.query(SysModule.module_flag).all()
    if not p_module_info:
        p_module_flag = []
    else:
        p_module_flag = [i[0] for i in p_module_info]

    s_module_info = new_session.query(SysSubModule.sub_module_flag).all()
    if not s_module_info:
        s_module_flag = []
    else:
        s_module_flag = [i[0] for i in s_module_info]

    f_func_point_info = new_session.query(SysFuncPoint.func_point_flag).all()
    if not f_func_point_info:
        f_func_point_flag = []
    else:
        f_func_point_flag = [i[0] for i in f_func_point_info]

    all_module_flag = p_module_flag + s_module_flag + f_func_point_flag

    return all_module_flag


def filter_modules(modules, select_flags, is_delete=True):
    """
    获取权限展示状态
    is_delete=True 表示不在 select_flags 的模块会被删除，为 False 表示不删除，但是会设置键值对 show: False
    """
    filtered_modules = []
    for module in modules:
        # 先处理子模块
        if "children" in module:
            module["children"] = filter_modules(module["children"], select_flags, is_delete)

        # 处理功能点
        if "func_point" in module:
            module["func_point"] = [func for func in module["func_point"] if func["module_flag"] in select_flags or not is_delete]
            for func in module["func_point"]:
                func["show"] = func["module_flag"] in select_flags

        # 检查当前模块
        if module["module_flag"] in select_flags:
            module["show"] = True
            filtered_modules.append(module)
        elif module.get("children") or module.get("func_point"):
            # 如果子模块或功能点存在剩余部分，保留模块
            module["show"] = False
            filtered_modules.append(module)
        elif not is_delete:
            # 不删除，但设置 show: False
            module["show"] = False
            filtered_modules.append(module)

    return filtered_modules


def update_permission(new_session, update_module_flag: list, role_id: Union[None, str, list] = None, user_id: Optional[str] = None, c_user_id: Optional[str] = None) -> (bool, str):
    """
    更新功能权限
    """
    if user_id:
        selected_module_flag = get_selected_module_flag(new_session, user_id=user_id)
    else:
        selected_module_flag = get_selected_module_flag(new_session, role_id=role_id)

    if update_module_flag:
        if set(update_module_flag) == set(selected_module_flag):
            # return False, "所选模块无更新"
            return True, "更新功能权限成功"
    else:
        if user_id:
            # 将用户功能权限设置为空的情况
            empty_module_user = EmptyModuleUser(empty_user_id=configs.snow_worker.get_id(), user_id=user_id, c_user_id=c_user_id)
            new_session.add(empty_module_user)

    # 新增的模块插入
    insert_data = []
    is_delete_empty = True
    for flag in update_module_flag:
        if flag not in selected_module_flag:
            if role_id:
                new_module_flag_data = RoleSysModuleFlag(role_module_flag_id=configs.snow_worker.get_id(), role_id=role_id, module_flag=flag, c_user_id=c_user_id)
            else:
                new_module_flag_data = UserSysModuleFlag(user_module_flag_id=configs.snow_worker.get_id(), user_id=user_id, module_flag=flag, c_user_id=c_user_id)
                if is_delete_empty:
                    is_delete_empty = False
                    new_session.query(EmptyModuleUser).filter(EmptyModuleUser.user_id == user_id).delete()
            insert_data.append(new_module_flag_data)

    if insert_data:
        new_session.add_all(insert_data)

    # 减少的模块删除
    delete_data = []
    for flag in selected_module_flag:
        if flag not in update_module_flag:
            delete_data.append(flag)

    if delete_data:
        if role_id:
            new_session.query(RoleSysModuleFlag).filter(and_(RoleSysModuleFlag.role_id == role_id, RoleSysModuleFlag.module_flag.in_(delete_data))).delete()
        else:
            new_session.query(UserSysModuleFlag).filter(and_(UserSysModuleFlag.user_id == user_id, UserSysModuleFlag.module_flag.in_(delete_data))).delete()
    return True, "更新功能权限成功"


def get_user_func_flag_value(new_session, user_id: str, role_id: str):
    """
    获取当前用户功能点列表
    """
    if role_id == "1":
        func_flag_info = new_session.query(SysFuncPoint.func_point_id, SysFuncPoint.func_point_value).all()
    else:
        func_flag_info = (
            new_session.query(UserSysModuleFlag.module_flag, SysFuncPoint.func_point_value)
            .join(SysFuncPoint, SysFuncPoint.func_point_flag == UserSysModuleFlag.module_flag)
            .filter(UserSysModuleFlag.user_id == user_id)
            .all()
        )

        if not func_flag_info:
            func_flag_info = (
                new_session.query(RoleSysModuleFlag.module_flag, SysFuncPoint.func_point_value)
                .join(SysFuncPoint, SysFuncPoint.func_point_flag == RoleSysModuleFlag.module_flag)
                .filter(RoleSysModuleFlag.role_id == role_id)
                .all()
            )

    func_point_value = []
    if func_flag_info:
        func_point_value = [i[1] for i in func_flag_info]

    func_point_value = list(set(func_point_value))
    return func_point_value


def get_role_show_module(new_session, curr_role_id: str):
    """
    获取角色可以展示的功能模块
    """
    all_module_data = get_all_sys_info(new_session)

    # 当前角色可以展示的模块
    if curr_role_id in ["1", "2"]:
        curr_selected_module_flag = get_selected_module_flag(new_session, role_id="1")
        show_module = filter_modules(all_module_data, curr_selected_module_flag, is_delete=False)
    else:
        curr_selected_module_flag = get_selected_module_flag(new_session, role_id=curr_role_id)
        show_module = filter_modules(all_module_data, curr_selected_module_flag, is_delete=True)

    return show_module


def get_user_permission(new_session, user_id: Optional[str] = None, all_=False):
    """
    获取用户拥有的数据权限，all_ 为 True 时获取系统所有数据权限
    """
    permissions = new_session.query(UserDataPermission).where(UserDataPermission.user_id == user_id).all()

    return permissions


def get_selected_data_id(new_session, user_id: Optional[str] = None, all_=False):
    """
    获取用户拥有的数据权限，all_ 为 True 时获取系统所有数据权限
    """
    if all_:
        selected_info = new_session.query(Project.project_id, Subject.subject_id).outerjoin(Subject, Subject.project_id == Project.project_id).all()
    else:
        selected_info = new_session.query(UserDataPermission.project_id, UserDataPermission.subject_id).filter(UserDataPermission.user_id == user_id).all()

    selected_project_id = list(set([i[0] for i in selected_info if i[0]])) if selected_info else []
    selected_subject_id = list(set([i[1] for i in selected_info if i[1]])) if selected_info else []

    return selected_project_id, selected_subject_id


def get_user_selected_data_flag(new_session, user_id, role_id_list):
    """
    获取用户的数据权限
    现在通过id判断
    """
    if "1" in role_id_list:
        role_id = "1"
    else:
        role_id = role_id_list[0]
    if role_id == "1" or role_id == "2":
        user_project_id, user_subject_id = get_selected_data_id(new_session, user_id=user_id, all_=True)
    else:
        user_project_id, user_subject_id = get_selected_data_id(new_session, user_id=user_id)
    return user_project_id, user_subject_id


def update_user_data_permission(new_session, path_list: list[list], user_id: str, c_user_id):
    """
    更新数据权限
    """
    old_permissions = get_user_permission(new_session, user_id=user_id)

    new_permissions = []
    for path in path_list:
        permission = UserDataPermission(user_data_permission_id=configs.snow_worker.get_id(), user_id=user_id, c_user_id=c_user_id, u_user_id=c_user_id)
        if len(path) > 0:
            permission.project_id = path[0]
        if len(path) > 1:
            permission.subject_id = path[1]
        if len(path) > 2:
            permission.ques_type_code = path[2]
        if len(path) > 3:
            permission.ques_code = path[3]
        new_permissions.append(permission)

    adds, removes = compare_permissions(old_permissions, new_permissions)
    new_session.add_all(adds)
    for r in removes:
        new_session.delete(r)

    return True, "更新角色数据权限成功"


def compare_permissions(old_permissions, new_permissions):
    """
    对比两个权限列表，基于 project_id 和 subject_id 属性判断对象是否相同

    Args:
        old_permissions: 旧的权限列表 (UserDataPermission对象列表)
        new_permissions: 新的权限列表 (UserDataPermission对象列表)

    Returns:
        dict: 包含三个部分的字典：
            - added_permissions: 新增的权限对象列表
            - removed_permissions: 删除的权限对象列表
    """
    # 创建基于 (project_id, subject_id) 的集合
    old_keys = set()
    new_keys = set()

    # 用于存储权限对象到键的映射
    old_permission_map = {}
    new_permission_map = {}

    # 处理旧权限列表
    for perm in old_permissions:
        project_id = getattr(perm, "project_id", None)
        subject_id = getattr(perm, "subject_id", None)
        ques_type_code = getattr(perm, "ques_type_code", None)
        ques_code = getattr(perm, "ques_code", None)
        key = (project_id, subject_id, ques_type_code, ques_code)
        old_keys.add(key)
        old_permission_map[key] = perm

    # 处理新权限列表
    for perm in new_permissions:
        project_id = getattr(perm, "project_id", None)
        subject_id = getattr(perm, "subject_id", None)
        ques_type_code = getattr(perm, "ques_type_code", None)
        ques_code = getattr(perm, "ques_code", None)
        key = (project_id, subject_id, ques_type_code, ques_code)
        new_keys.add(key)
        new_permission_map[key] = perm

    # 找出新增的权限（new_permissions 中有，old_permissions 中没有）
    added_keys = new_keys - old_keys
    added_permissions = [new_permission_map[key] for key in added_keys]

    # 找出删除的权限（old_permissions 中有，new_permissions 中没有）
    removed_keys = old_keys - new_keys
    removed_permissions = [old_permission_map[key] for key in removed_keys]

    return added_permissions, removed_permissions


def get_pro_sub_list(new_session):
    """
    获取项目科目列表
    """
    pro_sub_info = new_session.query(Project.project_id, Subject.subject_id).join(Subject, Subject.project_id == Project.project_id).all()

    return pro_sub_info


def add_user_data_permission(new_session, user_id, project_id_list=None, subject_id_list=None):
    """
    添加用户数据权限（项目、科目列表）
    """
    add_permissions = []
    pro_sub_id_list = get_pro_sub_list(new_session)

    # 获取需要添加的科目列表
    if subject_id_list:
        has_subject_ids = new_session.query(UserDataPermission.subject_id).where(UserDataPermission.user_id == user_id).distinct().all()
        new_add_subject_ids = list(set(subject_id_list) - set(has_subject_ids))
        for add_s_id in new_add_subject_ids:
            p_id = next(p_id for p_id, s_id in pro_sub_id_list if s_id == add_s_id)
            permission = UserDataPermission(user_data_permission_id=configs.snow_worker.get_id(), user_id=user_id, project_id=p_id, subject_id=add_s_id)
            add_permissions.append(permission)

    # 获取需要添加的项目列表
    if project_id_list:
        new_add_project_ids = list(set(project_id_list) - set([p.project_id for p in add_permissions]))
        has_project_ids = new_session.query(UserDataPermission.project_id).where(UserDataPermission.user_id == user_id).distinct().all()
        new_add_project_ids = list(set(new_add_project_ids) - set(has_project_ids))
        for add_p_id in new_add_project_ids:
            permission = UserDataPermission(user_data_permission_id=configs.snow_worker.get_id(), user_id=user_id, project_id=add_p_id)
            add_permissions.append(permission)

    if add_permissions:
        new_session.add_all(add_permissions)


def get_data_permission_sub_id_list(new_session, user_id):
    """
    获取用户数据权限-科目列表
    """
    sub_ids = new_session.execute(select(UserDataPermission.subject_id).where(UserDataPermission.user_id == user_id)).scalars().all()
    sub_ids = list(set(sub_ids))
    return sub_ids


def get_data_permission_sub_code_list(new_session, user_id):
    """
    获取用户数据权限-科目列表
    """
    sub_ids = new_session.execute(
        select(UserDataPermission.subject_id, Subject.subject_code).outerjoin(Subject, UserDataPermission.subject_id == Subject.subject_id).where(UserDataPermission.user_id == user_id)
    ).all()
    sub_ids = [p[1] for p in sub_ids]
    sub_ids = list(set(sub_ids))
    return sub_ids


def get_data_permission_pro_id_list(new_session, user_id):
    """
    获取用户数据权限-项目列表
    """
    ids = new_session.execute(select(UserDataPermission.project_id).where(UserDataPermission.user_id == user_id)).scalars().all()
    ids = list(set(ids))
    return ids


def get_data_permission_ques_type_code_list(new_session, user_id):
    """
    获取用户数据权限-题型code列表
    """
    ids = new_session.execute(select(UserDataPermission.ques_type_code).where(UserDataPermission.user_id == user_id)).scalars().all()
    ids = list(set(ids))
    return ids


def get_selected_data_flag(new_session, user_id: Optional[str] = None, all_=False):
    """
    获取用户拥有的数据权限，all_ 为 True 时获取系统所有数据权限
    """
    selected_project_flag = []
    selected_subject_flag = []

    if all_:
        selected_info = new_session.query(Project.project_id, Subject.subject_id).outerjoin(Subject, Subject.project_id == Project.project_id).all()
    else:
        selected_info = new_session.query(UserDataPermission.project_id, UserDataPermission.subject_id).filter(UserDataPermission.user_id == user_id).all()

    if selected_info:
        selected_project_flag = list(set([i[0] for i in selected_info if i[0]]))
        selected_subject_flag = list(set([i[1] for i in selected_info if i[1]]))

    return selected_project_flag, selected_subject_flag
