"""
从模式用到的 api
"""
import json
import os
from typing import Any

from fastapi import APIRouter, Depends

from apps.operation_ques.op_engine_mark_services import request_file_download, invoke_op_engine
from apps.operation_ques.schemas import ExecuteEngineReq
from apps.operation_ques.slave_op_engine_mark_services import modify_path
from settings import logger, configs

from apps.base.schemas import BaseResponse
from apps.users.services import get_current_user
from helper import response_utils

salve_op_mark_router = APIRouter()


@salve_op_mark_router.get(path="/check_engine_exists", response_model=BaseResponse, summary="检查操作题题引擎是否存在")
async def check_engine_exists(user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 检查操作题题引擎是否存在")
    if not os.path.exists(configs.OP_ENGINE_ABSOLUTE_PATH):
        return BaseResponse(code=response_utils.no_field, msg="操作题评分引擎不存在")
    return BaseResponse()


@salve_op_mark_router.post(path="/execute_engine_mark", response_model=BaseResponse, summary="执行操作题引擎评分")
async def execute_engine_mark(query: ExecuteEngineReq, user: Any = Depends(get_current_user)):
    logger.info(f"{user['username']} 执行操作题引擎评分")
    record_id, file_id, ques_id, ques_file_name, answer_file_name = query.model_dump().values()
    url = f"{configs.MASTER_SERVER_URL}{configs.VERSION}/op_mark/download_op_engine_file_stream"
    token = f"Bearer {user['token']}"
    ques_save_path = os.path.join(configs.PROJECT_PATH, f"op_engine_file\\{ques_id}")
    # 下载试题文件
    full_ques_save_path = os.path.join(ques_save_path, ques_file_name)
    if not os.path.exists(full_ques_save_path):
        data = {
            "ques_id": ques_id,
            "file_name": ques_file_name
        }
        msg = request_file_download(url, data, token, ques_save_path, ques_file_name)
        if msg:
            return BaseResponse(code=response_utils.server_error, msg=msg)
    else:
        logger.info(f"{ques_file_name} 试题文件已存在")

    # 下载考生作答文件
    data = {
        "ques_id": ques_id,
        "file_name": answer_file_name
    }
    answer_save_path = os.path.join(configs.PROJECT_PATH, f"op_engine_file\\{ques_id}\\stu_data")
    msg = request_file_download(url, data, token, answer_save_path, answer_file_name)
    if msg:
        return BaseResponse(code=response_utils.server_error, msg=msg)
    # 重新定义考生作答文件里的试题文件路径和结果文件生成路径
    full_answer_save_path = os.path.join(answer_save_path, answer_file_name)
    full_result_save_path = modify_path(full_ques_save_path, full_answer_save_path) + ".jdetrs"
    # 请求引擎
    is_success, msg = invoke_op_engine(file_id, full_answer_save_path)
    if not is_success:
        return BaseResponse(code=response_utils.server_error, msg=msg)
    # todo: 解析文件返回结果
    return BaseResponse()
