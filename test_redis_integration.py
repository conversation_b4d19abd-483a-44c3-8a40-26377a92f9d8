"""
Redis集成测试脚本
测试Redis服务管理器的各项功能
"""
import time
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from factory_apps.redis_db.redis_service_manager import RedisServiceManager, ensure_redis_running, stop_redis
from factory_apps.redis_db.redis_cahe import init_redis_connection, redis_session
from settings.logger import logger


def test_redis_service_manager():
    """测试Redis服务管理器"""
    print("=" * 60)
    print("Redis服务管理器测试")
    print("=" * 60)
    
    manager = RedisServiceManager()
    
    # 1. 测试Redis文件检查
    print("\n1. 检查Redis可执行文件...")
    if os.path.exists(manager.redis_exe_path):
        print(f"✓ Redis可执行文件存在: {manager.redis_exe_path}")
    else:
        print(f"✗ Redis可执行文件不存在: {manager.redis_exe_path}")
        print("请下载Redis Windows版本并放置在Redis-x64-3.0.504目录中")
        return False
    
    # 2. 测试端口检查
    print(f"\n2. 检查端口可用性...")
    port_available = manager.is_port_available(manager.redis_host, manager.redis_port)
    print(f"端口 {manager.redis_port} 可用: {port_available}")
    
    # 3. 测试Redis启动
    print(f"\n3. 测试Redis服务启动...")
    success, message = manager.start_redis_service()
    if success:
        print(f"✓ Redis启动成功: {message}")
    else:
        print(f"✗ Redis启动失败: {message}")
        return False
    
    # 4. 测试Redis连接
    print(f"\n4. 测试Redis连接...")
    if manager.is_redis_running():
        print("✓ Redis服务正在运行")
        
        # 测试基本操作
        try:
            with redis_session() as r:
                r.set('test_key', 'test_value')
                value = r.get('test_key')
                if value == 'test_value':
                    print("✓ Redis读写测试成功")
                else:
                    print("✗ Redis读写测试失败")
                r.delete('test_key')
        except Exception as e:
            print(f"✗ Redis操作测试失败: {e}")
    else:
        print("✗ Redis服务未运行")
        return False
    
    # 5. 测试Redis停止
    print(f"\n5. 测试Redis服务停止...")
    stop_success = manager.stop_redis_service()
    if stop_success:
        print("✓ Redis停止成功")
    else:
        print("✗ Redis停止失败")
    
    # 等待一下再检查
    time.sleep(2)
    if not manager.is_redis_running():
        print("✓ 确认Redis服务已停止")
    else:
        print("✗ Redis服务仍在运行")
    
    return True


def test_redis_auto_start():
    """测试Redis自动启动功能"""
    print("\n" + "=" * 60)
    print("Redis自动启动功能测试")
    print("=" * 60)
    
    # 1. 测试ensure_redis_running函数
    print("\n1. 测试自动启动函数...")
    success, message = ensure_redis_running()
    if success:
        print(f"✓ 自动启动成功: {message}")
    else:
        print(f"✗ 自动启动失败: {message}")
        return False
    
    # 2. 测试init_redis_connection函数
    print("\n2. 测试连接初始化...")
    connection_success = init_redis_connection()
    if connection_success:
        print("✓ Redis连接初始化成功")
    else:
        print("✗ Redis连接初始化失败")
        return False
    
    # 3. 测试多次调用
    print("\n3. 测试重复调用...")
    for i in range(3):
        success, message = ensure_redis_running()
        if success:
            print(f"  第{i+1}次调用: ✓ {message}")
        else:
            print(f"  第{i+1}次调用: ✗ {message}")
            return False
    
    return True


def test_configuration():
    """测试配置文件生成"""
    print("\n" + "=" * 60)
    print("Redis配置测试")
    print("=" * 60)
    
    manager = RedisServiceManager()
    
    # 1. 测试配置文件创建
    print("\n1. 测试配置文件创建...")
    try:
        config_path = manager.create_redis_config()
        if os.path.exists(config_path):
            print(f"✓ 配置文件创建成功: {config_path}")
            
            # 读取并验证配置文件内容
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if f"port {manager.redis_port}" in content:
                    print("✓ 端口配置正确")
                else:
                    print("✗ 端口配置错误")
                    return False
                
                if f"bind {manager.redis_host}" in content:
                    print("✓ 主机配置正确")
                else:
                    print("✗ 主机配置错误")
                    return False
        else:
            print(f"✗ 配置文件创建失败: {config_path}")
            return False
    except Exception as e:
        print(f"✗ 配置文件创建异常: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("卓帆智能阅卷系统 - Redis集成测试")
    print("测试开始时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("配置测试", test_configuration),
        ("服务管理器测试", test_redis_service_manager),
        ("自动启动测试", test_redis_auto_start),
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n开始执行: {test_name}")
            result = test_func()
            test_results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
            test_results.append((test_name, False))
    
    # 清理：确保Redis服务停止
    print(f"\n清理测试环境...")
    try:
        stop_redis()
        print("✓ Redis服务已停止")
    except Exception as e:
        print(f"清理时出错: {e}")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Redis集成功能正常")
        return True
    else:
        print("❌ 部分测试失败，请检查配置和环境")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
