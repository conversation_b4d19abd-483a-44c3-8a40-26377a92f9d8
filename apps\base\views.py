from fastapi import APIRouter
from fastapi.openapi.docs import (
    get_redoc_html,
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html
)

from apps.base.schemas import BaseResponse

base_router = APIRouter()


@base_router.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title="卓帆电子化考试阅卷管理系统API文档",
        swagger_js_url="/server_static/swagger-ui/swagger-ui-bundle.js",
        swagger_css_url="/server_static/swagger-ui/swagger-ui.css",
    )


@base_router.get(path="/sys_alive", response_model=BaseResponse, summary="判断程序是否存活")
async def sys_alive():
    return BaseResponse()
