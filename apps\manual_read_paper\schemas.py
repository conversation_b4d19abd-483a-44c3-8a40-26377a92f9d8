from typing import Optional, Literal, Union, List
from pydantic import BaseModel, Field
from pydantic.v1 import root_validator

from apps.base.schemas import PaginationModel


class CreateManualGroupReq(BaseModel):
    manual_group_name: str = Field(..., title="阅卷小组名称")
    project_id: str = Field(..., title="项目id")
    subject_id: str = Field(..., title="科目id")
    process_id: str = Field(..., title="阅卷流程id")
    create_num: int = Field(..., title="创建小组个数", ge=0)
    expert_ai_num: int = Field(0, title="AI 评分个数", ge=0)
    arbitrator_ai_num: int = Field(0, title="AI 仲裁个数", ge=0)
    quality_ai_num: int = Field(0, title="AI 质检个数", ge=0)
    remark: Optional[str] = Field(None, title="备注")


class SaveGroupUserReq(BaseModel):
    manual_group_user_list: list = Field(..., title="阅卷小组成员信息")
    # manual_group_user_list = [
    #     {
    #         "group_id": "25",
    #         "expert": ["36", "37"],  # 专家 id 列表
    #         "expert_name": ["abc", "AI评分1"],  # 专家姓名列表
    #         "arbitrator": ["39"],
    #         "arbitrator_name": ["def"],
    #         "quality": ["38"],
    #         "quality_name": ["xyz"],
    #     }
    # ]


class RandomDistributeGroupPeopleReq(BaseModel):
    manual_group_name: str = Field(..., title="用户在创建小组时输入的阅卷小组名称")
    new_group_id_list: list = Field(..., title="阅卷小组id列表")
    new_group_name_list: list = Field(..., title="阅卷小组名称列表")
    expert_num: int = Field(..., title="专家数量", ge=0)
    arbitrator_num: int = Field(..., title="仲裁数量", ge=0)
    quality_num: int = Field(..., title="质检数量", ge=0)
    expert_ai_num: int = Field(..., title="AI 评分个数", ge=0)
    arbitrator_ai_num: int = Field(..., title="AI 仲裁个数", ge=0)
    quality_ai_num: int = Field(..., title="AI 质检个数", ge=0)


class GetManualGroupReq(PaginationModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    manual_group_name: Optional[str] = Field(None, title="阅卷小组名称")
    manual_process_id: Optional[str] = Field(None, title="人工阅卷工作流id")
    c_name: Optional[str] = Field(None, title="创建人")
    is_create_manual_task: bool = Field(False, title="是否是创建任务时调用接口")


class UpdateManualGroupReq(BaseModel):
    manual_group_id: str = Field(..., title="阅卷小组id")
    manual_group_name: str = Field(..., title="阅卷小组名称")
    project_id: str = Field(..., title="项目id")
    subject_id: str = Field(..., title="科目id")
    remark: Optional[str] = Field(None, title="备注")
    expert: list = Field(..., title="专家 user_id 列表")
    arbitrator: list = Field(..., title="仲裁 user_id 列表")
    quality: list = Field(..., title="质检 user_id 列表")
    is_use_ai: bool = Field(..., title="是否使用AI助手")


class DeleteManualGroupReq(BaseModel):
    manual_group_id: Union[str, list] = Field(..., title="阅卷小组id")


class GetManualPeopleReq(PaginationModel):
    role_id: Literal["3", "4", "5", "6", "7"] = Field(..., title="角色id")
    selected_user_id: Optional[list] = Field([], title="前端已被选择状态下的用户 id 列表")
    name: Optional[str] = Field(None, title="姓名")
    is_distri: Literal[1, 2, 3] = Field(..., title="分配状态：1 表示全部，2 表示未分配，3 表示已分配")
    user_type: Literal[None, 1, 2] = Field(None, title="用户类型，1 为人，2 为AI")
    expert_ai_num: int = Field(..., title="AI 评分个数", ge=0)
    arbitrator_ai_num: int = Field(..., title="AI 仲裁个数", ge=0)
    quality_ai_num: int = Field(..., title="AI 质检个数", ge=0)


class CreateManualReadTaskReq(BaseModel):
    m_read_task_name: str = Field(..., title="阅卷任务名称", max_length=100)
    project_id: str = Field(..., title="项目id")
    subject_id: str = Field(..., title="科目id")
    manual_process_id: str = Field(..., title="人工阅卷工作流id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_type_code: str = Field(..., title="所要评分的题型的简码")
    ques_order: Optional[str] = Field(None, title="所要评分的题的题号")
    ques_code: str = Field(..., title="所要评分的题的编号")
    ques_id: str = Field(..., title="所要评分的题的id")
    score_step: float = Field(..., title="打分间隔")
    fetch_score_way: Literal[None, 1, 2, 3, 4] = Field(None, title="取分方式", description="专家个数超过1个时该字段不为空，1 为取最高分，2 为取最低分，3 为取平均分, 4 为去除最高和最低分取平均分")
    fetch_score_scope: Literal[None, 1, 2, 3, 4] = Field(None, title="平均范围", description="fetch_score_way 为3时该字段不为空，1 为全分组，2 为高分组，3 为中分组，4 为高分组")
    fetch_score_option: Literal[None, 1, 2, 3] = Field(None, title="平均分值计算", description="fetch_score_way 为3时该字段不为空，1 为向下舍入，2 为向上舍入，3 为四舍五入")
    arbitrate_threshold_type: Literal[None, 1, 2] = Field(None, title="仲裁阈值类型", description="有仲裁时不为空，1 表示百分比，2 表示分值")
    arbitrate_threshold: Optional[int] = Field(None, title="仲裁阈值，如果为 None 取模式里的值")
    arbitrate_score_diff: Literal[None, 1, 2, 3] = Field(None, title="仲裁分差取值方式", description="有仲裁且专家数量大于2才不为空，1 表示平均值，2 表示最大偏差，3 表示最小偏差")
    quality_ratio: Optional[int] = Field(None, title="质检抽样比例", gt=0, le=100)
    quality_upper_limit: Optional[int] = Field(None, title="质检上限比例")
    quality_lower_limit: Optional[int] = Field(None, title="质检下限比例")
    quality_reevaluation: Optional[int] = Field(None, title="质检重评比例")
    m_read_group_id_list: list = Field(..., title="绑定阅卷小组id")
    business_id: Optional[str] = Field(None, title="业务题型id")
    remark: Optional[str] = Field(None, title="备注")


class CreateBatchManualReadTaskReq(BaseModel):
    task_list: List[CreateManualReadTaskReq] = Field(..., title="阅卷任务信息列表")

    @root_validator
    def check_task_list_not_empty(cls, values):
        if not values.get('task_list'):
            raise ValueError('task_list 不能为空')
        return values


class ManualReadTaskIdReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")


class ManualReadTaskIdListReq(BaseModel):
    m_read_task_id_list: list = Field(..., title="阅卷任务id列表")


class UpdateManualReadTaskReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    m_read_task_name: str = Field(..., title="阅卷任务名称", max_length=100)
    project_id: str = Field(..., title="项目id")
    subject_id: str = Field(..., title="科目id")
    manual_process_id: str = Field(..., title="人工阅卷工作流id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_type_code: str = Field(..., title="所要评分的题型的简码")
    ques_order: Optional[str] = Field(None, title="所要评分的题的题号")
    ques_code: str = Field(..., title="所要评分的题的编号")
    score_step: float = Field(..., title="打分间隔")
    fetch_score_way: Literal[None, 1, 2, 3, 4] = Field(None, title="取分方式", description="专家个数超过1个时该字段不为空，1 为取最高分，2 为取最低分，3 为取平均分, 4 为去除最高和最低分取平均分")
    fetch_score_scope: Literal[None, 1, 2, 3, 4] = Field(None, title="平均范围", description="fetch_score_way 为3时该字段不为空，1 为全分组，2 为高分组，3 为中分组，4 为高分组")
    fetch_score_option: Literal[None, 1, 2, 3] = Field(None, title="平均分值计算", description="fetch_score_way 为3时该字段不为空，1 为向下舍入，2 为向上舍入，3 为四舍五入")
    arbitrate_threshold_type: Literal[None, 1, 2] = Field(None, title="仲裁阈值类型", description="有仲裁时不为空，1 表示百分比，2 表示分值")
    arbitrate_threshold: Optional[int] = Field(None, title="仲裁阈值，如果为 None 取模式里的值")
    arbitrate_score_diff: Literal[None, 1, 2, 3] = Field(None, title="仲裁分差取值方式", description="有仲裁且专家数量大于2才不为空，1 表示平均值，2 表示最大偏差，3 表示最小偏差")
    quality_ratio: Optional[int] = Field(None, title="质检抽样比例", gt=0, le=100)
    quality_upper_limit: Optional[int] = Field(None, title="质检上限比例")
    quality_lower_limit: Optional[int] = Field(None, title="质检下限比例")
    quality_reevaluation: Optional[int] = Field(None, title="质检重评比例")
    m_read_group_id_list: list = Field(..., title="绑定阅卷小组id")
    remark: Optional[str] = Field(None, title="备注")


class GetManualReadTaskReq(PaginationModel):
    m_read_task_id_list: Optional[list] = Field([], title="阅卷任务id")
    real_group_id_list: Optional[list] = Field([], title="该角色在任务里的所在组group_id，与m_read_task_id_list一一对应")
    m_read_task_name: Optional[str] = Field(None, title="阅卷任务名称")
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    paper_name: Optional[str] = Field(None, title="试卷名称")
    is_distri_task: Optional[bool] = Field(False, title="是否是获取分配任务时调用的接口")


class GetDistriManualTaskReq(PaginationModel):
    m_read_task_name: Optional[str] = Field(None, title="阅卷任务名称")
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    paper_name: Optional[str] = Field(None, title="试卷名称")
    task_type: Literal[None, 3, 4, 5] = Field(None, title="指定任务类型，None 则根据用户角色获取任务，3 表示专家评分，4 表示仲裁，5 表示质检")


class GetDistriManualTaskDetailReq(BaseModel):
    m_read_task_id: Optional[str] = Field(None, title="阅卷任务id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_id: Optional[str] = Field(None, title="试题id")
    ques_code: Optional[str] = Field(None, title="试题编码")
    ques_type_code: str = Field(None, title="所要评分的题型的简码")
    group_id: Optional[str] = Field(None, title="阅卷小组id")
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    batch_num: Optional[int] = Field(10, title="每批次获取的数量")
    history_id: Optional[str] = Field(None, title="历史记录 id")
    distri_answer_id: Optional[str] = Field(None, title="作答信息分配id")
    user_id: Optional[str] = Field(None, title="用户id", description="AI 开始阅卷任务的时候传的参数")
    role_id: Optional[str] = Field(None, title="角色id", description="AI 开始阅卷任务的时候传的参数")


class StartManualAiTaskReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_id: str = Field(None, title="试题id")
    ques_code: str = Field(..., title="试题编码")
    ques_type_code: str = Field(..., title="所要评分的题型的简码")
    group_id: Optional[str] = Field(None, title="阅卷小组id")
    project_id: str = Field(..., title="项目id")
    subject_id: str = Field(..., title="科目id")
    batch_num: Optional[int] = Field(10, title="每批次获取的数量")
    ai_type: int = Field(0, title="是否是机器人账号，0 为否，非 0 为机器人账号")
    execute_user_id: str = Field(..., title="用户id")
    execute_role_id: str = Field(..., title="用户角色id")
    is_mark_again: bool = Field(False, title="是否进行重新评分")


class GetAiMarkTaskProcessReq(BaseModel):
    m_read_task_id_list: list = Field(..., title="阅卷任务id")


class ManualMarkReq(BaseModel):
    m_read_task_id: Optional[str] = Field(None, title="阅卷任务id，用于第一次评分")
    distri_answer_id: Optional[str] = Field(None, title="作答信息分配id，用于第一次评分")
    ques_id: str = Field(..., title="试题id")
    expert_mark_score: float = Field(..., title="专家评分分数")
    mark_parse: Optional[str] = Field(None, title="评分解析")
    manual_mark_id: Optional[str] = Field(None, title="评分id，用于重评")
    group_id: str = Field(..., title="阅卷小组id，用于执行专家打分后的操作")
    mark_type: Literal[1, 2, 3] = Field(..., title="1 表示第一次评分，2 表示重新评分，3 表示质检返评")
    manual_aq_id: Optional[str] = Field(None, title="人工质检评分id，mark_type 为 3 时，该字段不能为空")
    stu_secret_num: Optional[str] = Field(None, title="考生密号")
    history_id: Optional[str] = Field(None, title="当前历史记录id，用于重评")
    step_score_list: Optional[list] = Field(None, title="操作题的评分步骤得分列表")


class ManualArbitrateReq(BaseModel):
    manual_aq_id: str = Field(..., title="仲裁数据的id")
    m_read_task_id: str = Field(..., title="阅卷任务id")
    distri_answer_id: str = Field(..., title="作答信息分配id")
    ques_id: str = Field(..., title="试题id")
    manual_mark_id_list: Optional[list] = Field(None, title="评分id列表")
    arbitrate_type: Literal[1, 2] = Field(..., title="1 表示第一次仲裁，2 表示重新仲裁")
    arbitrate_mark_score: float = Field(..., title="仲裁评分分数")
    arbitrator_parse: Optional[str] = Field(None, title="评分解析")
    group_id: str = Field(..., title="阅卷小组id")
    history_id: Optional[str] = Field(None, title="当前历史记录id，用于重新仲裁")
    step_score_list: Optional[list] = Field(None, title="操作题的评分步骤得分列表")


class ManualQualityReq(BaseModel):
    manual_aq_id: str = Field(..., title="质检数据的id")
    m_read_task_id: str = Field(..., title="阅卷任务id")
    distri_answer_id: str = Field(..., title="作答信息分配id")
    ques_id: str = Field(..., title="试题id")
    manual_mark_id: Optional[str] = Field(None, title="评分id")
    quality_result: bool = Field(..., title="质检结果")
    quality_suggestion: Optional[str] = Field(None, title="质检意见")
    group_id: Optional[str] = Field(..., title="阅卷小组id")
    quality_type: Literal[1, 2] = Field(..., title="1 表示第一次质检，2 表示重新质检")
    history_id: Optional[str] = Field(None, title="当前历史记录id，用于重新质检")


class GetMarkHistoryReq(PaginationModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    stu_secret_num: Optional[str] = Field(None, title="考生密号")
    start_time: Optional[str] = Field(None, title="开始时间")
    end_time: Optional[str] = Field(None, title="结束时间")


class DistriAnswerIdReq(BaseModel):
    history_id: str = Field(..., title="当前历史记录id")
    distri_answer_id: str = Field(..., title="作答信息分配id")
    paper_id: Optional[str] = Field(None, title="试卷编号")
    ques_id: Optional[str] = Field(None, title="试题id")
    ques_type_code: Optional[str] = Field(None, title="试题类型")


class GetManualTaskProcessReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    project_id: str = Field(..., title="项目id")
    subject_id: str = Field(..., title="科目id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_code: str = Field(..., title="试题编码")
    ques_id: str = Field(..., title="试题id")
    group_id: str = Field(..., title="阅卷小组id")


class GetArbitrateProcessReq(BaseModel):
    task_group_list: list = Field(..., title="阅卷任务id及其group_id")
    # task_group_list = [
    #     {
    #         "m_read_task_id": "1",
    #         "group_id": "1"
    #     }
    # ]


class GetManualQualityProcessReq(BaseModel):
    m_read_task_id: list = Field(..., title="阅卷任务id列表")


class GetExpertTaskProcessReq(BaseModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    group_id: str = Field(..., title="阅卷小组id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_code: str = Field(..., title="试题编码")
    ques_id: str = Field(..., title="试题id")


class GetPreNextMarkHistoryReq(BaseModel):
    history_id: str = Field(..., title="当前历史记录id")
    get_type: Literal[0, 1] = Field(..., title="0 表示上一条，1 表示下一条")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_id: str = Field(..., title="试题id")
    m_read_task_id: str = Field(..., title="阅卷任务id")
    manual_mark_id: str = Field(..., title="评分id")
    current_page: int = Field(..., title="当前页页码")
    page_size: int = Field(..., title="每页多少条")


class GetAiMarkTaskResultReq(PaginationModel):
    m_read_task_id: Optional[str] = Field(None, title="阅卷任务id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_code: str = Field(None, title="试题编码")
    subject_id: Optional[str] = Field(None, title="科目id")
    execute_user_id: str = Field(..., title="用户id")
    execute_role_id: str = Field(..., title="用户角色id")
    ai_expert_mark_score: Optional[float] = Field(0, title="查询条件，AI 评分分数")


class GetMarkTaskResultReq(PaginationModel):
    m_read_task_id: str = Field(..., title="阅卷任务id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    # ques_id: str = Field(..., title="试题id")
    execute_user_id: str = Field(..., title="用户id")
    execute_role_id: str = Field(..., title="用户角色id")
    score_range: Optional[list] = Field([], title="查询条件，评分分数范围")
    time_range: Optional[list] = Field([], title="查询条件，评分时间范围")
    stu_secret_num: Optional[str] = Field(None, title="查询条件，考生密号")


