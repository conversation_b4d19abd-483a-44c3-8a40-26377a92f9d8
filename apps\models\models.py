import datetime

from sqlalchemy import Column, Foreign<PERSON>ey, String, Integer, Float, BigInteger, JSON, Boolean, Enum, Text, DateTime, Index, DECIMAL, PrimaryKeyConstraint
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy import func
from sqlalchemy.dialects.mysql import LONGTEXT

# from factory_apps.mysql_db.databases import engine

# 声明基类
Base = declarative_base()


class ImportToolMixin:
    """导入工具标志基类"""

    __abstract__ = True
    from_tool = Column(Integer, default=0, comment="数据来源，0 系统（excel格式），1 导入工具，2 系统（sqlite格式）")


class DateTimeBaseMixin:
    """时间基类"""

    __abstract__ = True
    # datetime.datetime.now 为本机时间，func.now() 是数据库服务时间，统一使用本机时间
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")


class TaskExecuteRecord(Base, DateTimeBaseMixin):
    __tablename__ = "t_task_execute_record"
    __table_args__ = {"comment": "任务数据记录表，主要用于记录进度"}
    record_id = Column(String(50), primary_key=True, comment="任务记录id")
    record_type = Column(Integer, nullable=False, comment="任务的类型，1 表示人工阅卷任务，2 表示处理AI定标异常任务")
    success_count = Column(BigInteger, comment="成功个数")
    total_count = Column(BigInteger, comment="总个数")
    progress = Column(Float(precision=2), comment="进度，两位小数")
    extra_data = Column(JSON, comment="额外数据")
    start_time = Column(DateTime(timezone=True), default=func.now(), comment="开始时间")
    end_time = Column(DateTime(timezone=True), comment="结束时间")
    manual_read_task = relationship("ManualReadTask")


class AdministrativeRegion(Base):
    __tablename__ = "t_administrative_region"
    __table_args__ = {"comment": "行政区域表"}
    region_id = Column(String(50), primary_key=True, comment="行政区域id")
    region_level = Column(Integer, comment="行政区域级别，1 为省，2 为市，3 为区县")
    parent_region_code = Column(String(8), comment="父级行政区域编号")
    region_code = Column(String(10), comment="行政区域编号")
    region_name = Column(String(40), comment="行政区域名")


class UserInfo(Base, DateTimeBaseMixin):
    __tablename__ = "t_user_info"
    __table_args__ = {"comment": "用户信息表"}
    user_id = Column(String(50), primary_key=True, comment="用户id")
    username = Column(String(50), unique=True, index=True, comment="用户名")
    password = Column(String(255), nullable=False, comment="密码")
    name = Column(String(50), comment="姓名")
    phone = Column(String(11), comment="手机号")
    round_count = Column(Integer, comment="所属轮次")
    system_user_type = Column(Integer, comment="1 表示系统用户，2 表示业务用户")
    reside_address = Column(String(100), comment="居住地址")
    id_card = Column(String(18), comment="身份证号")
    email = Column(String(50), comment="邮箱")
    province_code = Column(String(2), comment="行政区域（省编号）")
    city_code = Column(String(4), comment="行政区域（市编号）")
    district_code = Column(String(6), comment="行政区域（区县编号）")
    work_unit = Column(String(100), comment="所在工作单位")
    post_code = Column(String(10), comment="邮编")
    bank_card_num = Column(String(19), comment="银行卡号")
    open_account_bank = Column(String(20), comment="开户银行")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_builtin = Column(Boolean, default=False, comment="是否为系统内置")
    already_login = Column(Boolean, default=False, comment="是否已经登录过")
    lock_state = Column(Integer, default=1, comment="1 为未锁定，2 为锁定，一旦锁定则无法修改和删除")
    user_type = Column(Integer, default=1, comment="用户类型，1 为人，2 为 AI")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class Role(Base, DateTimeBaseMixin):
    __tablename__ = "t_role"
    __table_args__ = {"comment": "角色表"}
    real_role_id = Column(String(50), primary_key=True, comment="角色id")
    role_id = Column(String(50), nullable=False, unique=True, index=True, comment="角色编码，用于业务逻辑判断")
    role_name = Column(String(40), unique=True, index=True, nullable=False, comment="角色名称")
    role_desc = Column(String(255), comment="角色描述")
    lock_state = Column(Integer, default=1, comment="1 为未锁定，2 为锁定，锁定则无法修改和删除")
    is_builtin = Column(Boolean, default=False, comment="是否为系统内置")
    system_user_type = Column(Integer, comment="1 表示系统角色，2 表示业务角色")

    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")
    user_role = relationship("UserRole")
    role_sys_module_flag = relationship("RoleSysModuleFlag")
    manual_group_user = relationship("ManualGroupUser")


class UserRole(Base, DateTimeBaseMixin):
    __tablename__ = "t_user_role"
    __table_args__ = {"comment": "用户角色表"}
    user_role_id = Column(String(50), primary_key=True)
    user_id = Column(String(50), comment="绑定的用户id", nullable=False)
    role_id = Column(String(50), ForeignKey("t_role.role_id", ondelete="CASCADE", onupdate="CASCADE"), comment="绑定的角色id", nullable=False)
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class SysDefaultPwd(Base, DateTimeBaseMixin):
    __tablename__ = "t_sys_default_pwd"
    __table_args__ = {"comment": "系统默认密码表"}
    pwd_id = Column(String(50), primary_key=True, comment="密码id")
    pwd_type = Column(Integer, default=1, comment="密码类型，1 表示用户默认和重置密码")
    pwd_value = Column(String(255), nullable=False, comment="密码，使用 AES 加密的密码")


class SysModule(Base, DateTimeBaseMixin):
    __tablename__ = "t_sys_module"
    __table_args__ = {"comment": "系统模块表"}
    module_id = Column(String(50), primary_key=True, comment="模块id")
    module_flag = Column(BigInteger, nullable=False, unique=True, comment="模块唯一标识")
    rank = Column(Integer, comment="排序序号")
    module_name = Column(String(30), nullable=False, comment="模块名称")
    web_path = Column(String(60), nullable=False, comment="前端路由路径")
    icon = Column(String(40), comment="前端模块图标")
    show_link = Column(Boolean, default=True, comment="是否显示在导航栏")
    sys_sub_module = relationship("SysSubModule")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class SysSubModule(Base, DateTimeBaseMixin):
    __tablename__ = "t_sys_sub_module"
    __table_args__ = {"comment": "系统子模块表"}
    sub_module_id = Column(String(50), primary_key=True, comment="子模块id")
    sub_module_flag = Column(BigInteger, nullable=False, unique=True, comment="模块唯一标识")
    rank = Column(Integer, comment="排序序号")
    module_id = Column(String(50), ForeignKey("t_sys_module.module_id", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, comment="父模块id")
    sub_module_name = Column(String(30), nullable=False, comment="子模块名称")
    sub_web_path = Column(String(60), nullable=False, comment="前端路由路径")
    sub_icon = Column(String(40), comment="前端模块图标")
    sub_show_link = Column(Boolean, default=True, comment="是否显示在导航栏")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class SysFuncPoint(Base, DateTimeBaseMixin):
    __tablename__ = "t_sys_func_point"
    __table_args__ = {"comment": "系统功能点表"}
    func_point_id = Column(String(50), primary_key=True, comment="功能点id")
    func_point_flag = Column(BigInteger, nullable=False, unique=True, comment="功能点唯一标识")
    rank = Column(Integer, comment="排序序号")
    parent_module_flag = Column(String(50), nullable=False, comment="上一级模块唯一标识")
    func_point_name = Column(String(30), nullable=False, comment="功能点名称")
    func_point_value = Column(String(100), nullable=False, comment="功能值，对应前端代码的值")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class RoleSysModuleFlag(Base, DateTimeBaseMixin):
    __tablename__ = "t_role_sys_module_flag"
    __table_args__ = {"comment": "角色对应模块标识表"}
    role_module_flag_id = Column(String(50), primary_key=True, comment="角色对应模块标识id")
    role_id = Column(String(50), ForeignKey("t_role.role_id", ondelete="CASCADE", onupdate="CASCADE"), comment="绑定的角色id", nullable=False)
    module_flag = Column(BigInteger, nullable=False, comment="模块唯一标识")
    is_builtin = Column(Boolean, default=False, comment="是否为系统内置")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class UserSysModuleFlag(Base, DateTimeBaseMixin):
    __tablename__ = "t_user_sys_module_flag"
    __table_args__ = {"comment": "用户对应模块标识表"}
    user_module_flag_id = Column(String(50), primary_key=True, comment="角色对应模块标识id")
    user_id = Column(String(50), comment="绑定的用户id")
    module_flag = Column(BigInteger, nullable=False, comment="模块唯一标识")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class EmptyModuleUser(Base, DateTimeBaseMixin):
    __tablename__ = "t_empty_module_user"
    __table_args__ = {"comment": "空功能权限用户记录表"}
    empty_user_id = Column(String(50), primary_key=True, comment="空功能权限用户记录id")
    user_id = Column(String(50), comment="用户id")
    c_user_id = Column(String(50), comment="创建用户id")


class ExamType(Base, DateTimeBaseMixin):
    __tablename__ = "t_exam_type"
    __table_args__ = {"comment": "考试类型表"}
    exam_type_id = Column(String(50), primary_key=True, nullable=False, index=True)
    exam_type_name = Column(String(50), unique=True, index=True, nullable=False, comment="考试类别名称")
    remark = Column(Text, comment="备注")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class ExamWay(Base, DateTimeBaseMixin):
    __tablename__ = "t_exam_way"
    __table_args__ = {"comment": "考试形式表"}
    exam_way_id = Column(String(50), primary_key=True, nullable=False, index=True)
    exam_way_name = Column(String(10), unique=True, index=True, nullable=False, comment="考试形式名称")
    remark = Column(Text, comment="备注")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class Project(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_project"
    __table_args__ = {"comment": "项目信息表"}
    project_id = Column(String(50), primary_key=True, nullable=False, index=True)
    project_name = Column(String(50), unique=True, nullable=False, comment="项目名称")
    exam_type_id = Column(String(50), comment="考试类别id")
    exam_way_id = Column(String(50), comment="考试形式id")
    is_active = Column(Boolean, default=True, comment="使用状态")  # 启用或禁用
    remark = Column(Text, comment="备注")
    lock_state = Column(Integer, default=1, comment="1 为未锁定，2 为锁定，一旦锁定则无法修改和删除")
    project_flag = Column(BigInteger, nullable=False, index=True, comment="项目唯一标识")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")
    exam_paper = relationship("ExamPaper")
    manual_read_paper_group = relationship("ManualReadPaperGroup")
    manual_read_task = relationship("ManualReadTask")


class Subject(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_subject"
    __table_args__ = {"comment": "科目信息表"}
    subject_id = Column(String(50), primary_key=True)
    subject_name = Column(String(50), comment="科目名称", nullable=False)
    project_id = Column(String(50), comment="所属项目id")
    is_active = Column(Boolean, default=True, comment="使用状态")  # 启用或禁用
    remark = Column(Text, comment="备注")
    subject_flag = Column(BigInteger, nullable=False, index=True, comment="科目唯一标识")
    subject_code = Column(String(50), comment="科目编码")
    exam_mode = Column(Integer, comment="考试模式，0 表示抽参，1 表示抽卷")
    quality_mode = Column(Integer, comment="质检模式，0 表示不质检，1 表示评阅过程质检，2 表示评阅结束质检")
    reevaluated_count = Column(Integer, comment="随机复评次数")
    mark_mode = Column(Integer, comment="评分模式，1 表示得分点评分，2 表示整体评分")
    spy_num = Column(Integer, comment="每n份分发一次间谍卷")
    copy_ans_threshold = Column(Integer, comment="作答抄袭阈值")
    similar_paper_threshold = Column(Integer, comment="雷同卷阈值")
    copy_ques_threshold = Column(Integer, comment="抄袭题干阈值")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")
    lock_state = Column(Integer, default=1, comment="1 为未锁定，2 为锁定，一旦锁定则无法修改和删除")
    exam_paper = relationship("ExamPaper")
    ques_answer_mark = relationship("QuesAnswerMark")
    manual_read_paper_group = relationship("ManualReadPaperGroup")
    manual_read_task = relationship("ManualReadTask")
    subject_total_score = Column(DECIMAL(10, 2), comment="科目总分")
    subject_pass_score = Column(DECIMAL(10, 2), comment="科目及格分数")
    subject_score_interval = Column(DECIMAL(10, 2), comment="允许评分区间")
    verification_status = Column(Integer, default=0, comment="核验状态：0 未核验，1 核验通过，2 核验不通过")

class SubjectSession(Base):
    __tablename__ = "t_subject_session"
    __table_args__ = {"comment": "科目场次表"}
    session_id = Column(String(50), primary_key=True, comment="场次id")
    subject_id = Column(String(50), comment="科目id")
    exam_session = Column(Integer, comment="场次")


class WorkFlowEleTemp(Base, DateTimeBaseMixin):
    __tablename__ = "t_work_flow_ele_temp"
    __table_args__ = {"comment": "流程元素模板表"}
    ele_temp_id = Column(String(50), primary_key=True, nullable=False, index=True)
    ele_temp_name = Column(String(100), unique=True, index=True, comment="元素模板名称")
    ele_temp_type = Column(String(40), unique=True, index=True, comment="元素模板类型")
    ele_temp_json = Column(JSON, comment="元素模板JSON")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")

    work_flow_main_process_instance = relationship("WorkFlowMainProcessInstance")


class WorkFlowMainProcess(Base, DateTimeBaseMixin):
    __tablename__ = "t_work_flow_main_process"
    __table_args__ = {"comment": "工作流主流程表"}
    process_id = Column(String(50), primary_key=True, nullable=False, index=True)
    process_name = Column(String(100), unique=True, nullable=False, comment="工作流主流程名称")
    process_json = Column(JSON, comment="工作流主流程JSON")
    lock_state = Column(Integer, default=1, comment="1 为未锁定，2 为锁定，一旦锁定则无法修改和删除")
    is_builtin = Column(Boolean, default=False, comment="是否为系统内置")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")

    work_flow_main_process_instance = relationship("WorkFlowMainProcessInstance")
    manual_read_paper_group = relationship("ManualReadPaperGroup")
    manual_read_task = relationship("ManualReadTask")


class WorkFlowMainProcessInstance(Base, DateTimeBaseMixin):
    __tablename__ = "t_work_flow_main_process_instance"
    __table_args__ = {"comment": "工作流主流程元素实例表"}
    instance_id = Column(String(50), primary_key=True, nullable=False, index=True)
    instance_self_id = Column(String(40), nullable=False, index=True, comment="元素实例自带的id")
    parent_process_id = Column(String(50), ForeignKey("t_work_flow_main_process.process_id", ondelete="CASCADE", onupdate="CASCADE"), comment="所属主流程id", nullable=False)
    instance_ele_type = Column(String(40), ForeignKey("t_work_flow_ele_temp.ele_temp_type", ondelete="CASCADE", onupdate="CASCADE"), comment="所属元素模板类型", nullable=False)
    instance_ele_name = Column(String(100), comment="元素实例名称", nullable=True)
    instance_people_num = Column(Integer, comment="实例人数", nullable=True)
    instance_target_id = Column(String(40), comment="元素实例下一级id", nullable=True)
    instance_target_text = Column(String(100), comment="元素实例文本", nullable=True)
    instance_target_percentage = Column(Integer, comment="元素实例的值比例，比如多个专家时的判分阈值", nullable=True)
    instance_target_logic = Column(Integer, comment="父级的逻辑判断，比如通过为 1，不通过为 2", nullable=True)
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class ManualReadPaperGroup(Base, DateTimeBaseMixin):
    __tablename__ = "t_manual_read_paper_group"
    __table_args__ = {"comment": "人工阅卷小组表"}
    manual_group_id = Column(String(50), primary_key=True, nullable=False, index=True)
    manual_group_name = Column(String(100), unique=True, index=True, comment="小组名称")
    manual_project_id = Column(String(50), ForeignKey("t_project.project_id", ondelete="CASCADE", onupdate="CASCADE"), comment="所属项目id")
    manual_subject_id = Column(String(50), ForeignKey("t_subject.subject_id", ondelete="CASCADE", onupdate="CASCADE"), comment="所属科目id")
    manual_process_id = Column(String(50), ForeignKey("t_work_flow_main_process.process_id", ondelete="SET NULL", onupdate="CASCADE"), comment="所属阅卷主流程id")
    expert_ai_num = Column(Integer, default=0, comment="AI 评分个数，0 表示不使用，大于 0 表示使用的个数")
    arbitrator_ai_num = Column(Integer, default=0, comment="AI 仲裁个数，0 表示不使用，大于 0 表示使用的个数")
    quality_ai_num = Column(Integer, default=0, comment="AI 质检个数，0 表示不使用，大于 0 表示使用的个数")
    remark = Column(Text, comment="备注")
    lock_state = Column(Integer, default=1, comment="1 为未锁定，2 为锁定，一旦锁定则无法修改和删除")

    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")

    manual_group_user = relationship("ManualGroupUser")
    manual_read_task_group = relationship("ManualReadTaskGroup")


class ManualGroupUser(Base, DateTimeBaseMixin):
    __tablename__ = "t_manual_group_user"
    __table_args__ = {"comment": "阅卷小组对应用户表"}
    manual_group_user_id = Column(String(50), primary_key=True, nullable=False, index=True)
    manual_group_id = Column(String(50), ForeignKey("t_manual_read_paper_group.manual_group_id", ondelete="CASCADE", onupdate="CASCADE"), comment="绑定的人工阅卷小组id", nullable=False)
    user_id = Column(String(50), comment="绑定的用户id", nullable=False)
    role_id = Column(String(50), ForeignKey("t_role.role_id", ondelete="CASCADE", onupdate="CASCADE"), comment="绑定的用户角色", nullable=False)
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class UserDataPermission(Base, DateTimeBaseMixin):
    __tablename__ = "t_user_data_permission"
    __table_args__ = {"comment": "用户数据权限表，用于控制用户能看到的项目、科目对应的数据"}
    user_data_permission_id = Column(String(50), primary_key=True, index=True)
    user_id = Column(String(50), comment="绑定的用户id", nullable=False)
    # data_type = Column(Integer, comment="数据类型：1项目；2科目；3 试卷；4题型；5试题")
    # data_id = Column(Integer, comment="与数据类型对应的id")
    project_id = Column(String(50), comment="绑定的项目id")
    subject_id = Column(String(50), comment="绑定的科目id")
    paper_id = Column(String(50), comment="绑定的试卷id")
    ques_type_code = Column(String(50), comment="绑定的题型code")
    ques_code = Column(String(50), comment="绑定的试题code")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class PolicyBase(Base, ImportToolMixin):
    __tablename__ = "t_policy_base"
    __table_args__ = {"comment": "评分策略基础表"}
    policy_id = Column(String(50), primary_key=True, comment="评分策略id")
    policy_desc = Column(String(1000), comment="评分策略描述")
    attach_type = Column(Integer, comment="评分策略附带类型，1 指分数，2 指权重，3指比例，没有则为空。比如 少选得固定分，该字段填1")


class QuesType(Base):
    __tablename__ = "t_ques_type"
    __table_args__ = {"comment": "试题类型表"}
    ques_type_id = Column(String(50), primary_key=True, comment="试题类型id")
    ques_type_name = Column(String(20), unique=True, nullable=False, comment="题型名称")
    ques_type_code = Column(String(1), unique=True, nullable=False, comment="题型简码")
    ques_mark_rule = Column(JSON, comment="评分规则选项描述")
    parent_type = Column(Integer, comment="大题型分类：1 表示客观题，2 表示主观题，3 表示操作题, 4 表示组合题")
    ques_remark = Column(Text, comment="备注")


class BusinessQuesType(Base, ImportToolMixin):
    __tablename__ = "t_business_ques_type"
    __table_args__ = {"comment": "业务试题类型表，用于兼容抽参保存试题类型分数"}
    business_ques_type_id = Column(String(50), primary_key=True, comment="业务试题类型id，只作为该行数据唯一标识")
    project_id = Column(String(50), comment="所属项目id")
    subject_id = Column(String(50), comment="所属科目id")
    ques_type_code = Column(String(1), comment="题型基类")
    parent_ques_type_id = Column(String(50), comment="业务试题类型父id")
    ques_type_name = Column(String(50), comment="业务题型名称")
    ques_type_score = Column(DECIMAL(10, 2), comment="业务题型分值")
    ques_score_list = Column(JSON, default=[], comment="试题分值列表（分值保留两位小数）")
    policy_id = Column(String(1000), comment="评分策略id")
    policy_value = Column(String(10), comment="评分策略附带类型的值。比如 少选得固定分，该字段填固定分值")


class MarkRule(Base, DateTimeBaseMixin):
    __tablename__ = "t_mark_rule"
    __table_args__ = {"comment": "客观题判分规则表"}
    rule_id = Column(String(50), primary_key=True, comment="规则id")
    project_id = Column(String(50), comment="所属项目id")
    subject_id = Column(String(50), comment="所属科目id")
    rule_name = Column(String(30), unique=True, nullable=False, comment="规则名称")
    rule_year = Column(Integer, comment="考试年份")
    remark = Column(Text, comment="备注")
    include_ques_type_code = Column(JSON, nullable=False, comment="包含的试题类型简码")  # eg: ["A", "B"]
    lock_state = Column(Integer, default=1, comment="1 为未锁定，2 为锁定，一旦锁定则无法修改和删除")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")

    mark_rule_detail = relationship("MarkRuleDetail")
    exam_paper = relationship("ExamPaper")


class MarkRuleDetail(Base):
    __tablename__ = "t_mark_rule_detail"
    __table_args__ = {"comment": "客观题判分规则详情表"}
    rule_detail_id = Column(String(50), primary_key=True, comment="规则详情id")
    parent_rule_id = Column(String(50), ForeignKey("t_mark_rule.rule_id", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, comment="父规则id")
    ques_type_code = Column(String(1), nullable=False, comment="题型简码")
    mark_rule_detail = Column(JSON, nullable=False, comment="规则详情配置")


class ExamPaper(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_exam_paper"
    __table_args__ = {"comment": "试卷信息表"}
    paper_id = Column(String(50), primary_key=True, comment="试卷ID")
    paper_code = Column(String(30), comment="试卷编号")
    paper_name = Column(String(100), nullable=False, index=True, comment="试卷名称")
    project_id = Column(String(50), ForeignKey("t_project.project_id", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, comment="所属项目id")
    subject_id = Column(String(50), ForeignKey("t_subject.subject_id", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, comment="所属科目id")
    total_score = Column(DECIMAL(10, 2), nullable=False, comment="满分分值")
    remark = Column(Text, comment="备注")
    mark_rule_id = Column(String(50), ForeignKey("t_mark_rule.rule_id", ondelete="CASCADE", onupdate="CASCADE"), comment="绑定的评分规则id")#, ForeignKey("t_mark_rule.rule_id", ondelete="CASCADE", onupdate="CASCADE")
    lock_state = Column(Integer, default=1, comment="1 为未锁定，2 为锁定，一旦锁定则无法修改和删除")
    save_grade = Column(Integer, default=1, comment="1 为未保存，2 为已保存")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class ExamQuestion(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_exam_question"
    __table_args__ = {"comment": "试题表"}
    ques_id = Column(String(50), primary_key=True, comment="试题id")
    parent_ques_id = Column(String(50), index=True, comment="父试题id，如果没有父试题就为 null")
    knowledge_show = Column(String(255), comment="题库的题号")
    ques_code = Column(String(30), nullable=False, index=True, comment="试题编号")
    ques_type_code = Column(String(1), nullable=False, comment="题型简码")
    business_ques_type_id = Column(String(50), comment="业务试题类型id")
    small_ques_num = Column(String(30), comment="组合题小题序号")
    small_ques_int = Column(String(30), comment="小小题序号")
    ai_img_desc = Column(JSON, comment="ai解析题干图片后的试题描述")
    ques_desc = Column(JSON, nullable=False, comment="试题描述")  # JSON 格式以适应有富文本格式
    ques_choices = Column(JSON, default=[], comment="试题选项（模板里选项之间使用◎☆◎隔开）")
    # # 选择题
    # choices1 = [
    #     {
    #         "code": "1",
    #         "options": "A.选项描述"
    #     },
    #     {
    #         "code": "2",
    #         "options": "B.选项描述"
    #     }
    # ]
    # # 判断题
    # choices2 = [
    #     {
    #         "code": "0",  # "0" 表示错误
    #         "options": "选项描述"
    #     },
    #     {
    #         "code": "1",  # "1" 表示正确
    #         "options": "选项描述"
    #     }
    # ]
    ques_difficulty = Column(String(5), comment="试题难度")
    material_desc = Column(JSON, default={}, comment="试题素材描述，图片id 为键，图片描述为值")
    # material_desc： 图片id 为键，图片描述为值 {"EA8FA356C646C11158FC817A0E1B5226.jpg": "图中是一个生物兴趣小组参观中山大学生物博物馆时拍摄的某生物化石的图片，图片中化石上写着萨斯特鱼龙化石。"}
    standard_answer = Column(JSON, default=[], comment="参考答案")
    standard_answer_html = Column(JSON, default=[], comment="参考答案的html")
    synonymous_answer = Column(JSON, default=[], comment="同义答案")
    exclude_answer = Column(JSON, default=[], comment="排除答案")
    # standard_answer = ["A", "C", "E"]
    # standard_choices_code 根据 choices 的 code 和 standard_answer 转化
    standard_choices_code = Column(JSON, default=[], comment="参考答案代号，对应choices的code")
    d_out_of_order_group = Column(JSON, default=[], comment="填空题答案乱序序号列表，可有多组，序号从 1 开始，比如：[[1, 2], [3, 4]]")
    weight = Column(JSON, default=[], comment="填空题权重，比如：[[1,1],[2]]")
    standard_parse = Column(Text, comment="参考答案解析")
    e_mark_rule = Column(Text, comment="简答题评分规则")
    ques_score = Column(DECIMAL(10, 2), comment="大/小题分数")
    ques_mark_point = Column(JSON, default=[], comment="评分要点/评分标准")
    is_encrypt = Column(Integer, default=0, comment="是否加密，0 表示否，1 表示是")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")
    manual_read_state = Column(Integer, default=1, comment="人工阅卷分配状态，1 表示未分配，2 表示已分配")
    manual_mark = relationship("ManualMark")


class SmallExamQuestion(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_small_exam_question"
    __table_args__ = {"comment": "小小题表"}
    ques_id = Column(String(50), primary_key=True, comment="试题id")
    parent_ques_id = Column(String(50), index=True, nullable=False)
    ques_code = Column(String(30), nullable=False, index=True, comment="试题编号")
    ques_type_code = Column(String(1), nullable=False, comment="题型简码")
    business_ques_type_id = Column(String(50), comment="业务试题类型id")
    small_ques_num = Column(String(30), comment="小小题序号")
    ques_choices = Column(JSON, default=[], comment="小小题选项（模板里选项之间使用◎☆◎隔开）")
    # # 选择题
    # choices1 = [
    #     {
    #         "code": "1",
    #         "options": "A.选项描述"
    #     },
    #     {
    #         "code": "2",
    #         "options": "B.选项描述"
    #     }
    # ]
    # # 判断题
    # choices2 = [
    #     {
    #         "code": "0",  # "0" 表示错误
    #         "options": "选项描述"
    #     },
    #     {
    #         "code": "1",  # "1" 表示正确
    #         "options": "选项描述"
    #     }
    # ]
    ques_score = Column(DECIMAL(10, 2), comment="小小题分数")
    standard_answer = Column(JSON, default=[], comment="参考答案")
    standard_answer_html = Column(JSON, default=[], comment="参考答案的html")
    # standard_answer = ["A", "C", "E"]
    # standard_choices_code 根据 choices 的 code 和 standard_answer 转化
    standard_choices_code = Column(JSON, default=[], comment="参考答案代号，对应choices的code")
    max_input = Column(String(255), comment="简答题允许的最大字数")
    answer_group = Column(String(255), comment="答案分组")
    pic_content = Column(LONGTEXT, comment="底图")
    small_ques_int = Column(String(30), comment="小小题序号")
    mark_rule = Column(Text, comment="小小题评分规则")
    ques_mark_point = Column(JSON, default=[], comment="评分要点/评分标准")
    d_out_of_order_group = Column(JSON, default=[], comment="填空题答案乱序序号列表，可有多组，序号从 1 开始，比如：[[1, 2], [3, 4]]")
    standard_parse = Column(Text, comment="参考答案解析")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class QuesSetStdState(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_ques_set_std_state"
    __table_args__ = {"comment": "试题定标状态表"}
    state_id = Column(String(50), primary_key=True, comment="状态id")
    ques_id = Column(String(50), comment="试题id")
    set_std_state = Column(
        Integer, default=1, comment="定标状态，1 表示未定标，2 表示定标中，3 表示已定标，4 表示已暂停，5 表示已取消，6 表示待定标（表示用户点击了定标，但是前面有其他试题正在定标，此时的排队状态）"
    )
    running_state = Column(
        Integer, default=0, comment="运行状态，0 表示初始状态，1 表示正在获取数据中，2 表示正在标记中，3 表示评分中，4 表示暂停，5 表示正在取消中，6 表示已取消，7 表示已完成，8 表示已标记"
    )
    pause_state = Column(Integer, comment="暂停时的任务状态，1 表示正在获取数据中，2 表示正在标记中，3 表示评分中")
    launch_ip = Column(String(15), comment="记录启动或者继续定标任务的 IP 地址")
    set_std_time = Column(DateTime(timezone=True), comment="定标开始时间")
    set_std_end_time = Column(DateTime(timezone=True), comment="定标结束时间")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class QuesAiMark(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_ques_ai_mark"
    __table_args__ = {"comment": "试题AI评分表"}
    mark_id = Column(String(50), primary_key=True, comment="评分id")
    ques_id = Column(String(50), comment="试题id")
    ques_code = Column(String(50), comment="试题编号")
    small_ques_num = Column(Integer, comment="组合题小题序号")
    mark_state = Column(Integer, default=1, comment="评分状态，1 表示未评分，2 表示评分中，3 表示已评分")
    running_state = Column(Integer, default=0, comment="运行状态，0 表示初始状态，1 表示正在获取数据中，2 表示正在标记中，3 表示评分中，4 表示暂停，5 表示正在取消中，6 表示已取消，7 表示已完成")
    pause_state = Column(Integer, comment="暂停时的任务状态，1 表示正在获取数据中，2 表示正在标记中，3 表示评分中")
    start_time = Column(DateTime(timezone=True), comment="评分开始时间")
    end_time = Column(DateTime(timezone=True), comment="评分结束时间")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class PaperDetail(Base, ImportToolMixin):
    __tablename__ = "t_paper_detail"
    __table_args__ = {"comment": "试卷详情表"}
    paper_detail_id = Column(String(50), primary_key=True, comment="试卷详情id")
    paper_id = Column(String(50), nullable=False, comment="试卷id")
    paper_code = Column(String(30), nullable=False, comment="试卷编号")
    parent_ques_id = Column(String(50), comment="父试题id，如果没有父试题就为空")
    ques_id = Column(String(50), comment="试题id")
    ques_code = Column(String(30), nullable=False, comment="试题编号")
    ques_order = Column(String(12), nullable=False, comment="试题序号")
    ques_type_code = Column(String(1), nullable=False, comment="题型简码")
    ques_score_list = Column(JSON, default=[], nullable=False, comment="试题分值列表（分值保留两位小数）")
    manual_read_state = Column(Integer, default=1, comment="人工阅卷分配状态，1 表示未分配，2 表示已分配", nullable=False)


class ExamStudent(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_exam_student"
    __table_args__ = {"comment": "考生信息表"}
    stu_id = Column(String(50), primary_key=True, comment="考生id")
    stu_name = Column(String(255), comment="考生姓名")
    allow_exam_num = Column(String(50), index=True, nullable=False, comment="准考证号")
    stu_secret_num = Column(String(30), index=True, nullable=False, comment="考生密号")
    cert_number = Column(String(255), comment="证件号码")
    project_id = Column(String(50), comment="项目id（资格id）")
    project_code = Column(String(50), comment="项目编号（资格编号）")
    subject_id = Column(String(50), comment="科目id")
    subject_code = Column(String(50), comment="科目编号")
    exam_province_code = Column(String(15), comment="省级考区编号")
    exam_province_name = Column(String(40), comment="省级考区名称")
    exam_city_code = Column(String(255), comment="市级考区编号")
    exam_city_name = Column(String(255), comment="市级考区名称")
    exam_session = Column(Integer, comment="场次")
    exam_point_code = Column(String(15), comment="考点编号")
    exam_point_name = Column(String(40), comment="考点名称")
    exam_room_code = Column(String(15), comment="考场编号")
    exam_room_name = Column(String(40), comment="考场名称")
    is_violation = Column(Integer, comment="违纪标记（0：正常、-2：违纪）")
    is_exam = Column(Integer, comment="缺考标记（0：正常、-1：缺考）")
    is_all_do = Column(Integer, comment="全部作答标记：0 全做；1 存在未做")
    lock_state = Column(Integer, default=1, comment="1 为未锁定，2 为锁定，一旦锁定则无法修改和删除")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")

    __table_args__ = (
        Index("subject_id", "exam_session"),
    )



class StuTraj(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_stu_traj"
    traj_id = Column(String(50), primary_key=True, comment="轨迹id")
    allow_exam_num = Column(String(30), comment="准考证号")
    project_id = Column(String(50), comment="所属项目id")
    subject_id = Column(String(50), comment="所属科目id")
    paper_id = Column(String(50), comment="试卷id")
    process = Column(Float(precision=2), comment="作答进度")
    start_time = Column(DateTime(timezone=True), comment="作答开始时间")
    end_time = Column(DateTime(timezone=True), comment="作答结束时间")
    response_time = Column(Integer, comment="作答时长(秒为单位)")
    remain_time = Column(Integer, comment="考试剩余时长(秒为单位)")
    submit_type = Column(
        Integer,
        comment="交卷类型： 0:未交卷；1:异常处理；2:自动交卷(考生端倒计时结束自动交卷)；"
        "3:统一收卷(监考端执行统一收卷（模考）)；4:作弊收卷，5:强制收卷；"
        "6:单机收卷(考生端导出作答包单机收卷)；7:备份收卷；8:导入作答；9:补考，10:主动交卷（考生主动点击交卷按钮交卷）；"
        "11:自动化交卷(自动化或时间受限导致无题可做的交卷)",
    )

    __table_args__ = (Index("ix_t_stu_traj_allow_num_paper_id", "allow_exam_num", "paper_id"), {"comment": "考生作答轨迹表"})  # 创建联合索引


class StudentSubject(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_stu_subject"  # 考生科目表
    __table_args__ = {"comment": "科目场次表"}
    stu_subject_id = Column(String(50), primary_key=True, comment="考生科目数据id")
    allow_exam_num = Column(String(30), comment="准考证号")
    subject_id = Column(String(50), comment="科目id", index=True)
    ptr_file = Column(String(255), comment="ptr 文件路径")
    exam_session = Column(Integer, comment="场次")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class StuAnswer(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_stu_answer"
    answer_id = Column(String(50), comment="作答id", index=True)
    allow_exam_num = Column(String(30), comment="准考证号", index=True)
    stu_secret_num = Column(String(30), comment="考生密号", index=True)
    project_id = Column(String(50), comment="所属项目id")
    subject_id = Column(String(50), comment="所属科目id")
    exam_session = Column(Integer, comment="场次")
    paper_id = Column(String(50), comment="试卷id")
    ques_id = Column(String(50), nullable=False, comment="试题id")
    parent_ques_id = Column(String(50), index=True, comment="父试题id，如果没有父试题就为空")
    ques_order = Column(Integer, comment="大题序号")
    sub_ques_order = Column(Integer, comment="小题序号")
    confuse_ques_order = Column(Integer, comment="大题乱序序号")
    confuse_sub_ques_order = Column(Integer, comment="小题乱序序号")
    confuse_options = Column(String(6), comment="选项乱序串，如：DACB")
    ques_type_code = Column(String(1), nullable=False, comment="题型简码（以小题为单位）")
    is_do = Column(Boolean, comment="考生是否作答;0：未作答；1：已作答")
    answer_order = Column(Integer, comment="第几次作答")
    answer_type = Column(Integer, comment="作答数据类型（0代表纯文本、1代表h5、2表示rtf，为空也是0）")
    stu_answer = Column(LONGTEXT, comment="作答答案，填空题多个答案使用分隔符◎☆◎隔开")
    stu_answer_html = Column(Text, comment="作答答案h5")
    stu_answer_html_simple = Column(JSON, comment="作答答案h5字数")
    answer_image_path = Column(String(512), comment="考生作答图片存放路径")
    answer_image_width = Column(Integer, comment="考生作答图片宽")
    answer_image_height = Column(Integer, comment="考生作答图片高")
    answer_image_sign = Column(String(255), comment="考生作答图片签名")
    stu_answer_summary = Column(Text, comment="作答答案摘要")
    stu_answer_summary_html = Column(Text, comment="作答答案摘要h5")
    word_count = Column(Integer, comment="作答字数")
    is_marking = Column(Integer, comment="0 未选做；1 必做，已选做；2 系统自动选做")
    op_file = Column(String(255), comment="操作题存放考生作答生成的文件路径")
    mark_state = Column(Integer, comment="评分状态：1 表示未评分，2 表示评分成功，3 表示评分失败，4 表示作答答案待人工判断", index=True)
    manual_distri_state = Column(Integer, default=1, comment="人工阅卷分配状态：1 表示未分配，2 表示已分配")
    running_state = Column(Integer, default=1, comment="当前任务状态：1 表示不在评分任务中，2 表示评分中，3 表示评分完成", index=True)
    set_std_score = Column(DECIMAL(10, 2), comment="定标分数")
    manual_stu_score = Column(DECIMAL(10, 2), comment="人工阅卷考生得分")
    verification_status = Column(Integer, default=0, comment="核验状态：0 未核验，1 核验通过，2 核验不通过")
    
    __table_args__ = (
        PrimaryKeyConstraint("answer_id", "ques_id"),
        {
            "comment": "考生作答表（一个题形成一行数据）",
            "mysql_partition_by": "LIST COLUMNS(`ques_id`) ( PARTITION pdefault VALUES in ('0'))",
        },
    )  # 为满足以ques_id分区创建联合主键
    # __table_args__ = (PrimaryKeyConstraint("answer_id"), {"comment": "考生作答表（一个题形成一行数据）"})  # 为满足以ques_id分区创建联合主键


class StuAnswerDetail(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_stu_answer_detail"
    answer_detail_id = Column(String(50), comment="作答明细id", index=True)
    answer_id = Column(String(50), nullable=False, comment="作答id", index=True)
    paper_id = Column(String(50), comment="试卷id")
    ques_id = Column(String(50), nullable=False, comment="试题id")
    small_ques_order = Column(String(100), comment="小题序号（填空题为空格序号），兼容可乱序填空题几个空放在一起，使用分隔符◎☆◎隔开", index=True)
    ques_type_code = Column(String(1), nullable=False, comment="题型简码")
    stu_answer = Column(Text, comment="作答（当前空作答）")
    stu_answer_html = Column(Text, comment="作答（当前空作答）h5")
    order_stu_answer = Column(Text, comment="排序后的作答答案，只有当该题为填空题且答案可乱序该字段才有值，多个组使用☆◎☆隔开，同个组多个答案使用分隔符◎☆◎隔开")
    ooo_group_index = Column(Integer, default=0, comment="填空题答案可乱序时，标志该空答案属于哪个组，非选择题和答案不可乱序的填空题都为 0")
    score = Column(Text, nullable=False, comment="该空试题分值（两位小数），多个分数使用分隔符◎☆◎隔开")  # 两位小数
    same_answer_group_id = Column(String(50), comment="作答分组id", index=True)
    set_std_score = Column(DECIMAL(10, 2), comment="定标分数")
    set_std_answer_parse = Column(JSON, comment="定标评析")
    __table_args__ = (PrimaryKeyConstraint("answer_detail_id", "ques_id"), {
        "comment": "考生作答明细表（一个空形成一行数据）",
        "mysql_partition_by": "LIST COLUMNS(`ques_id`) ( PARTITION pdefault VALUES in ('0'))",
                                                                            })  # 为满足以ques_id分区创建联合主键


class SameStuAnswerGroup(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_same_stu_answer_group"
    same_answer_group_id = Column(String(50), comment="作答分组id", index=True)
    project_id = Column(String(50), comment="所属项目id")
    subject_id = Column(String(50), comment="所属科目id")
    paper_id = Column(String(50), comment="试卷id", index=True)
    ques_id = Column(String(50), nullable=False, comment="试题id")
    small_ques_order = Column(String(100), comment="小题序号（填空题为空格序号），兼容可乱序填空题几个空放在一起，使用分隔符◎☆◎隔开", index=True)
    stu_answer = Column(Text, comment="作答答案，兼容可乱序选择题几个空放在一起，兼容可乱序选择题几个空放在一起，使用分隔符◎☆◎隔开")
    stu_count = Column(BigInteger, comment="人数", index=True)
    answer_percentage = Column(String(20), comment="答案占比率", index=True)
    score = Column(String(300), comment="试题分值（两位小数），使用分隔符◎☆◎隔开")
    mark_state = Column(Integer, default=1, index=True, comment="评分结果状态：1 表示未评分，2 表示评分成功，3 表示评分失败，4 表示作答答案待人工判断")
    running_state = Column(Integer, default=1, comment="当前任务状态（AI 定标）：1 表示不在评分任务中，2 表示评分中，3 表示评分完成", index=True)
    mark_priority = Column(BigInteger, comment="评分优先标志", index=True)
    set_std_state = Column(Integer, default=1, comment="人工定标状态，1 表示未定标，2 表示已定标", index=True)
    mark_fail_reason = Column(String(500), comment="评分失败原因")
    retry_count = Column(Integer, default=0, comment="评分重试次数")
    model_id = Column(Integer, comment="评分模型的唯一标识符")
    ai_score = Column(DECIMAL(10, 2), comment="AI 评分", index=True)
    ai_mark_time = Column(DateTime(timezone=True), comment="最后一次 AI 评分时间")
    ai_answer_parse = Column(JSON, comment="AI 评析")
    ai_error_analysis = Column(String(300), comment="AI 错误分析")
    manual_score = Column(DECIMAL(10, 2), comment="手动定标评分，在 AI 定标详情里的人工定标，非人工阅卷")
    manual_answer_parse = Column(JSON, comment="手动定标评析")
    profession_score = Column(DECIMAL(10, 2), comment="专家评分", index=True)
    profession_parse = Column(JSON, comment="专家评析")
    manual_ai_diff_score = Column(DECIMAL(10, 2), comment="人机分差", index=True)
    set_std_score_type = Column(Integer, comment="定标分数类型：None 表示未定标，1 表示AI分数，2 表示专家分数")
    set_std_score = Column(DECIMAL(10, 2), comment="定标分数")
    set_std_time = Column(DateTime(timezone=True), comment="定标时间")
    set_std_user_id = Column(String(50), comment="人工定标用户id")

    __table_args__ = (
        PrimaryKeyConstraint("same_answer_group_id", "ques_id"),  # 创建联合主键
        {"comment": "考生作答分组表（将相同的题、相同的空、相同的考生答案进行分组，填空题不区分顺序的以一道题几个空都相同的作为一组）",
         "mysql_partition_by": "LIST COLUMNS(`ques_id`) ( PARTITION pdefault VALUES in ('0'))",
         },
    )


class QuesAnswerMark(Base, DateTimeBaseMixin):
    __tablename__ = "t_ques_answer_mark"  # 填空题考生答案评分表（作为填空题每种考生答案的判分临时表），简答题直接发送给 AI 判分，不需要这个临时表
    mark_id = Column(String(50), primary_key=True, comment="评分id")
    paper_code = Column(String(30), comment="试卷编号")
    ques_id = Column(String(50), ForeignKey("t_exam_question.ques_id", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, comment="试题id")
    ques_code = Column(String(30), ForeignKey("t_exam_question.ques_code", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, comment="试题编号")

    ques_id_foreign = relationship("ExamQuestion", foreign_keys=[ques_id])
    ques_code_foreign = relationship("ExamQuestion", foreign_keys=[ques_code])

    subject_id = Column(String(50), ForeignKey("t_subject.subject_id", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, comment="所属科目id")
    ques_type_code = Column(String(1), nullable=False, comment="题型简码")
    small_ques_order = Column(String(500), comment="填空题空格序号，兼容可乱序选择题几个空放在一起，使用分隔符◎☆◎隔开")
    stu_answer = Column(Text, comment="作答答案，兼容可乱序选择题几个空放在一起，兼容可乱序选择题几个空放在一起，使用分隔符◎☆◎隔开")
    stu_score = Column(DECIMAL(10, 2), comment="作答总评分")  # 两位小数
    single_score_list = Column(JSON, comment="每一空的分数列表")
    pending_review = Column(Integer, default=None, comment="是否待人工判断，0, 1")
    answer_parse = Column(JSON, comment="作答评析")
    mark_result = Column(Integer, comment="评分结果，1 表示正确，2 表示错误，3 表示部分正确，4 表示未评分， 5 表示分数有误，6 评分失败")


class CreateGradeRecord(Base, DateTimeBaseMixin):
    __tablename__ = "t_create_grade_record"
    __table_args__ = {"comment": "统计成绩记录表"}
    record_id = Column(String(50), primary_key=True, comment="记录id")
    paper_id_list = Column(JSON, comment="统计成绩的试卷")
    allow_exam_num = Column(String(30), comment="准考证号")
    success_count = Column(BigInteger, comment="完成个数")
    total_count = Column(BigInteger, comment="总个数")
    progress = Column(Float(precision=2), comment="统计成绩进度，两位小数")
    c_user_id = Column(String(50), comment="创建用户id")


class StuTotalGrade(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_stu_total_grade"
    __table_args__ = {"comment": "学生总成绩表"}
    grade_id = Column(String(50), primary_key=True, comment="成绩id")
    allow_exam_num = Column(String(30), nullable=False, comment="准考证号", index=True)
    project_id = Column(String(50), comment="所属项目id")
    subject_id = Column(String(50), comment="所属科目id")
    paper_id = Column(String(30), comment="试卷id")
    a_grade = Column(DECIMAL(10, 2), comment="单选题成绩")
    b_grade = Column(DECIMAL(10, 2), comment="多选题成绩")
    c_grade = Column(DECIMAL(10, 2), comment="判断题成绩")
    d_grade = Column(DECIMAL(10, 2), comment="填空题成绩")
    e_grade = Column(DECIMAL(10, 2), comment="简答题成绩")
    f_grade = Column(DECIMAL(10, 2), comment="组合题成绩")
    total_grade = Column(DECIMAL(10, 1), comment="总成绩")
    manual_a_grade = Column(DECIMAL(10, 2), comment="人工单选题成绩")
    manual_b_grade = Column(DECIMAL(10, 2), comment="人工多选题成绩")
    manual_c_grade = Column(DECIMAL(10, 2), comment="人工判断题成绩")
    manual_d_grade = Column(DECIMAL(10, 2), comment="人工填空题成绩")
    manual_e_grade = Column(DECIMAL(10, 2), comment="人工简答题成绩")
    manual_f_grade = Column(DECIMAL(10, 2), comment="人工组合题成绩")
    manual_total_grade = Column(DECIMAL(10, 1), comment="人工总成绩")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class StuTotalGradeDetail(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_stu_total_grade_detail"
    __table_args__ = {"comment": "学生成绩详情表"}
    detail_id = Column(String(50), primary_key=True, comment="成绩详情id")
    grade_id = Column(String(30), nullable=False, comment="成绩id", index=True)
    allow_exam_num = Column(String(30), nullable=False, comment="准考证号", index=True)
    grade_type = Column(Integer, comment="成绩类型，1 表示 AI 成绩，2 表示人工阅卷成绩", index=True)
    paper_id = Column(String(30), comment="试卷id")
    ques_id = Column(String(50), comment="试题id")
    ques_order = Column(Integer, comment="题序")
    stu_answer = Column(Text, comment="作答答案，填空题多个答案使用分隔符◎☆◎隔开")
    stu_score_list = Column(JSON, comment="考生得分列表")
    stu_score = Column(DECIMAL(10, 2), comment="考生得分")
    answer_parse = Column(JSON, comment="得分评析")
    c_user_id = Column(String(50), comment="评分人id")


class ImportDbDataMonitor(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_import_db_data_monitor"
    __table_args__ = {"comment": "导入数据监控表，用于判断是否进行数据导入后的数据汇总计算"}
    monitor_id = Column(String(50), primary_key=True, comment="监控id")
    monitor_type = Column(
        String(10),
        comment="按字符串的位数区分业务，值为0表示不需要，值为1表示需要汇总计算，值为2表示处理中，值为3表示处理完毕。"
        "第1位 表示导入了试题包，要更新redis里的试题信息和获取图片描述信息，"
        "第2位 表示要统计试题的使用次数，"
        "第3位 表示要统计分组表的同个答案的考生人数，"
        "第4位 表示要生成试题定标数据",
    )


class QuesUsed(Base, DateTimeBaseMixin, ImportToolMixin):
    __tablename__ = "t_ques_used"
    __table_args__ = {"comment": "试题考试人次表"}
    used_id = Column(String(50), primary_key=True, comment="id")
    ques_code = Column(String(50), comment="试题编号")
    used_count = Column(String(50), comment="考试人次数")


class ManualReadTask(Base, DateTimeBaseMixin):
    __tablename__ = "t_manual_read_task"  # 人工阅卷任务表
    m_read_task_id = Column(String(50), primary_key=True, comment="人工阅卷任务id")
    m_read_task_name = Column(String(100), nullable=False, comment="人工阅卷任务名称")
    project_id = Column(String(50), ForeignKey("t_project.project_id", ondelete="CASCADE", onupdate="CASCADE"), comment="所属项目id")
    subject_id = Column(String(50), ForeignKey("t_subject.subject_id", ondelete="CASCADE", onupdate="CASCADE"), comment="所属科目id")
    manual_process_id = Column(String(50), ForeignKey("t_work_flow_main_process.process_id", ondelete="SET NULL", onupdate="CASCADE"), comment="所属阅卷主流程id")
    paper_id = Column(String(30), comment="试卷id")
    ques_type_code = Column(String(1), nullable=False, comment="阅卷题型简码")
    ques_order = Column(String(12), comment="试题序号")
    ques_id = Column(String(50), nullable=False, comment="试题id")
    ques_code = Column(String(30), nullable=False, comment="试题编号")
    business_id = Column(String(50), comment="业务题型id")
    fetch_score_way = Column(Integer, comment="专家个数超过1个时该字段不为空，1 为取最高分，2 为取最低分，3 为取平均分, 4 为去除最高和最低分取平均分")
    fetch_score_scope = Column(Integer, comment="平均范围，fetch_score_way 为3时该字段不为空，1 为全分组，2 为高分组，3 为中分组，4 为低分组")
    fetch_score_option = Column(Integer, comment="平均分值计算，fetch_score_way 为3时该字段不为空，1 为向下舍入，2 为向上舍入，3 为四舍五入")
    """
    eg: 
        向下舍入: 1.553 -> 1.55
        向上舍入: 1.553 -> 1.56
        四舍五入: 1.553 -> 1.55
    """
    arbitrate_threshold_type = Column(Integer, comment="仲裁阈值类型，1 表示百分比，2 表示分值")
    arbitrate_threshold = Column(Integer, comment="仲裁阈值")
    arbitrate_score_diff = Column(Integer, comment="仲裁分差取值方式，1 表示平均值，2 表示最大偏差，3 表示最小偏差")
    arbitrate_deviation = Column(Integer, comment="仲裁离差，表示评卷员和仲裁员之间有效的分数差")
    quality_ratio = Column(Integer, comment="质检抽样比例，10 表示 10%")
    quality_upper_limit = Column(Integer, comment="质检上限比例，只有所选的小组包含质检 AI 该字段才不为空，百分比")
    quality_lower_limit = Column(Integer, comment="质检下限比例，只有所选的小组包含质检 AI 该字段才不为空，百分比")
    quality_reevaluation = Column(Integer, comment="质检重评比例，百分比")
    score_step = Column(Float(precision=2), nullable=False, comment="打分间隔")  # 两位小数
    remark = Column(Text, comment="备注")
    task_state = Column(Integer, default=1, comment="1 为未开始，2 为正在进行中，3 为已完成，4 为已作废，5 为已结束（不可重评的标志）")
    expert_read_record_id = Column(String(50), ForeignKey("t_task_execute_record.record_id", ondelete="SET NULL", onupdate="CASCADE"), comment="阅卷任务管理界面的阅卷任务记录")
    lock_state = Column(Integer, default=1, comment="1 为未锁定，2 为锁定，一旦锁定则无法修改和删除")
    launch_time = Column(DateTime(timezone=True), default=None, comment="任务发起时间")

    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")

    manual_read_task_group = relationship("ManualReadTaskGroup")
    manual_distribute_answer = relationship("ManualDistributeAnswer")
    manual_mark = relationship("ManualMark")
    manual_arbitrate_quality = relationship("ManualArbitrateQuality")


class ManualReadTaskGroup(Base, DateTimeBaseMixin):
    __tablename__ = "t_manual_read_task_group"  # 人工阅卷任务对应小组表
    m_read_task_group_id = Column(String(50), primary_key=True, comment="人工阅卷任务对应小组id")
    m_read_task_id = Column(String(50), ForeignKey("t_manual_read_task.m_read_task_id", ondelete="CASCADE", onupdate="CASCADE"), index=True, comment="所属人工阅卷任务id")
    manual_group_id = Column(String(50), ForeignKey("t_manual_read_paper_group.manual_group_id", ondelete="CASCADE", onupdate="CASCADE"), comment="绑定的人工阅卷小组id", nullable=False)


class ManualDistributeAnswer(Base, DateTimeBaseMixin):
    __tablename__ = "t_manual_distribute_answer"  # 作答信息分配表
    distri_answer_id = Column(String(50), primary_key=True, comment="作答信息分配id")
    m_read_task_id = Column(String(50), ForeignKey("t_manual_read_task.m_read_task_id", ondelete="CASCADE", onupdate="CASCADE"), index=True, comment="所属人工阅卷任务id")
    manual_group_id = Column(String(50), comment="绑定的人工阅卷小组id", nullable=False)
    stu_answer_id = Column(String(50), comment="作答id", nullable=False)
    stu_secret_num = Column(String(30), nullable=False, comment="考生密号")
    paper_id = Column(String(30), comment="试卷编号")
    ques_code = Column(String(30), nullable=False, comment="试题编号")
    ques_id = Column(String(50), nullable=False, comment="试题id")
    parent_ques_id = Column(String(50), comment="试题父id")
    ques_type_code = Column(String(1), nullable=False, comment="题型简码")
    stu_answer = Column(JSON, default=[], comment="考生作答答案列表")
    op_file = Column(String(255), comment="存放用户作答生成的操作题文件路径")
    quality_count = Column(Integer, default=0, comment="推送给质检的次数")
    final_mark_score = Column(DECIMAL(10, 2), comment="最后评分分数，用于分数统计")
    answer_parse = Column(JSON, comment="人工阅卷评析")
    manual_mark = relationship("ManualMark")
    manual_arbitrate_quality = relationship("ManualArbitrateQuality")


class ManualMark(Base, DateTimeBaseMixin):
    __tablename__ = "t_manual_mark"  # 人工阅卷评分表
    manual_mark_id = Column(String(50), primary_key=True, comment="人工阅卷评分id")
    distri_answer_id = Column(String(50), ForeignKey("t_manual_distribute_answer.distri_answer_id", ondelete="CASCADE", onupdate="CASCADE"), comment="作答信息分配id")
    m_read_task_id = Column(String(50), ForeignKey("t_manual_read_task.m_read_task_id", ondelete="CASCADE", onupdate="CASCADE"), index=True, comment="所属人工阅卷任务id")
    ques_id = Column(String(50), ForeignKey("t_exam_question.ques_id", ondelete="CASCADE", onupdate="CASCADE"), nullable=False, comment="试题id")
    mark_state = Column(
        Integer, comment="评分状态：1 表示专家评分完成，2 表示待仲裁，3 表示仲裁完成，4 表示待质检, 5 表示质检通过，" "6 表示质检不通过，待质检返评，7 表示质检不通过，已质检返评，8 表示评分完成"
    )
    mark_count = Column(Integer, default=0, comment="评分次数")
    expert_mark_score = Column(DECIMAL(10, 2), comment="专家评分")  # 两位小数
    op_step_score = Column(JSON, comment="操作题评分步骤得分列表")
    mark_parse = Column(String(1000), comment="评分评析")
    mark_person_id = Column(String(50), ForeignKey("t_user_info.user_id", ondelete="CASCADE", onupdate="CASCADE"), comment="评分人id", nullable=False)

    can_expert_mark_again = Column(Integer, default=1, comment="专家是否可以重评，0 表示否，1 表示是")
    not_mark_again_reason = Column(String(50), comment="专家不允许重评原因")
    final_mark_time = Column(DateTime, comment="最后评分时间，用于历史记录排序")
    expert_mark_time = Column(DateTime, comment="专家最后评分时间，用于对比仲裁分数是否为当前轮次的仲裁")

    mark_person = relationship("UserInfo", foreign_keys=[mark_person_id])


class ManualArbitrateQuality(Base, DateTimeBaseMixin):
    __tablename__ = "t_manual_arbitrate_quality"  # 人工仲裁质检评分表
    manual_aq_id = Column(String(50), primary_key=True, comment="人工仲裁质检评分id")
    distri_answer_id = Column(String(50), ForeignKey("t_manual_distribute_answer.distri_answer_id", ondelete="CASCADE", onupdate="CASCADE"), comment="作答信息分配id")
    m_read_task_id = Column(String(50), ForeignKey("t_manual_read_task.m_read_task_id", ondelete="CASCADE", onupdate="CASCADE"), index=True, comment="所属人工阅卷任务id")
    aq_type = Column(Integer, nullable=False, comment="仲裁或者质检，1 表示仲裁，2 表示质检")
    aq_count = Column(Integer, default=1, comment="仲裁或者质检的次数")
    aq_data = Column(JSON, comment="仲裁或者质检用到的数据")
    # 仲裁或者质检用到的数据格式，操作题添加 op_step_score
    # aq_data1 = {
    #     "expert_data": [{"manual_mark_id": "124", "mark_person_id": "30", "mark_score": 0.0, "op_step_score": [6, 1, 1, 0, 1]},
    #                     {"manual_mark_id": "133", "mark_person_id": "12", "mark_score": 2.0, "op_step_score": [6, 1, 1, 0, 1]}],
    #     # 仲裁时和质检的数据未仲裁时 arbitrate_data = None
    #     "arbitrate_data": {"manual_mark_id": "12", "mark_person_id": "60", "mark_score": 2.0, "op_step_score": [6, 1, 1, 0, 1]}
    # }
    aq_state = Column(Integer, comment="仲裁或者质检状态：1 表示待仲裁，2 表示已仲裁，3 表示待质检，4 表示质检完成")
    aq_mark_score = Column(DECIMAL(10, 2), comment="仲裁评分（该字段只适用于仲裁）")  # 两位小数
    op_step_score = Column(JSON, comment="操作题评分步骤得分列表")
    aq_result = Column(Integer, comment="None 表示未质检，1 表示质检通过，2 表示质检不通过，待质检返评，3 表示质检不通过，已质检返评（该字段只适用于质检）")
    aq_suggestion = Column(Text, comment="仲裁评析或质检意见")
    can_aq_again = Column(Integer, nullable=False, comment="同个轮次中是否可以重新仲裁或者质检，0 表示否，1 表示是")
    aq_user_id = Column(String(50), ForeignKey("t_user_info.user_id", ondelete="CASCADE", onupdate="CASCADE"), comment="仲裁或者质检人id")
    yet_return_user_id = Column(JSON, default=[], comment="已质检返评的专家id列表")
    history_id = Column(String(50), nullable=True, comment="关联的历史记录id")

    aq_user = relationship("UserInfo", foreign_keys=[aq_user_id])


class ManualMarkHistory(Base, DateTimeBaseMixin):
    __tablename__ = "t_manual_mark_history"  # 人工阅卷历史记录表
    history_id = Column(String(50), primary_key=True, comment="人工阅卷历史记录id")
    m_read_task_id = Column(String(50), ForeignKey("t_manual_read_task.m_read_task_id", ondelete="CASCADE", onupdate="CASCADE"), index=True, comment="所属人工阅卷任务id")
    distri_answer_id = Column(String(50), ForeignKey("t_manual_distribute_answer.distri_answer_id", ondelete="CASCADE", onupdate="CASCADE"), comment="作答信息分配id")
    history_type = Column(Integer, nullable=False, comment="1 表示专家评分，2 表示专家重评，3 表示专家质检返评，4 表示仲裁，5 表示重新仲裁，6 表示质检，7 表示重新质检")
    mark_count = Column(Integer, default=1, comment="评分、仲裁、质检的次数")
    mark_score = Column(DECIMAL(10, 2), comment="评分、仲裁分数，如果为质检，0 表示不通过，1 表示通过")  # 两位小数
    op_step_score = Column(JSON, comment="操作题评分步骤得分列表")
    mark_suggestion = Column(Text, comment="评分、仲裁、质检意见")
    mark_user_id = Column(String(50), ForeignKey("t_user_info.user_id", ondelete="SET NULL", onupdate="CASCADE"), comment="评分、仲裁、质检人员的id")

    mark_user = relationship("UserInfo", foreign_keys=[mark_user_id])


class ScoreParse(Base):
    __tablename__ = "t_score_parse"  # 分数评析表
    parse_id = Column(String(50), primary_key=True, comment="导入数据记录id")
    parse_type = Column(Integer, comment="评析类型，0 表示答案错误，1 表示答案一致，2 表示部分正确")
    parse_text = Column(String(100), comment="评析类型，答案错误，给 0 分；答案与参考答案一致，给满分；部分正确，给部分分")


class SetStdBackup(Base, DateTimeBaseMixin):
    __tablename__ = "t_set_std_backup"  # 定标备份表
    backup_id = Column(String(50), primary_key=True, comment="备份id")
    ques_id = Column(String(50), nullable=False, comment="试题id", index=True)
    same_answer_group_id = Column(String(50), comment="作答分组id", index=True)
    mark_state = Column(Integer, comment="评分结果状态：1 表示未评分，2 表示评分成功，3 表示评分失败，4 表示作答答案待人工判断")
    running_state = Column(Integer, comment="当前任务状态（AI 定标）：1 表示不在评分任务中，2 表示评分中，3 表示评分完成")
    set_std_state = Column(Integer, comment="人工定标状态，1 表示未定标，2 表示已定标")
    mark_fail_reason = Column(String(500), comment="评分失败原因")
    ai_score = Column(DECIMAL(10, 2), comment="AI 评分")
    ai_answer_parse = Column(JSON, comment="AI 评析")
    manual_ai_diff_score = Column(DECIMAL(10, 2), comment="人机分差")
    set_std_score_type = Column(Integer, comment="定标分数类型：None 表示未定标，1 表示AI分数，2 表示专家分数")
    set_std_score = Column(DECIMAL(10, 2), comment="定标分数")
    set_std_time = Column(DateTime(timezone=True), comment="定标时间")


class ImportDataRecord(Base, DateTimeBaseMixin):
    __tablename__ = "t_import_data_record"  # 导入数据记录表
    record_id = Column(String(50), primary_key=True, comment="导入数据记录id")
    record_type = Column(Integer, nullable=False, comment="记录的类型，1 表示导入试卷，2 表示导入考生，3 表示导入作答信息，4 表示导出的作答聚类及其AI评分")
    success_count = Column(BigInteger, comment="完成个数")
    total_count = Column(BigInteger, comment="总个数")
    progress = Column(Float(precision=2), comment="进度，两位小数")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")


class OperationStepGroup(Base, ImportToolMixin):
    __tablename__ = "t_operation_step_group"  # 操作题分组表
    knowledge_group_id = Column(String(50), primary_key=True, comment="评分分组ID")
    score_gene = Column(DECIMAL(18, 8), comment="分值")
    description = Column(Text, comment="评分点描述")
    order_id = Column(Integer, comment="显示顺序")
    score_rule = Column(Text, comment="评分规则")
    group_max_score_id = Column(String(50), comment="评分点分组（取组内最高分）")
    group_text = Column(Text, comment="评析")


class OperationStep(Base, ImportToolMixin):
    __tablename__ = "t_operation_step"  # 操作题步骤参数表
    auto_id = Column(String(50), primary_key=True, comment="评分分组ID")
    question_id = Column(String(50), comment="试题ID")
    knowledge = Column(String(50), comment="知识点ID")
    para = Column(Text, comment="参数")
    para1 = Column(Text, comment="参数1")
    para2 = Column(Text, comment="参数2")
    para3 = Column(Text, comment="参数3")
    para4 = Column(String(250), comment="参数4")
    para5 = Column(String(250), comment="参数5")
    para6 = Column(String(250), comment="参数6")
    para7 = Column(String(250), comment="参数7")
    para8 = Column(String(250), comment="参数8")
    para9 = Column(String(250), comment="参数9")
    paraA = Column(Text, comment="备用参数")
    is_score = Column(Integer, default=1, comment="是否评分（步骤得分标识）（1：是、0：否）")
    score_gene = Column(DECIMAL(18, 2), comment="分值")
    description = Column(Text, comment="描述")
    right_count = Column(Integer, default=0, comment="正确数")
    wrong_count = Column(Integer, default=0, comment="错误数")
    knowledge_group_id = Column(String(50), comment="评分分组ID")
    remark = Column(Text, comment="备注")
    order_id = Column(Integer, comment="显示顺序")


class OpEngineMarkRecord(Base, DateTimeBaseMixin):
    __tablename__ = "t_op_engine_mark_record"  # 操作题引擎评分记录表
    record_id = Column(String(50), primary_key=True, comment="记录id")
    filter_condition = Column(
        JSON,
        comment="""
    任务过滤条件，以字典的形式存储
    filter_condition = {
        "1": [{"answer_id": "xxx", "ques_id": "xxx", "op_file": "xxx", "score": xxx}],  # 表示用户通过前端数据列表手动选的
        "2": {"project_id": "xxx", "subject_id": "xxx", "paper_code": "xxx", "ques_code": "xxx", "ques_order": "xxx", "stu_answer": "xxx", "mark_result": xxx, "mark_state": [xxx], "stu_score_range": [xxx, xxx], "search_time": [xxx, xxx]},  # 表示筛选条件
        "3": "all"  # 表示全部数据
    }
    """,
    )
    finish_count = Column(BigInteger, comment="完成个数")
    total_count = Column(BigInteger, comment="总个数")
    running_state = Column(Integer, comment="任务状态，1 表示正在获取数据中，2 表示正在标记中，3 表示评分中，4 表示暂停，5 表示正在取消中，6 表示已取消，7 表示已完成")
    pause_state = Column(Integer, comment="暂停时的任务状态，1 表示正在获取数据中，2 表示正在标记中，3 表示评分中")
    already_remark_ques_id = Column(JSON, comment="暂停时的任务状态为 2 表示正在标记中 时，将已标记的 ques_id 写入")
    c_user_id = Column(String(50), comment="创建用户id")


class SuppImportDataRecord(Base, DateTimeBaseMixin):
    __tablename__ = "t_supp_import_data_record"  # 补充导入工具导入的数据记录表
    record_id = Column(String(50), primary_key=True, comment="导入数据记录id")
    import_count = Column(Integer, comment="导入工具导入成功的作答包的个数")
    finish_flag = Column(Integer, default=0, comment="是否已完成，0 为未完成，1 为进行中，2 为已完成")


class ExamSchedule(Base, ImportToolMixin):
    __tablename__ = "texamschedule"  # 导入工具所需表
    scheduleid = Column(String(50), primary_key=True, comment="考试任务ID")
    schedulename = Column(String(500), default=None, comment="考试任务名称")
    exammode = Column(Integer, default=None, comment="考试模式。（0：正考，1：模考）")
    pftotaltask = Column(Integer, default=None, comment="评分任务总数")
    completedtasknum = Column(Integer, default=None, comment="已完成评分任务数量")


class PtqRecive(Base, ImportToolMixin):
    __tablename__ = "tptqrecive"  # 导入工具所需表
    ptqid = Column(BigInteger, primary_key=True)
    ptqfile = Column(String(1500), default=None)
    EncryptMethod = Column(String(10), default=None)
    EncryptPWD = Column(String(255), default=None)
    jaFileName = Column(String(1000), default=None)
    jaContent = Column(LONGTEXT)
    jpFileName = Column(String(1000), default=None)
    jpContent = Column(LONGTEXT)
    descibleInfo = Column(LONGTEXT)


class TptrRecvicers(Base, ImportToolMixin):
    __tablename__ = "tptrrecvicers"  # 导入工具所需表
    recviceRsID = Column(BigInteger, primary_key=True, comment="回收记录ID")
    verifycode = Column(String(50), default=None, comment="PTR校验码")
    ptrFile = Column(String(500), default=None, comment="PTR文件。举例：M_202110_111005001_1_0317195805(1).PRT")
    recviceRsLog = Column(String(500), default=None, comment="回收日志。失败时候需要记录原因")
    recviceRsIsOk = Column(BigInteger, default=None, comment="回收状态。（0: 失败,1:成功）")
    examKcNo = Column(String(50), default=None, comment="考场编号")
    examTime = Column(String(50), default=None, comment="场次")
    ptrSEQ = Column(BigInteger, default=None, comment="PTR序号。（考生最高成绩对应的ptr序号）")
    scheduleid = Column(String(50), default=None, comment="考试任务ID。来源（tExamSchedule.scheduleid）")
    recviceTime = Column(String(50), default=None, comment="导入时间。yyyy-MM-dd HH:mm:ss")
    examPointNo = Column(String(50), default=None, comment="考点编号")
    decompressIsOK = Column(Integer, default=0)
    importDataIsOK = Column(Integer, default=0)
    delFileIsOK = Column(Integer, default=0)


class StudentOpImport(Base, ImportToolMixin):
    __tablename__ = "tstudentopimport"  # 导入工具所需表
    pfid = Column(String(50), primary_key=True, comment="考试任务ID")
    sourcePath = Column(String(2000), default=None)
    targetPath = Column(String(2000), default=None)
    importState = Column(BigInteger, default=None, comment="0：未执行\r\n2：导入完成\r\n3：导入异常")
    errLog = Column(Text, comment="已完成评分任务数量")


class SysOrganizationCode(Base, ImportToolMixin):
    __tablename__ = "tsysorganizationcode"  # 导入工具所需表
    organizationCodeID = Column(String(50), primary_key=True, comment="机构代码ID")
    organizationNo = Column(String(50), default=None, comment="机构代码")
    organizationName = Column(String(200), default=None, comment="机构名称")
    parentOrganizationID = Column(String(50), default=None, comment="父ID")
    remark = Column(String(500), comment="备注")
    nodeType = Column(Integer, primary_key=True, comment="节点类型（1：部，2：省，3：市，4：考区，5：学校，6：考场）")
    endFlag = Column(Integer, default=None, comment="末节点标志（0：非末节点，1：末节点）")
    gradeID = Column(Integer, default=None, comment="级次(在树节点的第几层次)")
    c_user_id = Column(String(50), default=None)


class SchoolInfo(Base, ImportToolMixin):
    __tablename__ = "tschoolinfo"  # 导入工具所需表
    schooID = Column(BigInteger, primary_key=True, comment="学校ID")
    schooNo = Column(String(50), default=None, comment="考点编号")
    schooName = Column(String(500), default=None, comment="考点名称")
    taskID = Column(BigInteger, default=None, comment="任务ID。来源（tDataAnalyzeTask.taskID）")
    c_user_id = Column(String(50), default=None)


class SchoolExamSubject(Base, ImportToolMixin):
    __tablename__ = "tschoolexamsubject"  # 导入工具所需表
    schoolExamSubjectID = Column(BigInteger, primary_key=True, comment="学校考试科目id")
    schooID = Column(BigInteger, comment="学校ID。来源（tSchoolInfo.schoolID）")
    taskID = Column(BigInteger, default=None, comment="任务ID。来源（tDataAnalyzeTask.taskID）")
    examSubjectID = Column(String(50), default=None, comment="考试科目ID")
    arrangedNum = Column(Integer, default=None, comment="总人数")
    joinExamNum = Column(Integer, comment="参考人数（有成绩的人数）")
    c_user_id = Column(String(50), default=None)


class Student(Base, ImportToolMixin):
    __tablename__ = "tstudent"  # 导入工具所需表
    studentid = Column(BigInteger, primary_key=True, comment="学生ID。与（tSuSubjectInfo.studentid一致）")
    schoolExamSubjectID = Column(BigInteger, default=None, comment="学校考试科目id。来源（tSchoolExamSubject.schoolExamSubjectID）")
    stuName = Column(String(50))
    permitcardid = Column(String(500), default=None, comment="准考证号。例如：202101180202111001")
    taskID = Column(BigInteger, default=None, comment="所属任务，来源（tDataAnalyzeTask.taskID）")
    c_user_id = Column(String(50))


class Room(Base, ImportToolMixin):
    __tablename__ = "troom"  # 导入工具所需表
    roomid = Column(BigInteger, primary_key=True, comment="考场ID")
    roomNo = Column(String(50), default=None, comment="考场编号")
    roomName = Column(String(500), default=None, comment="考场名称")
    schooID = Column(BigInteger, default=None, comment="所属学校id")
    c_user_id = Column(String(50))


class StuRoom(Base, ImportToolMixin):
    __tablename__ = "tsturoom"  # 导入工具所需表
    roomstuid = Column(BigInteger, primary_key=True, comment="学生所属考场ID")
    roomid = Column(BigInteger, default=None, comment="考场ID")
    BatchID = Column(String(500), default=None, comment="批次")
    PermitCardID = Column(BigInteger, default=None, comment="准考证号")
    c_user_id = Column(String(50))

class StuAnswerSimilarity(Base,DateTimeBaseMixin):
    __tablename__ = "t_stu_answer_similarity"
    ans_similarity_id = Column(String(50), primary_key=True, comment="答案图片相似度表ID")
    answer_id = Column(String(50), default=None, comment="作答id(参考)")
    answer_id_to = Column(String(50), default=None, comment="作答id(对照)")
    similarity = Column(DECIMAL(10, 2),comment="作答相似度")


if __name__ == "__main__":
    # 直接使用 sqlalchemy 生成表（不推荐）
    # Base.metadata.create_all(bind=engine)
    """
    推荐使用 alembic:
        # 初始化项目
        alembic init

        # 自动生成迁移文件
        alembic revision --autogenerate -m "备注"

        # 更新数据库
        # 升级
        alembic upgrade head
        # 降级
        alembic downgrade head

        # 列出所有的迁移版本及其信息
        alembic history

        # 展示当前数据库中的版本号
        alembic current

        # 查看执行的sql语句
        alembic upgrade head --sql
    """
    pass
