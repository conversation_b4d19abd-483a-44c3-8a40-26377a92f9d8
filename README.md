# 010602智能定标阅卷系统前后端发布

## apps 功能模块说明
### sys_manage
- 系统模块管理

### users
- 用户管理

### base
- 基础
- 系统基础模块，负责路由设置、基础接口，如：API 文档接口、全局缓存设置

### ai_set_std
- AI 定标
- 对应 AI 定标页面，该模块会将试题和作答发送给 AI 进行评分

### ai_mark 
- AI 评分
- 对应 AI 评分页面，将 AI 定标后的评分结果进行赋分

### data_statistics
- 数据统计模块
- 负责评卷员、仲裁员、质检员 首页的数据统计图表接口

### data_transfer
- 数据传输
- 负责数据的导入和导出

### grade_manage
- 成绩模块
- 负责成绩的管理

### human_common
- 新版人工阅卷通用模块
- 评卷员、小组管理

### human_mark_accept
- 验收管理

### human_mark_exception
- 异常卷管理

### human_official_mark
- 新版人工阅卷正评管理

### human_prepare
- 评分准备

### human_statistics
- 统计管理

### human_try_mark
- 试评管理：试评是一种特殊的正评；抽样评分是一种特殊的试评任务。
- 试评流程：
  - 管理员抽样评分：创建试评任务-》创建试评任务试题-》开始评分
  - 评阅员评分：创建创建评阅员试评任务-》开始评分

### manual_read_paper
- 老版人工阅卷模块

### models
- 数据表模型定义

### operation_ques
- 操作题评分

### permission
- 功能权限管理

### projects_manage
- 项目、科目管理

### ques_manage
- 试题管理

### ques_type
- 试题类型管理

### read_paper
- 最早期 AI 阅卷使用的模块

### set_std_check
- AI 复核模块

## 阅卷包导入逻辑
- 涉及的表：
  - t_exam_question
  - t_business_ques_type
  - t_small_exam_question
  - 
  - t_project
  - t_subject
  - 
  - t_exam_paper
  - t_paper_detail


  - 考生作答数据导入
    - t_stu_traj：一个科目一条
    - t_stu_answer：一条作答一条
    - t_stu_answer_detail: 一个空或简单一条
    - t_same_stu_answer_group：作答分组（相同的作答一条）