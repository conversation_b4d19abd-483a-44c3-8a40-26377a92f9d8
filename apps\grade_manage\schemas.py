from typing import Optional, Literal
from pydantic import BaseModel, Field

from apps.base.schemas import PaginationModel


class GetStuGradeReq(PaginationModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_code: Optional[str] = Field(None, title="试卷编号")
    stu_secret_num: Optional[str] = Field(None, title="考生密号")
    grade_type: Literal[None, 1, 2] = Field(None, title="统计成绩类型，1 表示 AI，2 表示人工，用于标记查询哪个成绩")
    stu_score_range: Optional[list] = Field(None, title="考生成绩分数段")
    is_export: bool = Field(False, title="是否导出")


class CreateStuGradeReq(BaseModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_code: Optional[str] = Field(None, title="试卷编号")
    stu_secret_num: Optional[str] = Field(None, title="考生密号")
    objective_grade: Literal[None, 1, 2] = Field(None, title="客观题成绩统计方式，1 表示 AI，2 表示人工")
    subjective_grade: Literal[None, 1, 2] = Field(None, title="主观题成绩统计方式，1 表示 AI，2 表示人工")
    op_grade: Literal[None, 1, 2] = Field(None, title="操作题成绩统计方式，1 表示 AI，2 表示人工")
    grade_type: Literal[1, 2] = Field(..., title="统计成绩类型，1 表示 AI，2 表示人工，用于标记将成绩存储到哪个字段")
    record_id: str = Field(..., title="生成考生记录id")


class GetStuGradeDetailReq(BaseModel):
    grade_id: str = Field(..., title="成绩id")
    grade_type: int = Field(..., title="成绩类型，1 表示智能阅卷成绩，2 表示人工阅卷成绩")
    paper_code: str = Field(..., title="试卷编号")


class GetStuGradeItemDetailReq(BaseModel):
    ques_id: str = Field(..., title="试题id")
    paper_code: str = Field(..., title="试卷编号")
    stu_answer: list = Field([], title="考生答案")

class GetSubjectScoreReq(PaginationModel):
    project_id: Optional[str] = Field(None,description="资格id")
    subject_id: Optional[str] = Field(None, description="科目id")
    stu_secret_num: Optional[str] = Field(None, description="考生密号")
    mark_score_range: Optional[list] = Field(None, description="考生分数")

class GetSubjectScoreDetailReq(BaseModel):
    student_subject_grade_id: str = Field(..., description="考生科目成绩id")