"""
Excel写入工具类
提供将数据写入Excel文件的功能
"""

import os
import pandas as pd
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import logging

# 配置日志
logger = logging.getLogger(__name__)


class ExcelWriter:
    """Excel写入器类"""
    
    def __init__(self, default_save_path: str = "./output/"):
        """
        初始化Excel写入器
        
        Args:
            default_save_path: 默认保存路径
        """
        self.default_save_path = default_save_path
        # 确保默认路径存在
        os.makedirs(default_save_path, exist_ok=True)
        logger.info(f"ExcelWriter初始化完成，默认保存路径: {default_save_path}")
    
    def write_data_to_excel(
        self, 
        data: List[Dict[str, Any]], 
        file_name: str, 
        headers: Optional[List[str]] = None,
        save_path: Optional[str] = None
    ) -> str:
        """
        将数据写入Excel文件
        
        Args:
            data: 要写入的数据列表
            file_name: Excel文件名
            headers: 表头列表
            save_path: 保存路径
            
        Returns:
            str: 文件保存路径
        """
        # 参数验证
        self._validate_data(data)
        self._validate_file_name(file_name)
        
        # 确定保存路径
        if save_path is None:
            save_path = self.default_save_path
        else:
            # 确保自定义路径存在
            os.makedirs(save_path, exist_ok=True)
        
        # 构建完整文件路径
        file_path = os.path.join(save_path, file_name)
        
        # 处理数据
        processed_data = self._process_data(data, headers)
        
        # 写入Excel文件
        try:
            df = pd.DataFrame(processed_data)
            df.to_excel(file_path, index=False, engine='openpyxl')
            logger.info(f"Excel文件写入成功: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"写入Excel文件失败: {str(e)}")
            raise IOError(f"写入Excel文件失败: {str(e)}")
    
    def write_dict_to_excel(
        self,
        data: Dict[str, Any],
        file_name: str,
        save_path: Optional[str] = None
    ) -> str:
        """
        将字典数据写入Excel文件（支持单行数据）
        
        Args:
            data: 要写入的字典数据
            file_name: Excel文件名
            save_path: 保存路径
            
        Returns:
            str: 文件保存路径
        """
        # 参数验证
        self._validate_dict_data(data)
        self._validate_file_name(file_name)
        
        # 确定保存路径
        if save_path is None:
            save_path = self.default_save_path
        else:
            # 确保自定义路径存在
            os.makedirs(save_path, exist_ok=True)
        
        # 构建完整文件路径
        file_path = os.path.join(save_path, file_name)
        
        # 处理数据：将字典转换为列表格式
        processed_data = [data]
        
        # 写入Excel文件
        try:
            df = pd.DataFrame(processed_data)
            df.to_excel(file_path, index=False, engine='openpyxl')
            logger.info(f"Excel文件写入成功: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"写入Excel文件失败: {str(e)}")
            raise IOError(f"写入Excel文件失败: {str(e)}")
    
    def write_dict_list_to_excel(
        self,
        data: List[Dict[str, Any]],
        file_name: str,
        headers: Optional[List[str]] = None,
        save_path: Optional[str] = None
    ) -> str:
        """
        将字典列表数据写入Excel文件（支持多行数据）
        
        Args:
            data: 要写入的字典数据列表
            file_name: Excel文件名
            headers: 表头列表
            save_path: 保存路径
            
        Returns:
            str: 文件保存路径
        """
        # 参数验证
        self._validate_data(data)
        self._validate_file_name(file_name)
        self._validate_headers(headers)
        
        # 确定保存路径
        if save_path is None:
            save_path = self.default_save_path
        else:
            # 确保自定义路径存在
            os.makedirs(save_path, exist_ok=True)
        
        # 构建完整文件路径
        file_path = os.path.join(save_path, file_name)
        
        # 处理数据
        processed_data = self._process_data(data, headers)
        
        # 写入Excel文件
        try:
            df = pd.DataFrame(processed_data)
            df.to_excel(file_path, index=False, engine='openpyxl')
            logger.info(f"Excel文件写入成功: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"写入Excel文件失败: {str(e)}")
            raise IOError(f"写入Excel文件失败: {str(e)}")
    
    def _validate_data(self, data: List[Dict[str, Any]]) -> None:
        """验证数据格式"""
        if not isinstance(data, list):
            raise ValueError("数据必须是列表类型")
        
        if len(data) == 0:
            logger.warning("数据为空列表")
            return
        
        if not isinstance(data[0], dict):
            raise ValueError("列表中的元素必须是字典类型")
    
    def _validate_dict_data(self, data: Dict[str, Any]) -> None:
        """验证字典数据格式"""
        if not isinstance(data, dict):
            raise ValueError("数据必须是字典类型")
    
    def _validate_headers(self, headers: Optional[List[str]]) -> None:
        """验证表头格式"""
        if headers is not None:
            if not isinstance(headers, list):
                raise ValueError("表头必须是列表类型")
            for header in headers:
                if not isinstance(header, str):
                    raise ValueError("表头必须是字符串类型")
    
    def _validate_file_name(self, file_name: str) -> None:
        """验证文件名"""
        if not file_name:
            raise ValueError("文件名不能为空")
        
        if not file_name.endswith('.xlsx'):
            raise ValueError("文件名必须以.xlsx结尾")
        
        # 检查非法字符
        illegal_chars = '<>:"|?*'
        for char in illegal_chars:
            if char in file_name:
                raise ValueError(f"文件名不能包含非法字符: {illegal_chars}")
        
        # 检查文件名长度
        if len(file_name) > 255:
            raise ValueError("文件名长度不能超过255个字符")
    
    def _process_data(self, data: List[Dict[str, Any]], headers: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """处理数据，确保数据格式正确"""
        if not data:
            return []
        
        # 如果没有指定表头，从数据中推断
        if headers is None:
            # 从第一个字典获取所有键作为表头
            headers = list(data[0].keys())
        
        # 确保所有字典都有相同的键
        processed_data = []
        for item in data:
            if isinstance(item, dict):
                # 创建新的字典，确保所有字段都有值
                processed_item = {header: item.get(header, '') for header in headers}
                processed_data.append(processed_item)
            else:
                # 如果不是字典，跳过
                logger.warning(f"跳过非字典数据项: {item}")
                continue
        
        return processed_data
