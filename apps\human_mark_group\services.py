import traceback
from typing import Dict, Optional

from sqlalchemy import func, and_, exists, or_
from sqlalchemy.orm import aliased

from apps.base.services import update_monitor_cal
from apps.human_task_manage.models import HumanReadTaskRound
from apps.models.models import ExamPaper, ExamQuestion, Subject, PaperDetail, QuesUsed, BusinessQuesType, \
    Project, UserInfo
from apps.human_mark_group.models import HumanMarkGroup
from factory_apps import session_depend
from settings import logger, configs
from utils.time_func import format_now_time


def get_human_group_condition(project_id: Optional[str] = None, subject_id: Optional[str] = None):
    project_query = HumanMarkGroup.project_id == project_id if project_id else True
    subject_query = HumanMarkGroup.subject_id == subject_id if subject_id else True

    condition = and_(project_query, subject_query)
    return condition


def get_human_group_tree(new_session, page_size, limit, offset=None, condition=True):
    """
    根据数据库中的 HumanMarkGroup 表，构建树状结构
    结构：
    [
        {
            "group_id": "1",
            "group_name": "数学科目",
            "group_level": 1,
            "children": [
                {
                    "group_id": "2",
                    "group_name": "题组A",
                    "group_level": 2,
                    "children": [
                        {
                            "group_id": "3",
                            "group_name": "小组1",
                            "group_level": 3,
                            "children": []
                        }
                    ]
                }
            ]
        }
    ]
    """
    total = new_session.query(HumanMarkGroup).filter(and_(condition, HumanMarkGroup.group_level == 1)).count()

    group_alias = aliased(HumanMarkGroup, name="group_alias")
    userinfo_c_alias = aliased(UserInfo, name="userinfo_c_alias")
    # 获取所有记录，并按 group_level 排序，以便先处理父级数据
    select_fields = [
        HumanMarkGroup.group_id, HumanMarkGroup.group_code, HumanMarkGroup.group_name, HumanMarkGroup.group_level,
        HumanMarkGroup.project_id, HumanMarkGroup.subject_id, HumanMarkGroup.parent_group_id, HumanMarkGroup.paper_id,
        HumanMarkGroup.ques_code, HumanMarkGroup.ques_id, HumanMarkGroup.is_used, HumanMarkGroup.c_user_id,
        HumanMarkGroup.u_user_id, HumanMarkGroup.created_time, HumanMarkGroup.updated_time, Project.project_name,
        Subject.subject_name, Subject.exam_mode, UserInfo.name, group_alias.group_code.label("parent_group_code"), userinfo_c_alias.name.label("c_name")
    ]

    # if page_size != -1:
    if limit:
        # todo: 分页有问题，需修改
        group_info = new_session.query(HumanMarkGroup.group_id).filter(and_(condition, HumanMarkGroup.group_level == 1)).limit(page_size).offset(offset)
        group_id_list = [i[0] for i in group_info] if group_info else []
        condition = or_(HumanMarkGroup.group_id.in_(group_id_list), HumanMarkGroup.parent_group_id.in_(group_id_list))
        group_info = new_session.query(HumanMarkGroup.group_id).filter(HumanMarkGroup.group_level == 3).all()
        small_group_id_list = [i[0] for i in group_info] if group_info else []
        print("small_group_id_list", small_group_id_list)
        group_id_list.extend(small_group_id_list)
        total_condition = or_(HumanMarkGroup.group_id.in_(group_id_list), HumanMarkGroup.parent_group_id.in_(group_id_list))
        human_group = new_session.query(*select_fields) \
            .join(Project, Project.project_id == HumanMarkGroup.project_id) \
            .join(Subject, Subject.subject_id == HumanMarkGroup.subject_id) \
            .outerjoin(UserInfo, UserInfo.user_id == HumanMarkGroup.u_user_id) \
            .outerjoin(userinfo_c_alias, userinfo_c_alias.user_id == HumanMarkGroup.c_user_id) \
            .outerjoin(group_alias, group_alias.group_id == HumanMarkGroup.parent_group_id) \
            .where(condition).order_by(HumanMarkGroup.group_level, HumanMarkGroup.group_code).all()
    else:
        # 获取全部
        human_group = new_session.query(*select_fields) \
            .join(Project, Project.project_id == HumanMarkGroup.project_id) \
            .join(Subject, Subject.subject_id == HumanMarkGroup.subject_id) \
            .outerjoin(UserInfo, UserInfo.user_id == HumanMarkGroup.u_user_id) \
            .outerjoin(userinfo_c_alias, userinfo_c_alias.user_id == HumanMarkGroup.c_user_id) \
            .outerjoin(group_alias, group_alias.group_id == HumanMarkGroup.parent_group_id) \
            .where(condition).order_by(HumanMarkGroup.group_level, HumanMarkGroup.group_code).all()

    if not human_group:
        return 0, []

    # 构建 group_id 到节点字典的映射
    node_map: Dict[str, Dict] = {
        record.group_id: {
            "group_id": record.group_id,
            "group_code": record.group_code,
            "group_name": record.group_name,
            "group_level": record.group_level,
            "project_id": record.project_id,
            "project_name": record.project_name,
            "subject_id": record.subject_id,
            "subject_name": record.subject_name,
            "exam_mode": record.exam_mode,
            "parent_group_id": record.parent_group_id,
            "parent_group_code": record.parent_group_code,
            "paper_id": record.paper_id,
            "ques_code": record.ques_code,
            "ques_id": record.ques_id,
            "is_used": record.is_used,
            "c_user_id": record.c_user_id,
            "u_user_id": record.u_user_id,
            "u_name": record.name if record.name is not None else record.c_name,
            "created_time": record.created_time and str(record.created_time).replace("T", " "),
            "updated_time": record.updated_time and str(record.updated_time).replace("T", " ") if record.updated_time is not None else record.created_time and str(record.created_time).replace("T", " "),
            "children": []
        }
        for record in human_group
    }

    # 构建树状结构
    root_nodes = []
    for record in human_group:
        node_id = record.group_id
        if record.parent_group_id:
            # 将当前节点添加到父节点的 children 中
            parent_node = node_map.get(record.parent_group_id)
            if parent_node:
                parent_node["children"].append(node_map[node_id])
        else:
            # 没有父节点，为根节点
            root_nodes.append(node_map[node_id])

    return total, root_nodes


def get_human_group_code_name(new_session):
    """
    获取人工评卷组各层级
    """
    group_code_name_dict = {}
    human_group = new_session.query(HumanMarkGroup.group_level, func.group_concat(HumanMarkGroup.group_code),
                                    func.group_concat(HumanMarkGroup.group_name)) \
        .group_by(HumanMarkGroup.group_level).all()
    if not human_group:
        group_code_name_dict[1] = {}
        group_code_name_dict[2] = {}
        group_code_name_dict[3] = {}
        return group_code_name_dict

    for group_level, group_code_str, group_name_str in human_group:
        group_code_list = sorted(group_code_str.split(","))
        group_name_list = sorted(group_name_str.split(","))
        int_group_code_list = []
        for group_code in group_code_list:
            try:
                int_group_code_list.append(int(group_code))
            except ValueError:
                pass
        group_code_name_dict[group_level] = {
            "group_code_list": group_code_list,
            "group_name_list": group_name_list,
            "int_group_code_list": int_group_code_list
        }
    return group_code_name_dict


def auto_create_human_group(monitor_id, monitor_type, index):
    """
    自动创建科目组和题组
    """
    new_session = next(session_depend())
    add_group_data = []
    try:
        update_monitor_cal(new_session, monitor_id, monitor_type, index, "2")
        is_paper = new_session.query(ExamPaper).count()

        human_group_code_name = get_human_group_code_name(new_session)
        subject_group = human_group_code_name[1]
        ques_group = human_group_code_name[2]
        # 新增小组编码处理
        group_3_code_list = human_group_code_name.get(3, {}).get("int_group_code_list", [])
        # 自动创建科目组
        subject_info = new_session.query(Subject.project_id, Subject.subject_id, Subject.subject_name).all()
        if not subject_info:
            update_monitor_cal(new_session, monitor_id, monitor_type, index, "3")
            return False, "请先补充科目信息"

        for project_id, subject_id, subject_name in subject_info:
            # 科目组编码生成逻辑
            if subject_group.get("int_group_code_list"):
                max_subject_group_code = subject_group["int_group_code_list"][-1]
                new_sub_group_code = str((max_subject_group_code + 1)).rjust(3, "0")
                subject_group["int_group_code_list"].append(int(new_sub_group_code))
            else:
                new_sub_group_code = "001"
                subject_group["int_group_code_list"] = [int(new_sub_group_code)]

            new_sub_group_name = f"{subject_name}阅卷组"
            subject_group_id = configs.snow_worker.get_id()
            subject_item = HumanMarkGroup(group_id=subject_group_id, group_code=new_sub_group_code,
                                          group_name=new_sub_group_name, group_level=1, project_id=project_id,
                                          subject_id=subject_id, is_used=1)
            add_group_data.append(subject_item)
            # 题组创建逻辑
            if is_paper:
                ques_info = new_session.query(PaperDetail.paper_id, PaperDetail.ques_order, ExamQuestion.ques_code, ExamQuestion.ques_id) \
                    .join(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
                    .join(ExamQuestion, ExamQuestion.ques_id == PaperDetail.ques_id) \
                    .filter(and_(ExamPaper.project_id == project_id, ExamPaper.subject_id == subject_id,
                                 ExamQuestion.parent_ques_id.is_(None),
                                 ~ExamQuestion.ques_type_code.in_(["A", "B", "C"]))).all()
            else:
                ques_info = new_session.query(QuesUsed.used_id, QuesUsed.used_count, QuesUsed.ques_code, ExamQuestion.ques_id) \
                    .join(ExamQuestion, ExamQuestion.ques_code == QuesUsed.ques_code) \
                    .join(BusinessQuesType,
                          BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
                    .filter(and_(QuesUsed.used_count != 0, BusinessQuesType.project_id == project_id,
                                 BusinessQuesType.subject_id == subject_id, ExamQuestion.parent_ques_id.is_(None),
                                 ~ExamQuestion.ques_type_code.in_(["A", "B", "C"]))).all()

            if not ques_info:
                continue

            logger.info(f"自动创建 {subject_name} 下的题组")

            ques_code_list = []
            for paper_id, ques_order, ques_code, ques_id in ques_info:
                if ques_code in ques_code_list:
                    continue
                ques_code_list.append(ques_code)
                # 题组编码生成逻辑
                if ques_group.get("int_group_code_list"):
                    max_ques_group_code = ques_group["int_group_code_list"][-1]
                    new_ques_group_code = str((max_ques_group_code + 1)).rjust(7, "0")
                    ques_group["int_group_code_list"].append(int(new_ques_group_code))
                else:
                    new_ques_group_code = new_sub_group_code + "0001"
                    ques_group["int_group_code_list"] = [int(new_ques_group_code)]
                paper_id = paper_id if is_paper else None
                ques_order = ques_order if is_paper else None
                new_ques_group_name = ques_code
                ques_item = HumanMarkGroup(group_id=configs.snow_worker.get_id(), group_code=new_ques_group_code,
                                           group_name=new_ques_group_name, group_level=2, project_id=project_id,     #  new_ques_group_name
                                           subject_id=subject_id, parent_group_id=subject_group_id, paper_id=paper_id,
                                           ques_order=ques_order, ques_code=ques_code, ques_id=ques_id, is_used=1)
                add_group_data.append(ques_item)

                # 新增：自动创建小组
                # 每个题组默认创建1个小组
                for i in range(1, 2):  # 创建1个小组
                    # 小组编码生成逻辑
                    if group_3_code_list:
                        max_group3_code = group_3_code_list[-1]
                        new_group3_code = str((max_group3_code + 1)).rjust(9, "0")
                        group_3_code_list.append(int(new_group3_code))
                    else:
                        new_group3_code = new_ques_group_code + f"{i:02d}"
                        group_3_code_list = [int(new_group3_code)]

                    # 调用create_single_human_group创建小组
                    result, msg, group3_id = create_single_human_group(
                        new_session=new_session,
                        human_group_code_name=human_group_code_name,
                        project_id=project_id,
                        subject_id=subject_id,
                        group_level=3,
                        group_code=new_group3_code,
                        group_name=f"{ques_code}小组{i}",  # ques_code
                        parent_group_id=ques_item.group_id,  # 父组为当前题组
                        paper_id=paper_id,
                        ques_code=ques_code,
                        ques_order=ques_order,
                        user_id="system_auto"  # 系统自动创建
                    )

                    if result:
                        logger.info(f"自动创建小组 {new_group3_code} 成功")

        if add_group_data:
            new_session.add_all(add_group_data)
            new_session.commit()
        update_monitor_cal(new_session, monitor_id, monitor_type, index, "3")
        logger.info("自动创建科目组、题组和小组成功")
        return True, None
    except Exception as e:
        update_monitor_cal(new_session, monitor_id, monitor_type, index, "1")
        logger.error(f"自动创建科目组、题组和小组失败：{e}")
        logger.error(traceback.format_exc())
        return False, "自动创建科目组、题组和小组失败"


def valid_group_info(group_level, group_code, project_id, subject_id, parent_group_id, group_name, human_group_code_name, is_update=False):
    """
    检查小组信息
    """
    if not group_code.isdigit():
        return False, "小组编号只能输入数字"

    if not project_id or not subject_id:
        if group_level == 1:
            return False, "请选择资格和科目"
        else:
            if not parent_group_id:
                return False, "请选择父级小组"

    if not is_update:
        exit_group_code_name = human_group_code_name.get(group_level)

        if exit_group_code_name:
            group_code_list = exit_group_code_name["group_code_list"]
            group_name_list = exit_group_code_name["group_name_list"]

            if group_code in group_code_list:
                logger.warning(f"小组编号 {group_code} 已存在")
                return False, f"小组编号 {group_code} 已存在"

            if group_name in group_name_list:
                logger.warning(f"小组名称 {group_name} 已存在")
                return False, f"小组名称 {group_name} 已存在"
    return True, None


def create_single_human_group(new_session, human_group_code_name, project_id, subject_id, group_level, group_code,
                              group_name, parent_group_id, paper_id, ques_code, ques_order, user_id):
    """
    创建单个人工阅卷小组
    """
    result, msg = valid_group_info(group_level, group_code, project_id, subject_id, parent_group_id, group_name, human_group_code_name)
    if not result:
        return False, msg, None

    if group_level != 1 and (not project_id or not subject_id):
        project_id, subject_id, query_ques_code = new_session.query(HumanMarkGroup.project_id, HumanMarkGroup.subject_id, HumanMarkGroup.ques_code).filter(HumanMarkGroup.group_id == parent_group_id).first()
        if not ques_code:
            ques_code = query_ques_code

    group_id = configs.snow_worker.get_id()
    if group_level == 1:
        ques_id = None
    else:
        ques_id, ques_code = new_session.query(ExamQuestion.ques_id, ExamQuestion.ques_code).filter(and_(ExamQuestion.ques_code == ques_code, ExamQuestion.parent_ques_id.is_(None))).first()

    item = HumanMarkGroup(group_id=group_id, group_code=group_code, group_name=group_name, group_level=group_level,
                          project_id=project_id, subject_id=subject_id, parent_group_id=parent_group_id,
                          paper_id=paper_id, ques_code=ques_code, ques_id=ques_id, ques_order=ques_order, is_used=1,
                          c_user_id=user_id, created_time=format_now_time())

    new_session.add(item)
    new_session.commit()
    return True, None, group_id


def auto_create_excel_human_group(new_session, create_groups: list, user_id: str):
    """
    自动创建导入的 excel 的小组
    """
    human_group_code_name = get_human_group_code_name(new_session)

    subject_info = new_session.query(Subject.subject_id, Subject.subject_name, Subject.exam_mode).all()

    if not subject_info:
        return False, "请先创建科目"

    subject_exam_mode = {}

    for i in subject_info:
        subject_id, subject_name, exam_mode = i
        if exam_mode is None:
            return False, f"请先设置科目 {subject_name} 的考试模式"
        subject_exam_mode[subject_id] = exam_mode

    parent_group = {
        1: {},
        2: {}
    }

    for index, groups in enumerate(create_groups):
        group_level = index + 1

        for group in groups:
            print("group", group)

            project_id, subject_id, group_code, group_name, ques_code = group["project_id"], group["subject_id"], group["group_code"], group["group_name"], group["ques_code"]

            pro_sub_condition = and_(HumanMarkGroup.project_id == project_id, HumanMarkGroup.subject_id == subject_id)

            if group_level == 1:
                # 创建科目组
                group_exist = new_session.query(exists().where(and_(HumanMarkGroup.group_level == 1, pro_sub_condition))).scalar()
                print("group_exist1", group_name, group_exist)
                if not group_exist:
                    result, msg, group_id = create_single_human_group(new_session, human_group_code_name, project_id,
                                                                      subject_id, group_level, group_code, group_name, None,
                                                                      None, None, None, user_id)
                    if result:
                        parent_group[1][subject_id] = group_id
                        logger.info(f"创建科目组 {group_name} 成功")
            else:
                is_paper = subject_exam_mode[subject_id]
                if is_paper:
                    detail_info = new_session.query(PaperDetail.paper_id, PaperDetail.ques_order).filter(PaperDetail.ques_code == ques_code).first()
                    paper_id, ques_order = detail_info
                else:
                    paper_id, ques_order = None, None

                paper_condition = HumanMarkGroup.paper_id == paper_id if paper_id else True

                if group_level == 2:
                    # 创建题组
                    group_exist = new_session.query(exists().where(and_(HumanMarkGroup.group_level == 2, pro_sub_condition, paper_condition, HumanMarkGroup.ques_code == ques_code))).scalar()
                    print("group_exist2", group_name, group_exist)
                    if not group_exist:
                        parent_group_id = parent_group[1].get(subject_id)
                        if not parent_group_id:
                            parent_group_id = new_session.query(HumanMarkGroup.group_id).filter(and_(HumanMarkGroup.group_level == 1, pro_sub_condition)).scalar()

                        parent_group[1][subject_id] = parent_group_id

                        result, msg, group_id = create_single_human_group(new_session, human_group_code_name, project_id, subject_id, group_level, group_code, group_name, parent_group_id, paper_id, ques_code, ques_order, user_id)

                        if result:
                            parent_group[2][ques_code] = group_id
                            logger.info(f"创建题组 {group_name} 成功")
                else:
                    # 创建评卷阅小组
                    group_exist = new_session.query(exists().where(and_(HumanMarkGroup.group_level == 3, pro_sub_condition, paper_condition, HumanMarkGroup.ques_code == ques_code))).scalar()
                    print("group_exist3", group_name, group_exist)
                    if not group_exist:
                        parent_group_id = parent_group[2].get(ques_code)
                        if not parent_group_id:
                            parent_group_id = new_session.query(HumanMarkGroup.group_id).filter(and_(HumanMarkGroup.group_level == 2, pro_sub_condition, paper_condition, HumanMarkGroup.ques_code == ques_code)).scalar()

                        parent_group[2][ques_code] = parent_group_id
                        result, msg, group_id = create_single_human_group(new_session, human_group_code_name, project_id, subject_id, group_level, group_code, group_name, parent_group_id, paper_id, ques_code, ques_order, user_id)
                        if result:
                            logger.info(f"创建评卷阅小组 {group_name} 成功")
    return True, None


