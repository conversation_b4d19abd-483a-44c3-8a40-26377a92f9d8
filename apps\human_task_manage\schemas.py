from typing import Optional, Literal, List
from pydantic import BaseModel, Field

from apps.base.schemas import PaginationModel


class CreateHumanMarkTask(BaseModel):
    task_name: str = Field(..., description="人工阅卷任务名称")
    project_id: str = Field(..., max_length=50, description="项目id")
    subject_id: str = Field(..., max_length=50, description="科目id")
    process_id: str = Field(..., max_length=50, description="阅卷主流程id")
    paper_id: Optional[str] = Field(None, max_length=30, description="试卷id")
    ques_type_code: str = Field(..., description="阅卷题型简码")
    business_type_name: Optional[str] = Field(None, comment="业务题型名称")
    ques_order: Optional[str] = Field(None, max_length=12, description="试题序号")
    ques_id: str = Field(..., max_length=50, description="试题id")
    ques_code: str = Field(..., max_length=30, description="试题编号")
    business_id: Optional[str] = Field(None, max_length=50, description="业务题型id")
    fetch_score_way: Literal[None, 1, 2, 3, 4] = Field(None, description="专家个数超过1个时该字段不为空，1 为取最高分，2 为取最低分，3 为取平均分, 4 为去除最高和最低分取平均分")
    fetch_score_scope: Literal[None, 1, 2, 3, 4] = Field(None, description="平均范围，fetch_score_way 为3时该字段不为空，1 为全分组，2 为高分组，3 为中分组，4 为低分组")
    fetch_score_option: Literal[None, 1, 2, 3] = Field(None, description="平均分值计算，fetch_score_way 为3时该字段不为空，1 为向下舍入，2 为向上舍入，3 为四舍五入")
    arbitrate_threshold_type: Literal[None, 1, 2] = Field(None, description="仲裁阈值类型，1 表示百分比，2 表示分值")
    arbitrate_threshold: Optional[int] = Field(None, description="仲裁阈值")
    arbitrate_score_diff: Literal[None, 1, 2, 3] = Field(None, description="仲裁分差取值方式，1 表示平均值，2 表示最大偏差，3 表示最小偏差")
    deviation_threshold_type: Literal[None, 1, 2] = Field(None, description="仲裁离差类型，1 表示百分比，2 表示分值")
    arbitrate_deviation: Optional[int] = Field(None, description="仲裁离差，表示评卷员和仲裁员之间有效的分数差")
    mark_score_step: Optional[float] = Field(1, description="评分步长，打分间隔")
    group_id_list: list = Field(..., description="阅卷小组id列表")


class CheckHumanTasksExistReq(BaseModel):
    task_type: Literal[1, 2] = Field(..., description="任务类型")
    ques_code_list: list = Field(..., description="试题编号列表")


class CreateHumanMarkTasksReq(BaseModel):
    task_type: Literal[1, 2, 3] = Field(..., description="阅卷任务类型")
    human_task_list: List[CreateHumanMarkTask] = Field(..., description="人工阅卷任务列表")


class GetHumanReadTaskReq(PaginationModel):
    task_type: Literal[None, 1, 2, 3] = Field(..., description="阅卷任务类型")
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    paper_id: Optional[str] = Field(None, description="试卷id")
    task_name: Optional[str] = Field(None, description="阅卷任务名称")
    business_type_name: Optional[str] = Field(None, description="业务题型名称")
    ques_order: Optional[str] = Field(None, description="题号")
    ques_code: Optional[str] = Field(None, description="试题编号")
    round_state_list: List[int] = Field(None, description="任务状态")
    round_count: Optional[int] = Field(None, description="轮次")
    task_id_list: list = Field([], description="任务id列表，用于后端过滤数据")
    round_id_list: list = Field([], description="轮次id列表，用于后端过滤数据")


class GetRoundReq(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")


class BaseHumanReadRoundReq(BaseModel):
    task_id: str = Field(..., description="任务id")
    round_id: str = Field(..., description="轮次id")


class UpdateHumanReadRoundReq(CreateHumanMarkTask):
    task_id: str = Field(..., description="任务id")
    round_id: str = Field(..., description="轮次id")


class GetTaskListReq(PaginationModel):
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    round_count: Optional[str] = Field(None,description="轮次")


class GetGroupListReq(PaginationModel):
    task_id: str = Field(..., description="任务id")
    project_id: Optional[str] = Field(None, description="项目id")
    subject_id: Optional[str] = Field(None, description="科目id")
    round_id: Optional[str] = Field(None, description="轮次id")


class LaunchTaskRoundReq(BaseModel):
    task_type: Literal[1, 2] = Field(..., description="阅卷任务类型")
    launch_list: List[BaseHumanReadRoundReq] = Field(..., description="发起任务的信息")


class GetRoundByTaskReq(BaseModel):
    task_id: str = Field(..., description="任务id")


class GetStepByQuesCodeReq(BaseModel):
    ques_code: str = Field(..., description="试题编号")
    task_type: Literal[1, 2] = Field(..., description="1 表示正评，2 表示未评")
