from typing import Optional

from pydantic import BaseModel, Field

from apps.base.schemas import PaginationModel


class GetSetStdQuesReq(PaginationModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_type_code_list: list = Field([], title="试题类型简码列表")
    ques_order: Optional[str] = Field(None, title="试题序号")
    small_ques_num: Optional[int] = Field(None, title="小题序号")
    ques_code: Optional[str] = Field(None, title="试题编号")
    set_std_state_list: Optional[list] = Field(None, title="定标状态")


class SetStdIdReq(BaseModel):
    ques_id: str = Field(..., title="试题id")


class StartSetStdReq(BaseModel):
    ques_id_list: list = Field(..., title="试题id列表")
    is_auto_trigger: bool = Field(False, comment="非前端参数，区分是用户触发的，还是程序自动触发的")
    is_auto_launch: bool = Field(False, comment="非前端参数，区分是否是系统自启任务")


class QuesSetStdDetailReq(PaginationModel):
    ques_id: str = Field(..., title="试题id")
    same_answer_group_id: Optional[str] = Field(None, title="答案分组 id")
    answer_cluster: Optional[str] = Field(None, title="考生答案")
    mark_state: Optional[int] = Field(None, title="评分状态（对应 AI 定标的详情里的 AI 定标状态），1 表示未定标，2 表示已定标（定标成功），3 表示定标异常，4 表示重新定标")
    stu_score: Optional[dict] = Field(None, title="考生分数查询条件")
    # 查询条件类型为 dict，参数说明
    # {
    #     "type": "1",  # 1 表示小于，2 表示大于，3 表示等于
    #     "value": value  # 传整数或浮点数
    # }
    search_time: Optional[list] = Field([], title="评分时间范围")
    small_ques_order: Optional[str] = Field(None, title="填空题的第几空")


class AgainSetStdReq(QuesSetStdDetailReq):
    select_group_id_list: list = Field([], title="前端勾选的分组id 列表")


class ManualSetStdReq(BaseModel):
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_id: str = Field(..., title="试题id")
    group_id_list: list = Field(..., title="答案分组 id 列表")
    manual_score: float = Field(..., title="人工定标分数")
    manual_answer_parse: list = Field(..., title="人工定标评析")


class ExportClusterScoreReq(BaseModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_type_code_list: list = Field([], title="试题类型简码列表")
    ques_order: Optional[str] = Field(None, title="试题序号")
    ques_code: Optional[str] = Field(None, title="试题编号")
    set_std_state_list: Optional[list] = Field(None, title="定标状态")
    ques_id_list: Optional[list] = Field([], title="试题id列表")
    total_count: Optional[int] = Field(None, title="ques_id_list有值时，该参数为导出的条数，否则为空")


class ExcSetStdReq(GetSetStdQuesReq):
    ques_id_list: list = Field([], title="试题id列表")


class GetExcSetStdProgressReq(BaseModel):
    record_id: Optional[str] = Field(None, title="记录id")


class GetScoreParseReq(BaseModel):
    parse_type: int = Field(..., title="解析类型")
