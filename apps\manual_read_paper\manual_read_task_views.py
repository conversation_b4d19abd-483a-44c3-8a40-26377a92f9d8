import re
import threading
import traceback

from fastapi import APIRouter, Depends

from apps.base.services import request_api
from apps.ques_manage.services import get_business_data
from settings import logger
from sqlalchemy import select, exists, and_, func, text
from sqlalchemy.orm import Session
from typing import Any

from apps.base.global_cache import set_stu_num_by_task_id, set_single_task_state, set_manual_mark_info, \
    get_redis_ques_info_dict
from apps.manual_read_paper.manual_read_services import get_student_count
from apps.manual_read_paper.manual_task_services import launch_manual_task_main, get_task_type, \
    get_manual_total_percentage
from apps.manual_read_paper.schemas import (CreateManualReadTaskReq, GetManualReadTaskReq, UpdateManualReadTaskReq,
                                            ManualReadTaskIdReq, CreateBatchManualReadTaskReq, ManualReadTaskIdListReq)
from apps.manual_read_paper.services import manual_task_query_condition, update_task_group_info, get_task_state_name
from apps.permission.services import get_user_selected_data_flag, update_user_data_permission
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse

from apps.models.models import UserInfo, Subject, ManualReadTask, ManualReadTaskGroup, ManualReadPaperGroup, ExamPaper, \
    Project, QuesType, WorkFlowMainProcess, ExamQuestion, ExamStudent, ManualDistributeAnswer, TaskExecuteRecord, \
    ManualGroupUser, StuAnswer, PaperDetail, BusinessQuesType
from helper import response_utils
from factory_apps import session_depend
from settings import configs
from utils.time_func import format_now_time
from utils.utils import find_duplicates_ordered

manual_task_router = APIRouter()


@manual_task_router.post(path="/create_manual_task", response_model=BaseResponse, summary="创建阅卷任务")
async def create_manual_task(query: CreateManualReadTaskReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建阅卷任务")
    (m_read_task_name, project_id, subject_id, manual_process_id, paper_id, ques_type_code, ques_order, ques_code,
     ques_id, score_step, fetch_score_way, fetch_score_scope, fetch_score_option, arbitrate_threshold_type,
     arbitrate_threshold, arbitrate_score_diff, quality_ratio, quality_upper_limit, quality_lower_limit,
     quality_reevaluation, m_read_group_id_list, business_id, remark) = query.model_dump().values()

    is_exist = new_session.query(exists().where(ManualReadTask.m_read_task_name == m_read_task_name)).scalar()
    if is_exist:
        return BaseResponse(code=response_utils.fields_exist, msg=f"阅卷任务名 {m_read_task_name} 已存在")

    try:
        # 考生数量就是该任务阅卷数据总量
        task_total_count = get_student_count(new_session, project_id, subject_id)
        # 生成阅卷任务记录信息
        record_id = configs.snow_worker.get_id()
        new_record = TaskExecuteRecord(record_id=record_id, record_type=1, progress=0, success_count=0,
                                       total_count=task_total_count)
        new_session.add(new_record)

        if paper_id and not ques_order:
            ques_order = new_session.query(PaperDetail.ques_order).filter(
                and_(PaperDetail.paper_id == paper_id, PaperDetail.ques_id == ques_id)).scalar()
        new_manual_task_id = configs.snow_worker.get_id()
        new_manual_task = ManualReadTask(m_read_task_id=new_manual_task_id, m_read_task_name=m_read_task_name,
                                         project_id=project_id, subject_id=subject_id, business_id=business_id,
                                         manual_process_id=manual_process_id, paper_id=paper_id,
                                         ques_type_code=ques_type_code, ques_order=ques_order,
                                         ques_code=ques_code, ques_id=ques_id, fetch_score_way=fetch_score_way,
                                         fetch_score_scope=fetch_score_scope, fetch_score_option=fetch_score_option,
                                         arbitrate_threshold_type=arbitrate_threshold_type,
                                         arbitrate_threshold=arbitrate_threshold,
                                         arbitrate_score_diff=arbitrate_score_diff, quality_ratio=quality_ratio,
                                         quality_upper_limit=quality_upper_limit,
                                         quality_lower_limit=quality_lower_limit,
                                         quality_reevaluation=quality_reevaluation, score_step=score_step,
                                         task_state=1, remark=remark, c_user_id=user.get("user_id"),
                                         expert_read_record_id=record_id)
        new_session.add(new_manual_task)
    except Exception as e:
        logger.error(f"创建阅卷任务失败，{e}")
        traceback.print_exc()
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"创建阅卷任务失败")

    try:
        new_task_group_list = []
        for group_id in m_read_group_id_list:
            new_task_group = ManualReadTaskGroup(m_read_task_group_id=configs.snow_worker.get_id(),
                                                 m_read_task_id=new_manual_task_id, manual_group_id=group_id)
            new_task_group_list.append(new_task_group)
        new_session.add_all(new_task_group_list)
        new_session.query(ManualReadPaperGroup).filter(
            ManualReadPaperGroup.manual_group_id.in_(m_read_group_id_list)).update({ManualReadPaperGroup.lock_state: 2})

        is_paper = new_session.query(ExamPaper).count()
        if is_paper:
            new_session.query(PaperDetail).filter(PaperDetail.ques_id == ques_id).update({PaperDetail.manual_read_state: 2})
            # 如果选择的是组合题，组合题下的小题状态也修改掉
            if ques_type_code == "F":
                new_session.query(PaperDetail).filter(PaperDetail.parent_ques_id == ques_id).update(
                    {PaperDetail.manual_read_state: 2})
        else:
            new_session.query(ExamQuestion).filter(ExamQuestion.ques_id == ques_id).update(
                {ExamQuestion.manual_read_state: 2})
            # 如果选择的是组合题，组合题下的小题状态也修改掉
            if ques_type_code == "F":
                new_session.query(ExamQuestion).filter(ExamQuestion.parent_ques_id == ques_id).update(
                    {ExamQuestion.manual_read_state: 2})

        new_session.query(WorkFlowMainProcess).filter(WorkFlowMainProcess.process_id == manual_process_id).update(
            {WorkFlowMainProcess.lock_state: 2})
        # 给小组成员添加数据权限
        # 获取小组人员id
        user_id_info = new_session.query(ManualGroupUser.user_id, ManualGroupUser.role_id, UserInfo.name) \
            .join(UserInfo, UserInfo.user_id == ManualGroupUser.user_id) \
            .filter(ManualGroupUser.manual_group_id.in_(m_read_group_id_list)).all()
        user_id_list, expert_id_list = [], []
        for user_id, role_id, name in user_id_info:
            user_id_list.append(user_id)
            if role_id == "3":
                if user_id in expert_id_list:
                    return BaseResponse(code=response_utils.fields_exist, msg=f"{name} 该专家存在于所选的多个小组中")
                else:
                    expert_id_list.append(user_id)
        for user_id in user_id_list:
            _, msg = update_user_data_permission(new_session, [project_id], [subject_id],
                                                 user_id=user_id, c_user_id=user.get("user_id"), is_create_pro_sub=True)
            logger.info(msg)
        new_session.commit()
        set_stu_num_by_task_id(new_manual_task_id)
        set_single_task_state(new_manual_task_id, 1)
        logger.info(f"创建阅卷任务 {m_read_task_name} 成功")
        return BaseResponse(msg="创建阅卷任务并给小组成员添加数据权限成功")
    except Exception as e:
        logger.error(f"创建阅卷任务失败，{e}")
        logger.error(traceback.format_exc())
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="创建阅卷任务失败")


@manual_task_router.post(path="/create_batch_manual_task", response_model=BaseResponse, summary="批量创建阅卷任务")
async def create_manual_task(query: CreateBatchManualReadTaskReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 批量创建阅卷任务")

    task_name_list = [task.m_read_task_name for task in query.task_list]

    duplicates_task_list = find_duplicates_ordered(task_name_list)
    if duplicates_task_list:
        return BaseResponse(code=response_utils.operation_repeat, msg=f"{'，'.join(duplicates_task_list)} 任务名重复")

    exist_info = new_session.query(ManualReadTask.m_read_task_name).filter(
        ManualReadTask.m_read_task_name.in_(task_name_list)).all()
    if exist_info:
        duplicates_task_list = [i[0] for i in exist_info]
        return BaseResponse(code=response_utils.operation_repeat, msg=f"{'，'.join(duplicates_task_list)} 任务名已存在")

    fail_list = []
    for task in query.task_list:
        req_data = {
            "m_read_task_name": task.m_read_task_name,
            "project_id": task.project_id,
            "subject_id": task.subject_id,
            "manual_process_id": task.manual_process_id,
            "paper_id": task.paper_id,
            "ques_type_code": task.ques_type_code,
            "ques_order": task.ques_order,
            "ques_code": task.ques_code,
            "ques_id": task.ques_id,
            "score_step": task.score_step,
            "fetch_score_way": task.fetch_score_way,
            "fetch_score_scope": task.fetch_score_scope,
            "fetch_score_option": task.fetch_score_option,
            "arbitrate_threshold_type": task.arbitrate_threshold_type,
            "arbitrate_threshold": task.arbitrate_threshold,
            "arbitrate_score_diff": task.arbitrate_score_diff,
            "quality_ratio": task.quality_ratio,
            "quality_upper_limit": task.quality_upper_limit,
            "quality_lower_limit": task.quality_lower_limit,
            "quality_reevaluation": task.quality_reevaluation,
            "m_read_group_id_list": task.m_read_group_id_list,
            "business_id": task.business_id,
            "remark": task.remark
        }
        token = f"Bearer {user['token']}"
        url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/manual_task/create_manual_task"
        res, msg = request_api(url, "POST", req_data, token, "重新AI定标的数据", is_print=False)
        if res == 0:
            fail_list.append(task.m_read_task_name)
    if fail_list:
        logger.error(f'{"，".join(fail_list)} 任务创建失败')
    return BaseResponse(msg="批量创建任务成功")


@manual_task_router.post(path="/get_manual_task", response_model=BaseResponse, summary="获取阅卷任务列表")
async def get_manual_task(query: GetManualReadTaskReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取阅卷任务列表")
    (current_page, page_size, m_read_task_id_list, real_group_id_list, m_read_task_name, project_id, subject_id,
     paper_id, paper_name, is_distri_task) = query.model_dump().values()
    curr_user_id = user['user_id']

    user_project_flag, user_subject_flag = get_user_selected_data_flag(new_session, user["user_id"], user["role"])

    task_data = []

    select_fields = [ManualReadTask.m_read_task_id, ManualReadTask.m_read_task_name, ManualReadTask.project_id,
                     Project.project_name, ManualReadTask.subject_id, Subject.subject_name, ManualReadTask.paper_id,
                     ExamPaper.paper_name, ManualReadTask.manual_process_id, ManualReadTask.business_id,
                     WorkFlowMainProcess.process_name,
                     func.group_concat(ManualReadTaskGroup.manual_group_id).label("manual_group_id_str"),
                     func.group_concat(ManualReadPaperGroup.manual_group_name).label("manual_group_name_str"),
                     ManualReadTask.ques_type_code, QuesType.ques_type_name, ManualReadTask.remark, UserInfo.username,
                     ManualReadTask.created_time, ManualReadTask.updated_time, ManualReadTask.lock_state,
                     ManualReadTask.ques_order, ManualReadTask.ques_code, ManualReadTask.score_step,
                     ManualReadTask.quality_ratio, ManualReadTask.fetch_score_way, ManualReadTask.fetch_score_option,
                     ManualReadTask.fetch_score_scope, ManualReadTask.arbitrate_threshold_type,
                     ManualReadTask.arbitrate_threshold, ManualReadTask.arbitrate_score_diff,
                     ManualReadTask.quality_upper_limit, ManualReadTask.quality_lower_limit,
                     ManualReadTask.quality_reevaluation, UserInfo.name, PaperDetail.ques_score_list,
                     TaskExecuteRecord.progress, ExamQuestion.ques_id, PaperDetail.parent_ques_id,
                     ManualReadTask.launch_time, ManualReadTask.task_state]

    group_by_fields = [ManualReadTask.m_read_task_id, ManualReadTask.m_read_task_name, ManualReadTask.project_id,
                       Project.project_name, ManualReadTask.subject_id, Subject.subject_name, ManualReadTask.paper_id,
                       ExamPaper.paper_name, ManualReadTask.remark, ManualReadTask.manual_process_id,
                       ManualReadTask.business_id, WorkFlowMainProcess.process_name, ManualReadTask.ques_type_code,
                       QuesType.ques_type_name, UserInfo.username, ManualReadTask.created_time,
                       ManualReadTask.updated_time, ManualReadTask.lock_state, ManualReadTask.ques_order,
                       ManualReadTask.ques_code, ManualReadTask.score_step, ManualReadTask.quality_ratio,
                       ManualReadTask.fetch_score_way, ManualReadTask.fetch_score_option,
                       ManualReadTask.fetch_score_scope, ManualReadTask.arbitrate_threshold_type,
                       ManualReadTask.arbitrate_threshold, ManualReadTask.arbitrate_score_diff,
                       ManualReadTask.quality_upper_limit, ManualReadTask.quality_lower_limit,
                       ManualReadTask.quality_reevaluation, UserInfo.name, PaperDetail.ques_score_list,
                       TaskExecuteRecord.progress, ExamQuestion.ques_id, PaperDetail.parent_ques_id,
                       ManualReadTask.launch_time]

    if m_read_task_id_list and not is_distri_task:
        total = 1
        task_stmt = select(*select_fields) \
            .join(UserInfo, ManualReadTask.c_user_id == UserInfo.user_id) \
            .outerjoin(ExamPaper, ExamPaper.paper_id == ManualReadTask.paper_id) \
            .join(ExamQuestion, ExamQuestion.ques_id == ManualReadTask.ques_id) \
            .join(PaperDetail, PaperDetail.ques_id == ManualReadTask.ques_id) \
            .join(Project, ManualReadTask.project_id == Project.project_id) \
            .join(Subject, ManualReadTask.subject_id == Subject.subject_id) \
            .join(WorkFlowMainProcess, WorkFlowMainProcess.process_id == ManualReadTask.manual_process_id) \
            .join(ManualReadTaskGroup, ManualReadTaskGroup.m_read_task_id == ManualReadTask.m_read_task_id) \
            .join(ManualReadPaperGroup, ManualReadPaperGroup.manual_group_id == ManualReadTaskGroup.manual_group_id) \
            .join(QuesType, ManualReadTask.ques_type_code == QuesType.ques_type_code) \
            .outerjoin(TaskExecuteRecord, TaskExecuteRecord.record_id == ManualReadTask.expert_read_record_id) \
            .where(and_(ManualReadTask.m_read_task_id.in_(m_read_task_id_list), TaskExecuteRecord.record_type == 1)) \
            .group_by(*group_by_fields)
    else:
        if page_size == -1:
            # 查询小组成员包含当前用户的任务
            group_id_info = new_session.query(ManualGroupUser.manual_group_id).filter(
                ManualGroupUser.user_id == user['user_id']).all()
            group_id_list = [i[0] for i in group_id_info]
            total_condition = and_(ManualReadPaperGroup.manual_group_id.in_(group_id_list), TaskExecuteRecord.record_type == 1)
            total = new_session.query(ManualReadTask.m_read_task_id) \
                .join(ManualReadTaskGroup, ManualReadTaskGroup.m_read_task_id == ManualReadTask.m_read_task_id) \
                .join(ManualReadPaperGroup, ManualReadPaperGroup.manual_group_id == ManualReadTaskGroup.manual_group_id) \
                .where(total_condition).count()

            task_stmt = select(*select_fields) \
                .join(UserInfo, ManualReadTask.c_user_id == UserInfo.user_id) \
                .outerjoin(ExamPaper, ExamPaper.paper_id == ManualReadTask.paper_id) \
                .join(ExamQuestion, ExamQuestion.ques_id == ManualReadTask.ques_id) \
                .outerjoin(PaperDetail, PaperDetail.ques_id == ManualReadTask.ques_id) \
                .join(Project, ManualReadTask.project_id == Project.project_id) \
                .join(Subject, ManualReadTask.subject_id == Subject.subject_id) \
                .join(WorkFlowMainProcess, WorkFlowMainProcess.process_id == ManualReadTask.manual_process_id) \
                .join(ManualReadTaskGroup, ManualReadTaskGroup.m_read_task_id == ManualReadTask.m_read_task_id) \
                .join(ManualReadPaperGroup, ManualReadPaperGroup.manual_group_id == ManualReadTaskGroup.manual_group_id) \
                .join(QuesType, ManualReadTask.ques_type_code == QuesType.ques_type_code) \
                .outerjoin(TaskExecuteRecord, TaskExecuteRecord.record_id == ManualReadTask.expert_read_record_id) \
                .where(total_condition) \
                .group_by(*group_by_fields)
        else:
            limit = current_page - 1
            offset = limit * page_size
            # 拼凑查询条件
            condition = manual_task_query_condition(m_read_task_name, project_id, subject_id)

            paper_id_condition = ExamPaper.paper_id == paper_id if paper_id else True
            paper_name_condition = ExamPaper.paper_name.ilike(f"%{paper_name}%") if paper_name else True

            if m_read_task_id_list:
                total_condition = and_(condition, paper_id_condition, paper_name_condition,
                                       ManualReadTask.m_read_task_id.in_(m_read_task_id_list),
                                       Project.project_flag.in_(user_project_flag),
                                       Subject.subject_flag.in_(user_subject_flag))
            else:
                total_condition = and_(condition, paper_id_condition, paper_name_condition,
                                       Project.project_flag.in_(user_project_flag),
                                       Subject.subject_flag.in_(user_subject_flag),
                                       TaskExecuteRecord.record_type == 1)

            total = new_session.query(ManualReadTask.m_read_task_id) \
                .join(Project, ManualReadTask.project_id == Project.project_id) \
                .join(Subject, ManualReadTask.subject_id == Subject.subject_id) \
                .outerjoin(ExamPaper, ExamPaper.paper_id == ManualReadTask.paper_id) \
                .outerjoin(TaskExecuteRecord, TaskExecuteRecord.record_id == ManualReadTask.expert_read_record_id) \
                .where(total_condition).count()

            task_stmt = select(*select_fields) \
                .join(UserInfo, ManualReadTask.c_user_id == UserInfo.user_id) \
                .outerjoin(ExamPaper, ExamPaper.paper_id == ManualReadTask.paper_id) \
                .join(ExamQuestion, ExamQuestion.ques_id == ManualReadTask.ques_id) \
                .outerjoin(PaperDetail, PaperDetail.ques_id == ManualReadTask.ques_id) \
                .join(Project, ManualReadTask.project_id == Project.project_id) \
                .join(Subject, ManualReadTask.subject_id == Subject.subject_id) \
                .join(WorkFlowMainProcess, WorkFlowMainProcess.process_id == ManualReadTask.manual_process_id) \
                .join(ManualReadTaskGroup, ManualReadTaskGroup.m_read_task_id == ManualReadTask.m_read_task_id) \
                .join(ManualReadPaperGroup, ManualReadPaperGroup.manual_group_id == ManualReadTaskGroup.manual_group_id) \
                .join(QuesType, ManualReadTask.ques_type_code == QuesType.ques_type_code) \
                .outerjoin(TaskExecuteRecord, TaskExecuteRecord.record_id == ManualReadTask.expert_read_record_id) \
                .where(total_condition) \
                .group_by(*group_by_fields) \
                .limit(page_size).offset(offset)

    try:
        role_id = user["role"][0]
        task_type = get_task_type(role_id)

        all_ques_info = get_redis_ques_info_dict(new_session)

        new_session.execute(text("SET SESSION group_concat_max_len = 900000;"))
        result = new_session.execute(task_stmt)
        for row in result:
            manual_group_id_list = row.manual_group_id_str.split(",")
            manual_group_name_list = row.manual_group_name_str.split(",")
            # 去重且要保证 id 和 name 一一对应
            finally_group_id_list = []
            finally_group_name_list = []
            for i, j in zip(manual_group_id_list, manual_group_name_list):
                if i not in finally_group_id_list:
                    finally_group_id_list.append(i)
                    finally_group_name_list.append(j)

            m_read_task_id, project_id, subject_id, paper_id, ques_code, ques_id, task_state_num, ques_score_list = (
                row.m_read_task_id, row.project_id, row.subject_id, row.paper_id, row.ques_code, row.ques_id,
                row.task_state, row.ques_score_list)

            if ques_score_list is None:
                ques_type_score = all_ques_info[ques_id]["ques_type_score"]
                if ques_type_score is not None:
                    ques_score_list = [ques_type_score]

            # percentage 表示的是专家、组长、质检页面的个人任务进度
            # process 表示的是阅卷任务管理的进度
            if real_group_id_list:
                curr_user_group_id = list(set(finally_group_id_list) & set(real_group_id_list))[0]
            else:
                curr_user_group_id = finally_group_id_list[0]
            percentage = get_manual_total_percentage(new_session, role_id, m_read_task_id, curr_user_id,
                                                     curr_user_group_id)

            if task_state_num == 2:
                if 0 <= percentage <= 100:
                    task_state_num = 2

            task_state_name = get_task_state_name(task_state_num)

            task_item = {
                "m_read_task_id": m_read_task_id,
                "m_read_task_name": row.m_read_task_name,
                "project_id": project_id,
                "project_name": row.project_name,
                "subject_id": subject_id,
                "subject_name": row.subject_name,
                "paper_id": row.paper_id,
                "paper_name": row.paper_name,
                "business_id": row.business_id,
                "manual_process_id": row.manual_process_id,
                "manual_process_name": row.process_name,
                "manual_group_id_list": finally_group_id_list,
                "manual_group_name_list": finally_group_name_list,
                "ques_type_code": row.ques_type_code,
                "ques_type_name": row.ques_type_name,
                "remark": row.remark,
                "c_user_name": row.username,
                "created_time": row.created_time and str(row.created_time).replace("T", " "),
                "updated_time": row.updated_time and str(row.updated_time).replace("T", " "),
                "lock_state": row.lock_state,
                "task_state_num": task_state_num,
                "task_state": task_state_name,
                "ques_order": re.sub(r'^0+', '', row.ques_order) if row.ques_order else None,
                "ques_code": ques_code,
                "score_step": row.score_step,
                "quality_ratio": row.quality_ratio,
                "fetch_score_way": row.fetch_score_way,
                "fetch_score_option": row.fetch_score_option,
                "task_type": task_type,
                "percentage": percentage,
                "group_id": curr_user_group_id,
                "fetch_score_scope": row.fetch_score_scope,
                "arbitrate_threshold_type": row.arbitrate_threshold_type,
                "arbitrate_threshold": row.arbitrate_threshold,
                "arbitrate_score_diff": row.arbitrate_score_diff,
                "quality_upper_limit": row.quality_upper_limit,
                "quality_lower_limit": row.quality_lower_limit,
                "quality_reevaluation": row.quality_reevaluation,
                "c_name": row.name,
                "ques_score_list": ques_score_list,
                "progress": row.progress if row.progress is not None else 0,
                "ques_id": ques_id,
                "parent_ques_id": row.parent_ques_id,
                "launch_time": row.launch_time and str(row.launch_time).replace("T", " ")
            }
            task_data.append(task_item)
    except Exception as e:
        logger.error(f"获取阅卷任务列表失败，{e}")
        traceback.print_exc()
        return BaseResponse(code=response_utils.server_error, msg=f"获取阅卷任务列表失败")
    logger.info("获取阅卷任务列表成功")
    data = {
        "data": sorted(task_data, key=lambda x: x["percentage"]),
        "total": total
    }
    return BaseResponse(msg="获取阅卷任务列表成功", data=data)


@manual_task_router.post(path="/update_manual_task", response_model=BaseResponse, summary="编辑人工阅卷任务")
async def update_manual_group(query: UpdateManualReadTaskReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 编辑人工阅卷任务")
    try:
        (m_read_task_id, m_read_task_name, project_id, subject_id, manual_process_id, paper_id, ques_type_code,
         ques_order, ques_code, score_step, fetch_score_way, fetch_score_scope, fetch_score_option,
         arbitrate_threshold_type, arbitrate_threshold, arbitrate_score_diff, quality_ratio, quality_upper_limit,
         quality_lower_limit, quality_reevaluation, m_read_group_id_list, remark) = query.model_dump().values()

        task_data = new_session.query(ManualReadTask.lock_state, ManualReadTask.paper_id, ManualReadTask.ques_code) \
            .filter(ManualReadTask.m_read_task_id == m_read_task_id).first()
        if not task_data:
            return BaseResponse(code=response_utils.no_field, msg="没有该人工阅卷任务")
        lock_state, raw_paper_id, raw_ques_code = task_data[0], task_data[1], task_data[2]
        if lock_state == 2:
            return BaseResponse(code=response_utils.permission_deny, msg="该人工阅卷任务已被使用，不允许编辑")

        new_session.query(ManualReadTask).filter(ManualReadTask.m_read_task_id == m_read_task_id).update({
            ManualReadTask.m_read_task_name: m_read_task_name,
            ManualReadTask.project_id: project_id,
            ManualReadTask.subject_id: subject_id,
            ManualReadTask.manual_process_id: manual_process_id,
            ManualReadTask.paper_id: paper_id,
            ManualReadTask.ques_type_code: ques_type_code,
            ManualReadTask.ques_order: ques_order,
            ManualReadTask.ques_code: ques_code,
            ManualReadTask.score_step: score_step,
            ManualReadTask.fetch_score_way: fetch_score_way,
            ManualReadTask.fetch_score_scope: fetch_score_scope,
            ManualReadTask.fetch_score_option: fetch_score_option,
            ManualReadTask.arbitrate_threshold_type: arbitrate_threshold_type,
            ManualReadTask.arbitrate_threshold: arbitrate_threshold,
            ManualReadTask.arbitrate_score_diff: arbitrate_score_diff,
            ManualReadTask.quality_ratio: quality_ratio,
            ManualReadTask.quality_upper_limit: quality_upper_limit,
            ManualReadTask.quality_lower_limit: quality_lower_limit,
            ManualReadTask.quality_reevaluation: quality_reevaluation,
            ManualReadTask.u_user_id: user.get("user_id")
        })

        if paper_id:
            if raw_paper_id != paper_id or raw_ques_code != ques_code:
                new_session.query(PaperDetail).filter(
                    and_(PaperDetail.paper_id == raw_paper_id, PaperDetail.ques_code == raw_ques_code)).update(
                    {PaperDetail.manual_read_state: 1})
                new_session.query(PaperDetail).filter(
                    and_(PaperDetail.paper_id == paper_id, PaperDetail.ques_code == ques_code)).update(
                    {PaperDetail.manual_read_state: 2})

        update_group_result, update_group_msg = update_task_group_info(m_read_task_id, m_read_group_id_list,
                                                                       new_session)
        if update_group_result:
            logger.info("编辑人工阅卷任务成功")
            new_session.commit()
            return BaseResponse(msg="编辑人工阅卷任务成功")
        else:
            new_session.rollback()
            traceback.print_exc()
            logger.error("编辑人工阅卷任务失败")
            return BaseResponse(code=response_utils.server_error, msg="编辑人工阅卷任务失败")
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.error(f"编辑人工阅卷任务失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg="编辑人工阅卷任务失败")


@manual_task_router.post(path="/delete_manual_task", response_model=BaseResponse, summary="删除人工阅卷任务")
async def delete_manual_task(query: ManualReadTaskIdReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 删除人工阅卷任务")
    m_read_task_id = query.m_read_task_id
    task_data = new_session.query(ManualReadTask.m_read_task_id, ManualReadTask.lock_state, ManualReadTask.paper_id,
                                  ManualReadTask.ques_code, ManualReadTask.project_id, ManualReadTask.subject_id,
                                  ManualReadTask.expert_read_record_id) \
        .filter(ManualReadTask.m_read_task_id == m_read_task_id).first()
    if not task_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该人工阅卷任务")

    m_read_task_id, lock_state, paper_id, ques_code, project_id, subject_id, expert_read_record_id = task_data
    if lock_state == 2:
        return BaseResponse(code=response_utils.permission_deny, msg="该人工阅卷任务已被使用，不允许删除")

    try:
        task_group_info = new_session.query(ManualReadTaskGroup.manual_group_id).filter(
            ManualReadTaskGroup.m_read_task_id == m_read_task_id).all()
        new_session.query(ManualReadTask).filter(ManualReadTask.m_read_task_id == m_read_task_id).delete()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除人工阅卷任务信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除人工阅卷任务信息失败")
    if task_group_info:
        m_read_group_id_list = [i[0] for i in task_group_info]
        new_session.query(ManualReadPaperGroup).filter(
            ManualReadPaperGroup.manual_group_id.in_(m_read_group_id_list)).update({ManualReadPaperGroup.lock_state: 1})
    new_session.query(PaperDetail).filter(
        and_(PaperDetail.paper_id == paper_id, PaperDetail.ques_code == ques_code)).update(
        {PaperDetail.manual_read_state: 1})
    new_session.query(ExamStudent).filter(ExamStudent.project_id == project_id).update({ExamStudent.lock_state: 1})
    new_session.query(TaskExecuteRecord).filter(TaskExecuteRecord.record_id == expert_read_record_id).delete()
    new_session.commit()
    logger.info("删除人工阅卷任务信息成功")
    return BaseResponse(msg="删除人工阅卷任务信息成功")


@manual_task_router.post(path="/launch_manual_task", response_model=BaseResponse, summary="发起人工阅卷任务")
async def execute_manual_task(query: ManualReadTaskIdListReq, user: Any = Depends(get_current_user),
                              new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 发起人工阅卷任务")
    m_read_task_id_list = query.m_read_task_id_list

    # 获取该任务的相关数据
    task_list_info = new_session.query(ManualReadTask.m_read_task_id, ManualReadTask.m_read_task_name,
                                       ManualReadTask.project_id, ManualReadTask.subject_id, ManualReadTask.paper_id,
                                       ManualReadTask.manual_process_id, ManualReadTask.ques_type_code,
                                       ManualReadTask.ques_order, ManualReadTask.ques_code, ManualReadTask.ques_id,
                                       ManualReadTask.task_state).filter(
        ManualReadTask.m_read_task_id.in_(m_read_task_id_list)).all()

    yet_launch_list = []
    for task_info in task_list_info:
        m_read_task_name, task_state = task_info.m_read_task_name, task_info.task_state
        if task_state != 1:
            yet_launch_list.append(m_read_task_name)

    if yet_launch_list:
        return BaseResponse(code=response_utils.operation_repeat, msg=f"{'，'.join(yet_launch_list)} 任务已发起，请勿重复发起")

    for task_info in task_list_info:
        m_read_task_id, m_read_task_name, project_id, subject_id, paper_id, process_id, ques_type_code, ques_order, ques_code, ques_id, task_state = task_info

        parent_ques_info = new_session.query(ExamQuestion.parent_ques_id) \
            .filter(and_(ExamQuestion.ques_code == ques_code, ExamQuestion.ques_type_code != "F")).first()
        parent_ques_id = parent_ques_info[0] if parent_ques_info else None

        manual_group_info = new_session.query(ManualReadTaskGroup.manual_group_id).filter(
            ManualReadTaskGroup.m_read_task_id == m_read_task_id).all()
        group_id_list = [i[0] for i in manual_group_info] if manual_group_info else []

        try:
            if ques_type_code != "F":
                result, msg = launch_manual_task_main(new_session, m_read_task_id, project_id, subject_id, paper_id,
                                                      ques_code, ques_type_code, group_id_list, ques_id, parent_ques_id,
                                                      True)
            else:
                result, msg = True, None
                small_ques_id_info = new_session.query(ExamQuestion.ques_id, ExamQuestion.ques_type_code).filter(
                    ExamQuestion.parent_ques_id == ques_id).all()
                if not small_ques_id_info:
                    return BaseResponse(code=response_utils.params_error, msg=f"{m_read_task_name} 任务的试题无小题，请检查试题信息是否缺失")
                for small_ques_id, small_ques_type_code in small_ques_id_info:
                    small_result, msg = launch_manual_task_main(new_session, m_read_task_id, project_id, subject_id,
                                                                paper_id, ques_code, small_ques_type_code, group_id_list,
                                                                small_ques_id, ques_id, True)
                    if not small_result:
                        result = False
                        break

            if result:
                # 开启线程获取该任务的阅卷人员对应的阅卷数据
                condition = ManualReadTask.m_read_task_id == m_read_task_id
                threading.Thread(target=set_manual_mark_info, args=(condition,)).start()

                # 将任务标记为已发起并记录发起时间
                new_session.query(ManualReadTask).filter(ManualReadTask.m_read_task_id == m_read_task_id).update({
                    ManualReadTask.task_state: 2,
                    ManualReadTask.lock_state: 2,
                    ManualReadTask.launch_time: format_now_time()
                })
                new_session.query(ExamStudent).filter(ExamStudent.project_id == project_id).update(
                    {ExamStudent.lock_state: 2})
                new_session.commit()
                set_single_task_state(m_read_task_id, 2)
                logger.info(f"{m_read_task_name} 人工阅卷任务发起成功")
            else:
                logger.error(msg)
                new_session.rollback()
                return BaseResponse(code=response_utils.server_error, msg=f"{m_read_task_name} 人工阅卷任务发起失败，{msg}")
        except Exception as e:
            logger.error(traceback.format_exc())
            new_session.rollback()
            # 还原未发起阅卷时的状态
            new_session.query(ManualDistributeAnswer).filter(
                ManualDistributeAnswer.m_read_task_id == m_read_task_id).delete()
            ques_id_info = new_session.query(ExamQuestion.ques_id).filter(ExamQuestion.ques_code == ques_code).all()
            ques_id_list = [i[0] for i in ques_id_info]
            if paper_id:
                condition = StuAnswer.paper_id == paper_id
            else:
                condition = and_(StuAnswer.project_id == project_id, StuAnswer.subject_id == subject_id)
            new_session.query(StuAnswer).filter(
                and_(condition, StuAnswer.ques_id.in_(ques_id_list))).update({
                StuAnswer.manual_distri_state: 1
            })
            new_session.commit()
            logger.error(f"{m_read_task_name} 人工阅卷任务发起失败，{e}")
            logger.error(traceback.print_exc())
            return BaseResponse(code=response_utils.server_error, msg=f"{m_read_task_name} 人工阅卷任务发起失败")
    return BaseResponse(msg="人工阅卷任务发起成功")


@manual_task_router.post(path="/check_manual_task_finished", response_model=BaseResponse,
                         summary="检查人工阅卷任务是否已完成")
async def check_manual_task_finished(query: ManualReadTaskIdReq, user: Any = Depends(get_current_user),
                                     new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 检查人工阅卷任务是否已完成")
    m_read_task_id = query.m_read_task_id
    curr_user_id = user["user_id"]

    # 获取该任务的相关数据
    task_info = new_session.query(ManualReadTask.c_user_id, ManualReadTask.task_state).filter(
        ManualReadTask.m_read_task_id == m_read_task_id).first()

    c_user_id, task_state = task_info

    # if curr_user_id != c_user_id:
    #     return BaseResponse(code=response_utils.permission_deny, msg="你不是该任务的创建人，无法结束该任务")

    if task_state == 5:
        return BaseResponse(code=response_utils.operation_repeat, msg="该任务已结束")
    elif task_state == 1:
        return BaseResponse(msg="该任务暂未发起，确定结束该任务吗？")

    try:
        not_finished_item = new_session.query(ManualDistributeAnswer.distri_answer_id) \
            .filter(and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id,
                         ManualDistributeAnswer.final_mark_score.is_(None))).first()
        if not_finished_item:
            return BaseResponse(msg="该任务阅卷未完成，确定结束该任务吗？")
        return BaseResponse(msg="确定结束该任务吗？")
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.error(f"检查人工阅卷任务是否已完成失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg="检查人工阅卷任务是否已完成失败")


@manual_task_router.post(path="/terminate_manual_task", response_model=BaseResponse, summary="结束人工阅卷任务")
async def terminate_manual_task(query: ManualReadTaskIdReq, user: Any = Depends(get_current_user),
                                new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 结束人工阅卷任务")
    m_read_task_id = query.m_read_task_id

    try:
        is_can_terminate = new_session.query(ManualDistributeAnswer).filter(
            and_(ManualDistributeAnswer.m_read_task_id == m_read_task_id,
                 ManualDistributeAnswer.final_mark_score.is_(None))).first()
        if is_can_terminate:
            return BaseResponse(code=response_utils.no_field, msg="该任务未完成，无法结束任务")

        new_session.query(ManualReadTask).filter(ManualReadTask.m_read_task_id == m_read_task_id).update({
            ManualReadTask.lock_state: 2,
            ManualReadTask.task_state: 5
        })
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        traceback.print_exc()
        logger.error(f"结束人工阅卷任务失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg="结束人工阅卷任务失败")
    set_single_task_state(m_read_task_id, 5)
    logger.info("结束人工阅卷任务成功")
    return BaseResponse(msg="结束人工阅卷任务成功")
