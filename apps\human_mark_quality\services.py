from sqlalchemy import func

from apps.human_task_manage.models import HumanRoundDistriAnswer, HumanPersonDistriAnswer
from settings import configs


def create_back_again_mark_data(new_session, round_id, stu_secret_num, ques_code, ques_id, answer_id, group_id, reviewer_id, again_mark_count):
    round_distri_id = configs.snow_worker.get_id()
    new_round_distri = HumanRoundDistriAnswer(distri_id=round_distri_id, round_id=round_id, stu_secret_num=stu_secret_num, ques_code=ques_code,
                                              ques_id=ques_id, answer_id=answer_id, is_distri=1, quality_state=0, is_again_mark=again_mark_count)

    new_reviewer_distri = HumanPersonDistriAnswer(person_distri_id=configs.snow_worker.get_id(), round_id=round_id, round_distri_id=round_distri_id,
                                                  group_id=group_id, ques_code=ques_code, ques_id=ques_id, user_id=reviewer_id, stu_secret_num=stu_secret_num,
                                                  answer_id=answer_id, is_again_mark=again_mark_count)
    new_session.add_all([new_round_distri, new_reviewer_distri])
    return True
