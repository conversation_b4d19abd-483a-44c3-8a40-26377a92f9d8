from typing import Optional, Literal
from pydantic import BaseModel, Field
from apps.base.schemas import PaginationModel


class CreateRoleReq(BaseModel):
    role_name: str = Field(..., description="角色名称")
    role_desc: Optional[str] = Field(None, description="角色描述")


class GetRoleReq(PaginationModel):
    system_user_type: Literal[0, 1, 2] = Field(0, description="0 表示所有角色，1 表示系统角色，2 表示业务角色")
    role_name: Optional[str] = Field(None, description="角色名称")


class UpdateRoleReq(BaseModel):
    role_id: str = Field(..., description="角色id")
    role_name: str = Field(..., description="角色名称")
    role_desc: Optional[str] = Field(None, description="角色描述")


class DeleteRoleReq(BaseModel):
    role_id: str = Field(..., description="角色id")


class GetRoleModuleReq(BaseModel):
    role_id_list: Optional[list] = Field(..., description="角色id列表")


class GetModuleReq(BaseModel):
    user_id: str = Field(..., description="用户id")
    login_role_id_list: Optional[list] = Field(None, description="登录的角色id")


class UpdateRoleModuleReq(BaseModel):
    role_id: str = Field(..., description="角色id")
    module_flag: list = Field(..., description="模块唯一标识列表")


class UpdateUserModuleReq(BaseModel):
    user_id: str = Field(..., description="用户id")
    module_flag: list = Field(..., description="模块唯一标识列表")


class UpdateUseDataReq(BaseModel):
    user_id: str = Field(..., description="用户id")
    path_list: list[list] = Field(..., description="路径列表")
    # subject_flag: list = Field(..., description="科目唯一标识列表")

class DataPermission(BaseModel):
    project_id: str = Field(..., description="项目id")
    subject_id: str = Field(..., description="科目id")
    paper_id: str = Field(..., description="试卷id")    
    ques_type_id: str = Field(..., description="题型id")    
    ques_id: str = Field(..., description="试题id")    

class AddDataPermission(BaseModel):
    user_id: str = Field(..., description="用户id")
    permissions: list[DataPermission] = Field(..., description="权限列表")



    