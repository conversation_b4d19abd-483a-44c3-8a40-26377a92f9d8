import threading
import traceback

from fastapi import APIRouter, Depends

from apps.grade_manage.models import HumanStudentSubjectGrade, HumanStudentSubjectGradeDetail
from apps.permission.services import get_user_selected_data_flag
from apps.base.human_global_cache import get_round_group_member
from apps.human_mark_group.models import HumanMarkGroup
from apps.human_official_mark.services import get_official_task_process
from apps.human_task_manage.services import filter_human_read_task, get_human_mark_round_info, distribute_human_task_data, save_round_distri_answer, check_has_mark_point, \
    check_round_group_member, exist_task_info, get_round_count
from apps.load_global_data import load_task_answer
from apps.models.models import WorkFlowMainProcess, ExamPaper, Project, Subject, UserInfo, BusinessQuesType, ExamQuestion, WorkFlowMainProcessInstance
from settings import logger
from sqlalchemy import func, select, and_
from sqlalchemy.orm import Session
from typing import Any

from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.human_task_manage.schemas import CreateHumanMarkTasksReq, CheckHumanTasksExistReq, GetHumanReadTaskReq, BaseHumanReadRoundReq, \
    UpdateHumanReadRoundReq, GetRoundReq, GetTaskListReq, GetGroupListReq, LaunchTaskRoundReq, GetRoundByTaskReq, GetStepByQuesCodeReq
from apps.human_task_manage.models import HumanReadTask, HumanReadRoundGroup, HumanReadTaskRound, HumanRoundDistriAnswer, HumanPersonDistriAnswer, HumanArbitrateDistriAnswer, \
    HumanQualityDistriAnswer
from helper import response_utils
from factory_apps import session_depend
from settings import configs

human_task_router = APIRouter()


@human_task_router.post(path="/check_human_task_exist", response_model=BaseResponse, summary="检查人工阅卷任务是否已经创建")
async def check_human_task_exist(query: CheckHumanTasksExistReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    检查人工阅卷任务是否已经创建，已存在会返回试题对应的任务名和小组信息
    """
    logger.info(f"{user['username']} 检查人工阅卷任务是否已经创建")
    task_type, ques_code_list = query.model_dump().values()

    task_info = new_session.query(HumanReadTask.ques_code, HumanReadTask.task_name, func.group_concat(HumanMarkGroup.group_id),
                                  func.group_concat(HumanMarkGroup.group_name)) \
        .join(HumanReadRoundGroup, HumanReadRoundGroup.task_id == HumanReadTask.task_id) \
        .join(HumanMarkGroup, HumanMarkGroup.group_id == HumanReadRoundGroup.group_id) \
        .filter(HumanReadTask.ques_code.in_(ques_code_list), HumanReadTask.task_type == task_type) \
        .group_by(HumanReadTask.ques_code, HumanReadTask.task_name).all()

    if not task_info:
        return BaseResponse(data={"data": {}})

    exists_ques_task_dict = {}
    for ques_code, task_name, group_id_str, group_name_str in task_info:
        group_list = []
        group_id_list, group_name_list = group_id_str.split(","), group_name_str.split(",")
        for group_id, group_name in zip(group_id_list, group_name_list):
            group_item = {
                "group_id": group_id,
                "group_name": group_name
            }
            group_list.append(group_item)
        exists_ques_task_dict[ques_code] = {
            "task_name": task_name,
            "group_list": group_list
        }

    data = {"data": exists_ques_task_dict}
    if exists_ques_task_dict:
        return BaseResponse(data=data)
    return BaseResponse(data=data)


@human_task_router.post(path="/create_human_mark_task", response_model=BaseResponse, summary="创建人工阅卷任务")
async def create_mark_task(query: CreateHumanMarkTasksReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建人工阅卷任务")
    curr_user_id = user.get("user_id")
    task_type, human_task_list = query.task_type, query.human_task_list

    ques_code_list = [task.ques_code for task in human_task_list]
    all_ques_code_list, task_type_dict, ques_task_dict = exist_task_info(new_session, ques_code_list)

    try:
        round_id_list = []

        for task in human_task_list:
            (task_name, project_id, subject_id, process_id, paper_id, ques_type_code, business_type_name, ques_order, ques_id, ques_code,
             business_id, fetch_score_way, fetch_score_scope, fetch_score_option, arbitrate_threshold_type, arbitrate_threshold,
             arbitrate_score_diff, deviation_threshold_type, arbitrate_deviation, mark_score_step,
             group_id_list) = task.model_dump().values()

            round_id = configs.snow_worker.get_id()

            # 任务不存在则创建任务和第一轮次，任务存在则只创建轮次
            if ques_code not in task_type_dict.get(task_type, []):
                # 创建人工阅卷任务
                task_id = configs.snow_worker.get_id()

                round_count = 1

                # 创建人工阅卷任务
                new_task = HumanReadTask(task_id=task_id, task_name=task_name, task_type=task_type, project_id=project_id, subject_id=subject_id,
                                         paper_id=paper_id, ques_type_code=ques_type_code, business_type_name=business_type_name,
                                         ques_order=ques_order, ques_id=ques_id, ques_code=ques_code, business_id=business_id,
                                         c_user_id=curr_user_id)
                new_session.add(new_task)
            else:
                # 计算新增轮次是第几轮
                task_id = ques_task_dict[task_type][ques_code]
                old_round_count = new_session.query(func.max(HumanReadTaskRound.round_count)).filter(HumanReadTaskRound.task_id == task_id).scalar()
                round_count = old_round_count + 1

            # 创建轮次
            new_round = HumanReadTaskRound(round_id=round_id, task_id=task_id, round_count=round_count, process_id=process_id, fetch_score_way=fetch_score_way,
                                           fetch_score_scope=fetch_score_scope, fetch_score_option=fetch_score_option,
                                           arbitrate_threshold_type=arbitrate_threshold_type, arbitrate_threshold=arbitrate_threshold,
                                           arbitrate_score_diff=arbitrate_score_diff, deviation_threshold_type=deviation_threshold_type,
                                           arbitrate_deviation=arbitrate_deviation, mark_score_step=mark_score_step,
                                           round_state=1, c_user_id=curr_user_id)
            new_session.add(new_round)

            round_id_list.append(round_id)

            # 创建轮次小组关联信息
            for group_id in group_id_list:
                new_task_group = HumanReadRoundGroup(round_group_id=configs.snow_worker.get_id(), round_id=round_id, task_id=task_id, group_id=group_id)
                new_session.add(new_task_group)

        new_session.commit()
        threading.Thread(target=load_task_answer, args=(all_ques_code_list,)).start()
        for round_id in round_id_list:
            get_round_group_member(new_session, round_id, True)
        logger.info(f"创建人工阅卷任务成功，共创建 {len(human_task_list)} 个任务")
        return BaseResponse(msg=f"创建人工阅卷任务成功，共创建 {len(human_task_list)} 个任务")
    except Exception as e:
        logger.error(traceback.format_exc())
        logger.error(f"创建人工阅卷任务失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="创建人工阅卷任务失败")


@human_task_router.post(path="/get_mark_task_list", response_model=BaseResponse, summary="获取人工阅卷任务列表")
async def get_mark_task_list(query: GetHumanReadTaskReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取人工阅卷任务列表")
    current_page, page_size, task_type, project_id, subject_id, paper_id, task_name, business_type_name, ques_order, ques_code, round_state_list, round_count, task_id_list, round_id_list = query.model_dump().values()
    round_stmt = select(HumanReadTask.task_id, HumanReadTask.task_name, HumanReadTask.project_id, HumanReadTask.subject_id, HumanReadTask.paper_id,
                        HumanReadTask.task_type, HumanReadTask.business_type_name, HumanReadTask.ques_id, HumanReadTask.ques_code, HumanReadTaskRound.round_id,
                        HumanReadTaskRound.round_count, HumanReadTaskRound.process_id, HumanReadTaskRound.round_state, HumanReadTaskRound.fetch_score_way,
                        HumanReadTaskRound.fetch_score_scope, HumanReadTaskRound.fetch_score_option, HumanReadTaskRound.arbitrate_threshold_type,
                        HumanReadTaskRound.arbitrate_threshold, HumanReadTaskRound.arbitrate_score_diff, HumanReadTaskRound.deviation_threshold_type,
                        HumanReadTaskRound.arbitrate_deviation, HumanReadTaskRound.mark_score_step, HumanReadTaskRound.created_time, HumanReadTaskRound.updated_time,
                        HumanReadTaskRound.try_mark_ques_num, HumanReadTaskRound.allow_diff_score,
                        UserInfo.name, WorkFlowMainProcess.process_name, Project.project_name, Subject.subject_name, ExamPaper.paper_name,
                        BusinessQuesType.ques_type_score, ExamQuestion.ques_type_code) \
        .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .outerjoin(WorkFlowMainProcess, WorkFlowMainProcess.process_id == HumanReadTaskRound.process_id) \
        .outerjoin(ExamPaper, ExamPaper.paper_id == HumanReadTask.paper_id) \
        .join(Project, Project.project_id == HumanReadTask.project_id) \
        .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
        .join(ExamQuestion, ExamQuestion.ques_id == HumanReadTask.ques_id) \
        .join(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
        .join(UserInfo, UserInfo.user_id == HumanReadTaskRound.c_user_id)

    if page_size == -1:
        condition = and_(HumanReadTask.task_id.in_(task_id_list), HumanReadTaskRound.round_id.in_(round_id_list))
        round_stmt = round_stmt.filter(condition)
        total = len(round_id_list)
    else:
        limit = current_page - 1
        offset = limit * page_size

        condition = filter_human_read_task(task_type, project_id, subject_id, paper_id, task_name, business_type_name, ques_order, ques_code, round_state_list, round_count)

        total = new_session.query(HumanReadTask.task_id) \
            .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
            .outerjoin(ExamPaper, ExamPaper.paper_id == HumanReadTask.paper_id) \
            .join(Project, Project.project_id == HumanReadTask.project_id) \
            .join(Subject, Subject.subject_id == HumanReadTask.subject_id) \
            .filter(condition).count()

        round_stmt = round_stmt.filter(condition).order_by(HumanReadTask.created_time.desc(), HumanReadTaskRound.round_id).limit(page_size).offset(offset)

    result = new_session.execute(round_stmt).all()
    round_list = [dict(row._mapping) for row in result]

    round_id_list = []
    round_count_list = []
    process_id_list = []
    for item in round_list:
        item["created_time"] = item["created_time"] and str(item["created_time"]).replace("T", " ")
        item["updated_time"] = item["updated_time"] and str(item["updated_time"]).replace("T", " ")
        round_id_list.append(item["round_id"])
        round_count_list.append(item["round_count"])
        process_id_list.append(item["process_id"])

    # 所有任务的最大轮次
    max_round_count = max(round_count_list) if round_count_list else 0

    # 模式里的几评，一个作答需要几个专家评分
    reviewer_num_info = new_session.query(WorkFlowMainProcessInstance.parent_process_id, WorkFlowMainProcessInstance.instance_people_num).filter(and_(
        WorkFlowMainProcessInstance.parent_process_id.in_(process_id_list), WorkFlowMainProcessInstance.instance_ele_type == "bpmn:userTask")).all()

    reviewer_num_dict = {i.parent_process_id: i.instance_people_num for i in reviewer_num_info}

    # 进度
    process_data = get_official_task_process(new_session, round_id_list)

    group_info = new_session.query(HumanReadRoundGroup.round_id, func.group_concat(HumanMarkGroup.group_id), func.group_concat(HumanMarkGroup.group_name)) \
        .join(HumanMarkGroup, HumanMarkGroup.group_id == HumanReadRoundGroup.group_id) \
        .filter(HumanReadRoundGroup.round_id.in_(round_id_list)) \
        .group_by(HumanReadRoundGroup.round_id)
    group_data = {}
    if group_info:
        for round_id, group_id_str, group_name_str in group_info:
            group_data[round_id] = {
                "group_id_list": group_id_str.split(","),
                "group_name_list": group_name_str.split(",")
            }
    for round_item in round_list:
        round_id = round_item["round_id"]
        round_item["group_id_list"] = group_data[round_id]["group_id_list"]
        round_item["group_name_list"] = group_data[round_id]["group_name_list"]
        process_id = round_item["process_id"]
        round_item["reviewer_num"] = reviewer_num_dict.get(process_id)

        round_item["total_count"] = process_data.get(round_id, {}).get("total_count")
        round_item["marked_count"] = process_data.get(round_id, {}).get("marked_count")
        round_item["process"] = process_data.get(round_id, {}).get("process")

    data = {
        "data": round_list,
        "total": total,
        "round_count_list": [i for i in range(1, max_round_count + 1)] if max_round_count else []
    }

    return BaseResponse(data=data, msg="获取人工阅卷任务列表成功")


@human_task_router.post(path="/delete_human_mark_round", response_model=BaseResponse, summary="删除人工阅卷任务")
async def delete_human_mark_round(query: BaseHumanReadRoundReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 删除人工阅卷任务")
    task_id, round_id = query.model_dump().values()

    round_state = new_session.query(HumanReadTaskRound.round_state).filter(HumanReadTaskRound.round_id == round_id).scalar()
    if round_state != 1:
        return BaseResponse(code=response_utils.permission_deny, msg="该任务轮次已被使用，不允许删除")

    round_count = new_session.query(HumanReadTaskRound).filter(HumanReadTaskRound.task_id == task_id).count()

    try:
        if round_count == 1:
            # 如果任务下只有一个轮次，删除该轮次同时也把该任务删掉
            new_session.query(HumanReadTask).filter(HumanReadTask.task_id == task_id).delete()
        new_session.query(HumanReadTaskRound).filter(HumanReadTaskRound.round_id == round_id).delete()
        new_session.query(HumanReadRoundGroup).filter(HumanReadRoundGroup.round_id == round_id).delete()
        new_session.query(HumanRoundDistriAnswer).filter(HumanRoundDistriAnswer.round_id == round_id).delete()
        new_session.query(HumanPersonDistriAnswer).filter(HumanPersonDistriAnswer.round_id == round_id).delete()
        new_session.query(HumanArbitrateDistriAnswer).filter(HumanArbitrateDistriAnswer.round_id == round_id).delete()
        new_session.query(HumanQualityDistriAnswer).filter(HumanQualityDistriAnswer.round_id == round_id).delete()
        grade_info = new_session.query(HumanStudentSubjectGradeDetail.student_subject_grade_id.distinct()).filter(HumanStudentSubjectGradeDetail.round_id == round_id).all()
        if grade_info:
            grade_id_list = [i[0] for i in grade_info]
            new_session.query(HumanStudentSubjectGrade).filter(HumanStudentSubjectGrade.student_subject_grade_id.in_(grade_id_list)).delete()
            new_session.query(HumanStudentSubjectGradeDetail).filter(HumanStudentSubjectGradeDetail.round_id == round_id).delete()
        new_session.commit()
    except:
        logger.error(traceback.format_exc())
        new_session.rollback()
        new_session.close()
        return BaseResponse(code=response_utils.server_error, msg="删除人工阅卷任务失败")
    new_session.close()
    return BaseResponse(msg="删除人工阅卷任务成功")


@human_task_router.post(path="/pause_human_mark_round", response_model=BaseResponse, summary="暂停人工阅卷任务")
async def pause_human_mark_round(query: BaseHumanReadRoundReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 暂停人工阅卷任务")
    task_id, round_id = query.model_dump().values()
    round_state = new_session.query(HumanReadTaskRound.round_state).filter(HumanReadTaskRound.round_id == round_id).scalar()
    if round_state != 2:
        return BaseResponse(code=response_utils.permission_deny, msg="该任务轮次当前状态不允许暂停")

    if round_state == 4:
        return BaseResponse(code=response_utils.operation_repeat, msg="该任务轮次已暂停，请勿重复操作")

    new_session.query(HumanReadTaskRound).filter(HumanReadTaskRound.round_id == round_id).update({
        HumanReadTaskRound.round_state: 4
    })
    new_session.commit()
    return BaseResponse(msg="暂停人工阅卷任务成功")


@human_task_router.post(path="/continue_human_mark_round", response_model=BaseResponse, summary="继续人工阅卷任务")
async def continue_human_mark_round(query: BaseHumanReadRoundReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 继续人工阅卷任务")
    task_id, round_id = query.model_dump().values()
    round_state = new_session.query(HumanReadTaskRound.round_state).filter(HumanReadTaskRound.round_id == round_id).scalar()

    if round_state != 4:
        return BaseResponse(code=response_utils.operation_repeat, msg="该任务轮次非暂停状态，无法继续")

    new_session.query(HumanReadTaskRound).filter(HumanReadTaskRound.round_id == round_id).update({
        HumanReadTaskRound.round_state: 2
    })
    new_session.commit()
    return BaseResponse(msg="继续人工阅卷任务")


@human_task_router.post(path="/finish_human_mark_round", response_model=BaseResponse, summary="结束人工阅卷任务")
async def finish_human_mark_round(query: BaseHumanReadRoundReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 结束人工阅卷任务")
    task_id, round_id = query.model_dump().values()
    round_state = new_session.query(HumanReadTaskRound.round_state).filter(HumanReadTaskRound.round_id == round_id).scalar()
    if round_state == 1:
        return BaseResponse(code=response_utils.permission_deny, msg="该任务轮次未发起，无法结束")

    if round_state == 5:
        return BaseResponse(code=response_utils.operation_repeat, msg="该任务轮次已结束，请勿重复操作")

    new_session.query(HumanReadTaskRound).filter(HumanReadTaskRound.round_id == round_id).update({
        HumanReadTaskRound.round_state: 5
    })
    new_session.commit()
    return BaseResponse(msg="结束人工阅卷任务")


@human_task_router.post(path="/update_human_mark_round", response_model=BaseResponse, summary="编辑人工阅卷任务")
async def update_human_mark_round(query: UpdateHumanReadRoundReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 编辑人工阅卷任务")
    curr_user_id = user.get("user_id")

    (task_name, project_id, subject_id, process_id, paper_id, ques_type_code, business_type_name, ques_order, ques_id, ques_code,
     business_id, fetch_score_way, fetch_score_scope, fetch_score_option, arbitrate_threshold_type, arbitrate_threshold,
     arbitrate_score_diff, deviation_threshold_type, arbitrate_deviation, mark_score_step, group_id_list, task_id, round_id) = query.model_dump().values()

    round_info = new_session.query(HumanReadTaskRound.round_id, HumanReadTaskRound.round_state, func.group_concat(HumanReadRoundGroup.group_id)) \
        .join(HumanReadRoundGroup, HumanReadRoundGroup.round_id == HumanReadTaskRound.round_id) \
        .filter(HumanReadTaskRound.round_id == round_id) \
        .group_by(HumanReadTaskRound.round_id, HumanReadTaskRound.round_state).all()

    round_id, round_state, raw_group_id_list = round_info[0][0], round_info[0][1], round_info[0][2].split(",")

    if round_state != 1:
        return BaseResponse(code=response_utils.permission_deny, msg="该任务轮次已被使用，不允许编辑")

    # 更新任务
    new_session.query(HumanReadTask).filter(HumanReadTask.task_id == task_id).update({
        HumanReadTask.task_name: task_name,
        HumanReadTask.project_id: project_id,
        HumanReadTask.subject_id: subject_id,
        HumanReadTask.paper_id: paper_id,
        HumanReadTask.ques_type_code: ques_type_code,
        HumanReadTask.business_type_name: business_type_name,
        HumanReadTask.ques_order: ques_order,
        HumanReadTask.ques_id: ques_id,
        HumanReadTask.ques_code: ques_code,
        HumanReadTask.business_id: business_id,
        HumanReadTask.u_user_id: curr_user_id
    })

    new_session.query(HumanReadTaskRound).filter(HumanReadTaskRound.round_id == round_id).update({
        HumanReadTaskRound.process_id: process_id,
        HumanReadTaskRound.fetch_score_way: fetch_score_way,
        HumanReadTaskRound.fetch_score_scope: fetch_score_scope,
        HumanReadTaskRound.fetch_score_option: fetch_score_option,
        HumanReadTaskRound.arbitrate_threshold_type: arbitrate_threshold_type,
        HumanReadTaskRound.arbitrate_threshold: arbitrate_threshold,
        HumanReadTaskRound.arbitrate_score_diff: arbitrate_score_diff,
        HumanReadTaskRound.deviation_threshold_type: deviation_threshold_type,
        HumanReadTaskRound.arbitrate_deviation: arbitrate_deviation,
        HumanReadTaskRound.mark_score_step: mark_score_step,
        HumanReadTaskRound.u_user_id: curr_user_id
    })

    if set(group_id_list) != set(raw_group_id_list):
        # 找出新增的组
        add_groups = [group_id for group_id in group_id_list if group_id not in raw_group_id_list]

        # 找出删除的组
        remove_groups = [group_id for group_id in raw_group_id_list if group_id not in group_id_list]

        if add_groups:
            for group_id in add_groups:
                new_group = HumanReadRoundGroup(round_group_id=configs.snow_worker.get_id(), round_id=round_id, task_id=task_id, group_id=group_id)
                new_session.add(new_group)

        if remove_groups:
            new_session.query(HumanReadRoundGroup).filter(HumanReadRoundGroup.group_id.in_(remove_groups)).delete()

    new_session.commit()
    return BaseResponse(msg="编辑人工阅卷任务成功")


@human_task_router.post(path="/launch_human_mark_task", response_model=BaseResponse, summary="发起人工阅卷任务")
async def launch_human_mark_task(query: LaunchTaskRoundReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 发起人工阅卷任务")
    task_type, launch_list = query.task_type, query.launch_list

    task_id_list = [i.task_id for i in launch_list]
    round_id_list = [i.round_id for i in launch_list]

    # 调用获取任务轮次信息接口
    result, msg = get_human_mark_round_info(task_type, task_id_list, round_id_list)
    if not result:
        return BaseResponse(code=response_utils.server_error, msg="获取任务信息失败")

    round_data = result["data"]

    # 包含简答题需判断试题是否添加了评分点，没有添加不允许发起任务
    # result, msg = check_has_mark_point(new_session, round_data)
    # if not result:
    #     return BaseResponse(code=response_utils.params_error, msg=msg)

    is_check = True if task_type == 1 else False
    result, round_group_dict, msg = check_round_group_member(new_session, round_data, is_check)

    if not result:
        return BaseResponse(code=response_utils.params_error, msg=msg)

    # 获取并保存评分数据
    fail_task_name_list = save_round_distri_answer(new_session, task_type, round_data)

    if fail_task_name_list:
        return BaseResponse(code=response_utils.server_error, msg=f"{'，'.join(fail_task_name_list)} 获取并保存评分数据失败")

    result = distribute_human_task_data(round_data, round_group_dict, True)

    if not result:
        return BaseResponse(code=response_utils.server_error, msg="发起人工阅卷任务失败")

    new_session.query(HumanReadTaskRound).filter(HumanReadTaskRound.round_id.in_(round_id_list)).update({HumanReadTaskRound.round_state: 2})
    new_session.commit()

    return BaseResponse(msg="发起人工阅卷任务成功")


@human_task_router.post(path="/get_round", response_model=BaseResponse, summary="获取轮次列表")
async def get_round(query: GetRoundReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取轮次列表")
    # 解析分页及过滤参数
    current_page, page_size, project_id, subject_id = query.model_dump().values()
    if page_size != -1:
        limit = current_page - 1
        offset = limit * page_size
    else:
        offset = 0

    # 构造过滤条件
    project_cond = HumanReadTask.project_id == project_id if project_id else True
    subject_cond = HumanReadTask.subject_id == subject_id if subject_id else True
    condition = and_(project_cond, subject_cond)

    # 统计总数
    total = new_session.query(func.count(func.distinct(HumanReadTaskRound.round_count))) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .filter(condition).scalar()

    # 查询轮次信息
    if page_size == -1:
        round_stmt = select(
            func.max(HumanReadTaskRound.round_id).label("round_id"),
            HumanReadTaskRound.round_count,
            func.max(HumanReadTaskRound.process_id).label("process_id"),
            func.max(HumanReadTaskRound.round_state).label("round_state"),
            func.max(HumanReadTaskRound.created_time).label("created_time"),
            func.max(HumanReadTaskRound.updated_time).label("updated_time"),
            func.max(HumanReadTask.task_id).label("task_id"),
            func.max(HumanReadTask.task_name).label("task_name")
        ).join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
            .filter(condition) \
            .group_by(HumanReadTaskRound.round_count) \
            .order_by(func.max(HumanReadTask.created_time).desc(), HumanReadTaskRound.round_count)
    else:
        round_stmt = select(
            func.max(HumanReadTaskRound.round_id).label("round_id"),
            HumanReadTaskRound.round_count,
            func.max(HumanReadTaskRound.process_id).label("process_id"),
            func.max(HumanReadTaskRound.round_state).label("round_state"),
            func.max(HumanReadTaskRound.created_time).label("created_time"),
            func.max(HumanReadTaskRound.updated_time).label("updated_time"),
            func.max(HumanReadTask.task_id).label("task_id"),
            func.max(HumanReadTask.task_name).label("task_name")
        ).join(HumanReadTask, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
            .filter(condition) \
            .group_by(HumanReadTaskRound.round_count) \
            .order_by(func.max(HumanReadTask.created_time).desc(), HumanReadTaskRound.round_count)\
            .limit(page_size).offset(offset)

    result = new_session.execute(round_stmt)
    round_list = []
    for row in result:
        round_item = {
            "round_id": row.round_id,
            "round_count": row.round_count,
            "process_id": row.process_id,
            "round_state": row.round_state,
            "created_time": row.created_time and str(row.created_time).replace("T", " "),
            "updated_time": row.updated_time and str(row.updated_time).replace("T", " "),
            "task_id": row.task_id,
            "task_name": row.task_name
        }
        round_list.append(round_item)

    data = {"data": round_list, "total": total}
    return BaseResponse(msg="获取轮次列表成功", data=data)


@human_task_router.post(path="/get_task_list", response_model=BaseResponse, summary="获取题组列表")
async def get_task_list(query: GetTaskListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    获取（任务）列表，支持分页和按项目、科目、轮次过滤
    """
    logger.info(f"{user['username']} 获取任务列表")
    # 解析分页及过滤参数
    current_page, page_size, project_id, subject_id, round_count = query.model_dump().values()
    if page_size != -1:
        limit = current_page - 1
        offset = limit * page_size
    else:
        offset = 0

    # 构造过滤条件
    project_cond = HumanReadTask.project_id == project_id if project_id else True
    subject_cond = HumanReadTask.subject_id == subject_id if subject_id else True
    round_cond = HumanReadTaskRound.round_count == round_count if round_count else True
    condition = and_(project_cond, subject_cond, round_cond)

    # 统计总数
    total = new_session.query(HumanReadTask.task_id) \
        .join(HumanReadTaskRound, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .filter(condition).count()

    # 查询任务信息
    stmt = select(
        HumanReadTask.task_id,
        HumanReadTask.task_name,
        HumanReadTask.project_id,
        HumanReadTask.subject_id,
        HumanReadTaskRound.round_id,
        HumanReadTaskRound.round_count,
        HumanReadTaskRound.process_id,
        HumanReadTaskRound.round_state,
        HumanReadTaskRound.created_time,
        HumanReadTaskRound.updated_time
    ).join(HumanReadTaskRound, HumanReadTask.task_id == HumanReadTaskRound.task_id) \
        .filter(condition) \
        .order_by(HumanReadTask.created_time.desc(), HumanReadTaskRound.round_id)

    if page_size != -1:
        stmt = stmt.limit(page_size).offset(offset)

    result = new_session.execute(stmt)
    task_list = []
    for row in result:
        task_list.append({
            "task_id": row.task_id,
            "task_name": row.task_name,
            "project_id": row.project_id,
            "subject_id": row.subject_id,
            "round_id": row.round_id,
            "round_count": row.round_count,
            "process_id": row.process_id,
            "round_state": row.round_state,
            "created_time": row.created_time and str(row.created_time).replace("T", " "),
            "updated_time": row.updated_time and str(row.updated_time).replace("T", " ")
        })

    data = {"data": task_list, "total": total}
    return BaseResponse(data=data, msg="获取任务列表成功")

@human_task_router.post(path="/get_ques_group_list", response_model=BaseResponse, summary="获取题组/小組列表")
async def get_ques_group_list(query: GetTaskListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    获取题组列表，支持分页和按项目、科目、轮次过滤
    """
    logger.info(f"{user['username']} 获取题组、小組列表")
    # 解析分页及过滤参数
    current_page, page_size, project_id, subject_id, round_count = query.model_dump().values()
    if page_size != -1:
        limit = current_page - 1
        offset = limit * page_size
    else:
        offset = 0

    # 构造过滤条件
    project_cond = HumanMarkGroup.project_id == project_id if project_id else True
    subject_cond = HumanMarkGroup.subject_id == subject_id if subject_id else True
    condition = and_(project_cond, subject_cond)

    # 统计总数
    total = new_session.query(HumanMarkGroup.group_id) .filter(condition).count()

    # 查询题组信息
    stmt = select(
        HumanMarkGroup.group_id,
        HumanMarkGroup.group_name,
        HumanMarkGroup.group_level,
        HumanMarkGroup.parent_group_id
    ).filter(condition) \
        .order_by(HumanMarkGroup.group_id)

    if page_size != -1:
        stmt = stmt.limit(page_size).offset(offset)

    result = new_session.execute(stmt)
    ques_group_list = []
    for row in result:
        ques_group_list.append({
            "ques_group_id": row.group_id,
            "name": row.group_name,
            "group_level": row.group_level,
            "parent_group_id": row.parent_group_id
        })

    data = {"data": ques_group_list, "total": total}
    return BaseResponse(data=data, msg="获取题组、小組列表成功")

@human_task_router.post(path="/get_group_list", response_model=BaseResponse, summary="获取小组列表")
async def get_group_list(query: GetGroupListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    获取小组（阅卷小组）列表，支持分页和按任务、项目、科目、轮次过滤
    """
    logger.info(f"{user['username']} 获取小组列表")
    # 解析分页及过滤参数
    current_page, page_size, task_id, project_id, subject_id, round_id = query.model_dump().values()

    # 构造过滤条件
    task_cond = HumanReadTask.task_id == task_id
    project_cond = HumanReadTask.project_id == project_id if project_id else True
    subject_cond = HumanReadTask.subject_id == subject_id if subject_id else True
    round_cond = HumanReadRoundGroup.round_id == round_id if round_id else True
    condition = and_(task_cond, project_cond, subject_cond, round_cond)

    # 查询小组信息
    common_stmt = select(
        HumanReadRoundGroup.round_id,
        HumanMarkGroup.group_id,
        HumanMarkGroup.group_name
    ).join(HumanReadTask, HumanReadTask.task_id == HumanReadRoundGroup.task_id) \
        .join(HumanMarkGroup, HumanMarkGroup.group_id == HumanReadRoundGroup.group_id) \
        .where(condition)

    try:
        if page_size == -1:
            total = new_session.query(HumanReadRoundGroup.round_id) \
                .join(HumanReadTask, HumanReadTask.task_id == HumanReadRoundGroup.task_id) \
                .filter(condition).count()
            stmt = common_stmt
        else:
            limit = current_page - 1
            offset = limit * page_size
            total = new_session.query(HumanReadRoundGroup.round_id) \
                .join(HumanReadTask, HumanReadTask.task_id == HumanReadRoundGroup.task_id) \
                .filter(condition).count()
            stmt = common_stmt.order_by(HumanReadRoundGroup.round_id).limit(page_size).offset(offset)

        result = new_session.execute(stmt)
        group_list = []
        for row in result:
            group_list.append({
                "round_id": row.round_id,
                "group_id": row.group_id,
                "group_name": row.group_name
            })

        data = {"data": group_list, "total": total}
        return BaseResponse(data=data, msg="获取小组列表成功")
    except Exception as e:
        logger.error(traceback.format_exc())
        logger.error(f"获取小组列表失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg="获取小组列表失败")


@human_task_router.post(path="/get_round_by_task", response_model=BaseResponse, summary="根据任务id获取轮次")
async def get_round_by_task(query: GetRoundByTaskReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 根据任务id获取轮次")
    task_id = query.task_id
    round_info = new_session.execute(select(HumanReadTaskRound.round_id, HumanReadTaskRound.round_count).where(HumanReadTaskRound.task_id == task_id)).all()
    round_data = [dict(row._mapping) for row in round_info]
    data = {"data": round_data}
    return BaseResponse(data=data)


@human_task_router.post(path="/get_step_by_ques_code", response_model=BaseResponse, summary="根据试题编号获取任务评分步长")
async def get_step_by_ques_code(query: GetStepByQuesCodeReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 根据试题编号获取任务评分步长")
    ques_code, task_type = query.ques_code, query.task_type
    task_id = new_session.query(HumanReadTask.task_id).filter(and_(HumanReadTask.task_type == task_type, HumanReadTask.ques_code == ques_code)).scalar()
    if task_id:
        mark_step = new_session.query(HumanReadTaskRound.mark_score_step).filter(and_(HumanReadTaskRound.task_id == task_id, HumanReadTaskRound.round_count == 1)).scalar()
        data = {"mark_score_step": mark_step}
        return BaseResponse(data=data, msg="根据试题编号获取任务评分步长成功")
    return BaseResponse(code=response_utils.no_field, msg="该试题的任务评分步长不存在")
