from sqlalchemy import and_

from apps.models.models import ManualGroupUser, ManualReadTaskGroup, ManualReadTask


def get_curr_user_task_list(new_session, curr_user_id, role_id, only_id=True, show_not_launch=True):
    """
    获取当前用户拥有的任务
    """
    group_id_info = new_session.query(ManualGroupUser.manual_group_id).filter(and_(
        ManualGroupUser.user_id == curr_user_id, ManualGroupUser.role_id == role_id)).all()
    if not group_id_info:
        return []
    group_id_list = [i[0] for i in group_id_info]

    if show_not_launch:
        condition = and_(ManualReadTaskGroup.manual_group_id.in_(group_id_list), ManualReadTask.task_state != 4)
    else:
        condition = and_(ManualReadTaskGroup.manual_group_id.in_(group_id_list), ~ManualReadTask.task_state.in_([1, 4]))
    m_read_task_info = new_session.query(ManualReadTaskGroup.m_read_task_id.distinct(), ManualReadTask.m_read_task_name,
                                         ManualReadTask.project_id, ManualReadTask.subject_id,
                                         ManualReadTask.paper_id, ManualReadTask.ques_code, ManualReadTask.ques_id) \
        .join(ManualReadTask, ManualReadTask.m_read_task_id == ManualReadTaskGroup.m_read_task_id) \
        .filter(condition) \
        .group_by(ManualReadTaskGroup.m_read_task_id).all()
    if only_id:
        m_read_task_id_list = [task[0] for task in m_read_task_info] if m_read_task_info else []
        return m_read_task_id_list
    m_read_task_list = []
    if m_read_task_info:
        for task in m_read_task_info:
            task_item = {
                "m_read_task_id": task[0],
                "m_read_task_name": task.m_read_task_name,
                "project_id": task.project_id,
                "subject_id": task.subject_id,
                "paper_id": task.paper_id,
                "ques_code": task.ques_code,
                "ques_id": task.ques_id
            }
            m_read_task_list.append(task_item)
    return m_read_task_list
