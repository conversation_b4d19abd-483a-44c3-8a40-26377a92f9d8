import copy
import traceback

from sqlalchemy import select, func, and_, or_
from settings import logger

from apps.models.models import SysModule, SysSubModule, SysFuncPoint, Project, Subject, BusinessQuesType, ExamQuestion, ExamPaper, PaperDetail, QuesType


from factory_apps import session_depend


def format_module_data(module_data: list):
    # 创建一个空字典用于存储转换后的结果
    result = {}

    # 遍历原始数据
    for item in module_data:
        module_id = item["module_id"]
        sub_module_id = item["sub_module_id"]
        func_point_id = item["func_point_id"]

        # 如果模块还不存在于结果字典中，则创建一个新的模块条目
        if module_id not in result:
            result[module_id] = {
                "module_id": module_id,
                "module_name": item["module_name"],
                "rank": item["rank"],
                "web_path": item["web_path"],
                "icon": item["icon"],
                "flag": 1,
                "show_link": item["show_link"],
                "module_flag": item["module_flag"],
                "func_point": [],
                "children": [],
            }

        # 初始化一个功能点实例
        func_point_item = {
            "module_id": func_point_id,
            "rank": item["func_point_rank"],
            "module_name": item["func_point_name"],
            "flag": 3,
            "module_flag": item["func_point_flag"],
            "func_point_value": item["func_point_value"],
        }

        # 将子模块添加到模块的子模块列表中
        if sub_module_id:
            result[module_id]["children"].append(
                {
                    "module_id": sub_module_id,
                    "rank": item["sub_rank"],
                    "module_name": item["sub_module_name"],
                    "web_path": item["sub_web_path"],
                    "icon": item["sub_icon"],
                    "show_link": item["sub_show_link"],
                    "parent_module_id": item["parent_module_id"],
                    "flag": 2,
                    "module_flag": item["sub_module_flag"],
                    "func_point": [],
                }
            )
            func_point_dict = {}
            if func_point_id:
                if func_point_id not in func_point_dict:
                    func_point_dict[func_point_id] = func_point_item
                    result[module_id]["children"]["func_point"].append(func_point_dict)
        else:
            if func_point_id:
                result[module_id]["func_point"].append(func_point_item)
            result[module_id]["children"] = []

    # 将结果字典转换为列表
    formatted_result = list(result.values())
    # print(formatted_result)
    return formatted_result


def get_module_info(new_session):
    """获取系统菜单并格式化"""
    module_data = []
    module_stmt = (
        select(
            SysModule.module_id,
            SysModule.rank,
            SysModule.module_name,
            SysModule.web_path,
            SysModule.icon,
            SysSubModule.sub_module_id,
            SysSubModule.rank,
            SysSubModule.sub_module_name,
            SysSubModule.sub_web_path,
            SysSubModule.sub_icon,
            SysModule.show_link,
            SysSubModule.sub_show_link,
            SysSubModule.module_id,
            SysModule.module_flag,
            SysSubModule.sub_module_flag,
            SysFuncPoint.func_point_id,
            SysFuncPoint.func_point_name,
            SysFuncPoint.rank,
            SysFuncPoint.func_point_flag,
            SysFuncPoint.func_point_value,
        )
        .outerjoin(SysSubModule)
        .outerjoin(SysFuncPoint, SysModule.module_flag == SysFuncPoint.parent_module_flag)
        .order_by(SysModule.rank, SysSubModule.rank, SysFuncPoint.rank)
    )
    try:
        result = new_session.execute(module_stmt)
        for row in result:
            module_item = {
                "module_id": row[0],
                "rank": row[1],
                "module_name": row[2],
                "web_path": row[3],
                "icon": row[4],
                "sub_module_id": row[5],
                "sub_rank": row[6],
                "sub_module_name": row[7],
                "sub_web_path": row[8],
                "sub_icon": row[9],
                "show_link": row[10],
                "sub_show_link": row[11],
                "parent_module_id": row[12],
                "module_flag": row[13],
                "sub_module_flag": row[14],
                "func_point_id": row[15],
                "func_point_name": row[16],
                "func_point_rank": row[17],
                "func_point_flag": row[18],
                "func_point_value": row[19],
            }
            module_data.append(module_item)
    except Exception as e:
        logger.error(f"获取系统模块列表失败，{e}")
        new_session.close()
        return []
    logger.info("获取系统模块列表成功")
    # print(module_data)
    final_module_data = format_module_data(module_data)
    return final_module_data


def get_func_point_info(new_session):
    """获取系统功能点信息"""
    func_dict = {}
    func_stmt = select(SysSubModule.sub_module_id, SysFuncPoint.func_point_id, SysFuncPoint.func_point_name, SysFuncPoint.rank, SysFuncPoint.func_point_flag, SysFuncPoint.func_point_value).join(
        SysFuncPoint, SysFuncPoint.parent_module_flag == SysSubModule.sub_module_flag
    )
    try:
        result = new_session.execute(func_stmt)
        for row in result:
            sub_module_id = row[0]
            module_item = {"flag": 3, "module_id": row[1], "module_name": row[2], "rank": row[3], "module_flag": row[4], "func_point_value": row[5]}
            if sub_module_id in func_dict:
                func_dict[sub_module_id].append(module_item)
            else:
                func_dict[sub_module_id] = [module_item]
    except Exception as e:
        logger.error(f"获取系统功能点失败，{e}")
    logger.info("获取系统功能点成功")
    return func_dict


def get_all_sys_info(new_session):
    # 将模块、子模块、功能点对应关系格式化
    final_module_data = get_module_info(new_session)
    func_point_dict = get_func_point_info(new_session)

    # 添加子模块下的功能点
    for index, item in enumerate(final_module_data):
        for inner_index, child in enumerate(item["children"]):
            sub_module_id = child["module_id"]
            if sub_module_id in func_point_dict:
                final_module_data[index]["children"][inner_index]["func_point"] = func_point_dict[sub_module_id]

    # 将模块和功能点都按照 rank 进行排序
    final_module_data = sorted(final_module_data, key=lambda x: x["rank"])
    sorted_module_data = []
    for module in final_module_data:
        new_module = copy.deepcopy(module)
        new_module["func_point"] = sorted(new_module["func_point"], key=lambda x: x["rank"])
        new_module["children"] = sorted(new_module["children"], key=lambda x: x["rank"])
        for child in new_module["children"]:
            child["func_point"] = sorted(child["func_point"], key=lambda x: x["rank"])
        sorted_module_data.append(new_module)
    return sorted_module_data


def get_format_data_permission(data_permission: list):
    """
    格式化数据权限
    """
    for data in data_permission:
        subject_id_list = data["subject_id_list"]
        subject_name_list = data["subject_name_list"]
        if not subject_id_list:
            subject_id_list = []
            subject_name_list = []
        mix = zip(subject_id_list, subject_name_list)
        data["children"] = []
        for i in mix:
            item = {"flag": i[0], "name": i[1], "type": 2}
            data["children"].append(item)
        del data["subject_id_list"]
        del data["subject_name_list"]
    return data_permission


def get_sys_data_permission():
    logger.info(f"获取系统所有数据权限")
    # data_permission = []
    try:
        new_session = next(session_depend())
        # projectSubjects = new_session.execute(data_stmt)

        # 抽参
        paramSelect = (
            select(
                Project.project_id,
                Project.project_name,
                Project.project_flag,
                Subject.subject_flag,
                Subject.subject_id,
                Subject.subject_name,
                BusinessQuesType.business_ques_type_id,
                BusinessQuesType.ques_type_code,
                BusinessQuesType.ques_type_name,
                ExamQuestion.ques_code,
                ExamQuestion.ques_desc,
            )
            .outerjoin(Subject, Subject.project_id == Project.project_id)
            .outerjoin(
                BusinessQuesType,
                and_(
                    Subject.subject_id == BusinessQuesType.subject_id,
                    or_(BusinessQuesType.parent_ques_type_id == "", BusinessQuesType.parent_ques_type_id.is_(None)),
                ),
            )
            .outerjoin(QuesType, BusinessQuesType.ques_type_code == QuesType.ques_type_code)
            .outerjoin(ExamQuestion, ExamQuestion.business_ques_type_id == BusinessQuesType.business_ques_type_id)
            .where(Subject.exam_mode == 0)
        )
        paramDatas = new_session.execute(paramSelect).mappings().all()
        paramHierarchy = [
            {"flag": "project_flag", "id": "project_id", "name": "project_name", "data_type": 1},
            {"flag": "subject_flag", "id": "subject_id", "name": "subject_name", "data_type": 2},
            {"flag": "ques_type_code", "id": "business_ques_type_id", "name": "ques_type_name", "data_type": 4},
            {"flag": "ques_code", "name": "ques_code", "desc": "ques_desc", "data_type": 5},
        ]
        paramTree = build_tree_with_id_name(paramDatas, paramHierarchy)

        # 抽卷
        paperSelect = (
            select(
                Project.project_id,
                Project.project_name,
                Project.project_flag,
                Subject.subject_flag,
                Subject.subject_id,
                Subject.subject_name,
                ExamPaper.paper_id,
                ExamPaper.paper_name,
                QuesType.ques_type_code,
                QuesType.ques_type_name,
                PaperDetail.ques_id,
                PaperDetail.ques_code,
            )
            .outerjoin(Subject, Subject.project_id == Project.project_id)
            .outerjoin(ExamPaper, Subject.subject_id == ExamPaper.subject_id)
            .outerjoin(PaperDetail, PaperDetail.paper_id == ExamPaper.paper_id)
            .outerjoin(QuesType, PaperDetail.ques_type_code == QuesType.ques_type_code)
            .where(Subject.exam_mode == 1)
        )
        paperDatas = new_session.execute(paperSelect).mappings().all()
        paperHierarchy = [
            {"flag": "project_flag", "id": "project_id", "name": "project_name", "data_type": 1},
            {"flag": "subject_flag", "id": "subject_id", "name": "subject_name", "data_type": 2},
            {"flag": "paper_id", "name": "paper_name", "data_type": 3},
            {"flag": "ques_type_code", "name": "ques_type_name", "data_type": 4},
            {"flag": "ques_id", "name": "ques_code", "data_type": 5},
        ]
        paperTree = build_tree_with_id_name(paperDatas, paperHierarchy)
        paramTree = paramTree + paperTree
    except Exception as e:
        traceback.print_exc()
        logger.error(f"获取数据权限失败，{e}")
        return []
    logger.info("获取数据权限成功")

    # format_data_permission = get_format_data_permission(data_permission)
    return paramTree


def update_module_rank_when_create(new_session, flag: int, rank: int):
    """
    调整新建时受影响的模块的顺序
    """
    if flag == 1:
        affected_modules = new_session.query(SysModule).filter(SysModule.rank >= rank).all()
    elif flag == 2:
        affected_modules = new_session.query(SysSubModule).filter(SysSubModule.rank >= rank).all()
    else:
        affected_modules = new_session.query(SysFuncPoint).filter(SysFuncPoint.rank >= rank).all()
    if affected_modules:
        for module in affected_modules:
            module.rank += 1


def update_module_rank_when_update(new_session, flag: int, module_id: str, new_rank: int):
    """
    调整更新时受影响的模块的顺序
    """
    if flag == 1:
        search_module = SysModule
        current_module = new_session.query(search_module).filter(search_module.module_id == module_id).first()
    elif flag == 2:
        search_module = SysSubModule
        current_module = new_session.query(search_module).filter(search_module.sub_module_id == module_id).first()
    else:
        search_module = SysFuncPoint
        current_module = new_session.query(search_module).filter(search_module.func_point_id == module_id).first()
    current_rank = current_module.rank

    if current_rank == new_rank:
        return True, None

    if new_rank < current_rank:
        # 将介于 new_rank 和 current_rank 之间的模块顺序加一
        affected_modules = new_session.query(search_module).filter(search_module.rank >= new_rank, search_module.rank < current_rank).all()
        for module in affected_modules:
            module.rank += 1
    else:
        # 将介于 current_rank 和 new_rank 之间的模块顺序减一
        affected_modules = new_session.query(search_module).filter(search_module.rank > current_rank, search_module.rank <= new_rank).all()
        for module in affected_modules:
            module.rank -= 1
    return True, None


def update_module_rank_when_delete(new_session, flag: int, module_id: str):
    """
    调整删除时受影响的模块的顺序
    """
    if flag == 1:
        search_module = SysModule
        current_module = new_session.query(search_module).filter(search_module.module_id == module_id).first()
    elif flag == 2:
        search_module = SysSubModule
        current_module = new_session.query(search_module).filter(search_module.sub_module_id == module_id).first()
    else:
        search_module = SysFuncPoint
        current_module = new_session.query(search_module).filter(search_module.func_point_id == module_id).first()
    current_rank = current_module.rank

    new_session.delete(current_module)

    affected_modules = new_session.query(search_module).filter(search_module.rank > current_rank).all()
    for module in affected_modules:
        module.rank -= 1


def build_tree_with_id_name(data, hierarchy):
    def insert_node(tree, row):
        current_level = tree
        path_so_far = []

        for index, level in enumerate(hierarchy):
            #    for level in hierarchy:
            node_id = row[level["flag"]]
            path_so_far.append(node_id)
            node_name = row.get(level.get("name")) if "name" in level else None
            desc = row.get(level.get("desc")) if "desc" in level else None
            # id = row.get(level.get("id")) if "id" in level else None
            flag = node_id
            # if id:
            #     flag = id

            # 查找当前层是否已有该节点
            found = None
            for node in current_level:
                if node["flag"] == flag:
                    found = node
                    break

            if found is None:
                new_node = {"flag": flag, "type": index + 1, "path": list(path_so_far)}
                if node_name:
                    new_node["name"] = node_name
                if desc:
                    new_node["desc"] = desc
                if level != hierarchy[-1]:
                    new_node["children"] = []
                new_node["data_type"] = level["data_type"]
                current_level.append(new_node)
                current_level = new_node.get("children", [])
            else:
                current_level = found.get("children", [])

    tree = []
    for row in data:
        insert_node(tree, row)
    return tree


if __name__ == "__main__":
    get_func_point_info()
