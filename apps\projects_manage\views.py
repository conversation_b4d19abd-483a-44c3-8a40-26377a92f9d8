import threading
import traceback
import httpx

from fastapi import APIRouter, Depends, Request, BackgroundTasks
from apps.base.global_cache import get_redis_ques_info_dict
from settings import logger
from sqlalchemy import select, exists, and_
from sqlalchemy.orm import Session
from typing import Any

from apps.permission.services import update_user_data_permission, add_user_data_permission
from apps.projects_manage.services import subject_query_condition, get_project_tree
from apps.read_paper.common_services import get_user_data_flag, get_sub_data_id, get_project_data_id
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.projects_manage.schemas import GetProjectReq, GetSubjectReq, CreateProjectReq, CreateSubjectReq, \
    UpdateProjectReq, UpdateSubjectReq, DeleteProjectReq, DeleteSubjectReq, UpdateSubjectStateReq, GetSeveralSubjectReq, \
    UpdateProjectStateReq, GetSubjectSessionReq
from apps.models.models import Project, UserInfo, <PERSON>ject, ExamType, ExamWay, Subject<PERSON>ession, ManualReadPaperGroup, BusinessQuesType
from helper import response_utils
from factory_apps import session_depend
from utils.utils import create_timestamp_id
from settings import configs
from apps.human_mark_group.models import HumanMarkGroup
from apps.logs.services import create_business_log, get_client_ip
project_router = APIRouter()


@project_router.get(path="/get_exam_type", response_model=BaseResponse, summary="获取考试类型列表")
async def get_exam_type(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考试类型列表")
    exam_type_data = []
    exam_type_stmt = select(ExamType.exam_type_id, ExamType.exam_type_name)
    try:
        result = new_session.execute(exam_type_stmt)
        for row in result:
            exam_type_item = {
                "exam_type_id": row[0],
                "exam_type_name": row[1]
            }
            exam_type_data.append(exam_type_item)
    except Exception as e:
        logger.error(f"获取考试类型列表失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取考试类型列表失败")
    logger.info("获取考试类型列表成功")
    data = {
        "data": exam_type_data
    }
    return BaseResponse(msg="获取考试类型列表成功", data=data)


@project_router.get(path="/get_exam_way", response_model=BaseResponse, summary="获取考试方式列表")
async def get_exam_way(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取考试方式列表")
    exam_way_data = []
    exam_way_stmt = select(ExamWay.exam_way_id, ExamWay.exam_way_name)
    try:
        result = new_session.execute(exam_way_stmt)
        for row in result:
            exam_way_item = {
                "exam_way_id": row[0],
                "exam_way_name": row[1]
            }
            exam_way_data.append(exam_way_item)
    except Exception as e:
        logger.error(f"获取考试方式列表失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取考试方式列表失败")
    logger.info("获取考试方式列表成功")
    data = {
        "data": exam_way_data
    }
    return BaseResponse(msg="获取考试方式列表成功", data=data)


@project_router.post(path="/create_project", response_model=BaseResponse, summary="创建资格")
# @log_operation(module="基础管理", op_type=1, op_content="创建资格", page="资格管理")
async def create_project(query: CreateProjectReq,
                         user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend),
                         ip:str = Depends(get_client_ip)):
    logger.info(f"{user['username']} 创建资格")

    is_exist = new_session.query(exists().where(Project.project_name == query.project_name)).scalar()
    if is_exist:
        return BaseResponse(code=response_utils.fields_exist, msg=f"资格名 {query.project_name} 已存在")

    try:
        project_flag = create_timestamp_id()
        project_id = configs.snow_worker.get_id()
        new_project = Project(project_id=project_id, project_name=query.project_name,
                              exam_type_id=query.exam_type_id, exam_way_id=query.exam_way_id, remark=query.remark,
                              is_active=True, project_flag=project_flag, c_user_id=user.get("user_id"))
        new_session.add(new_project)
        add_user_data_permission(new_session, user["user_id"], [project_id])
        # result, msg = update_user_data_permission(new_session, [project_flag], [], user["user_id"], user["user_id"], is_create_pro_sub=True)
        # if not result:
        #     new_session.rollback()
        #     logger.error(f"更新项目数据权限失败，{msg}")
        #     return BaseResponse(code=response_utils.server_error, msg="创建项目失败")
        new_session.commit()
        logger.info("创建资格成功")
        
        # 直接调用业务日志接口
        try:
            from datetime import datetime
            from pytz import timezone as pytz_timezone
            op_time = datetime.now(pytz_timezone('Asia/Shanghai'))
            await create_business_log(
                new_session=new_session,
                ip=ip,  
                module="基础管理",
                page="资格管理",
                op_type=1,  # 新增操作
                op_user_id=user.get("user_id") if isinstance(user, dict) else getattr(user, "user_id", "unknown"),
                op_time=op_time,
                op_content=f"创建资格，资格ID: {project_id}"
            )
        except Exception as log_error:
            logger.error(f"写入业务日志失败: {log_error}")
            
        return BaseResponse(msg="创建资格成功")
    except Exception as e:
        logger.error(f"创建资格失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="创建资格失败")


@project_router.post(path="/get_project", response_model=BaseResponse, summary="获取资格列表")
async def get_project(query: GetProjectReq, user: Any = Depends(get_current_user),
                      new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取资格列表")
    current_page, page_size, project_id, c_name, exam_way_id = query.model_dump().values()
    # 获取用户的数据权限
    project_condition, _ = get_user_data_flag(new_session, user)

    project_data = []
    if page_size == -1:
        total = new_session.query(Project.project_id).where(project_condition).count()
        project_stmt = select(Project.project_id, Project.project_name, UserInfo.username, ExamType.exam_type_name,
                              ExamWay.exam_way_name, Project.created_time, Project.updated_time, Project.remark,
                              ExamType.exam_type_id, ExamWay.exam_way_id, Project.is_active, UserInfo.name) \
            .join(UserInfo, Project.c_user_id == UserInfo.user_id) \
            .outerjoin(ExamType, Project.exam_type_id == ExamType.exam_type_id) \
            .outerjoin(ExamWay, Project.exam_way_id == ExamWay.exam_way_id) \
            .where(project_condition)
    else:
        limit = current_page - 1
        offset = limit * page_size
        # 拼凑查询条件
        if project_id:
            condition = and_(Project.project_id == project_id, project_condition)
        else:
            condition = project_condition

        if c_name:
            user_condition = UserInfo.name.ilike(f"%{c_name}%")
        else:
            user_condition = True
        if exam_way_id:
            exam_way_condition = Project.exam_way_id.in_(exam_way_id) if isinstance(exam_way_id,
                                                                                    list) else Project.exam_way_id == exam_way_id
        else:
            exam_way_condition = True
        total = new_session.query(Project.project_id).join(UserInfo, UserInfo.user_id == Project.c_user_id).where(
            condition).where(and_(condition, user_condition)).count()
        project_stmt = select(Project.project_id, Project.project_name, UserInfo.username, ExamType.exam_type_name,
                              ExamWay.exam_way_name, Project.created_time, Project.updated_time, Project.remark,
                              Project.is_active, Project.exam_type_id, Project.exam_way_id,
                              UserInfo.name) \
            .join(UserInfo, Project.c_user_id == UserInfo.user_id) \
            .outerjoin(ExamType, Project.exam_type_id == ExamType.exam_type_id) \
            .outerjoin(ExamWay, Project.exam_way_id == ExamWay.exam_way_id) \
            .where(and_(condition, user_condition, exam_way_condition)) \
            .order_by(Project.created_time.desc(), Project.project_id.desc()) \
            .limit(page_size).offset(offset)

    try:
        result = new_session.execute(project_stmt)
        for row in result:
            # print(row)
            project_item = {
                "project_id": row.project_id,
                "project_name": row.project_name,
                "u_user_name": row.username,
                "exam_type": row.exam_type_name,
                "exam_way": row.exam_way_name,
                "created_time": row.created_time and str(row.created_time).replace("T", " "),
                "updated_time": row.updated_time and str(row.updated_time).replace("T", " "),
                "remark": row.remark,
                "c_name": row.name,
                "is_active": row.is_active,
                "exam_type_id": row.exam_type_id,
                "exam_way_id": row.exam_way_id
            }
            project_data.append(project_item)
    except Exception as e:
        logger.error(f"获取资格列表失败，{e}")
        logger.error(traceback.print_exc())
        return BaseResponse(code=response_utils.server_error, msg=f"获取资格列表失败")

    logger.info("获取资格列表成功")
    data = {
        "data": project_data,
        "total": total
    }
    return BaseResponse(msg="获取资格列表成功", data=data)


@project_router.post(path="/update_project", response_model=BaseResponse, summary="编辑资格信息")
# @log_operation(module="基础管理", op_type=3, op_content="编辑资格信息", page="资格管理")
async def update_project(background_tasks:BackgroundTasks,query: UpdateProjectReq,
                         user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend),
                         ip:str = Depends(get_client_ip)):
    project_id, project_name, exam_type_id, exam_way_id, remark = query.model_dump().values()
    logger.info(f"{user['username']} 编辑资格信息，id 为 {project_id}")
    project_data = new_session.query(Project.project_id, Project.lock_state).filter(
        Project.project_id == project_id).first()
    if not project_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该资格")
    else:
        if project_data[1] == 2:
            return BaseResponse(code=response_utils.permission_deny, msg="该资格已被使用，不允许编辑该资格")

    try:
        new_session.query(Project).filter(Project.project_id == project_id).update({
            Project.project_name: project_name,
            Project.exam_type_id: exam_type_id,
            Project.exam_way_id: exam_way_id,
            Project.remark: remark,
            Project.u_user_id: user.get("user_id")
        })
        new_session.commit()
        background_tasks.add_task(get_redis_ques_info_dict, new_session, True)
    except Exception as e:
        new_session.rollback()
        logger.error(f"编辑资格信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"编辑资格信息失败")
    # threading.Thread(target=get_redis_ques_info_dict, args=(new_session, True)).start()
    logger.info("编辑资格信息成功")
    
    # 直接调用业务日志接口
    try:
        from datetime import datetime
        from pytz import timezone as pytz_timezone
        op_time = datetime.now(pytz_timezone('Asia/Shanghai'))
        # 获取更新前的项目信息用于记录变更内容
        old_project = new_session.query(Project.project_name, Project.exam_type_id, Project.exam_way_id, Project.remark).filter(Project.project_id == project_id).first()
        old_project_info = f"原项目名称: {old_project[0] if old_project else '未知'}, 原考试类型ID: {old_project[1] if old_project else '未知'}, 原考试方式ID: {old_project[2] if old_project else '未知'}, 原备注: {old_project[3] if old_project else '未知'}"
        new_project_info = f"新项目名称: {project_name}, 新考试类型ID: {exam_type_id}, 新考试方式ID: {exam_way_id}, 新备注: {remark}"
        op_content = f"编辑资格信息，资格ID: {project_id}。{old_project_info} -> {new_project_info}"
        
        await create_business_log(
            new_session=new_session,
            ip=ip,  
            module="基础管理",
            page="资格管理",
            op_type=3,  # 修改操作
            op_user_id=user.get("user_id") if isinstance(user, dict) else getattr(user, "user_id", "unknown"),
            op_time=op_time,
            op_content=op_content
        )
    except Exception as log_error:
        logger.error(f"写入业务日志失败: {log_error}")
    
    return BaseResponse(msg="编资格信息成功")


@project_router.post(path="/delete_project", response_model=BaseResponse, summary="删除资格信息")
# @log_operation(module="基础管理", op_type=2, op_content="删除资格信息", page="资格管理")
async def delete_project(query: DeleteProjectReq,
                         user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend),
                         ip:str = Depends(get_client_ip)):
    project_id = query.project_id
    logger.info(f"{user['username']} 删除资格信息，id 为 {project_id}")

    project_data = new_session.query(Project.lock_state).filter(Project.project_id == project_id).first()
    if not project_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该资格")
    else:
        if project_data[0] == 2:
            return BaseResponse(code=response_utils.permission_deny, msg="该资格已被使用，不允许删除")

    # 查询该项目下是否有科目，有科目就不让删除
    subject_exist = new_session.query(exists().where(Subject.project_id == project_id)).scalar()
    if subject_exist:
        return BaseResponse(code=response_utils.permission_deny, msg="该资格已被使用，不允许删除")

    try:
        new_session.query(Project).filter(Project.project_id == project_id).delete()
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除资格信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除资格信息失败")
    logger.info("删除资格信息成功")
    
    # 直接调用业务日志接口
    try:
        from datetime import datetime
        from pytz import timezone as pytz_timezone
        op_time = datetime.now(pytz_timezone('Asia/Shanghai'))
        await create_business_log(
            new_session=new_session,
            ip=ip,  
            module="基础管理",
            page="资格管理",
            op_type=2,  # 删除操作
            op_user_id=user.get("user_id") if isinstance(user, dict) else getattr(user, "user_id", "unknown"),
            op_time=op_time,
            op_content=f"删除资格信息，资格ID: {project_id}"
        )
    except Exception as log_error:
        logger.error(f"写入业务日志失败: {log_error}")
    
    return BaseResponse(msg="删除资格信息成功")


@project_router.post(path="/extract_range", response_model=BaseResponse, summary="获取抽取范围")
async def extract_range(user: Any = Depends(get_current_user),
                        new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取抽取范围")
    # 获取用户的数据权限
    # project_condition, subject_condition = get_user_data_flag(new_session, user)
    extrated_data = new_session.query(
        Project.project_name,
        Project.project_id,
        SubjectSession.exam_session,
        SubjectSession.session_id,
        Subject.subject_name,
        Subject.subject_id
    ).join(
        Subject, SubjectSession.subject_id == Subject.subject_id
    ).join(
        Project, Subject.project_id == Project.project_id
    ).order_by(SubjectSession.exam_session).all()
    extra_range_list = [{"project_name": row[0], "project_id": row[1], "exam_session": row[2], "session_id": row[3],
                         "subject_name": row[4], "subject_id": row[5]} for row in extrated_data]

    return_data = {
        "extra_range_list": extra_range_list
    }
    return BaseResponse(data=return_data, msg="获取抽取范围成功")


@project_router.post(path="/get_subject", response_model=BaseResponse, summary="获取科目列表")
async def get_subject(query: GetSubjectReq, user: Any = Depends(get_current_user),
                      new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取科目列表")
    current_page, page_size, project_id, subject_id, c_name, exam_mode = query.model_dump().values()

    # subject_condition = get_sub_data_id(new_session, user)

    subject_condition = get_project_data_id(new_session, user)
    subject_data = []
    common_stmt = select(Subject.subject_id, Subject.subject_name, Project.project_name, UserInfo.username,
                         Subject.created_time, Subject.updated_time, Subject.remark, Subject.project_id,
                         Subject.subject_total_score, Subject.subject_pass_score, Subject.subject_score_interval,
                         Subject.spy_num,
                         Subject.exam_mode, Subject.subject_code, Subject.is_active, UserInfo.name) \
        .join(Project, Subject.project_id == Project.project_id) \
        .outerjoin(UserInfo, Subject.u_user_id == UserInfo.user_id)
    if page_size == -1: # 数据全部返回
        if project_id:
            condition = and_(Subject.project_id == project_id, subject_condition)
        else:
            condition = subject_condition
        total = new_session.query(Subject.subject_id).where(condition).count()
        subject_stmt = common_stmt.where(condition)
    else:
        # 拼凑查询条件
        condition = subject_query_condition(project_id, subject_id)
        if c_name:
            user_condition = and_(UserInfo.name.ilike(f"%{c_name}%"), subject_condition)
        else:
            user_condition = subject_condition
        print(condition, user_condition)
        exam_mode_condition = Subject.exam_mode.in_(exam_mode) if exam_mode else True
        limit = current_page - 1
        offset = limit * page_size
        total = new_session.query(Subject.subject_id).where(
            and_(condition, user_condition, exam_mode_condition)).count()
        subject_stmt = common_stmt \
            .where(and_(condition, user_condition, exam_mode_condition)) \
            .order_by(Subject.created_time.desc(), Subject.subject_id.desc()) \
            .limit(page_size).offset(offset)
    try:
        result = new_session.execute(subject_stmt)
        for row in result:
            subject_item = {
                "subject_id": row.subject_id,
                "subject_name": row.subject_name,
                "subject_code": row.subject_code,
                "project_name": row.project_name,
                "exam_mode": row.exam_mode,
                "spy_num": row.spy_num,
                "is_active": row.is_active,
                "u_user_name": row.username,
                "created_time": row.created_time and str(row.created_time).replace("T", " "),
                "updated_time": row.updated_time and str(row.updated_time).replace("T", " "),
                "remark": row.remark,
                "project_id": row.project_id,
                "c_name": row.name,
                "subject_total_score": float(row.subject_total_score) if row.subject_total_score is not None else None,
                "subject_pass_score": float(row.subject_pass_score) if row.subject_pass_score is not None else None,
                "subject_score_interval": float(row.subject_score_interval) if row.subject_score_interval is not None else None
            }
            subject_data.append(subject_item)
    except Exception as e:
        logger.error(traceback.format_exc())
        logger.error(f"获取科目列表失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"获取科目列表失败")
    logger.info("获取科目列表成功")

    data = {
        "data": subject_data,
        "total": total
    }
    return BaseResponse(msg="获取科目列表成功", data=data)


@project_router.post(path="/create_subject", response_model=BaseResponse, summary="创建科目")
# @log_operation(module="基础管理", op_type=1, op_content="创建科目", page="科目管理")
async def create_subject(query: CreateSubjectReq,
                         user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend),
                         ip:str = Depends(get_client_ip)):
    logger.info(f"{user['username']} 创建科目")
    subject_name, project_id, exam_mode, subject_code, spy_num, subject_total_score, subject_pass_score, subject_score_interval, remark = query.model_dump().values()
    curr_user_id = user.get("user_id")

    existing_code = new_session.query(Subject.subject_id).filter(
        and_(Subject.project_id == project_id, Subject.subject_code == subject_code,Subject.subject_id == subject_code)).first()
    existing_name = new_session.query(Subject.subject_id).filter(
        and_(Subject.project_id == project_id, Subject.subject_name == subject_name)).first()
    if existing_code:
        return BaseResponse(code=response_utils.fields_exist, msg=f"科目编码 {subject_code} 已存在")
    elif existing_name:
        return BaseResponse(code=response_utils.fields_exist, msg=f"科目名称 {subject_name} 已存在")

    try:
        # subject_id = configs.snow_worker.get_id()
        subject_id = subject_code
        new_subject = Subject(subject_id=subject_id, subject_name=subject_name, project_id=project_id,
                              exam_mode=exam_mode, subject_code=subject_code, spy_num=spy_num,
                              subject_score_interval=subject_score_interval,
                              subject_total_score=subject_total_score,
                              subject_pass_score=subject_pass_score, is_active=True, remark=remark,
                              subject_flag=create_timestamp_id(), u_user_id=curr_user_id)
        new_session.add(new_subject)

        add_user_data_permission(new_session, curr_user_id, [project_id], [subject_id])
        new_session.query(Project).filter(Project.project_id == project_id).update({Project.lock_state: 2})
        new_session.commit()
        logger.info("创建科目成功")
        
        # 直接调用业务日志接口
        try:
            from datetime import datetime
            from pytz import timezone as pytz_timezone
            op_time = datetime.now(pytz_timezone('Asia/Shanghai'))
            await create_business_log(
                new_session=new_session,
                ip=ip,  
                module="基础管理",
                page="科目管理",
                op_type=1,  # 新增操作
                op_user_id=user.get("user_id") if isinstance(user, dict) else getattr(user, "user_id", "unknown"),
                op_time=op_time,
                op_content=f"创建科目，科目ID: {subject_id}"
            )
        except Exception as log_error:
            logger.error(f"写入业务日志失败: {log_error}")
            
        return BaseResponse(msg="创建科目成功")
    except Exception as e:
        logger.error(f"创建科目失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg=f"创建科目失败")


@project_router.post(path="/update_subject", response_model=BaseResponse, summary="编辑科目信息")
# @log_operation(module="基础管理", op_type=3, op_content="编辑科目信息", page="科目管理")
async def update_subject(query: UpdateSubjectReq,
                         user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend),
                         ip:str = Depends(get_client_ip)):
    subject_id, subject_name, project_id, exam_mode, subject_code, spy_num, subject_total_score, subject_pass_score, subject_score_interval, remark = query.model_dump().values()
    logger.info(f"{user['username']} 编辑科目信息，id 为 {subject_id}")
    subject_data = new_session.query(Subject.subject_id, Subject.lock_state).filter(
        Subject.subject_id == subject_id).first()
    if not subject_data:
        return BaseResponse(code=response_utils.no_field, msg="没有该科目")
    else:
        if subject_data[1] == 2:
            return BaseResponse(code=response_utils.permission_deny, msg="该科目已被使用，不允许编辑")

    # 检查科目编码和名称在同个项目中是否唯一
    # 当subject_id为None时，不做校验
    if subject_id is not None:
        existing_code = new_session.query(Subject.subject_id).filter(
            and_(Subject.project_id == project_id, Subject.subject_code == subject_code,
                 Subject.subject_id != subject_id)).first()
        existing_name = new_session.query(Subject.subject_id).filter(
            and_(Subject.project_id == project_id, Subject.subject_name == subject_name,
                 Subject.subject_id != subject_id)).first()
        if existing_code:
            return BaseResponse(code=response_utils.fields_exist, msg=f"科目编码 {subject_code} 已存在")
        elif existing_name:
            return BaseResponse(code=response_utils.fields_exist, msg=f"科目名称 {subject_name} 已存在")

    try:
        new_session.query(Subject).filter(Subject.subject_id == subject_id).update({
            Subject.project_id: query.project_id,
            Subject.subject_name: subject_name,
            Subject.exam_mode: exam_mode,
            Subject.subject_code: subject_code,
            Subject.spy_num: spy_num,
            Subject.subject_score_interval: subject_score_interval,
            Subject.subject_total_score: subject_total_score,
            Subject.subject_pass_score: subject_pass_score,
            Subject.remark: remark,
            Subject.u_user_id: user.get("user_id")
        })
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"编辑科目信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"编辑科目信息失败")
    logger.info("编辑科目信息成功")
    
    # 直接调用业务日志接口
    try:
        from datetime import datetime
        from pytz import timezone as pytz_timezone
        op_time = datetime.now(pytz_timezone('Asia/Shanghai'))
        # 获取更新前的科目信息用于记录变更内容
        old_subject = new_session.query(Subject.subject_name, Subject.project_id, Subject.exam_mode, Subject.subject_code,
                                        Subject.spy_num, Subject.subject_score_interval, Subject.subject_total_score,
                                        Subject.subject_pass_score, Subject.remark).filter(Subject.subject_id == subject_id).first()
        old_subject_info = f"原科目名称: {old_subject[0] if old_subject else '未知'}, 原项目ID: {old_subject[1] if old_subject else '未知'}, 原考试模式: {old_subject[2] if old_subject else '未知'}, 原科目编码: {old_subject[3] if old_subject else '未知'}, 原抽样数: {old_subject[4] if old_subject else '未知'}, 原分数间隔: {old_subject[5] if old_subject else '未知'}, 原总分: {old_subject[6] if old_subject else '未知'}, 原及格分: {old_subject[7] if old_subject else '未知'}, 原备注: {old_subject[8] if old_subject else '未知'}"
        new_subject_info = f"新科目名称: {subject_name}, 新项目ID: {project_id}, 新考试模式: {exam_mode}, 新科目编码: {subject_code}, 新抽样数: {spy_num}, 新分数间隔: {subject_score_interval}, 新总分: {subject_total_score}, 新及格分: {subject_pass_score}, 新备注: {remark}"
        op_content = f"编辑科目信息，科目ID: {subject_id}。{old_subject_info} -> {new_subject_info}"
        
        await create_business_log(
            new_session=new_session,
            ip=ip,  
            module="基础管理",
            page="科目管理",
            op_type=3,  # 修改操作
            op_user_id=user.get("user_id") if isinstance(user, dict) else getattr(user, "user_id", "unknown"),
            op_time=op_time,
            op_content=op_content
        )
    except Exception as log_error:
        logger.error(f"写入业务日志失败: {log_error}")
    
    return BaseResponse(msg="编辑科目信息成功")


@project_router.post(path="/delete_subject", response_model=BaseResponse, summary="删除科目信息")
# @log_operation(module="基础管理", op_type=2, op_content="删除科目信息", page="科目管理")
async def delete_subject(query: DeleteSubjectReq,
                         user: Any = Depends(get_current_user),
                         new_session: Session = Depends(session_depend),
                         ip:str = Depends(get_client_ip)):
    subject_id = query.subject_id
    logger.info(f"{user['username']} 删除科目信息，id 为 {subject_id}")
    subject_data = new_session.query(Subject.subject_id, Subject.lock_state).filter(
        Subject.subject_id == subject_id).first()
    if not subject_data:
        logger.info(f"没有该科目{subject_id}")
        return BaseResponse(code=response_utils.no_field, msg="没有该科目")
    else:
        if subject_data[1] == 2:
            return BaseResponse(code=response_utils.permission_deny, msg="该科目已被使用，不允许删除")

    try:
        # 先获取要删除科目的project_id
        subject = new_session.query(Subject.project_id).filter(Subject.subject_id == subject_id).first()
        if subject:
            project_id = subject[0]
            # 检查科目下是否有小组
            group_exist = new_session.query(HumanMarkGroup.group_id).filter(
                HumanMarkGroup.subject_id == subject_id
            ).first()
            if group_exist:
                return BaseResponse(code=response_utils.permission_deny, msg="该科目下存在阅卷小组，不允许删除")
            
            # 检查科目下是否有题型
            # 通过检查该科目下是否有题型关联关系来判断
            ques_type_exist = new_session.query(BusinessQuesType.business_ques_type_id).filter(
                BusinessQuesType.subject_id == subject_id
            ).first()
            if ques_type_exist:
                return BaseResponse(code=response_utils.permission_deny, msg="该科目下存在题型，不允许删除")
            
            new_session.query(Subject).filter(Subject.subject_id == subject_id).delete()
            # 检查该资格下是否还有其他科目
            other_subjects = new_session.query(Subject.subject_id).filter(
                and_(Subject.project_id == project_id, Subject.subject_id != subject_id)
            ).first()
            # 如果没有其他科目，则将资格锁定状态改为未锁定
            if not other_subjects:
                new_session.query(Project).filter(Project.project_id == project_id).update({
                    Project.lock_state: 1
                })
            new_session.commit()
            
            # 通过HTTP请求调用业务日志接口
            try:
                # 构造业务日志数据
                log_data = {
                    "op_user_id": user.get("user_id") if isinstance(user, dict) else getattr(user, "user_id", "unknown"),
                    "op_type": 2,  # 删除操作
                    "module": "基础管理",
                    "page": "科目管理",
                    "op_content": f"删除科目信息，科目ID: {subject_id}"
                }
                
                # 构造请求URL
                base_url = f"http://{configs.BACKEND_IP}:{configs.PORT}"
                log_url = f"{base_url}{configs.VERSION}/logs/write_business_log"
                
                # 发送HTTP请求
                async with httpx.AsyncClient() as client:
                    response = await client.post(log_url, json=log_data)
                    if response.status_code != 200:
                        logger.error(f"写入业务日志失败，状态码: {response.status_code}, 响应: {response.text}")
            except Exception as log_error:
                logger.error(f"调用业务日志接口失败: {log_error}")
        else:
            new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"删除科目信息失败，{e}")
        return BaseResponse(code=response_utils.server_error, msg=f"删除科目信息失败")
    logger.info("删除科目信息成功")
    
    # 直接调用业务日志接口
    try:
        from datetime import datetime
        from pytz import timezone as pytz_timezone
        op_time = datetime.now(pytz_timezone('Asia/Shanghai'))
        await create_business_log(
            new_session=new_session,
            ip=ip,  
            module="基础管理",
            page="科目管理",
            op_type=2,  # 删除操作
            op_user_id=user.get("user_id") if isinstance(user, dict) else getattr(user, "user_id", "unknown"),
            op_time=op_time,
            op_content=f"删除科目信息，科目ID: {subject_id}"
        )
    except Exception as log_error:
        logger.error(f"写入业务日志失败: {log_error}")
    
    return BaseResponse(msg="删除科目信息成功")


@project_router.post(path="/update_subject_state", response_model=BaseResponse, summary="更改科目的使用状态")
# @log_operation(module="基础管理", op_type=3, op_content="更改科目的使用状态", page="科目管理")
async def update_subject_state(query: UpdateSubjectStateReq,
                               user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend),
                               ip:str = Depends(get_client_ip)):
    logger.info(f"{user['username']} 修改科目状态")
    role_id_list = user['role']
    curr_user_id = user["user_id"]
    subject_id, is_active = query.subject_id, query.is_active

    if "1" not in role_id_list and "2" not in role_id_list:
        return BaseResponse(code=response_utils.permission_deny, msg="只有管理员才能修改科目状态")

    try:
        new_session.query(Subject).filter(Subject.subject_id == subject_id).update({
            Subject.is_active: is_active,
            Subject.u_user_id: curr_user_id
        })
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"修改科目状态失败：{e}")
        return BaseResponse(code=response_utils.server_error, msg="修改科目状态失败")
    logger.info(f"修改科目状态成功")
    
    # 直接调用业务日志接口
    try:
        from datetime import datetime
        from pytz import timezone as pytz_timezone
        op_time = datetime.now(pytz_timezone('Asia/Shanghai'))
        # 构造业务日志数据
        op_content = f"更改科目使用状态，科目ID: {subject_id}，状态: {'启用' if is_active else '禁用'}"
        await create_business_log(
            new_session=new_session,
            ip=ip,  
            module="基础管理",
            page="科目管理",
            op_type=3,  # 修改操作
            op_user_id=user.get("user_id") if isinstance(user, dict) else getattr(user, "user_id", "unknown"),
            op_time=op_time,
            op_content=op_content
        )
    except Exception as log_error:
        logger.error(f"写入业务日志失败: {log_error}")
    
    return BaseResponse(msg=f"修改科目状态成功")


@project_router.post(path="/update_project_state", response_model=BaseResponse, summary="更改资格的使用状态")
# @log_operation(module="基础管理", op_type=3, op_content="更改资格的使用状态", page="资格管理")
async def update_project_state(query: UpdateProjectStateReq,
                               user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend),
                               ip:str = Depends(get_client_ip)):
    logger.info(f"{user['username']} 修改资格状态")
    role_id_list = user['role']
    curr_user_id = user["user_id"]
    project_id, is_active = query.project_id, query.is_active

    if "1" not in role_id_list and "2" not in role_id_list:
        return BaseResponse(code=response_utils.permission_deny, msg="只有管理员才能修改资格状态")

    try:
        new_session.query(Project).filter(Project.project_id == project_id).update({
            Project.is_active: is_active,
            Project.u_user_id: curr_user_id
        })
        new_session.commit()
    except Exception as e:
        new_session.rollback()
        logger.error(f"修改资格状态失败：{e}")
        return BaseResponse(code=response_utils.server_error, msg="修改资格状态失败")
    logger.info(f"修改资格状态成功")
    
    # 直接调用业务日志接口
    try:
        from datetime import datetime
        from pytz import timezone as pytz_timezone
        op_time = datetime.now(pytz_timezone('Asia/Shanghai'))
        # 构造业务日志数据
        op_content = f"更改资格使用状态，资格ID: {project_id}，状态: {'启用' if is_active else '禁用'}"
        await create_business_log(
            new_session=new_session,
            ip=ip,  
            module="基础管理",
            page="资格管理",
            op_type=3,  # 修改操作
            op_user_id=user.get("user_id") if isinstance(user, dict) else getattr(user, "user_id", "unknown"),
            op_time=op_time,
            op_content=op_content
        )
    except Exception as log_error:
        logger.error(f"写入业务日志失败: {log_error}")
    
    return BaseResponse(msg=f"修改资格状态成功")


@project_router.post(path="/get_project_detail", response_model=BaseResponse,
                     summary="获取资格科目场次信息树")
async def get_project_detail(user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取科目场次列表")

    try:
        result = get_project_tree(new_session)
        return BaseResponse(
            code=200,
            message="获取成功",
            data=result
        )

    except Exception as e:
        logger.error(f"获取资格科目场次信息树失败: {str(e)}")
        return BaseResponse(
            code=500,
            message="服务器内部错误",
            data=None
        )


@project_router.post(path="/get_subject_session", response_model=BaseResponse,
                     summary="获取科目场次")
async def get_subject_session(query: GetSubjectSessionReq, user: Any = Depends(get_current_user),
                      new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取科目场次列表")
    project_id, subject_id = query.model_dump().values()
    subject_session_condition = get_project_data_id(new_session, user)  ## 这个是啥意思？权限控制？
    subject_session_data = new_session.query(SubjectSession.session_id, SubjectSession.exam_session) \
        .join(
        Subject, SubjectSession.subject_id == Subject.subject_id
    ) \
        .where(and_(SubjectSession.subject_id == subject_id, Subject.project_id == project_id)) \
        .order_by(SubjectSession.exam_session).all()
    return_data = [
        {
            "session_id": row.session_id,
            "exam_session": row.exam_session
        } 
        for row in subject_session_data
    ]
    return BaseResponse(data=return_data, msg="获取场次列表成功")



@project_router.post(path="/get_subject_by_project_list", response_model=BaseResponse,
                     summary="根据多个资格获取科目列表")
async def get_subject_by_project_list(query: GetSeveralSubjectReq, user: Any = Depends(get_current_user),
                                      new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 根据多个资格获取科目列表")
    project_id_list = query.project_id_list
    subject_data = []
    stmt = select(Subject.subject_id, Subject.subject_name).where(Subject.project_id.in_(project_id_list))
    result = new_session.execute(stmt)
    for row in result:
        subject_item = {
            "subject_id": row.subject_id,
            "subject_name": row.subject_name
        }
        subject_data.append(subject_item)

    data = {
        "data": subject_data,
    }
    logger.info("根据多个资格获取科目列表成功")
    return BaseResponse(msg="根据多个资格获取科目列表成功", data=data)
