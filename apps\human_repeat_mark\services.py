import logging
import os
import json

from settings import configs
from openpyxl import Workbook
from openpyxl.styles import Alignment, Border, Side
from datetime import datetime, timedelta


def export_technology(time, sheet1_data, sheet2_data, save_path):
    """
    导出技术验收
    """
    EXAM_TASK_path_str = os.path.join(configs.PROJECT_PATH, r"server_static\settings\EXAM_TASK.json")
    # EXAM_TASK_path_str = os.path.join(r"D:\010602RPS_Backendpublishing\server_static\settings\EXAM_TASK.json")
    EXAM_TASK = json.load(open(EXAM_TASK_path_str, encoding="utf-8"))
    wb = Workbook()
    ws = wb.worksheets[0]
    ws.title = "技术验收表"
    ws.merge_cells("A1:G1")
    ws["A1"] = EXAM_TASK["technology_name"].format(datetime.now().year)
    ws["A1"].alignment = Alignment(vertical="center", horizontal="center")

    ws["A2"] = EXAM_TASK["point_name"]
    ws["G2"] = time

    title_dict = ["专业/科目名称", "评阅试卷总数", "验收试卷总数", "验收不合格试卷总数", "允许最大不合格份数", "专业/科目专家验收结论", "专业/科目验收专家签字"]
    ws.append(title_dict)
    # 为标题行添加边框
    for cell in ws.iter_rows(min_row=3, max_row=3, min_col=1, max_col=len(title_dict)):
        for cell in cell:
            cell.border = Border(left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))
    # 为第一张表的数据行添加边框
    for row in sheet1_data:
        ws.append(row)
        # 为当前行的所有单元格添加边框
        for cell in ws.iter_rows(min_row=ws.max_row, max_row=ws.max_row, min_col=1, max_col=len(title_dict)):
            for cell in cell:
                cell.border = Border(left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))

    ws.column_dimensions["A"].width = 13
    ws.column_dimensions["B"].width = 13
    ws.column_dimensions["C"].width = 13
    ws.column_dimensions["D"].width = 18
    ws.column_dimensions["E"].width = 18
    ws.column_dimensions["F"].width = 18
    ws.column_dimensions["G"].width = 21

    ws = wb.create_sheet("技术验收明细表")
    ws.merge_cells("A1:H1")
    ws["A1"] = EXAM_TASK["technology_detail_name"].format(datetime.now().year)
    ws["A1"].alignment = Alignment(vertical="center", horizontal="center")

    title_dict = ["专业/科目名称", "专业准考证加密号", "评阅得分", "验收得分", "差异分值", "允许最大差异分值", "是否通过验收", "专业技术验收专家"]
    ws.append(title_dict)
    # 为第二张表的标题行添加边框
    for cell in ws.iter_rows(min_row=2, max_row=2, min_col=1, max_col=len(title_dict)):
        for cell in cell:
            cell.border = Border(left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))
    # 为第二张表的数据行添加边框
    for row in sheet2_data:
        ws.append(row)
        # 为当前行的所有单元格添加边框
        for cell in ws.iter_rows(min_row=ws.max_row, max_row=ws.max_row, min_col=1, max_col=len(title_dict)):
            for cell in cell:
                cell.border = Border(left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))

    ws.column_dimensions["A"].width = 13
    ws.column_dimensions["B"].width = 18
    ws.column_dimensions["C"].width = 13
    ws.column_dimensions["D"].width = 13
    ws.column_dimensions["E"].width = 13
    ws.column_dimensions["F"].width = 18
    ws.column_dimensions["G"].width = 18
    ws.column_dimensions["H"].width = 18
    wb.save(save_path)


def manager_technology(time, sheet1_data, sheet2_data, save_path):
    """
    导出技术验收
    """
    EXAM_TASK_path_str = os.path.join(configs.PROJECT_PATH, r"server_static\settings\EXAM_TASK.json")
    EXAM_TASK = json.load(open(EXAM_TASK_path_str, encoding="utf-8"))
    wb = Workbook()
    ws = wb.worksheets[0]
    ws.title = "管理验收表 "
    ws.merge_cells("A1:G1")
    ws["A1"] = EXAM_TASK["manager_name"].format(datetime.now().year)
    ws["A1"].alignment = Alignment(vertical="center", horizontal="center")

    ws["A2"] = EXAM_TASK["point_name"]
    ws["H2"] = time

    title_dict = ["专业/科目名称", "评阅试卷总数", "抽取验收试卷总数", "验收通过试卷总数", "验收不通过试卷总数", "验收结论", "验收组长签字", "阅卷点负责人签字"]
    ws.append(title_dict)
    # 为标题行添加边框
    for cell in ws.iter_rows(min_row=3, max_row=3, min_col=1, max_col=len(title_dict)):
        for cell in cell:
            cell.border = Border(left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))
    # 为第一张表的数据行添加边框
    for row in sheet1_data:
        ws.append(row)
        # 为当前行的所有单元格添加边框
        for cell in ws.iter_rows(min_row=ws.max_row, max_row=ws.max_row, min_col=1, max_col=len(title_dict)):
            for cell in cell:
                cell.border = Border(left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))

    ws.column_dimensions["A"].width = 13
    ws.column_dimensions["B"].width = 13
    ws.column_dimensions["C"].width = 18
    ws.column_dimensions["D"].width = 18
    ws.column_dimensions["E"].width = 18
    ws.column_dimensions["F"].width = 23
    ws.column_dimensions["G"].width = 18
    ws.column_dimensions["H"].width = 21

    ws = wb.create_sheet("管理验收明细表")
    ws.merge_cells("A1:H1")
    ws["A1"] = EXAM_TASK["manager_detail_name"].format(datetime.now().year)
    ws["A1"].alignment = Alignment(vertical="center", horizontal="center")

    title_dict = ["专业/科目名称", "专业准考证加密号", "评阅得分", "是否通过验收", "专业/科目验收专家"]
    ws.append(title_dict)
    # 为第二张表的标题行添加边框
    for cell in ws.iter_rows(min_row=2, max_row=2, min_col=1, max_col=len(title_dict)):
        for cell in cell:
            cell.border = Border(left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))
    # 为第二张表的数据行添加边框
    for row in sheet2_data:
        ws.append(row)
        # 为当前行的所有单元格添加边框
        for cell in ws.iter_rows(min_row=ws.max_row, max_row=ws.max_row, min_col=1, max_col=len(title_dict)):
            for cell in cell:
                cell.border = Border(left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))

    ws.column_dimensions["A"].width = 13
    ws.column_dimensions["B"].width = 18
    ws.column_dimensions["C"].width = 13
    ws.column_dimensions["D"].width = 13
    ws.column_dimensions["E"].width = 21

    wb.save(save_path)


if __name__ == "__main__":
    data = [("john", 18)]
    file_path = "example.xlsx"
    export_technology("项目名称", data, data, file_path)
