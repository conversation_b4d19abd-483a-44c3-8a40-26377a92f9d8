from typing import Optional

import requests

from helper.response_utils import ai_not_response_code, ai_can_not_connect_code, ai_not_response, ai_can_not_connect
from settings import logger, configs
from utils.utils import sum_with_precision


def ai_set_std_req(same_answer_group_id: str, ques_id: str, subject: str, subject_info: str, ques_type: str,
                   ques_desc: str, std_answer: list, stu_answer: list, std_score: list, e_mark_rule: str,
                   mark_point: list, ques_material: Optional[str] = None, is_multiple: int = 0):
    """
    AI 定标接口
    """
    data = {
        "same_answer_group_id": same_answer_group_id,
        "ques_id": ques_id,
        "subject": subject,
        "subject_info": subject_info,
        "ques_type": ques_type,
        "ques_desc": ques_desc,
        "std_answer": std_answer,
        "stu_answer": stu_answer,
        "std_score": std_score,  # 空对应的分数
        "e_mark_rule": e_mark_rule,
        "mark_point": mark_point,
        "ques_material": ques_material.replace("\n", "").replace("\xa0", " ") if ques_material else None,
        "is_multiple": is_multiple
    }

    logger.info(f"发送给 AI 参数：{data}")
    try:
        res = requests.post(configs.SET_STD_URL, json=data, timeout=(5, configs.AI_TIMEOUT_SECOND))
    except requests.exceptions.RequestException:
        return ai_not_response_code, ai_not_response
    except:
        return ai_can_not_connect_code, ai_can_not_connect
    res_code = res.status_code
    print(res.json())
    if res_code != 200:
        logger.error(f"AI 返回错误码 {res_code}")
        return res_code, f"AI 返回错误码 {res_code}"
    logger.info(f"AI 返回数据：{res.json()}")
    result = res.json()["data"]
    return res_code, result

def ai_small_set_std_req(same_answer_group_id: str, ques_id: str, ques_desc: str, subject: str,
                        e_mark_rule: str, std_score: list[str], sub_ques_list: list, 
                        ques_material: Optional[str] = None):
    """
    AI定标，小题包含小小题
    """
    data = {
        "same_answer_group_id": same_answer_group_id,
        "ques_id": ques_id,
        "ques_desc": ques_desc,
        "subject": subject,
        "e_mark_rule": e_mark_rule,
        "std_score": std_score,
        "sub_ques_list": sub_ques_list,
        "ques_material": ques_material.replace("\n", "").replace("\xa0", " ") if ques_material else None,
    }

    logger.info(f"发送给 AI 参数：{data}")
    try:
        res = requests.post(configs.CS_SET_STD_URL, json=data, timeout=(5, configs.AI_TIMEOUT_SECOND))
    except requests.exceptions.RequestException:
        return ai_not_response_code, ai_not_response
    except:
        return ai_can_not_connect_code, ai_can_not_connect
    res_code = res.status_code

    if res_code != 200:
        logger.error(f"AI 返回错误码 {res_code}")
        return res_code, f"AI 返回错误码 {res_code}"
    logger.info(f"AI 返回数据：{res.json()}")
    result = res.json()["data"]
    return res_code, result

def check_ai_mark(ques_score, ai_score):
    """
    检查 AI 评分的分数
    """
    if isinstance(ques_score, list):
        ques_score = sum_with_precision(ques_score)
    fina_score = ques_score if ai_score > ques_score else ai_score
    return fina_score


def supple_images_desc_req(ques_data: list):
    """
    AI 补充图片描述接口
    """
    try:
        res = requests.post(configs.SUPPLE_IMAGES_DESC_URL, json=ques_data, timeout=(5, configs.AI_TIMEOUT_SECOND))
    except requests.exceptions.RequestException:
        return ai_not_response_code, ai_not_response
    except requests.exceptions.ConnectTimeout:
        return ai_can_not_connect_code, ai_can_not_connect

    res_code = res.status_code

    if res_code != 200:
        logger.error(f"AI 返回错误码 {res_code}")
        return res_code, None
    result = res.json()
    logger.info(f"AI 返回数据：{result}")
    if result["code"] != 200:
        return result["code"], result["msg"]
    data = result["data"]
    return res_code, data


def get_ai_mp(subject_name, ques_id, ques_type_code, ques_desc_text, ques_material_text, ques_score, ques_mark_point, generate_num):
    ques_data = {
        "subject_name": subject_name,
        "ques_id": ques_id,
        "ques_type_code": ques_type_code,
        "ques_desc": ques_desc_text,
        "ques_material": ques_material_text,
        "ques_score": ques_score,
        "ques_mark_point": ques_mark_point,
        "generate_num": generate_num
    }

    logger.info(f"发送给 AI 参数：{ques_data}")

    try:
        res = requests.post(configs.GET_MP_URL, json=ques_data, timeout=(5, configs.AI_TIMEOUT_SECOND))
    except requests.exceptions.RequestException:
        return ai_not_response_code, ai_not_response
    except requests.exceptions.ConnectTimeout:
        return ai_can_not_connect_code, ai_can_not_connect

    res_code = res.status_code

    if res_code != 200:
        logger.error(f"AI 返回错误码 {res_code}")
        return res_code, None
    result = res.json()
    logger.info(f"AI 返回数据：{result}")
    if result["code"] != 200:
        return result["code"], result["msg"]
    data = result["data"]
    return res_code, data
