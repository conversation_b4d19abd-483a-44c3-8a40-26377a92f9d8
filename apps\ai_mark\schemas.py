from typing import Optional, Literal
from pydantic import BaseModel, Field

from apps.base.schemas import PaginationModel


class GetAiMarkReq(PaginationModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    ques_type_code_list: list = Field([], title="试题类型简码列表")
    ques_code: Optional[str] = Field(None, title="试题编号")
    mark_state_list: Optional[list] = Field(None, title="评分状态：1 表示未评分，2 表示评分中，3 表示已评分，4 表示已暂停，5 表示已取消")


class StartAiMarkReq(BaseModel):
    ques_info: list = Field(None, title="试卷和试题信息")
    # ques_info = [{
    #     paper_id: "试卷id，为兼容抽参设置为可选参数",
    #     ques_id: "试题id"
    # }]


class CreateGradeReq(BaseModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    stu_secret_num: Optional[str] = Field(None, title="考生密号")
    grade_type: Literal[1, 2] = Field(1, title="成绩类型，1 表示 AI 成绩，2 表示人工阅卷成绩")


class GetGradeListReq(PaginationModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    stu_secret_num: Optional[str] = Field(None, title="考生密号")
    score_range: Optional[list] = Field(None, title="评分成绩")
    query_score_type: Literal[1, 2] = Field(1, title="要查询的分数范围类型，1 表示 AI 成绩，2 表示人工阅卷成绩")
    export_type: Optional[int] = Field(None, title="导出类型，1 为excel，2 为sqlite")
    grade_type: Literal[1, 2] = Field(1, title="成绩类型，1 表示 AI 成绩，2 表示人工阅卷成绩")


class GetAiGradeDetailReq(BaseModel):
    grade_id: str = Field(..., title="成绩id")
    grade_type: Literal[1, 2] = Field(1, title="成绩类型，1 表示 AI 成绩，2 表示人工阅卷成绩")


class GetImportProcessReq(BaseModel):
    process_id: str = Field(..., title="进度id")

class UploadFileReq(BaseModel):
    process_id: str = Field(..., title="进度id")
    folder_path: str = Field(..., title="文件夹路径")

class ClearStuMarkRecordReq(BaseModel):
    project_id: Optional[str] = Field(None, title="项目id")
    subject_id: Optional[str] = Field(None, title="科目id")
    paper_id: Optional[str] = Field(None, title="试卷id")
    stu_secret_num: Optional[str] = Field(None, title="考生密号")
    score_range: Optional[list] = Field(None, title="评分成绩")
    stu_secret_num_list: list = Field([], title="前端勾选的考生密号数据列表")
