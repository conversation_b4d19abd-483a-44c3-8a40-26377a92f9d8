import traceback

from fastapi import APIRouter, Depends

from apps.human_task_manage.models import HumanReadTask, HumanReadTaskRound, HumanReadRoundGroup
from apps.base.human_global_cache import get_round_group_member
from apps.human_task_manage.services import exist_task_info
from apps.human_try_mark.schemas import CreateTryMarkTasksReq
from apps.manual_read_paper.manual_read_services import get_process_id_by_name
from settings import logger
from sqlalchemy import func, select, and_
from sqlalchemy.orm import Session
from typing import Any

from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse

from helper import response_utils
from factory_apps import session_depend
from settings import configs

human_try_mark_router = APIRouter()


@human_try_mark_router.post(path="/create_try_mark_task", response_model=BaseResponse, summary="创建人工阅卷试评任务")
async def create_try_mark_task(query: CreateTryMarkTasksReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建人工阅卷试评任务")
    curr_user_id = user.get("user_id")
    human_task_list = query.human_task_list

    ques_code_list = [task.ques_code for task in human_task_list]
    all_ques_code_list, task_type_dict, ques_task_dict = exist_task_info(new_session, ques_code_list)

    try:
        round_id_list = []

        # 试评绑定单评
        process_id = get_process_id_by_name(new_session, "单评")

        for task in human_task_list:
            (task_name, project_id, subject_id, ques_type_code, business_type_name, ques_id, ques_code, business_id, mark_score_step,
             try_mark_ques_num, allow_diff_score, group_id_list) = task.model_dump().values()

            round_id = configs.snow_worker.get_id()

            # 任务不存在则创建任务和第一轮次，任务存在则只创建轮次
            if ques_code not in task_type_dict.get(2, []):
                # 创建人工阅卷任务
                task_id = configs.snow_worker.get_id()

                round_count = 1

                # 创建人工阅卷任务
                new_task = HumanReadTask(task_id=task_id, task_name=task_name, task_type=2, project_id=project_id, subject_id=subject_id,
                                         ques_type_code=ques_type_code, business_type_name=business_type_name, ques_id=ques_id, ques_code=ques_code,
                                         business_id=business_id, c_user_id=curr_user_id)
                new_session.add(new_task)
            else:
                # 计算新增轮次是第几轮
                task_id = ques_task_dict[2][ques_code]
                old_round_count = new_session.query(func.max(HumanReadTaskRound.round_count)).filter(HumanReadTaskRound.task_id == task_id).scalar()
                round_count = old_round_count + 1

            # 创建轮次
            new_round = HumanReadTaskRound(round_id=round_id, task_id=task_id, round_count=round_count, process_id=process_id, mark_score_step=mark_score_step,
                                           try_mark_ques_num=try_mark_ques_num, allow_diff_score=allow_diff_score, round_state=1, c_user_id=curr_user_id)
            new_session.add(new_round)

            round_id_list.append(round_id)

            # 创建轮次小组关联信息
            for group_id in group_id_list:
                new_task_group = HumanReadRoundGroup(round_group_id=configs.snow_worker.get_id(), round_id=round_id, task_id=task_id, group_id=group_id)
                new_session.add(new_task_group)

        new_session.commit()
        for round_id in round_id_list:
            get_round_group_member(new_session, round_id, True)
        logger.info(f"创建人工阅卷试评任务成功，共创建 {len(human_task_list)} 个任务")
        return BaseResponse(msg=f"创建人工阅卷试评任务成功，共创建 {len(human_task_list)} 个任务")
    except Exception as e:
        logger.error(traceback.format_exc())
        logger.error(f"创建人工阅卷任务失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="创建人工阅卷试评任务失败")
