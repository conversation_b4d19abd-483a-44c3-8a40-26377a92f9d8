import re
import traceback

from fastapi import APIRouter, Depends
from openpyxl.styles.builtins import total
from sqlalchemy import select, and_

from apps.ai_set_std.set_std_services import trans_set_std_state
from apps.base.global_cache import get_redis_ques_info_dict
from apps.read_paper.common_services import splice_image_path
from apps.set_std_check.schemas import SingleQuesReq, AnswerClusterReq, ClusterSetStdReq, GetClusterParseReq
from apps.set_std_check.services import format_ques_tree, get_cluster_condition
from helper import response_utils
from settings import logger, configs
from typing import Any
from sqlalchemy.orm import Session

from apps.users.services import get_current_user
from apps.models.models import QuesSetStdState, ExamQuestion, PaperDetail, ExamPaper, Subject, QuesType, SameStuAnswerGroup, \
    StuAnswerDetail, BusinessQuesType
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from utils.time_func import format_now_time
from utils.utils import sum_with_precision

set_std_check_router = APIRouter()


@set_std_check_router.get(path="/get_ques_tree", response_model=BaseResponse, summary="获取定标复核的试题树")
async def get_ques_tree(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取定标复核的试题树")
    ques_data = []
    state_stmt = select(ExamQuestion.ques_id, ExamQuestion.ques_code, Subject.subject_name, ExamPaper.paper_id,
                        ExamPaper.paper_name,
                        QuesType.ques_type_name, PaperDetail.ques_order, QuesSetStdState.set_std_state) \
        .outerjoin(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
        .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
        .join(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
        .join(Subject, Subject.subject_id == ExamPaper.subject_id) \
        .join(QuesSetStdState, QuesSetStdState.ques_id == ExamQuestion.ques_id) \
        .where(ExamQuestion.ques_type_code.in_(["D", "E"])) \
        .order_by(ExamQuestion.ques_id)

    try:
        result = new_session.execute(state_stmt)
        for row in result:
            item = {
                "ques_id": row.ques_id,
                "ques_code": row.ques_code,
                "subject_name": row.subject_name,
                "paper_id": row.paper_id,
                "paper_name": row.paper_name,
                "ques_type_name": row.ques_type_name,
                "ques_order": row.ques_order,
                "set_std_state": row.set_std_state,
                "set_std_state_str": trans_set_std_state(row.set_std_state)
            }
            ques_data.append(item)
        # 改成带 children 的树状结构
        tree_data = format_ques_tree(ques_data)
    except Exception as e:
        logger.error(f"获取定标复核的试题树失败，{e}")
        logger.error(traceback.print_exc())
        return BaseResponse(code=response_utils.server_error, msg="获取定标复核的试题树失败")

    logger.info("获取定标复核的试题树成功")
    data = {
        "data": tree_data
    }
    return BaseResponse(msg="获取定标复核的试题树成功", data=data)


@set_std_check_router.post(path="/get_single_ques_info", response_model=BaseResponse, summary="获取单题信息")
async def get_single_ques_info(query: SingleQuesReq, user: Any = Depends(get_current_user),
                               new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取单题信息")
    paper_id, ques_id = query.model_dump().values()
    space_answer_list = []
    all_ques_info = get_redis_ques_info_dict(new_session)
    ques_info = all_ques_info[ques_id]

    ques_info["ques_desc"], ques_info["ques_choices"] = splice_image_path(ques_info["ques_desc"], ques_info["ques_choices"])
    if ques_info["ques_material"]:
        ques_info["ques_material"], _ = splice_image_path(ques_info["ques_material"], [])

    if paper_id:
        ques_order, ques_score_list, parent_ques_id = new_session.query(PaperDetail.ques_order, PaperDetail.ques_score_list, PaperDetail.parent_ques_id) \
            .filter(and_(PaperDetail.paper_id == paper_id, PaperDetail.ques_id == ques_id)).first()
        ques_info["ques_order"] = re.sub(r'^0+', '', ques_order) if ques_order else None
        ques_info["ques_score_list"] = ques_score_list
        ques_info["parent_ques_order"] = None
        total_score = sum_with_precision(ques_score_list)
    else:
        ques_order, parent_ques_id = None, None
        ques_score= new_session.query(ExamQuestion.ques_score).filter(ExamQuestion.ques_id == ques_id).scalar()
            # .join(ExamQuestion, ExamQuestion.business_ques_type_id == BusinessQuesType.business_ques_type_id) \
        total_score = ques_score if ques_score is not None else ques_score


    ques_info["total_score"] = total_score

    # 用正则匹配出组合题大题的题号
    if parent_ques_id:
        match = re.search(r'^(\d+)', ques_order) if ques_order else None
        if match:
            ques_info["parent_ques_order"] = match.group(1)

    if ques_info["ques_type_code"] == "D":
        # 填空题
        # weight = ques_info["weight"]
        # ques_info["total_weight"] = weight[1][0]
        standard_answer = ques_info["standard_answer"]
        d_out_of_group = ques_info["d_out_of_order_group"]

        space_stu_answer_list = []
        if d_out_of_group:
            # [[1, 2], [3, 4], 5, 6]
            for d_group in d_out_of_group:
                stu_answer_item_list = []
                if isinstance(d_group, list):
                    for i in d_group:
                        stu_answer_item_list.append(standard_answer[i - 1])

                    d_group_str = [str(i) for i in d_group]
                    answer_item = {
                        "index": configs.NEW_SPLIT_FLAG.join(d_group_str),
                        "answer_item": configs.NEW_SPLIT_FLAG.join(stu_answer_item_list)
                    }
                    space_stu_answer_list.append(answer_item)
                else:
                    answer_item = {
                        "index": str(d_group),
                        "answer_item": standard_answer[d_group - 1]
                    }
                    space_stu_answer_list.append(answer_item)
        else:
            space_stu_answer_list = [{"index": str(i + 1), "answer_item": j} for i, j in enumerate(standard_answer)]
        for item in space_stu_answer_list:
            index, stu_answer_item = item["index"], item["answer_item"]
            index_list = index.split(configs.NEW_SPLIT_FLAG)
            # space_weight_list = []
            # space_weight = 0
            # for i in index_list:
            #     single_weight = weight[0][int(i) - 1]
            #     space_weight_list.append(str(single_weight))
            #     space_weight += single_weight
            if configs.NEW_SPLIT_FLAG in stu_answer_item:
                stu_answer_item = " ".join([f"({index + 1}){i}" for index, i in enumerate(stu_answer_item.split(configs.NEW_SPLIT_FLAG))])
            answer_item = {
                # "space_weight": ":".join(space_weight_list),
                "space_score": ques_info["total_score"] ,
                "space_answer": stu_answer_item,
                "space_synonymous_answer": None,
                "space_exclude_answer": None,
                "small_ques_order": index
            }
            space_answer_list.append(answer_item)
    else:
        # 简答题
        space_answer_list = [{
            "point_weight": [i["score"] for i in ques_info["ques_mark_point"]] if ques_info["ques_mark_point"] else [],
            "mark_point": [i["point"] for i in ques_info["ques_mark_point"]] if ques_info["ques_mark_point"] else [],
            "small_ques_order": "1",
            "space_score": total_score
        }]

    data = {
        "ques_info": ques_info,
        "answer_info_list": space_answer_list
    }
    return BaseResponse(msg="获取单题信息成功", data=data)


@set_std_check_router.post(path="/get_answer_cluster", response_model=BaseResponse, summary="获取单题（空）聚类类别")
async def get_answer_cluster(query: AnswerClusterReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取单题（空）聚类类别")
    (current_page, page_size, paper_id, ques_id, ques_type_code, space_num, stu_answer, set_std_state, stu_count,
     ai_score, manual_score, answer_percentage, manual_ai_diff_score) = query.model_dump().values()
    result, filter_condition = get_cluster_condition(stu_answer, set_std_state, stu_count, ai_score, manual_score,
                                                     answer_percentage, manual_ai_diff_score)
    if not result:
        return BaseResponse(code=response_utils.params_error, msg=filter_condition)

    limit = current_page - 1
    offset = limit * page_size

    if ques_type_code == "D":
        condition = and_(SameStuAnswerGroup.ques_id == ques_id, SameStuAnswerGroup.paper_id == paper_id,
                         SameStuAnswerGroup.small_ques_order == space_num, filter_condition)
    else:
        condition = and_(SameStuAnswerGroup.ques_id == ques_id, SameStuAnswerGroup.paper_id == paper_id,
                         filter_condition)

    cluster_info = new_session.query(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.stu_answer,
                                     SameStuAnswerGroup.stu_count, SameStuAnswerGroup.answer_percentage,
                                     SameStuAnswerGroup.ai_score, SameStuAnswerGroup.set_std_state) \
        .filter(condition).order_by(SameStuAnswerGroup.set_std_time.desc(),
                                    SameStuAnswerGroup.same_answer_group_id.desc()) \
        .limit(page_size).offset(offset).all()

    if not cluster_info:
        return BaseResponse(data={"data": []})

    cluster_data = [{
        "same_answer_group_id": row.same_answer_group_id,
        "stu_answer": row.stu_answer,
        "stu_count": row.stu_count,
        "answer_percentage": row.answer_percentage,
        "ai_score": row.ai_score,
        "set_std_state": row.set_std_state,
        "set_std_state_str": "未定标" if row.set_std_state == 1 else "已定标",
    } for row in cluster_info]

    return BaseResponse(data={"data": cluster_data})


@set_std_check_router.post(path="/get_cluster_parse", response_model=BaseResponse, summary="获取聚类类别评分解析")
async def get_cluster_parse(query: GetClusterParseReq, user: Any = Depends(get_current_user),
                            new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取聚类类别评分解析")
    ques_id, same_answer_group_id = query.model_dump().values()
    parse_info = new_session.query(SameStuAnswerGroup.ai_answer_parse, SameStuAnswerGroup.profession_parse) \
        .filter(and_(SameStuAnswerGroup.ques_id == ques_id,
                     SameStuAnswerGroup.same_answer_group_id == same_answer_group_id)).first()
    if not parse_info:
        return BaseResponse(code=response_utils.params_error, msg="无该聚类类别评分解析")
    ai_answer_parse, profession_parse = parse_info
    data = {
        "ai_answer_parse": ai_answer_parse,
        "profession_parse": profession_parse
    }
    return BaseResponse(data=data)


@set_std_check_router.post(path="/cluster_set_std", response_model=BaseResponse, summary="聚类类别定标")
async def get_answer_cluster(query: ClusterSetStdReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 聚类类别定标")
    ques_id, same_answer_group_id_list, set_std_score_type, set_std_score, profession_parse = query.model_dump().values()
    stu_answer_info = new_session.query(SameStuAnswerGroup.same_answer_group_id, SameStuAnswerGroup.stu_answer,
                                        SameStuAnswerGroup.score, SameStuAnswerGroup.ai_score).filter(
        and_(SameStuAnswerGroup.ques_id == ques_id,
             SameStuAnswerGroup.same_answer_group_id.in_(same_answer_group_id_list))).all()
    if not stu_answer_info:
        return BaseResponse(code=response_utils.params_error, msg="无该聚类类别")
    no_ai_mark_cluster = []
    is_invalid = False
    stu_answer_list = list(stu_answer_info)
    for group_id, cluster, space_score_str, ai_score in stu_answer_list:
        if not is_invalid:
            is_invalid = True
            if configs.NEW_SPLIT_FLAG in space_score_str:
                space_score = sum(space_score_str.split(configs.NEW_SPLIT_FLAG))
            else:
                space_score = float(space_score_str)
            if set_std_score > space_score:
                return BaseResponse(code=response_utils.params_error, msg=f"定标分数大于该题（空）分数：{space_score}")
        if ai_score is None:
            no_ai_mark_cluster.append(cluster)
    if no_ai_mark_cluster:
        return BaseResponse(code=response_utils.params_error,
                            msg=f"{'，'.join(no_ai_mark_cluster)} 聚类类别未进行AI定标")

    # 分批定标
    batch_size = 2000  # 每批处理的数量
    total_batches = (len(stu_answer_list) + batch_size - 1) // batch_size

    for batch in range(total_batches):
        start = batch * batch_size
        end = start + batch_size
        batch_data = stu_answer_list[start: end]
        batch_group_id_list = [i[0] for i in batch_data]
        manual_ai_score_diff_data = []

        update_data = {
            SameStuAnswerGroup.set_std_state: 2,
            SameStuAnswerGroup.set_std_score: set_std_score,
            SameStuAnswerGroup.set_std_time: format_now_time()
        }
        if set_std_score_type == 1:
            update_data[SameStuAnswerGroup.set_std_score_type] = 1
        else:
            update_data[SameStuAnswerGroup.set_std_score_type] = 2
            update_data[SameStuAnswerGroup.profession_score] = set_std_score
            update_data[SameStuAnswerGroup.profession_parse] = profession_parse

            for group_id, cluster, space_score_str, ai_score in batch_data:
                # 计算人机分差
                score_diff = set_std_score - float(ai_score)
                manual_ai_score_diff_data.append({
                    "same_answer_group_id": group_id,
                    "ques_id": ques_id,
                    "manual_ai_diff_score": score_diff
                })

        new_session.query(SameStuAnswerGroup).filter(
            and_(SameStuAnswerGroup.ques_id == ques_id,
                 SameStuAnswerGroup.same_answer_group_id.in_(batch_group_id_list))
        ).update(update_data)
        if manual_ai_score_diff_data:
            new_session.bulk_update_mappings(SameStuAnswerGroup, manual_ai_score_diff_data)
        # new_session.query(StuAnswerDetail).filter(
        #     and_(StuAnswerDetail.ques_id == ques_id,
        #          StuAnswerDetail.same_answer_group_id.in_(batch_group_id_list))
        # ).update({
        #     StuAnswerDetail.set_std_score: set_std_score,
        #     StuAnswerDetail.set_std_answer_parse:
        # })

        new_session.commit()
        logger.info(f"{batch + 1}/{total_batches} 批定标成功")
    return BaseResponse(msg="聚类类别定标成功")
