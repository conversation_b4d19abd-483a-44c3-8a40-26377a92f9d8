import datetime

from sqlalchemy import Column, String, Integer, JSON, DateTime, DECIMAL, PrimaryKeyConstraint
from sqlalchemy.orm import declarative_base


# 声明基类
Base = declarative_base()


class HumanReadTask(Base):
    __tablename__ = "t_human_read_task"
    __table_args__ = {"comment": "任务表"}
    task_id = Column(String(50), primary_key=True, comment="人工阅卷任务id")
    task_name = Column(String(100), nullable=False, comment="人工阅卷任务名称")
    task_type = Column(Integer, comment="任务类型，1 表示正评任务，2 表示试评任务，3 表示质检")
    project_id = Column(String(50), comment="项目id")
    subject_id = Column(String(50), comment="科目id")
    paper_id = Column(String(30), comment="试卷id")
    ques_type_code = Column(String(1), comment="阅卷题型简码")
    business_type_name = Column(String(30), comment="业务题型名称")
    ques_order = Column(String(12), comment="试题序号")
    ques_id = Column(String(50), nullable=False, comment="试题id")
    ques_code = Column(String(30), nullable=False, comment="试题编号")
    business_id = Column(String(50), comment="业务题型id")
    score_type = Column(Integer, comment="评分使用轮次：0 单轮次；1 所有轮次")
    select_round_count = Column(Integer, comment="选取轮次")
    select_score_type = Column(Integer, comment="取分方式：0 最高分；1 最低分")
    c_user_id = Column(String(50), comment="创建用户id")
    u_user_id = Column(String(50), comment="更新用户id")
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")


class HumanReadTaskRound(Base):
    __tablename__ = "t_human_read_task_round"
    __table_args__ = {"comment": "任务轮次表"}
    round_id = Column(String(50), primary_key=True, comment="轮次id")
    task_id = Column(String(50), comment="人工阅卷任务id")
    round_count = Column(Integer, comment="轮次")
    process_id = Column(String(50), comment="阅卷主流程id")
    try_mark_ques_num = Column(Integer, comment="试评题数，试评任务参数")
    allow_diff_score = Column(DECIMAL(10, 2), comment="允许偏差分数，试评任务参数")
    fetch_score_way = Column(Integer, comment="专家个数超过1个时该字段不为空，1 为取最高分，2 为取最低分，3 为取平均分, 4 为去除最高和最低分取平均分")
    fetch_score_scope = Column(Integer, comment="平均范围，fetch_score_way 为3时该字段不为空，1 为全分组，2 为高分组，3 为中分组，4 为低分组")
    fetch_score_option = Column(Integer, comment="平均分值计算，fetch_score_way 为3时该字段不为空，1 为向下舍入，2 为向上舍入，3 为四舍五入")
    """
    eg: 
        向下舍入: 1.553 -> 1.55
        向上舍入: 1.553 -> 1.56
        四舍五入: 1.553 -> 1.55
    """
    arbitrate_threshold_type = Column(Integer, comment="仲裁阈值类型，1 表示百分比，2 表示分值")
    arbitrate_threshold = Column(Integer, comment="仲裁阈值")
    arbitrate_score_diff = Column(Integer, comment="仲裁分差取值方式，1 表示平均值，2 表示最大偏差，3 表示最小偏差")
    deviation_threshold_type = Column(Integer, comment="仲裁离差类型，1 表示百分比，2 表示分值")
    arbitrate_deviation = Column(Integer, comment="仲裁离差，表示评卷员和仲裁员之间有效的分数差")
    mark_score_step = Column(DECIMAL(10, 2), nullable=False, comment="评分步长，打分间隔")  # 两位小数
    rate_granularity = Column(Integer, comment="评分粒度：1 整题，2 得分点")
    round_state = Column(Integer, default=1, comment="轮次状态，1 为未发起，2 为正在进行中，3 为已完成，4 为已暂停，5 为已结束，状态不为 1 无法修改和删除")
    launch_time = Column(DateTime(timezone=True), default=None, comment="任务轮次发起时间")
    end_time = Column(DateTime(timezone=True), default=None, comment="任务轮次结束时间")
    c_user_id = Column(String(50), comment="创建人id")
    u_user_id = Column(String(50), comment="更新人id")
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")


class HumanReadRoundGroup(Base):
    __tablename__ = "t_human_read_round_group"
    __table_args__ = {"comment": "人工阅卷任务轮次对应小组表"}
    round_group_id = Column(String(50), primary_key=True, comment="人工阅卷任务对应小组id")
    round_id = Column(String(50), comment="任务轮次对应小组id")
    task_id = Column(String(50), comment="所属人工阅卷任务id")
    group_id = Column(String(50), comment="绑定的人工阅卷小组id")
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")


class HumanRoundDistriAnswer(Base):
    __tablename__ = "t_human_round_distri_answer"
    distri_id = Column(String(50), nullable=False, comment="作答信息分配id")
    round_id = Column(String(50), comment="轮次id")
    stu_secret_num = Column(String(30), index=True, comment="考生密号")
    ques_code = Column(String(50), nullable=False, comment="试题编号")
    ques_id = Column(String(50), comment="试题id")
    answer_id = Column(String(50), comment="作答id，对应t_stu_answer的answer_id")
    is_distri = Column(Integer, comment="是否已分配，0 表示否，1 表示是")
    is_arbitrate = Column(Integer, comment="是否被仲裁，0 表示否，1 表示是")
    round_score = Column(DECIMAL(10, 2), comment="轮次评分分数")
    reviewer_id = Column(String(50), comment="所取分数的评阅员id")
    quality_state = Column(Integer, default=0, comment="质检状态，0 表示未质检，1 表示已质检")
    is_again_mark = Column(Integer, default=0, comment="是否为质检打回重阅的数据，0 表示否，1 表示是")
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")

    __table_args__ = (PrimaryKeyConstraint("distri_id", "ques_code"), {"comment": "每个轮次作答信息分配表，使用 ques_code 进行分区"})  # 创建联合主键


class HumanPersonDistriAnswer(Base):
    __tablename__ = "t_human_person_distri_answer"
    person_distri_id = Column(String(50), nullable=False, comment="每人作答信息分配id")
    round_id = Column(String(50), comment="轮次id")
    round_distri_id = Column(String(50), comment="作答信息分配id")
    user_id = Column(String(50), comment="评卷员id")
    group_id = Column(String(50), comment="阅卷小组id")
    ques_code = Column(String(50), nullable=False, comment="试题编号")
    ques_id = Column(String(50), comment="试题id")
    stu_secret_num = Column(String(30), index=True, comment="考生密号")
    answer_id = Column(String(50), comment="作答id")
    is_answer_marked = Column(Integer, default=0, comment="该作答是否已评，0 未评，非 0 表示质检退回重评次数")
    mark_point_score_list = Column(JSON, comment="评分点分数列表")
    mark_score = Column(DECIMAL(10, 2), comment="评分分数")
    cost_time = Column(Integer, comment="评分耗时（秒）")
    mark_remark = Column(JSON, comment="前端标记数据")
    mark_time = Column(DateTime(timezone=True), comment="评分时间")
    is_effective = Column(Integer, comment="是否有效，无需仲裁的都为有效，需要仲裁的判断仲裁离差是否超过仲裁离差阈值，0 表示无效，1 表示有效")
    is_again_mark = Column(Integer, default=0, comment="是否为质检打回重阅的数据，0 表示否，1 表示是")
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")

    __table_args__ = (PrimaryKeyConstraint("person_distri_id", "ques_code"), {"comment": "每人作答信息分配表，使用 ques_code 进行分区"})  # 创建联合主键


class HumanArbitrateDistriAnswer(Base):
    __tablename__ = "t_human_arbitrate_distri_answer"
    __table_args__ = {"comment": "仲裁分配作答表"}
    arbitrate_id = Column(String(50), primary_key=True, comment="仲裁id")
    group_distri_id = Column(String(50), comment="作答信息分配id")
    round_id = Column(String(50), index=True, comment="轮次id")
    user_id = Column(String(50), comment="仲裁员id")
    answer_id = Column(String(50), comment="作答id")
    mark_point_score_list = Column(JSON, comment="评分点分数列表")
    arbitrate_score = Column(DECIMAL(10, 2), comment="仲裁分数")
    cost_time = Column(Integer, comment="评分耗时（秒）")
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")


class HumanQualityDistriAnswer(Base):
    __tablename__ = "t_human_quality_distri_answer"
    __table_args__ = {"comment": "质检分配作答表"}
    quality_id = Column(String(50), primary_key=True, comment="质检id")
    round_id = Column(String(50), index=True, comment="轮次id")
    round_distri_id = Column(String(50), comment="轮次作答信息分配id")
    person_distri_id = Column(String(50), comment="评阅员作答信息分配id")
    stu_secret_num = Column(String(30), index=True, comment="考生密号")
    answer_id = Column(String(50), comment="作答id")
    mark_point_score_list = Column(JSON, comment="评分点分数列表")
    quality_type = Column(Integer, comment="质检类型，1 表示质检通过，2 表示修改提交，3 表示退回重评")
    quality_score = Column(DECIMAL(10, 2), comment="质检分数")
    quality_result = Column(Integer, comment="质检结果，0 为不通过，1 为通过")
    cost_time = Column(Integer, comment="评分耗时（秒）")
    quality_user_id = Column(String(50), comment="质检员id")
    created_time = Column(DateTime(timezone=True), default=datetime.datetime.now, index=True, comment="创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")


# class SysLog(Base):
#     __tablename__ = "t_sys_log"
#     __table_args__ = {"comment": "系统日志表，暂时用日志文件"}
#     log_id = Column(String(50), primary_key=True, comment="日志id")
#     ip = Column(String(11), comment="用户ip")
#     module = Column(String(50), comment="所属模块")
#     page = Column(String(50), comment="所属页面")
#     api = Column(String(50), comment="接口地址")
#     params = Column(JSON, comment="参数")
#     code = Column(String(10), comment="状态码")
#     result = Column(String(50), comment="返回结果")
#     except_info = Column(String(50), comment="异常信息")
#     resp_time = Column(String(50), comment="响应时长（毫秒）")
#     op_user_id = Column(String(50), comment="操作人id")
#     op_time = Column(DateTime(timezone=True), default=None, comment="操作时间")


class BusinessLog(Base):
    __tablename__ = "t_business_log"
    __table_args__ = {"comment": "业务日志表，只记录任务级的业务操作"}
    log_id = Column(String(50), primary_key=True, comment="日志id")
    ip = Column(String(15), comment="用户ip")
    module = Column(String(50), comment="所属模块")
    page = Column(String(50), comment="所属页面")
    op_type = Column(Integer, comment="操作类型，1 为新增、2 为删除、3 为修改")
    op_content = Column(String(500), comment="操作内容")
    op_user_id = Column(String(50), comment="操作人id")
    op_time = Column(DateTime(timezone=True), default=None, comment="操作时间")
    created_time = Column(DateTime(timezone=True), default=None, comment="日志创建时间")
    updated_time = Column(DateTime(timezone=True), default=None, onupdate=datetime.datetime.now, comment="更新时间")
