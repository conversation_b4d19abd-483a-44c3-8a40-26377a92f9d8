import json
import threading

from fastapi import APIRouter, Depends
from sqlalchemy import and_, select, func, or_
from sqlalchemy.orm import Session
import requests
from apps.ai_set_std.ai_interface import supple_images_desc_req, get_ai_mp
from apps.ai_set_std.set_std_services import get_set_std_ques_condition, check_ai_terminal_alive
from apps.base.global_cache import get_redis_ques_info_dict, get_redis_error_type_id
from apps.base.services import get_import_db_monitor_info
from apps.human_task_manage.models import HumanReadTask, HumanReadTaskRound
from apps.logs.services import create_business_log, get_client_ip
from apps.ques_manage.models import ErrorAnalysisSummary, ErrorType
import uuid
from apps.ques_manage.schemas import SuppleImagesReq, GetQuesListReq, GetQuesDetailReq, EditQuesReq, \
    GetAdjoinQuesDetailReq, GenerateMPReq, GetQuesDetailListReq, \
    GetAllBusinessQuesTypeReq, GetCommonErrorAnalysisReq, GetCreateQuestionReq, GetQualityQuesReq
from apps.ques_manage.services import format_ques_img_data, save_img_desc, cal_ques_used_count, edit_single_ques_info, \
    valid_d_out_of_group, batch_handle_ques_info, combine_image_desc, save_ai_mp, edit_single_small_ques_info, \
    fetch_ques_detail, get_distribute_condition, get_error_summary, create_single_ques, create_single_small_ques
from apps.ques_type.services import get_all_ques_type
from apps.read_paper.common_services import splice_image_path
from helper.rich_text_utils import parse_images_id_base64_dict
from settings import logger, configs
from typing import Any

from apps.models.models import ExamPaper, UserInfo, Project, Subject, ExamQuestion, QuesType, PaperDetail, QuesUsed, \
    BusinessQuesType, SmallExamQuestion, SameStuAnswerGroup
from factory_apps import session_depend
from apps.base.schemas import BaseResponse
from helper import response_utils
from apps.users.services import get_current_user
from utils.utils import sum_with_precision

ques_manage_router = APIRouter()


@ques_manage_router.post(path="/get_ques_list", response_model=BaseResponse, summary="获取试题列表")
async def get_ques_list(query: GetQuesListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取试题列表")
    current_page, page_size, project_id, subject_id, paper_id, ques_type_code_list, bs_ques_type_name, ques_code, ques_name, used_count_list, has_mark_point, business_id_list, ques_desc, task_type, distribute_state = query.model_dump().values()

    if used_count_list and len(used_count_list) != 2:
        return BaseResponse(code=response_utils.params_error, msg="参数错误")

    # 标志试题使用考次是否还处于计算中
    is_calculating = False

    # 判断是否需要重新加载redis的数据并获取图片描述信息
    monitor_id, monitor_type = get_import_db_monitor_info(new_session)
    if monitor_id:
        if check_ai_terminal_alive():
            index = 0
            if monitor_type[index] == "1":
                logger.info("更新redis里的试题信息")
                get_redis_ques_info_dict(new_session, is_reload=True)

                # 开启线程后台静默处理试题信息
                threading.Thread(target=batch_handle_ques_info, args=(monitor_id, monitor_type, index)).start()
        else:
            logger.warning("AI 服务器异常，跳过补充试题图片描述信息")

        index = 1
        if monitor_type[index] == "1":
            logger.info("统计试题使用考次")
            threading.Thread(target=cal_ques_used_count, args=(monitor_id, monitor_type, index)).start()
            is_calculating = False
        elif monitor_type[index] == "2":
            is_calculating = False

    distribute_condition, exists_ques_code_list = get_distribute_condition(new_session, task_type, distribute_state)
    if task_type:
        # all_ques_type = get_all_ques_type(new_session)
        big_ques_query = or_(ExamQuestion.parent_ques_id.is_(None), ExamQuestion.parent_ques_id == "")
    else:
        big_ques_query = True

    limit = current_page - 1
    offset = limit * page_size

    is_paper = new_session.query(ExamPaper).count()
    ques_condition = get_set_std_ques_condition(project_id, subject_id, paper_id, ques_type_code_list, ques_code, None,
                                                None, ques_name, None, ques_desc, None,
                                                has_mark_point, is_paper)
    condition = and_(ques_condition, distribute_condition, big_ques_query)

    select_fields = [
        ExamQuestion.ques_id, ExamQuestion.ques_code, ExamQuestion.ques_desc, QuesType.ques_type_name, UserInfo.name,
        func.sum(func.coalesce(QuesUsed.used_count, 0)).label("used_count"), ExamQuestion.updated_time,
        ExamQuestion.created_time,ExamQuestion.knowledge_show,
        ExamQuestion.ques_mark_point, Project.project_id, Project.project_name,
        Subject.subject_id, Subject.subject_name, ExamQuestion.ques_type_code, ExamQuestion.ques_score,
        ExamQuestion.from_tool, ExamQuestion.small_ques_int
    ]

    f_business_dict = {}

    if is_paper:
        total = new_session.query(ExamQuestion.ques_id) \
            .outerjoin(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
            .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
            .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
            .outerjoin(Project, Project.project_id == ExamPaper.project_id) \
            .outerjoin(Subject, Subject.subject_id == ExamPaper.subject_id) \
            .outerjoin(UserInfo, UserInfo.user_id == ExamQuestion.u_user_id) \
            .outerjoin(QuesUsed, QuesUsed.ques_code == ExamQuestion.ques_code) \
            .filter(and_(ExamQuestion.small_ques_num.is_(None), condition)) \
            .group_by(ExamQuestion.ques_id, QuesUsed.used_count, Project.project_id, Project.project_name,
                      Subject.subject_id, Subject.subject_name).count()

        ques_info = new_session.query(*select_fields) \
            .outerjoin(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
            .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
            .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
            .outerjoin(Project, Project.project_id == ExamPaper.project_id) \
            .outerjoin(Subject, Subject.subject_id == ExamPaper.subject_id) \
            .outerjoin(UserInfo, UserInfo.user_id == ExamQuestion.u_user_id) \
            .outerjoin(QuesUsed, QuesUsed.ques_code == ExamQuestion.ques_code) \
            .filter(and_(ExamQuestion.small_ques_num.is_(None), condition)) \
            .group_by(ExamQuestion.ques_id, QuesUsed.used_count, Project.project_id, Project.project_name,
                      Subject.subject_id, Subject.subject_name) \
            .order_by(ExamQuestion.ques_code) \
            .limit(page_size).offset(offset).all()
    else:
        new_business_id_list = []
        if business_id_list:
            new_business_id_list = [i[:-1] for i in business_id_list]
            new_business_id_list.extend(business_id_list)
        business_condition = BusinessQuesType.business_ques_type_id.in_(
            new_business_id_list) if new_business_id_list else True
        if bs_ques_type_name:
            business_ids = new_session.execute(
                select(BusinessQuesType.parent_ques_type_id, BusinessQuesType.business_ques_type_id).where(
                    BusinessQuesType.ques_type_name.ilike(f"%{bs_ques_type_name}%"))).all()

            all_ids = []
            for parent_id, child_id in business_ids:
                if parent_id is not None:  # 过滤 None 值
                    all_ids.append(parent_id)
                if child_id is not None:
                    all_ids.append(child_id)
            business_ids = list(set(all_ids))
            bs_ques_type_query = BusinessQuesType.business_ques_type_id.in_(business_ids)
        else:
            bs_ques_type_query = True

        select_fields.extend(
            [BusinessQuesType.business_ques_type_id.label("business_id"),
             BusinessQuesType.ques_type_name.label("business_type_name"),
             BusinessQuesType.ques_type_score.label("ques_type_score")])

        total = new_session.query(ExamQuestion.ques_id) \
            .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
            .outerjoin(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
            .outerjoin(Project, Project.project_id == BusinessQuesType.project_id) \
            .outerjoin(Subject, Subject.subject_id == BusinessQuesType.subject_id) \
            .outerjoin(UserInfo, UserInfo.user_id == ExamQuestion.u_user_id) \
            .outerjoin(QuesUsed, QuesUsed.ques_code == ExamQuestion.ques_code) \
            .filter(and_(or_(ExamQuestion.parent_ques_id.is_(None), ExamQuestion.parent_ques_id == ""), condition,
                         business_condition, bs_ques_type_query))
        if used_count_list and len(used_count_list) == 2:
            min_count, max_count = used_count_list
            total =  total.group_by(ExamQuestion.ques_id, Project.project_id, Project.project_name, Subject.subject_id,
                      Subject.subject_name).having(func.sum(func.coalesce(QuesUsed.used_count, 0)).between(min_count, max_count)) \
                     .count()
        else:
            total = total.group_by(ExamQuestion.ques_id, Project.project_id, Project.project_name, Subject.subject_id,
                      Subject.subject_name).count()
        ques_info = new_session.query(*select_fields) \
            .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
            .outerjoin(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
            .outerjoin(Project, Project.project_id == BusinessQuesType.project_id) \
            .outerjoin(Subject, Subject.subject_id == BusinessQuesType.subject_id) \
            .outerjoin(UserInfo, UserInfo.user_id == ExamQuestion.u_user_id) \
            .outerjoin(QuesUsed, QuesUsed.ques_code == ExamQuestion.ques_code) \
            .filter(and_(or_(ExamQuestion.parent_ques_id.is_(None), ExamQuestion.parent_ques_id == ""), condition,
                         business_condition, bs_ques_type_query)) \
            .group_by(ExamQuestion.ques_id, Project.project_id, Project.project_name, Subject.subject_id,
                      Subject.subject_name)
        if used_count_list and len(used_count_list) == 2:
            min_count, max_count = used_count_list
            ques_info = ques_info.having(func.sum(func.coalesce(QuesUsed.used_count, 0)).between(min_count, max_count)).order_by(ExamQuestion.ques_code) \
                .limit(page_size).offset(offset).all()
        else:
            ques_info = ques_info.order_by(ExamQuestion.ques_code) \
            .limit(page_size).offset(offset).all()

        f_business_info = new_session.query(BusinessQuesType.parent_ques_type_id,
                                            BusinessQuesType.ques_type_name).filter(
            BusinessQuesType.parent_ques_type_id.isnot(None)).all()
        if f_business_info:
            for parent_ques_type_id, ques_type_name in f_business_info:
                f_business_dict[parent_ques_type_id] = ques_type_name
    #
    # all_ques_info = get_redis_ques_info_dict(new_session)
    ques_data = []
    for row in ques_info:
        if is_calculating:
            used_count = "计算中"
        else:
            used_count = row.used_count if row.used_count else 0

        ques_id, ques_code = row.ques_id, row.ques_code
        # single_ques_info = all_ques_info[ques_id]

        if task_type:
            distribute_state = 2 if ques_code in exists_ques_code_list else 1
        else:
            distribute_state = None

        if is_paper:
            business_id, business_type_name, ques_type_score = None, None, None
        else:
            business_id, business_type_name, ques_type_score = row.business_id, row.business_type_name, row.ques_type_score
            # if row.ques_type_name == "组合题":
            #     if business_id:
            #         business_type_name = f_business_dict.get(business_id)

        ques_item = {
            "ques_id": ques_id,
            "project_id": row.project_id,
            "project_name": row.project_name,
            "subject_id": row.subject_id,
            "subject_name": row.subject_name,
            "ques_code": ques_code,
            "ques_desc": row.ques_desc["text"],
            "ques_type_code": row.ques_type_code,
            "ques_type_name": row.ques_type_name,
            "ques_mark_point": row.ques_mark_point,
            "business_id": business_id,
            "business_type_name": business_type_name,
            "ques_type_score": ques_type_score,
            "ques_score": row.ques_score,
            "ques_name": row.knowledge_show,
            "u_name": row.name,
            "from_tool": row.from_tool,
            "used_count": used_count,
            "distribute_state": distribute_state,
            "small_ques_int": row.small_ques_int,
            "created_time": row.created_time and str(row.created_time).replace("T", " "),
            "updated_time": row.updated_time and str(row.updated_time).replace("T", " ")
        }
        ques_data.append(ques_item)

    data = {
        "ques_data": ques_data,
        "total": total,
        "is_paper": is_paper
    }
    return BaseResponse(data=data)


@ques_manage_router.post(path="/get_ques_detail", response_model=BaseResponse, summary="获取试题详情")
async def get_ques_detail(query: GetQuesDetailReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取试题详情")
    data, msg = fetch_ques_detail(new_session, query.ques_code)
    if data is None:
        return BaseResponse(code=response_utils.server_error, msg=msg)
    return BaseResponse(data=data, msg=msg)


@ques_manage_router.post(path="/get_ques_detail_list", response_model=BaseResponse, summary="获取试题详情")
async def get_ques_detail(query: GetQuesDetailListReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取试题详情")
    ques_code_list = query.ques_code_list
    result = []
    for ques_code in ques_code_list:
        data, msg = fetch_ques_detail(new_session, ques_code)
        if data is not None:
            result.append(data)
            # return BaseResponse(code=response_utils.server_error, msg=msg)
    return BaseResponse(data=result, msg="获取试题详情成功")


@ques_manage_router.post(path="/get_adjoin_ques_detail", response_model=BaseResponse,
                         summary="获取上一题或下一题试题详情")
async def get_adjoin_ques_detail(query: GetAdjoinQuesDetailReq, user: Any = Depends(get_current_user),
                                 new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取试题详情")
    _, _, project_id, subject_id, paper_id, ques_type_code_list, bs_ques_type_code_list, ques_code, used_count_list, has_mark_point, business_id_list, ques_desc, curr_ques_code, adjoin_type = query.model_dump().values()

    no_field_msg = "暂无上一题" if adjoin_type == 1 else "暂无下一题"
    # ?
    if ques_code:
        return BaseResponse(code=response_utils.no_field, msg=no_field_msg)

    current_ques = new_session.query(ExamQuestion).filter(
        ExamQuestion.ques_code == query.curr_ques_code
    ).first()

    if not current_ques:
        return BaseResponse(code=response_utils.params_error, msg="当前试题不存在")

    adjoin_condition = ExamQuestion.ques_code < curr_ques_code if adjoin_type == 1 else ExamQuestion.ques_code > curr_ques_code

    order_by = ExamQuestion.ques_code.asc()
    if query.adjoin_type == 1:
        order_by = ExamQuestion.ques_code.desc()

    is_paper = new_session.query(ExamPaper).count()
    condition = get_set_std_ques_condition(project_id, subject_id, paper_id, ques_type_code_list, None, None,
                                           None, used_count_list, None, None, None, None, is_paper)

    if is_paper:
        ques_info = new_session.query(ExamQuestion.ques_code) \
            .outerjoin(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
            .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
            .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
            .outerjoin(Project, Project.project_id == ExamPaper.project_id) \
            .outerjoin(Subject, Subject.subject_id == ExamPaper.subject_id) \
            .outerjoin(UserInfo, UserInfo.user_id == ExamQuestion.c_user_id) \
            .outerjoin(QuesUsed, QuesUsed.ques_code == ExamQuestion.ques_code) \
            .filter(and_(ExamQuestion.small_ques_num.is_(None), condition, adjoin_condition)) \
            .order_by(order_by) \
            .group_by(ExamQuestion.ques_id, QuesUsed.used_count).first()
    else:
        new_business_id_list = []
        if business_id_list:
            new_business_id_list = [i[:-1] for i in business_id_list]
            new_business_id_list.extend(business_id_list)
        business_condition = BusinessQuesType.business_ques_type_id.in_(
            new_business_id_list) if new_business_id_list else True

        ques_info = new_session.query(ExamQuestion.ques_code) \
            .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
            .outerjoin(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
            .outerjoin(Project, Project.project_id == BusinessQuesType.project_id) \
            .outerjoin(Subject, Subject.subject_id == BusinessQuesType.subject_id) \
            .outerjoin(UserInfo, UserInfo.user_id == ExamQuestion.c_user_id) \
            .outerjoin(QuesUsed, QuesUsed.ques_code == ExamQuestion.ques_code) \
            .filter(and_(ExamQuestion.small_ques_num.is_(None), condition, business_condition, adjoin_condition)) \
            .order_by(order_by) \
            .group_by(ExamQuestion.ques_id, QuesUsed.used_count).first()
    # print(str(ques_info))
    if not ques_info:
        return BaseResponse(code=response_utils.no_field, msg=no_field_msg)
    # 目标ques_code
    dest_ques_code = ques_info[0]
    data, msg = fetch_ques_detail(new_session, dest_ques_code)
    if data is None:
        return BaseResponse(code=response_utils.server_error, msg=msg)
    return BaseResponse(data=data, msg=msg)


@ques_manage_router.post(path="/edit_ques", response_model=BaseResponse, summary="编辑试题")
async def edit_ques(query: EditQuesReq, user: Any = Depends(get_current_user),
                    new_session: Session = Depends(session_depend),ip:str = Depends(get_client_ip)):
    logger.info(f"{user['username']} 编辑试题")
    curr_user_id = user["user_id"]
    ques_id, ques_code, ques_choices, weight, d_out_of_order_group, standard_answer, standard_answer_html, standard_parse, e_mark_rule, ques_score, ques_mark_point, ques_name, children = query.model_dump().values()
    if d_out_of_order_group:
        result, msg, d_out_of_order_group = valid_d_out_of_group(d_out_of_order_group)
        if not result:
            return BaseResponse(code=response_utils.server_error, msg=msg)

    #如果ques_id为空，新增

    # 编辑主题
    result, msg = edit_single_ques_info(new_session, ques_id, ques_code, ques_choices, weight, d_out_of_order_group,
                                        standard_answer, standard_answer_html, standard_parse, e_mark_rule,ques_score,
                                        ques_mark_point, curr_user_id, ques_name)
    if not result:
        return BaseResponse(code=response_utils.server_error, msg="编辑试题失败")
    # 编辑子题
    if children:
        for child in children:
            child_ques_id, ques_choices, weight, standard_answer, standard_answer_html, standard_parse, e_mark_rule, ques_score, ques_mark_point, d_out_of_order_group = \
                child["ques_id"], child["ques_choices"], child["weight"], child["standard_answer"], child[
                    "standard_answer_html"], child["standard_parse"], child["e_mark_rule"], child["ques_score"], child["ques_mark_point"], \
                    child["d_out_of_order_group"]

            if child_ques_id:
                result, msg = edit_single_ques_info(new_session, child_ques_id, ques_code, ques_choices, weight, d_out_of_order_group,
                                                    standard_answer, standard_answer_html, standard_parse, e_mark_rule,
                                                    ques_score, ques_mark_point, curr_user_id, None)
            else:
                child_ques_id = configs.snow_worker.get_id()
                result, msg =  create_single_ques(new_session, curr_user_id, child_ques_id, None, child["ques_type_code"], child["ques_code"],
                                   ques_score, None, ques_choices, standard_answer, standard_answer_html,
                                   standard_parse, e_mark_rule, ques_mark_point, ques_id, child["small_ques_num"])


            if not result:
                return BaseResponse(code=response_utils.server_error, msg="编辑试题失败")

            # 如果有小小题
            if child["children"]:
                for small_ques in child["children"]:
                    small_ques_id, small_ques_choices, small_ques_standard_answer, small_ques_standard_answer_html, small_ques_standard_choices_code, small_ques_standard_parse, small_ques_mark_point, small_ques_mark_rule, small_ques_num, small_ques_score = \
                        small_ques["ques_id"], small_ques["ques_choices"], small_ques[
                            "standard_answer"], small_ques["standard_answer_html"], small_ques["standard_choices_code"], \
                            small_ques["standard_parse"], small_ques["ques_mark_point"], small_ques["e_mark_rule"], \
                        small_ques["small_ques_num"], small_ques["ques_score"]

                    if small_ques_id:
                        result, msg = edit_single_small_ques_info(new_session, small_ques_id, ques_code, small_ques_choices,
                                                                  small_ques_standard_answer,
                                                                  small_ques_standard_answer_html,
                                                                  small_ques_standard_choices_code,
                                                                  small_ques_standard_parse,
                                                                  small_ques_mark_point,
                                                                  small_ques_mark_rule,
                                                                  small_ques_score,
                                                                  small_ques_num,
                                                                  curr_user_id)
                    else:
                        small_ques_id = configs.snow_worker.get_id()
                        result,msg = create_single_small_ques(new_session, curr_user_id, small_ques_id,
                                                              small_ques["ques_type_code"], small_ques["ques_code"], small_ques_score,
                                                              small_ques_choices,
                                                              small_ques_standard_answer,
                                                              small_ques_standard_answer_html,
                                                              small_ques_standard_parse, small_ques_mark_rule,
                                                              small_ques_mark_point, child_ques_id, small_ques_num,small_ques["small_ques_int"])
                    if not result:
                        return BaseResponse(code=response_utils.server_error, msg="编辑小小题失败")

    new_session.commit()
    logger.info("编辑试题成功")
    # 直接调用业务日志接口
    try:
        from datetime import datetime
        from pytz import timezone as pytz_timezone
        op_time = datetime.now(pytz_timezone('Asia/Shanghai'))
        await create_business_log(
            new_session=new_session,
            ip=ip,
            module="基础管理",
            page="试题管理",
            op_type=3,  # 新增操作
            op_user_id=user.get("user_id") if isinstance(user, dict) else getattr(user, "user_id", "unknown"),
            op_time=op_time,
            op_content=f"编辑试题，试题ID: {ques_id}"
        )
    except Exception as log_error:
        logger.error(f"写入业务日志失败: {log_error}")
    # new_session.commit()
    get_redis_ques_info_dict(new_session, is_reload=True)
    return BaseResponse(msg="编辑试题成功")


@ques_manage_router.post(path="/ai_generate_mark_point", response_model=BaseResponse, summary="AI 生成评分标准")
async def edit_ques(query: GenerateMPReq, user: Any = Depends(get_current_user),
                    new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取 AI 生成评分标准")

    if not check_ai_terminal_alive():
        return BaseResponse(code=response_utils.terminal_error, msg="AI 服务连接失败，请联系管理员")

    subject_name, ques_id, ques_code, small_ques_num, generate_num = query.model_dump().values()

    all_ques_info = get_redis_ques_info_dict(new_session)
    ques_info = all_ques_info[ques_id]

    ques_type_code, ques_material, parent_material_desc, ques_desc, material_desc, ques_score_list, ques_type_score, \
        weight, e_mark_rule, ques_mark_point = ques_info["ques_type_code"], ques_info["ques_material"], \
        ques_info["parent_material_desc"], ques_info["ques_desc"], ques_info["material_desc"], \
        ques_info["ques_score_list"], ques_info["ques_type_score"], ques_info["weight"], ques_info["e_mark_rule"], \
        ques_info["ques_mark_point"]

    ques_desc_text = ques_desc["text"] if ques_desc else ""
    ques_material_text = ques_material["text"] if ques_material else ""
    if ques_score_list:
        ques_score = sum_with_precision(ques_score_list)
    else:
        ques_score = ques_info["ques_type_score"]

    # 合并试题图片描述
    parent_images_desc, sub_images_desc = combine_image_desc(parent_material_desc, material_desc)

    # 给试题添加图片描述
    ques_desc_text += sub_images_desc
    if ques_material_text:
        ques_material_text += parent_images_desc
    else:
        ques_material_text = parent_images_desc

    res_code, data = get_ai_mp(subject_name, ques_id, ques_type_code, ques_desc_text, ques_material_text, ques_score,
                               ques_mark_point, generate_num)

    if parent_material_desc:
        ques_flag = f"{ques_code}（{small_ques_num}）"
    else:
        ques_flag = {ques_code}

    if res_code != 200:
        logger.info(f"试题：{ques_flag} 获取评分标准失败")
        return BaseResponse(code=response_utils.server_error, msg=f"{ques_flag} 获取评分标准失败")

    logger.info(f"试题：{ques_id} 获取评分标准成功")
    generate_mp, mark_rule = data["generate_mp"], data["ques_mark_rule"]

    result = save_ai_mp(new_session, ques_id, ques_mark_point, generate_mp, mark_rule)
    if not result:
        logger.info(f"试题：{ques_flag} 评分标准保存失败")
        return BaseResponse(code=response_utils.server_error, msg=f"{ques_flag} 评分标准保存失败")

    get_redis_ques_info_dict(new_session, is_reload=True)
    return BaseResponse(msg="评分标准保存成功")


@ques_manage_router.post(path="/supple_images_desc", response_model=BaseResponse, summary="AI 补充试题图片描述信息")
async def supple_images_desc(query: SuppleImagesReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 将试题图片发送给 AI 补充描述信息")
    ques_code_list, is_auto_launch, ques_desc_info = query.model_dump().values()
    if not is_auto_launch:
        ques_desc_info = new_session.query(ExamQuestion.ques_code, ExamQuestion.ques_type_code, ExamQuestion.ques_id,
                                           ExamQuestion.ques_desc) \
            .order_by(ExamQuestion.ques_code, ExamQuestion.small_ques_num) \
            .filter(ExamQuestion.ques_code.in_(ques_code_list)).all()

    if not ques_desc_info:
        if is_auto_launch:
            return BaseResponse(msg="无需处理试题")
        return BaseResponse(code=response_utils.params_error, msg="无所选试题")
    ques_code_dict = {}
    for ques_code, ques_type_code, ques_id, ques_desc in ques_desc_info:
        ques_desc, _ = splice_image_path(ques_desc, [])
        ques_desc_html = ques_desc.get("html")
        ques_images_dict = parse_images_id_base64_dict(ques_desc_html)
        if ques_type_code == "F" or ques_images_dict:
            # 提取图片位置信息
            # image_positions = {img_id: data["position"] for img_id, data in ques_images_dict.items()}
            item = {
                "ques_code": ques_code,
                "ques_id": ques_id,
                "ques_type_code": ques_type_code,
                "ques_desc": ques_desc["text"],
                "ques_images_dict": ques_images_dict
            }
            if ques_code in ques_code_dict:
                ques_code_dict[ques_code].append(item)
            else:
                ques_code_dict[ques_code] = [item]

    ques_code_str = "，".join(list(ques_code_dict.keys()))
    ques_img_data = format_ques_img_data(ques_code_dict)
    res_code, data = supple_images_desc_req(ques_img_data)
    if res_code != 200:
        logger.info(f"试题：{ques_code_str} 获取图片补充描述信息失败")
        return BaseResponse(code=response_utils.terminal_error, msg="AI 处理异常")

    logger.info(f"试题：{ques_code_str} 获取图片描述信息成功")
    result = save_img_desc(new_session, data)
    if not result:
        logger.error(f"试题：{ques_code_str} 图片描述保存失败")
        return BaseResponse(code=response_utils.server_error, msg="图片描述保存失败")
    logger.info(f"试题：{ques_code_str} 图片描述保存成功")
    return BaseResponse(msg="图片描述保存成功")


@ques_manage_router.post(path="/all_business_ques_type", response_model=BaseResponse, summary="获取所有业务题型")
async def get_paper(query: GetAllBusinessQuesTypeReq, user: Any = Depends(get_current_user),
                    new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取所有业务题型")
    project_id, subject_id, is_remove_duplicate = query.model_dump().values()

    ques_data = []
    project_query = BusinessQuesType.project_id == project_id if project_id else True
    subject_query = BusinessQuesType.subject_id == subject_id if subject_id else True

    ques_type_info = new_session.query(BusinessQuesType.business_ques_type_id, BusinessQuesType.ques_type_code,
                                       BusinessQuesType.ques_type_name) \
        .filter(and_(project_query, subject_query)).all()
    f_ques_business_id = [i.business_ques_type_id for i in ques_type_info if
                          i.ques_type_code == "F"] if ques_type_info else []
    if ques_type_info:
        ques_type_name_list = []
        for business_id, ques_type_code, ques_type_name in ques_type_info:
            # if ques_type_code != "F":
            #     if business_id[:-1] in f_ques_business_id:
            #         business_ques_type = ques_type_code
            #         ques_type_code = "F"
            #     else:
            #         business_ques_type = ques_type_code
            #     print(business_ques_type)
            #     # 是否对 ques_type_name 去重
            #     if is_remove_duplicate:
            #         if ques_type_name in ques_type_name_list:
            #             continue
            #         else:
            #             ques_type_name_list.append(ques_type_name)
            #     item = {
            #         "ques_type_code": ques_type_code,
            #         "ques_type_name": ques_type_name,
            #         "business_id": business_id,
            #         "business_ques_type": business_ques_type
            #     }
            #     ques_data.append(item)

            if business_id[:-1] in f_ques_business_id:
                business_ques_type = ques_type_code
                ques_type_code = "F"
            else:
                business_ques_type = ques_type_code
            # 是否对 ques_type_name 去重
            if is_remove_duplicate:
                if ques_type_name in ques_type_name_list:
                    continue
                else:
                    ques_type_name_list.append(ques_type_name)
            item = {
                "ques_type_code": ques_type_code,
                "ques_type_name": ques_type_name,
                "business_id": business_id,
                "business_ques_type": business_ques_type
            }
            ques_data.append(item)
    data = {"data": ques_data}
    return BaseResponse(msg="获取所有业务题型成功", data=data)


@ques_manage_router.post(path="/get_common_error_analysis", response_model=BaseResponse, summary="获取常见错误分析")
async def get_common_error_analysis(query: GetCommonErrorAnalysisReq, user: Any = Depends(get_current_user),
                                    new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取常见错误分析")
    ques_id = query.ques_id
    if not ques_id:
        return BaseResponse(code=response_utils.params_error, msg="缺少 ques_id 参数")
    # 1. 查询错误汇总表
    summary_info = (
        new_session.query(ErrorAnalysisSummary.error_message_id)
        .filter(ErrorAnalysisSummary.ques_id == ques_id)
        .first()
    )
    # error_analysis_result = []
    if summary_info:
        error_analysis_result = get_error_summary(new_session, ques_id)
        logger.info("获取常见错误分析成功")
        return BaseResponse(data=error_analysis_result, msg="获取常见错误分析成功")
    # 2. # 2. 如果没有summary_info，查询SameStuAnswerGroup表，获取 AI 错误分析，科目名称和人数
    # 若无汇总，使用 SameStuAnswerGroup 数据并调用外部接口
    if not summary_info:
        error_info = (
            # 获取一个聚类下的相关信息
            new_session.query(
                SameStuAnswerGroup.ai_error_analysis,
                SameStuAnswerGroup.subject_id,
                SameStuAnswerGroup.stu_count,
                SameStuAnswerGroup.same_answer_group_id,
            )
            .filter(
                SameStuAnswerGroup.ques_id == ques_id,
                SameStuAnswerGroup.ai_error_analysis.isnot(None),
            )
            .all()
        )
        if not error_info:
            return BaseResponse(msg="没有错误记录")
        total_stu = new_session.query(func.sum(SameStuAnswerGroup.stu_count)).filter(
            SameStuAnswerGroup.ques_id == ques_id,
            SameStuAnswerGroup.ai_error_analysis.isnot(None)).scalar()
        subject_id = error_info[0].subject_id
        subject = new_session.query(Subject).filter(Subject.subject_id == subject_id).first()
        subject_name = subject.subject_name if subject else ""
        try:
            for ai_error_analysis, subject_id, stu_count, same_answer_group_id in error_info:
                # 3.1 调用外部接口获取错误分类
                try:
                    payload = {
                        "error_analysis": ai_error_analysis,
                        "subject_name": subject_name,
                        "ques_id": ques_id,
                        "same_answer_group_id": same_answer_group_id,
                    }
                    response = requests.post(
                        "http://192.168.0.200:7862/classify_error_analysis",
                        json=payload,
                        timeout=10,
                    )
                    response.raise_for_status()
                    external_response = response.json()
                except Exception as e:
                    logger.error(f"调用外部接口失败: {e}")
                    external_response = {"error_classifications": []}
                # 3.2 处理外部接口返回的错误分类并写入数据库
                for classification in external_response["error_classifications"]:
                    type_name = classification["category"]
                    confidence = classification["confidence"]

                    # 获取错误分类id
                    error_type_id = get_redis_error_type_id(type_name)
                    # error_type = new_session.query(ErrorType).filter(ErrorType.type_name == type_name).first()
                    error_message_id = configs.snow_worker.get_id()
                    summary_entry = ErrorAnalysisSummary(
                        error_message_id=error_message_id,
                        ques_id=ques_id,
                        same_answer_group_id=same_answer_group_id,
                        error_type_id=error_type_id,
                        error_analysis=ai_error_analysis,
                        confidence=confidence,
                        percentage=stu_count * 10000 / total_stu if total_stu else 0,
                    )
                    new_session.add(summary_entry)
            new_session.commit()
        except Exception as e:
            new_session.rollback()
            logger.error(f"获取常见错误分析失败: {e}")
            return BaseResponse(code=response_utils.server_error, msg="获取常见错误分析失败")
    result = get_error_summary(new_session, ques_id)
    return BaseResponse(data=result, msg="获取常见错误分析成功") if result else BaseResponse(
        code=response_utils.server_error, msg="获取常见错误分析失败")


@ques_manage_router.post(path="/img_similarity_analysis", response_model=BaseResponse,
                         summary="绘图题作答图片相似度分析")
async def img_similarity_analysis(query: GetCommonErrorAnalysisReq, user: Any = Depends(get_current_user),
                                  new_session: Session = Depends(session_depend)):
    pass


@ques_manage_router.post(path="/create_question", response_model=BaseResponse, summary="创建试题")
async def create_question(query: GetCreateQuestionReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 创建试题")
    (project_id, subject_id, business_ques_id, ques_type_code, ques_code, ques_score, ques_name, ques_choices,
     standard_answer, standard_answer_html,
     standard_parse, e_mark_rule, ques_mark_point, children) = query.model_dump().values()
    curr_user_id = user["user_id"]

    # 生成试题ID
    ques_id = configs.snow_worker.get_id()

    try:
        # 创建大题
        existing_ques = new_session.query(ExamQuestion.ques_id).filter(ExamQuestion.ques_code == ques_code).first()
        if existing_ques:
            return BaseResponse(code=response_utils.fields_exist, msg=f"试题编码 {ques_code} 已存在")
        result, msg = create_single_ques(new_session, curr_user_id, ques_id, business_ques_id, ques_type_code,
                                         ques_code, ques_score, ques_name, ques_choices, standard_answer,
                                         standard_answer_html, standard_parse, e_mark_rule, ques_mark_point, None)

        if not result:
            return BaseResponse(code=response_utils.server_error, msg="创建试题失败")
        # 处理子题
        if children:
            for child in children:
                parent_id = ques_id
                child_ques_id = configs.snow_worker.get_id()

                # 创建子题记录
                result, msg = create_single_ques(new_session, curr_user_id, child_ques_id, None,
                                                 child["ques_type_code"], ques_code,
                                                 child["ques_score"],
                                                 None, child["ques_choices"], child["standard_answer"],
                                                 child["standard_answer_html"],
                                                 child["standard_parse"], child["e_mark_rule"],
                                                 child["ques_mark_point"], parent_id, child["small_ques_num"])
                if not result:
                    return BaseResponse(code=response_utils.server_error, msg="创建试题失败")
                # 处理小小题
                if child["children"]:
                    for small_ques in child["children"]:
                        small_ques_id = configs.snow_worker.get_id()

                        result, msg = create_single_small_ques(new_session, curr_user_id, small_ques_id,
                                                               small_ques["ques_type_code"], ques_code,
                                                               small_ques["ques_score"], small_ques["ques_choices"],
                                                               small_ques["standard_answer"],
                                                               small_ques["standard_answer_html"],
                                                               small_ques["standard_parse"], small_ques["e_mark_rule"],
                                                               small_ques["ques_mark_point"], child_ques_id,
                                                               small_ques["small_ques_num"], small_ques["small_ques_int"])
                        if not result:
                            return BaseResponse(code=response_utils.server_error, msg="创建小小题失败")

        # 提交事务
        new_session.commit()
        logger.info(f"创建试题成功: {ques_code}")

        # 更新缓存
        get_redis_ques_info_dict(new_session, is_reload=True)

        return BaseResponse(msg="创建试题成功")

    except Exception as e:
        new_session.rollback()
        logger.error(f"创建试题失败: {str(e)}")

        return BaseResponse(code=response_utils.server_error, msg="创建试题失败")


@ques_manage_router.post(path="/delete_question", response_model=BaseResponse, summary="删除试题")
async def delete_question(query: GetQuesDetailReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    # 判断是否是导入
    logger.info(f"{user['username']} 删除试题")
    ques_code = query.ques_code
    if query.ques_id:
        #删除小题/小小题
        ques_ids = query.ques_id
        # for ques_id in  ques_ids:
        for ques_id in ques_ids:
            exam_question = new_session.query(ExamQuestion).filter(ExamQuestion.ques_id == ques_id).first()
            if exam_question:
                new_session.delete(exam_question)
            else:
                small_question = new_session.query(SmallExamQuestion).filter(
                    SmallExamQuestion.ques_id == ques_id).first()
                if small_question:
                    new_session.delete(small_question)
                else:
                    return BaseResponse(code=response_utils.server_error, msg=f"试题ID {ques_id} 不存在")
        new_session.commit()
        return BaseResponse(msg=f"成功删除 {len(ques_ids)} 个试题")
    else:
        exam_questions = new_session.query(ExamQuestion).filter(ExamQuestion.ques_code == ques_code).all()
        if exam_questions:
            # 如果是导入的试题无法删除
            first_question = exam_questions[0]
            if first_question.from_tool == 1:
                return BaseResponse(code=response_utils.server_error, msg="导入的试题无法删除")
            # 删除在试题表和小小题表删除
            try:
                for question in exam_questions:
                    new_session.delete(question)

                    # 删除所有相关的SmallExamQuestion记录
                small_questions = new_session.query(SmallExamQuestion).filter(
                    SmallExamQuestion.ques_code == ques_code).all()
                for small_question in small_questions:
                    new_session.delete(small_question)
                new_session.commit()
                return BaseResponse(msg="删除试题成功")
            except Exception as e:
                logger.error(f"删除试题失败,{e}")
                new_session.rollback()
                return BaseResponse(code=response_utils.server_error, msg="删除试题失败")
    return BaseResponse(code=response_utils.server_error, msg="删除试题失败")

@ques_manage_router.post(path="/get_quality_ques", response_model=BaseResponse, summary="获取质检试题信息")
async def get_quality_ques(query: GetQualityQuesReq, user: Any = Depends(get_current_user),
                          new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取质检试题信息")
    project_id, subject_id, is_quality = query.project_id, query.subject_id, query.is_quality
    project_query = Project.project_id == project_id if project_id else True
    subject_query = Subject.subject_id == subject_id if subject_id else True
    quality_query = HumanReadTaskRound.round_state.in_([2, 4]) if is_quality == 1 else True
    try:
        ques_stmt = new_session.query(ExamQuestion.knowledge_show,ExamQuestion.ques_code,ExamQuestion.ques_score)\
                    .join(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id)\
                    .join(Project, Project.project_id == BusinessQuesType.project_id)\
                    .join(Subject, Subject.subject_id == BusinessQuesType.subject_id)
        if is_quality == 1:
            ques_stmt = ques_stmt.join(HumanReadTask, HumanReadTask.ques_code == ExamQuestion.ques_code)\
                    .join(HumanReadTaskRound, HumanReadTaskRound.task_id == HumanReadTask.task_id)
        print(ques_stmt)
        ques_info = ques_stmt.filter(and_(project_query,subject_query, quality_query))\
                    .all()
        result = []
        for ques in ques_info:
            ques_item = {
                "ques_code": ques.ques_code,
                "ques_name": ques.knowledge_show,
                "ques_score": ques.ques_score
            }
            result.append(ques_item)
        logger.info(f"{user['username']} 获取试题信息成功")
        return BaseResponse(data=result,msg="获取试题信息成功")
    except Exception as e:
        new_session.rollback()
        logger.error(f"获取质检试题信息失败: {str(e)}")
        return BaseResponse(code=response_utils.server_error,msg="获取质检试题信息失败")

