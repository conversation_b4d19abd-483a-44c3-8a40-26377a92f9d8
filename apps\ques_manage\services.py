import json
import traceback

from bs4 import BeautifulSoup
from sqlalchemy import func, and_, select, Integer

from apps.ai_set_std.set_std_services import check_ai_terminal_alive
from apps.base.services import update_monitor_cal, request_api
from apps.human_task_manage.models import HumanReadTask
from apps.models.models import StuAnswer
from apps.ques_manage.models import ErrorAnalysisSummary, ErrorType
from apps.read_paper.common_services import get_ques_score_list
from factory_apps import session_depend
from helper.rich_text_utils import parse_html_entities
from utils.time_func import format_now_time
from utils.utils import sum_with_precision


def get_ques_id_list(new_session, ques_code: str):
    ques_id_list = list(new_session.execute(select(ExamQuestion.ques_id).where(ExamQuestion.ques_code == ques_code)).scalars().all())
    return ques_id_list


def format_ques_img_data(ques_code_dict: dict):
    """
    将试题信息和图片信息格式化为以下结构，xxx 为图片的 base64
    ques_data = [
        {
            "ques_code": "A00274",
            "ques_id": "10001583",
            "ques_type_code": "A",
            "ques_desc": "某生物兴趣小组参观中山大学生物博物馆时，对萨斯特鱼龙化石进行了观察和讨论。下列有关叙述，不正确的是",
            "ques_images_dict": {"EA8FA356C646C11158FC817A0E1B5226.jpg": "xxx"},
            "children": []
        },
        {
            "ques_code": "B00035",
            "ques_id": "10001643",
            "ques_type_code": "F",
            "ques_desc": "（7分）2022年7月11日，坐落在广州市的华南国家植物园正式揭牌，成为我国两个国家级植物园之一。",
            "ques_images_dict": {"14AF3E73337C4F30EBE527F87D25C159.jpg": "xxx"},
            "children": [
                {
                    "ques_code": "B00035",
                    "ques_id": "10001646",
                    "ques_type_code": "D",
                    "ques_desc": "如图是观鸟爱好者在园内拍摄到的黑水鸡给雏鸟喂食的情景。黑水鸡不能自己制造有机物，需从小鱼等食物中获取营养物质，属于该生态系统成分中的______者。",
                    "ques_images_dict": {"EE68004B56711682DEF20064D32BF9EA.jpg": "xxx"}
                }
            ]
        }
    ]
    """
    ques_code_img_detail_list = []
    for ques_code, ques_info in ques_code_dict.items():
        ques_detail = {}
        ques_type_code_list = [i["ques_type_code"] for i in ques_info]

        for ques_item in ques_info:
            if ques_item["ques_type_code"] == "F":
                ques_detail = ques_item
                ques_detail["children"] = []
            else:
                if "F" in ques_type_code_list:
                    ques_detail["children"].append(ques_item)
                else:
                    ques_detail = ques_item
                    ques_detail["children"] = []
        ques_code_img_detail_list.append(ques_detail)

    return {"ques_data": ques_code_img_detail_list}


def save_img_desc(new_session, data):
    """
    保存 AI 返回的试题图片信息
    """
    # """
    #     保存 AI 返回的试题图片信息（按图片索引位置替换）
    #     data格式示例:
    #     [
    #         {
    #             'ques_id': '10001583',
    #             'imgs': [
    #                 {
    #                     'img_id': '0',  # 代表HTML中第1个<img>标签
    #                     'description': '图中是一个生物兴趣小组...',
    #                     'status': 'SUCCESS'
    #                 }
    #             ],
    #             'code': 200,
    #             'msg': 'success'
    #         }
    #     ]
    #     """

    # material_desc = {"EA8FA356C646C11158FC817A0E1B5226.jpg": "图中是一个生物兴趣小组参观中山大学生物博物馆时拍摄的某生物化石的图片，图片中化石上写着萨斯特鱼龙化石。"}
    update_data = []
    try:
        for ques_item in data:
            ques_id = ques_item["ques_id"]
            images_list = ques_item["imgs"]
            images_desc_dict = {}

            # 获取原始试题信息
            ques = new_session.query(ExamQuestion).get(ques_id)
            if not ques:
                continue
            # 解析原始HTML
            soup = BeautifulSoup(ques.ques_desc.get("html", ""), "html.parser")
            img_tags = soup.find_all("img")
            # 创建图片ID到描述的映射
            img_desc_map = {int(img_info['img_id']): img_info['description']
                            for img_info in images_list if img_info.get('img_id').isdigit()}
            # 按图片出现顺序替换为【图片】+描述
            for index, img_tag in enumerate(img_tags):
                if index in img_desc_map:
                    # 创建新的文本节点
                    new_content = soup.new_tag("span")
                    new_content.string = f"【图片】{img_desc_map[index]}"

                    # 替换图片标签
                    img_tag.replace_with(new_content)
            for single_image in images_list:
                images_desc_dict[single_image["img_id"]] = single_image["description"]
            update_item = {
                "ques_id": ques_id,
                "material_desc": images_desc_dict,
                "ai_img_desc": {
                "text": soup.text,
                "html": str(soup)
            }
            }
            update_data.append(update_item)
        new_session.bulk_update_mappings(ExamQuestion, update_data)
        # new_session.query(ExamQuestion).filter(
        #     ExamQuestion.ques_id == ques_id
        # ).update({
        #     ExamQuestion.ques_desc: {
        #         "text": soup.text,
        #         "html": str(soup)
        #     }
        # })
        new_session.commit()
        return True
    except:
        logger.error(traceback.print_exc())
        return False


def cal_ques_used_count(monitor_id, monitor_type, index):
    new_session = next(session_depend())
    # 获取试题的考生数
    ques_info = new_session.query(ExamQuestion.ques_code, func.count(StuAnswer.answer_id)) \
        .outerjoin(StuAnswer, StuAnswer.ques_id == ExamQuestion.ques_id) \
        .group_by(ExamQuestion.ques_code,ExamQuestion.ques_id).all()
    if ques_info:
        update_monitor_cal(new_session, monitor_id, monitor_type, index, "2")
        # new_session.execute(text("TRUNCATE TABLE t_ques_used"))
        new_session.commit()

        add_data = []
        for ques_code, used_count in ques_info:
            add_item = QuesUsed(used_id=configs.snow_worker.get_id(), ques_code=ques_code, used_count=used_count,
                                created_time=format_now_time(), from_tool=2)
            add_data.append(add_item)

        if add_data:
            new_session.add_all(add_data)
            new_session.commit()
    update_monitor_cal(new_session, monitor_id, monitor_type, index, "3")


def edit_single_ques_info(new_session, ques_id, ques_code, ques_choices, weight, d_out_of_order_group, standard_answer, standard_answer_html, standard_parse, e_mark_rule, ques_score, ques_mark_point, curr_user_id,ques_name = None,):
    try:
        ques_info = new_session.query(ExamQuestion.ques_type_code, ExamQuestion.standard_answer, ExamQuestion.e_mark_rule,
                                      ExamQuestion.weight) \
            .filter(ExamQuestion.ques_id == ques_id).first()
        ques_type_code, raw_standard_answer, raw_mark_rule, raw_weight = ques_info or (
            None, None, None, None)

        if ques_type_code != "B":
            standard_choices_code = []
            if standard_answer:
                for answer in standard_answer:
                    for choice in ques_choices:
                        if choice["options"].startswith(answer):
                            standard_choices_code.append(choice["code"])
        else:
            standard_choices_code = standard_answer

        update_standard_answer = [parse_html_entities(i)[0] for i in
                                  standard_answer_html] if standard_answer_html else [parse_html_entities(i)[0] for i in
                                                                                      standard_answer]

        update_dict = {
            ExamQuestion.ques_choices: ques_choices,
            ExamQuestion.standard_answer: update_standard_answer,
            ExamQuestion.standard_answer_html: standard_answer_html if standard_answer_html else [],
            ExamQuestion.standard_choices_code: standard_choices_code,
            ExamQuestion.standard_parse: standard_parse,
            ExamQuestion.e_mark_rule: e_mark_rule,
            ExamQuestion.weight: weight,
            ExamQuestion.d_out_of_order_group: d_out_of_order_group,
            ExamQuestion.ques_mark_point: ques_mark_point,
            ExamQuestion.u_user_id: curr_user_id,
            ExamQuestion.ques_score: ques_score,
            ExamQuestion.knowledge_show: ques_name,
            ExamQuestion.ques_code: ques_code,
        }
        new_session.query(ExamQuestion).filter(ExamQuestion.ques_id == ques_id).update(update_dict)

        if ques_type_code == "D":
            # 填空题权重有变化需要更新试题每空分数
            if weight != raw_weight:
                paper_ques_info = new_session.query(PaperDetail.paper_id, PaperDetail.ques_score_list).filter(
                    PaperDetail.ques_id == ques_id).all()
                if paper_ques_info:
                    for paper_id, raw_ques_score_list in paper_ques_info:
                        ques_score = sum_with_precision(raw_ques_score_list)
                        space_weight_list, total_weight = weight[0], float(weight[1][0])
                        ques_score_list = get_ques_score_list(ques_score, total_weight, space_weight_list)
                        new_session.query(PaperDetail).filter(and_(PaperDetail.paper_id == paper_id, PaperDetail.ques_id == ques_id)).update({
                            PaperDetail.ques_score_list: ques_score_list
                        })
        return True, None
    except Exception as e:
        new_session.rollback()
        logger.error(f"编辑试题失败，{e}")
        logger.error(traceback.print_exc())
        return False, "编辑试题失败"


def edit_single_small_ques_info(new_session, small_ques_id, ques_code, small_ques_choices,
                                small_ques_standard_answer, small_ques_standard_answer_html,
                                small_ques_standard_choices_code, small_ques_standard_parse,small_ques_mark_point,
                                                              small_ques_mark_rule, small_ques_score, small_ques_num, curr_user_id):
    """编辑单个小小题信息"""
    try:
        # 获取小小题记录
        small_ques_info = new_session.query(SmallExamQuestion.ques_type_code, SmallExamQuestion.standard_answer,
                                            SmallExamQuestion.ques_score) \
            .filter(SmallExamQuestion.ques_id == small_ques_id).first()
        ques_type_code, raw_standard_answer, raw_ques_score = small_ques_info or (None,None,None)

        if ques_type_code != "B":  # B表示判断题
            if not small_ques_standard_choices_code:
                standard_choices_code = []
                if small_ques_standard_answer:
                    for answer in small_ques_standard_answer:
                        for choice in small_ques_choices:
                            if choice["options"].startswith(answer):
                                standard_choices_code.append(choice["code"])
                small_ques_standard_choices_code = standard_choices_code
        else:
            # 判断题直接使用标准答案作为选项代码
            small_ques_standard_choices_code = small_ques_standard_answer
        update_standard_answer = [parse_html_entities(i)[0] for i in
                                  small_ques_standard_answer_html] if small_ques_standard_answer_html else [parse_html_entities(i)[0] for i in
                                                                                      small_ques_standard_answer]
        # 更新字段
        update_dict = {
            SmallExamQuestion.ques_choices: small_ques_choices,
            SmallExamQuestion.ques_code: ques_code,
            SmallExamQuestion.standard_answer: update_standard_answer,
            SmallExamQuestion.standard_answer_html: small_ques_standard_answer_html if small_ques_standard_answer_html else [],
            SmallExamQuestion.standard_choices_code: small_ques_standard_choices_code,
            SmallExamQuestion.standard_parse: small_ques_standard_parse,
            SmallExamQuestion.ques_mark_point: small_ques_mark_point,
            SmallExamQuestion.mark_rule: small_ques_mark_rule,
            SmallExamQuestion.ques_score: small_ques_score,
            SmallExamQuestion.u_user_id: curr_user_id,
            SmallExamQuestion.small_ques_num: small_ques_num,
        }
        new_session.query(SmallExamQuestion).filter(SmallExamQuestion.ques_id == small_ques_id).update(update_dict)
        get_redis_ques_small_info_dict(new_session,True)
        return True, None
    except Exception as e:
        new_session.rollback()
        logger.error(f"编辑小小题失败，{e}")
        logger.error(traceback.print_exc())
        return False, "编辑小小题失败"

def create_single_ques(new_session, c_user_id, ques_id, business_ques_id, ques_type_code, ques_code, ques_score, ques_name, ques_choices, standard_answer, standard_answer_html,
     standard_parse, e_mark_rule, ques_mark_point, parent_id=None, small_ques_num=None):
    try:
        # 处理标准答案格式
        if ques_type_code != "B":  # 非判断题
            standard_choices_code = []
            if standard_answer:
                for answer in standard_answer:
                    for choice in ques_choices:
                        if choice["options"].startswith(answer):
                            standard_choices_code.append(choice["code"])
        else:  # 判断题
            standard_choices_code = standard_answer

        update_standard_answer = []
        # 处理富文本内容
        if standard_answer:
            update_standard_answer = [parse_html_entities(i)[0] for i in
                                  standard_answer_html] if standard_answer_html else [parse_html_entities(i)[0] for i in standard_answer]

        #ques_desc
        ques_desc = {
            "html":"",
            "text":""
        }
        # 创建主/小试题记录
        new_ques = ExamQuestion(
            ques_id=ques_id,
            parent_ques_id=parent_id if parent_id else None,
            ques_type_code=ques_type_code,
            ques_code=ques_code,
            ques_score=ques_score,
            standard_choices_code=standard_choices_code,
            knowledge_show=ques_name,
            ques_desc=ques_desc,
            standard_answer=update_standard_answer,
            standard_answer_html=standard_answer_html if standard_answer_html else [],
            standard_parse=standard_parse,
            e_mark_rule=e_mark_rule,
            ques_mark_point=ques_mark_point,
            business_ques_type_id=business_ques_id,
            small_ques_num=small_ques_num if small_ques_num else None,
            c_user_id=c_user_id
        )
        new_session.add(new_ques)
        return True, None
    except Exception as e:
        new_session.rollback()
        logger.error(f"创建试题失败，{e}")
        logger.error(traceback.print_exc())
        return False, "创建试题失败"

def create_single_small_ques(new_session, c_user_id, ques_id, ques_type_code, ques_code, ques_score, ques_choices, standard_answer, standard_answer_html,
     standard_parse, e_mark_rule, ques_mark_point, parent_id, small_ques_num, small_ques_int):
    try:
        # 处理标准答案格式
        if ques_type_code != "B":  # 非判断题
            standard_choices_code = []
            if standard_answer:
                for answer in standard_answer:
                    for choice in ques_choices:
                        if choice["options"].startswith(answer):
                            standard_choices_code.append(choice["code"])
        else:  # 判断题
            standard_choices_code = standard_answer

        # 处理富文本内容
        update_standard_answer = [parse_html_entities(i)[0] for i in
                                  standard_answer_html] if standard_answer_html else [parse_html_entities(i)[0] for i in
                                                                                      standard_answer]

        # 创建主/小试题记录
        new_ques = SmallExamQuestion(
            ques_id=ques_id,
            parent_ques_id=parent_id,
            ques_type_code=ques_type_code,
            ques_code=ques_code,
            ques_score=ques_score,
            standard_choices_code=standard_choices_code,
            standard_answer=update_standard_answer,
            standard_answer_html=standard_answer_html if standard_answer_html else [],
            standard_parse=standard_parse,
            mark_rule=e_mark_rule,
            ques_mark_point=ques_mark_point,
            small_ques_num=small_ques_num,
            small_ques_int=small_ques_int,
            c_user_id=c_user_id
        )
        new_session.add(new_ques)
        get_redis_ques_small_info_dict(new_session,True)
        return True, None
    except Exception as e:
        new_session.rollback()
        logger.error(f"创建试题小小题失败，{e}")
        logger.error(traceback.print_exc())
        return False, "创建试题小小题失败"

def valid_d_out_of_group(d_out_of_order_group):
    """
    校验试题 d_out_of_order_group 格式
    """
    if "，" in d_out_of_order_group:
        return False, "答案分组不允许存在中文逗号", d_out_of_order_group
    try:
        d_out_of_order_group = json.loads(d_out_of_order_group)
    except:
        return False, "答案分组字典格式错误", d_out_of_order_group

    # if not all(isinstance(i, list) for i in d_out_of_order_group):
    #     return False, "答案分组必须为一个二位数组", d_out_of_order_group
    return True, None, d_out_of_order_group


def get_min(elem):
    if isinstance(elem, list):
        return min(elem)
    else:
        return elem


def format_d_out_of_order_group(d_out_of_order_group):
    new_order_group = []
    for item in d_out_of_order_group:
        if isinstance(item, list) and len(item) == 1:
            new_item = item[0]
            new_order_group.append(new_item)
        else:
            new_order_group.append(item)

    sorted_order_group = sorted(new_order_group, key=get_min)
    return sorted_order_group


def format_all_order_group(new_session):
    try:
        now_time = format_now_time()
        order_group_list = []
        ques_info = new_session.query(ExamQuestion.ques_id, ExamQuestion.d_out_of_order_group).filter(
            ExamQuestion.d_out_of_order_group.isnot(None)).all()
        if ques_info:
            for ques_id, d_out_of_order_group in ques_info:
                if d_out_of_order_group:
                    new_order_group = format_d_out_of_order_group(d_out_of_order_group)
                    order_group_list.append({"ques_id": ques_id, "d_out_of_order_group": new_order_group, "updated_time": now_time})

        new_session.bulk_update_mappings(ExamQuestion, order_group_list)
        new_session.commit()
        return True, None
    except:
        logger.error(f"修改试题分组格式失败：{traceback.format_exc()}")
        return False, "修改试题分组格式失败"


def add_f_business_data(new_session):
    """
    补充组合题题干在 t_business_ques_type 的数据
    """
    try:
        business_list = []
        ques_info = new_session.query(ExamQuestion.business_ques_type_id).filter(ExamQuestion.ques_type_code == "F").all()
        if ques_info:
            business_data = get_business_data(new_session)
            row_business_id_list = list(business_data.keys())
            ques_business_id_list = list(set([i[0] for i in ques_info if i[0] not in row_business_id_list]))

            if ques_business_id_list:
                logger.info("补充组合题业务试题数据")
                add_info_dict = {}
                for business_id in ques_business_id_list:
                    for row_business_id, item in business_data.items():
                        if row_business_id[:-1] == business_id:
                            if business_id in add_info_dict:
                                if add_info_dict[business_id]["ques_type_score"] and item["ques_type_score"]:
                                    add_info_dict[business_id]["ques_type_score"] += item["ques_type_score"]
                                else:
                                    add_info_dict[business_id]["ques_type_score"] = item["ques_type_score"]
                            else:
                                add_info_dict[business_id] = {
                                    "project_id": item["project_id"],
                                    "subject_id": item["subject_id"],
                                    "ques_type_score": item["ques_type_score"]
                                }

                for business_id, item in add_info_dict.items():
                    ques_type_score = item["ques_type_score"]
                    if ques_type_score is None:
                        ques_score_list = None
                    else:
                        ques_score_list = [ques_type_score]
                    new_item = BusinessQuesType(business_ques_type_id=business_id, project_id=item["project_id"],
                                                subject_id=item["subject_id"], ques_type_score=ques_type_score,
                                                ques_score_list=ques_score_list, ques_type_code="F", from_tool=1)
                    business_list.append(new_item)
                if business_list:
                    new_session.add_all(business_list)
                    new_session.commit()
                    logger.info("补充组合题业务试题数据成功")
        return True, None
    except:
        logger.error(f"补充组合题业务试题数据失败：{traceback.format_exc()}")
        return False, "补充组合题业务试题数据失败"


def batch_supple_images_desc(new_session):
    """
    获取图片描述信息
    """
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/ques_manage/supple_images_desc"

    try:
        ques_desc_info = new_session.query(ExamQuestion.ques_code, ExamQuestion.ques_type_code, ExamQuestion.ques_id,
                                           ExamQuestion.ques_desc) \
            .order_by(ExamQuestion.ques_code, ExamQuestion.small_ques_num) \
            .filter(and_(ExamQuestion.material_desc.is_(None), ExamQuestion.ques_desc.ilike("%<img%"),
                         ExamQuestion.ques_type_code.in_(["D", "E", "F"]))).all()

        if ques_desc_info:
            batch_size = 4  # 每批处理的数量
            total_batches = (len(ques_desc_info) + batch_size - 1) // batch_size

            for batch in range(total_batches):
                start = batch * batch_size
                end = start + batch_size
                batch_data = ques_desc_info[start:end]
                req_data = {
                    "is_auto_launch": True,
                    "ques_desc_info": [list(i) for i in batch_data]
                }
                res, msg = request_api(url, "POST", req_data, configs.GLOBAL_TOKEN, "补充试题图片描述")
                if res == 0:
                    logger.error(f"补充试题图片描述失败：{msg}")
                    return False, msg
            logger.info("补充所有试题图片描述成功")
        return True, None
    except Exception as e:
        logger.error("补充试题图片描述失败:",e)
        logger.error(traceback.format_exc())
        return False, "补充试题图片描述失败"


def batch_handle_ques_info(monitor_id, monitor_type, index):
    """
    1、修改试题的分组格式
    2、补充组合题题干在 t_business_ques_type 的数据
    3、获取图片描述信息
    """
    new_session = next(session_depend())
    # 处理 d_out_of_order_group 格式
    update_monitor_cal(new_session, monitor_id, monitor_type, index, "2")
    result1, _ = format_all_order_group(new_session)
    result2, _ = add_f_business_data(new_session)
    # 获取图片描述信息
    if check_ai_terminal_alive():
        result3, _ = batch_supple_images_desc(new_session)
    else:
        result3 = False
    if result1 and result2 and result3:
        update_monitor_cal(new_session, monitor_id, monitor_type, index, "3")
    else:
        update_monitor_cal(new_session, monitor_id, monitor_type, index, "1")
    get_redis_ques_info_dict(new_session, is_reload=True)


def get_business_data(new_session):
    """
    获取业务试题类型对应的科目
    """
    business_data = {}
    business_info = new_session.query(BusinessQuesType.business_ques_type_id, BusinessQuesType.project_id,
                                      BusinessQuesType.subject_id, BusinessQuesType.ques_type_score,
                                      BusinessQuesType.ques_score_list, Subject.subject_name) \
        .outerjoin(Subject, Subject.subject_id == BusinessQuesType.subject_id).all()
    if business_info:
        for business_ques_type_id, project_id, subject_id, ques_type_score, ques_score_list, subject_name in business_info:
            business_data[business_ques_type_id] = {
                "project_id": project_id,
                "subject_id": subject_id,
                "subject_name": subject_name,
                "ques_type_score": float(ques_type_score) if ques_type_score is not None else None,
                "ques_score_list": ques_score_list
            }
    return business_data


def combine_image_desc(parent_material_desc, material_desc):
    parent_images_desc = ""
    sub_images_desc = ""
    # 获取试题图片描述
    count = 0
    if parent_material_desc:
        for images_name, desc in parent_material_desc.items():
            count += 1
            parent_images_desc += f"\n图{count}：{desc}"
    count = 0
    if material_desc:
        for images_name, desc in material_desc.items():
            count += 1
            sub_images_desc += f"\n图{count}：{desc}"
    return parent_images_desc, sub_images_desc


# ------------------------------
# 新增通用获取试题详情的函数
# ------------------------------
from sqlalchemy.orm import Session, aliased
from apps.models.models import ExamPaper, UserInfo, Project, Subject, ExamQuestion, QuesType, PaperDetail, QuesUsed, BusinessQuesType, SmallExamQuestion
from apps.read_paper.common_services import splice_image_path
from apps.base.global_cache import get_redis_ques_info_dict, get_redis_ques_small_info_dict
from helper import response_utils
from settings import logger, configs

def fetch_ques_detail(new_session: Session, ques_code: str):
    """
    根据 ques_code 查询试题详情，返回 (data, msg)。
    与原 get_ques_detail 接口保持相同返回结构，只返回 {"ques_detail": ...}。
    """
    # logger.info(f"获取试题详情，ques_code={ques_code}")
    user_info_alias = aliased(UserInfo, name="user_info_alias")
    ques_info = new_session.query(
        ExamQuestion.ques_id,
        ExamQuestion.ques_code,
        ExamQuestion.ques_desc,
        func.group_concat(Subject.subject_name).label("subject_name"),
        Subject.subject_id,
        Project.project_id,
        Project.project_name,
        BusinessQuesType.business_ques_type_id,
        BusinessQuesType.ques_type_name.label("business_ques_type_name"),
        QuesType.ques_type_code,
        QuesType.ques_type_name,
        ExamQuestion.small_ques_num,
        ExamQuestion.small_ques_int,
        ExamQuestion.ques_choices,
        ExamQuestion.parent_ques_id,
        ExamQuestion.ques_mark_point,
        ExamQuestion.standard_answer,
        ExamQuestion.standard_answer_html,
        ExamQuestion.standard_parse,
        ExamQuestion.weight,
        ExamQuestion.d_out_of_order_group,
        UserInfo.name,
        ExamQuestion.e_mark_rule,
        user_info_alias.name.label("u_name"),
        ExamQuestion.material_desc,
        ExamQuestion.created_time,
        ExamQuestion.updated_time,
        ExamQuestion.standard_choices_code,
        ExamQuestion.business_ques_type_id,
        ExamQuestion.ques_score,
        ExamQuestion.knowledge_show,
        ExamQuestion.from_tool
    ).outerjoin(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id) \
        .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id) \
        .outerjoin(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id) \
        .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code) \
        .outerjoin(Project, Project.project_id == BusinessQuesType.project_id) \
        .outerjoin(Subject, Subject.subject_id == BusinessQuesType.subject_id) \
        .outerjoin(UserInfo, UserInfo.user_id == ExamQuestion.c_user_id) \
        .outerjoin(user_info_alias, user_info_alias.user_id == ExamQuestion.u_user_id) \
        .filter(ExamQuestion.ques_code == ques_code) \
        .order_by(ExamQuestion.small_ques_num) \
        .group_by(ExamQuestion.ques_id)
    if not ques_info:
        return None, "无数据"
    all_ques_info = get_redis_ques_info_dict(new_session,is_reload=True)

    # 查询小小题信息
    small_ques_info = (new_session.query(
        SmallExamQuestion.ques_id,
        SmallExamQuestion.parent_ques_id,
        SmallExamQuestion.ques_code,
        QuesType.ques_type_name,
        SmallExamQuestion.ques_type_code,
        SmallExamQuestion.small_ques_num,
        SmallExamQuestion.ques_choices,
        SmallExamQuestion.ques_score,
        SmallExamQuestion.ques_mark_point,
        SmallExamQuestion.mark_rule,
        SmallExamQuestion.standard_answer,
        SmallExamQuestion.standard_answer_html,
        SmallExamQuestion.standard_choices_code,
        SmallExamQuestion.standard_parse,
        SmallExamQuestion.small_ques_int
    ).outerjoin(QuesType,QuesType.ques_type_code == SmallExamQuestion.ques_type_code)
        .filter(SmallExamQuestion.parent_ques_id.in_([row.ques_id for row in ques_info])) \
        .order_by(func.cast(SmallExamQuestion.small_ques_int, Integer)).all())

    # 将小小题按父题ID分组
    small_ques_dict = {}
    for small_ques in small_ques_info:
        parent_id = small_ques.parent_ques_id
        small_ques_dict.setdefault(parent_id, []).append({
            "ques_id": small_ques.ques_id,
            "ques_code": small_ques.ques_code,
            "ques_type_name": small_ques.ques_type_name,
            "ques_type_code": small_ques.ques_type_code,
            "ques_order": small_ques.small_ques_int,
            "e_mark_rule": small_ques.mark_rule,
            "ques_mark_point": small_ques.ques_mark_point,
            "small_ques_num": small_ques.small_ques_num,
            "ques_choices": small_ques.ques_choices,
            "ques_score": str(small_ques.ques_score) if small_ques.ques_score else None,
            "standard_answer": small_ques.standard_answer,
            "standard_answer_html": small_ques.standard_answer_html,
            "standard_choices_code": small_ques.standard_choices_code,
            "standard_parse": small_ques.standard_parse,
        })

    ques_data = []
    for row in ques_info:
        ques_id, ques_desc, ques_choices = row.ques_id, row.ques_desc, row.ques_choices
        ques_desc, ques_choices = splice_image_path(ques_desc, ques_choices)
        ques_info_dict = all_ques_info[ques_id]
        item = {
            "ques_id": ques_id,
            "ques_code": row.ques_code,
            "ques_desc": ques_desc,
            "subject_name": ques_info_dict["subject_name"] if ques_info_dict["subject_name"] else "加载中...",
            "subject_id": row.subject_id,
            "project_name": row.project_name,
            "project_id": row.project_id,
            "business_ques_type_id": row.business_ques_type_id,
            "business_ques_type_name": row.business_ques_type_name,
            "ques_type_code": row.ques_type_code,
            "ques_type_name": row.ques_type_name,
            "small_ques_int": row.small_ques_int,
            "knowledge_show": row.knowledge_show,
            "total_score": get_redis_ques_info_dict(new_session)[ques_id]["ques_type_score"],
            "small_ques_num": row.small_ques_num,
            "ques_choices": ques_choices,
            "parent_ques_id": row.parent_ques_id,
            "e_mark_rule": row.e_mark_rule,
            "ques_score": row.ques_score,
            "ques_mark_point": row.ques_mark_point,
            "standard_answer": row.standard_answer,
            "standard_answer_html": row.standard_answer_html,
            "weight": row.weight,
            "standard_parse": row.standard_parse,
            "standard_choices_code": row.standard_choices_code,
            "d_out_of_order_group": str(row.d_out_of_order_group) if row.d_out_of_order_group else None,
            "material_desc": row.material_desc,
            "c_name": row.name,
            "u_name": row.u_name,
            "from_tool": row.from_tool,
            "created_time": row.created_time and str(row.created_time).replace("T", " "),
            "updated_time": row.updated_time and str(row.updated_time).replace("T", " "),
        }
        parent_id = row.ques_id
        item["children"] = small_ques_dict.get(parent_id, [])
        ques_data.append(item)
    # 组装最终结构
    # ques_detail = {}
    # if len(ques_data) == 1:
    #     ques_detail = ques_data[0]
    #     ques_detail["children"] = []
    # else:
    #     f_total_score = 0
    #     for item in ques_data:
    #         if item["ques_type_code"] == "F":
    #             ques_detail = item
    #             ques_detail["children"] = []
    #         else:
    #             if item["total_score"] is not None:
    #                 f_total_score += item["total_score"]
    #             else:
    #                 f_total_score = None
    #             ques_detail["children"].append(item)
    #     for item in ques_data:
    #         if item["ques_type_code"] == "F":
    #             item["total_score"] = f_total_score
        # 组装最终结构 - 支持大题和小题都可能是组合题的逻辑
    def build_question_tree(ques_data):
        """构建试题树结构，支持多层嵌套"""
        # 按ques_id建立映射
        ques_map = {item["ques_id"]: item for item in ques_data}

        # 按parent_ques_id分组，用于快速查找子题
        children_map = {}
        for item in ques_data:
            parent_id = item["parent_ques_id"]
            if parent_id not in children_map:
                children_map[parent_id] = []
            children_map[parent_id].append(item)

        # 递归计算组合题分数
        def calculate_total_score(ques_item):
            """递归计算组合题的总分"""
            if ques_item["ques_type_code"] != "F":
                return ques_item.get("total_score", 0) or 0

            # 如果是组合题，计算所有直接子题的分数总和
            total_score = 0
            children = children_map.get(ques_item["ques_id"], [])

            for child in children:
                if child["ques_type_code"] == "F":
                    # 子题也是组合题，递归计算
                    child_score = calculate_total_score(child)
                    total_score += child_score
                else:
                    # 子题是普通题，直接加分数
                    child_score = child.get("total_score", 0) or 0
                    total_score += child_score

            # 更新组合题的总分
            ques_item["total_score"] = total_score
            return total_score

        # 构建完整的树结构
        root_questions = []

        # 找到所有根节点（parent_ques_id为None的试题）
        root_items = [item for item in ques_data if (item["parent_ques_id"] is None or item["parent_ques_id"]=="")]
        # 为每个根节点构建树结构
        for root_item in root_items:
            # 递归构建子树
            def build_children(item):
                # 为当前题设置children
                children = children_map.get(item["ques_id"], [])
                item["children"] = []

                # 为每个子题设置正确的children
                for child in children:
                    item["children"].append(child)
                    # 如果是小小题，直接添加到children中
                    # if child["ques_type_code"] != "F":
                    #     item["children"].append(child)
                    # else:
                    #     # 如果是组合题，递归处理
                    #     child_copy = child.copy()
                    #     build_children(child_copy)
                    #     item["children"].append(child_copy)

                # 计算当前题的总分
                calculate_total_score(item)

            # 构建根节点的子树
            root_copy = root_item.copy()
            build_children(root_copy)
            root_questions.append(root_copy)
        # 如果只有一个根节点，直接返回该节点
        if len(root_questions) == 1:
            return root_questions[0]
        else:
            # 如果有多个根节点，返回第一个作为主节点
            # 这种情况应该很少见，但为了兼容性保留
            return root_questions[0] if root_questions else {}

        # 构建最终的试题树结构
    ques_detail = build_question_tree(ques_data)
    data = {"ques_detail": ques_detail}
    return data, "获取试题详情成功"


def save_ai_mp(new_session, ques_id: str, ques_mark_point: list, generate_mp: list, mark_rule: str):
    """
    保存 AI 生成的评分标准
    """
    try:
        # 添加 AI 生成标识
        new_mark_point = [
            {
                "point": i["point"],
                "score": i["score"],
                "is_ai": 1
            }
            for i in generate_mp
        ]

        ques_mark_point = ques_mark_point if ques_mark_point is not None else []
        all_mark_point = [*ques_mark_point, *new_mark_point]

        new_session.query(ExamQuestion).filter(ExamQuestion.ques_id == ques_id).update({
            ExamQuestion.ques_mark_point: all_mark_point,
            ExamQuestion.e_mark_rule: mark_rule
        })
        new_session.commit()
        return True
    except:
        return False


def get_distribute_condition(new_session, task_type, distribute_state):
    distribute_condition = True
    exists_ques_code_list = []
    if task_type:
        # 检查是否已分配了人工阅卷任务
        task_info = new_session.query(HumanReadTask.ques_code).filter(HumanReadTask.task_type == task_type).all()
        exists_ques_code_list = [i[0] for i in task_info] if task_info else []

        if distribute_state:
            if distribute_state == 1:
                distribute_condition = ~ExamQuestion.ques_code.in_(exists_ques_code_list) if exists_ques_code_list else True
            else:
                distribute_condition = ExamQuestion.ques_code.in_(exists_ques_code_list) if exists_ques_code_list else True
    return distribute_condition, exists_ques_code_list


def get_error_summary(new_session,ques_id):
    error_analysis_result = []
    #返回示例
    """
        获取题目错误分析汇总（前 3 大错误类型，每类最多 3 条分析文本）。
        返回示例:
        [
            {
                "ques_id": 123,
                "type_name": "概念错误",
                "percentage": 12.34,   # 百分比，保留两位小数
                "analysis_texts": ["分析1", "分析2", "分析3"]
            },
            ...
        ]
    """
    if not ques_id:
        return []
    try:
        top_errors = (
            new_session.query(
                func.coalesce(ErrorAnalysisSummary.percentage, 0).label("pct"),
                ErrorAnalysisSummary.error_type_id,
                ErrorType.type_name,
            )
            .join(ErrorType, ErrorAnalysisSummary.error_type_id == ErrorType.type_id)
            .filter(ErrorAnalysisSummary.ques_id == ques_id)
            .order_by(func.coalesce(ErrorAnalysisSummary.percentage, 0).desc())
            .limit(3)
            .all()
        )
        for err in top_errors:
            type_name = err.type_name
            error_type_id = err.error_type_id
            same_type_analyses = (
                new_session.query(ErrorAnalysisSummary.error_analysis)
                .filter(
                    ErrorAnalysisSummary.ques_id == ques_id,
                    ErrorAnalysisSummary.error_type_id == error_type_id,
                )
                .order_by(func.coalesce(ErrorAnalysisSummary.percentage, 0).desc())
                .limit(3)
                .all()
            )
            analysis_texts = [a[0] for a in same_type_analyses]
            actual_count = len(analysis_texts)  # 实际返回的分析文本数量
            percentage  = round((err.pct or 0) / 100, 2)
            error_analysis_result.append(
                {
                    "ques_id": ques_id,
                    "type_name": type_name,
                    "percentage": percentage,
                    "analysis_texts": analysis_texts,
                    "actual_count":actual_count
                }
            )
        return error_analysis_result
    except Exception as e:
        logger.error(f"获取错误汇总失败 ques_id={ques_id}: {e}")
        return []
