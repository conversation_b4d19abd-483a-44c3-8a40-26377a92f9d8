"""
加载全局共享数据到 redis 中，以减少数据库交互，并提升系统性能和响应速度
"""

import json
import os
import threading
from typing import Optional

from sqlalchemy import func, and_

from apps.base.services import delete_keys_with_prefix
from apps.models.models import ExamQuestion, StuAnswer, ManualReadTask, ManualDistributeAnswer, ManualReadTaskGroup, ExamPaper, PaperDetail, Subject, QuesType, BusinessQuesType, SmallExamQuestion
from apps.ques_manage.models import ErrorType
from apps.read_paper.common_services import splice_image_path
from factory_apps import session_depend, redis_session
from settings import configs
from sqlalchemy import text, select, and_
from apps.human_repeat_mark.models import HumanRepeatRoundDetail


def get_manual_task_info(condition=True):
    """
    获取人工阅卷任务信息
    """
    new_session = next(session_depend())
    manual_task_info = (
        new_session.query(ManualReadTask.m_read_task_id, ManualReadTask.project_id, ManualReadTask.subject_id, ManualReadTask.paper_id, ManualReadTask.ques_id, ManualReadTask.ques_code)
        .filter(condition)
        .all()
    )
    return manual_task_info


def set_all_small_ques_count(new_session):
    """
    获取并设置所有试题的小题数量
    """
    small_ques_info = new_session.query(ExamQuestion.ques_code, func.coalesce(func.max(ExamQuestion.small_ques_num), 0)).group_by(ExamQuestion.ques_code)
    if small_ques_info:
        redis = next(redis_session())
        for ques_code, small_ques_count in small_ques_info:
            redis.set(f"small_ques_count_{ques_code}", small_ques_count)


def get_small_ques_count(ques_code):
    """
    获取试题的小题数量
    """
    redis = next(redis_session())
    small_ques_count_str = redis.get(f"small_ques_count_{ques_code}")
    if small_ques_count_str:
        small_ques_count = int(small_ques_count_str)
    else:
        new_session = next(session_depend())
        small_ques_count = new_session.query(func.coalesce(func.max(ExamQuestion.small_ques_num), 0)).filter(ExamQuestion.ques_code == ques_code).scalar()
        redis.set(f"small_ques_count_{ques_code}", small_ques_count)
    return small_ques_count


def get_student_count_by_stu_answer(new_session, project_id: str, subject_id: str, paper_id: Optional[str], ques_id: str, ques_code: Optional[str] = None):
    """
    根据考生作答表获取考生数量
    """
    paper_condition = StuAnswer.paper_id == paper_id if paper_id else True
    if ques_code:
        ques_id_info = new_session.query(ExamQuestion.ques_id).filter(ExamQuestion.ques_code == ques_code).all()
        ques_id_list = [i[0] for i in ques_id_info]
        condition = and_(StuAnswer.ques_id.in_(ques_id_list), StuAnswer.project_id == project_id, StuAnswer.subject_id == subject_id, paper_condition)
    else:
        condition = and_(StuAnswer.ques_id == ques_id, StuAnswer.project_id == project_id, StuAnswer.subject_id == subject_id, paper_condition)

    stu_count = new_session.query(StuAnswer.allow_exam_num.distinct()).filter(condition).count()
    return stu_count


def set_single_stu_num_thread(m_read_task_id, project_id, subject_id, paper_id, ques_id, ques_code=None):
    """
    设置单个任务的学生数量线程
    """
    new_session = next(session_depend())
    redis = next(redis_session())
    stu_count = get_student_count_by_stu_answer(new_session, project_id, subject_id, paper_id, ques_id, ques_code)
    redis.set(f"stu_count_{m_read_task_id}", stu_count)


def set_single_task_state(m_read_task_id, task_state):
    """
    设置单个任务的任务状态
    """
    redis = next(redis_session())
    redis.set(f"task_state_{m_read_task_id}", task_state)


def get_task_state_by_task_id(new_session, m_read_task_id):
    """
    通过任务id获取任务状态
    """
    redis = next(redis_session())
    task_state = redis.get(f"task_state_{m_read_task_id}")
    if not task_state:
        task_state = new_session.query(ManualReadTask.task_state).filter(ManualReadTask.m_read_task_id == m_read_task_id).scalar()
        redis.set(f"task_state_{m_read_task_id}", task_state)
    return int(task_state) if task_state else None


def set_stu_num_by_task_id(m_read_task_id: Optional[str] = None):
    """
    获取并设置所有或者指定任务的学生数量
    """
    condition = ManualReadTask.m_read_task_id == m_read_task_id if m_read_task_id else True
    manual_task_info = get_manual_task_info(condition)
    if manual_task_info:
        for m_read_task_id, project_id, subject_id, paper_id, ques_id, ques_code in manual_task_info:
            threading.Thread(target=set_single_stu_num_thread, args=(m_read_task_id, project_id, subject_id, paper_id, ques_id, ques_code)).start()


def get_stu_num_by_task_id(new_session, m_read_task_id, project_id, subject_id, paper_id, ques_id, ques_code):
    """
    通过任务id获取学生数量
    """
    redis = next(redis_session())
    stu_count = redis.get(f"stu_count_{m_read_task_id}")
    if stu_count:
        stu_count = int(redis.get(f"stu_count_{m_read_task_id}"))
    else:
        stu_count = get_student_count_by_stu_answer(new_session, project_id, subject_id, paper_id, ques_id)
        redis.set(f"stu_count_{m_read_task_id}", stu_count)
    return stu_count


def get_group_id_list_by_task_id(new_session, m_read_task_id):
    """
    通过任务id获取任务状态
    """
    redis = next(redis_session())
    group_id_json = redis.get(f"group_id_list_{m_read_task_id}")
    if not group_id_json:
        group_id_info = new_session.query(ManualReadTaskGroup.manual_group_id).filter(ManualReadTaskGroup.m_read_task_id == m_read_task_id).all()
        group_id_list = [i[0] for i in group_id_info] if group_id_info else []
        if group_id_list:
            redis.set(f"group_id_list_{m_read_task_id}", json.dumps(group_id_list))
    else:
        group_id_list = json.loads(group_id_json)
    return group_id_list


def set_manual_mark_info(condition=True):
    """
    设置阅卷任务每个小组需要评分的考生，校验考生是否属于该专家评分
    """
    # mark_dict = {
    #     m_read_task_id: {
    #         group_id: {
    #             distri_answer_id: True
    #         }
    #     }
    # }
    mark_distri_manage = {}
    new_session = next(session_depend())
    manual_task_info = (
        new_session.query(ManualReadTask.m_read_task_id, ManualDistributeAnswer.manual_group_id, ManualDistributeAnswer.distri_answer_id)
        .join(ManualDistributeAnswer, ManualDistributeAnswer.m_read_task_id == ManualReadTask.m_read_task_id)
        .filter(condition)
        .all()
    )
    for m_read_task_id, group_id, distri_answer_id in manual_task_info:
        if m_read_task_id in mark_distri_manage:
            if group_id in mark_distri_manage[m_read_task_id]:
                mark_distri_manage[m_read_task_id][group_id][distri_answer_id] = True
            else:
                mark_distri_manage[m_read_task_id][group_id] = {distri_answer_id: True}
        else:
            mark_distri_manage[m_read_task_id] = {group_id: {distri_answer_id: True}}
    redis = next(redis_session())
    redis.set("mark_distri_manage", json.dumps(mark_distri_manage))


def load_op_engine_file_paths(redis):
    """
    从磁盘加载评分引擎需要的考生素材作答数据
    """
    file_paths = {}
    for dir_path, _, filenames in os.walk(configs.OP_MATERIAL_PATH):
        for filename in filenames:
            if filename.endswith(".op"):
                full_path = os.path.join(dir_path, filename)
                part_path = full_path.split("\\")
                file_paths[filename] = "\\".join(part_path[:-2])
    redis.set("op_engine_answer_path", json.dumps(file_paths))
    return file_paths


def get_op_engine_answer_path():
    """
    从 redis 获取评分引擎需要的考生素材作答数据
    """
    redis = next(redis_session())
    op_engine_answer_path = redis.get("op_engine_answer_path")
    if op_engine_answer_path:
        op_engine_answer_path = json.loads(op_engine_answer_path)
        return op_engine_answer_path
    op_engine_answer_path = load_op_engine_file_paths(redis)
    return op_engine_answer_path


def load_manual_op_file_paths(redis):
    """
    从磁盘加载操作题人工阅卷需要的考生素材作答数据
    """
    file_paths = {}
    for dir_path, _, filenames in os.walk(configs.OP_MATERIAL_PATH):
        for filename in filenames:
            if filename.endswith(".op"):
                full_path = os.path.join(dir_path, filename)
                file_paths[filename] = full_path
    redis.set("op_manual_op_file_path", json.dumps(file_paths))
    return file_paths


def get_manual_op_file_path():
    """
    从 redis 获取操作题人工阅卷需要的考生素材作答数据
    """
    redis = next(redis_session())
    op_manual_op_file_path = redis.get("op_manual_op_file_path")
    if op_manual_op_file_path:
        op_manual_op_file_path = json.loads(op_manual_op_file_path)
        return op_manual_op_file_path
    op_manual_op_file_path = load_manual_op_file_paths(redis)
    return op_manual_op_file_path


def get_redis_ques_info_dict(new_session, is_reload=False):
    """
    将试题信息加载到redis里
    """
    redis = next(redis_session())
    ques_info_json = redis.get(f"ques_info_by_id")
    sql = text("set SESSION sort_buffer_size=4*1024*1024;")
    new_session.execute(sql)

    if is_reload or not ques_info_json:
        ques_info = (
            new_session.query(
                ExamQuestion.ques_id,
                ExamQuestion.ques_score,
                ExamQuestion.ques_code,
                ExamQuestion.parent_ques_id,
                ExamQuestion.ques_code,
                ExamQuestion.ques_type_code,
                ExamQuestion.small_ques_num,
                ExamQuestion.ques_desc,
                ExamQuestion.ques_choices,
                ExamQuestion.ques_difficulty,
                ExamQuestion.material_desc,
                ExamQuestion.standard_answer,
                ExamQuestion.standard_answer_html,
                ExamQuestion.synonymous_answer,
                ExamQuestion.exclude_answer,
                ExamQuestion.standard_choices_code,
                ExamQuestion.d_out_of_order_group,
                ExamQuestion.weight,
                ExamQuestion.standard_parse,
                ExamQuestion.e_mark_rule,
                ExamQuestion.ques_mark_point,
                QuesType.ques_type_name,
                BusinessQuesType.business_ques_type_id,
                BusinessQuesType.ques_type_score,
                PaperDetail.ques_score_list,
                BusinessQuesType.subject_id.label("qeus_type_subject_id"),
                func.group_concat(ExamPaper.subject_id).label("subject_id"),
            )
            .outerjoin(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id)
            .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id)
            .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code)
            .outerjoin(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id)
            .group_by(ExamQuestion.ques_id, PaperDetail.ques_score_list)
            .all()
        )
        ques_info = (
            new_session.query(
                ExamQuestion.ques_id,
                ExamQuestion.ques_score,
                ExamQuestion.ques_code,
                ExamQuestion.parent_ques_id,
                ExamQuestion.ques_code,
                ExamQuestion.ques_type_code,
                ExamQuestion.small_ques_num,
                ExamQuestion.ques_desc,
                ExamQuestion.ques_choices,
                ExamQuestion.ques_difficulty,
                ExamQuestion.material_desc,
                ExamQuestion.standard_answer,
                ExamQuestion.standard_answer_html,
                ExamQuestion.synonymous_answer,
                ExamQuestion.exclude_answer,
                ExamQuestion.standard_choices_code,
                ExamQuestion.d_out_of_order_group,
                ExamQuestion.weight,
                ExamQuestion.standard_parse,
                ExamQuestion.e_mark_rule,
                ExamQuestion.ques_mark_point,
                QuesType.ques_type_name,
                BusinessQuesType.business_ques_type_id,
                BusinessQuesType.ques_type_score,
                PaperDetail.ques_score_list,
                BusinessQuesType.subject_id.label("qeus_type_subject_id"),
                func.group_concat(ExamPaper.subject_id).label("subject_id"),
            )
            .outerjoin(PaperDetail, PaperDetail.ques_id == ExamQuestion.ques_id)
            .outerjoin(ExamPaper, ExamPaper.paper_id == PaperDetail.paper_id)
            .outerjoin(QuesType, QuesType.ques_type_code == ExamQuestion.ques_type_code)
            .outerjoin(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id)
            .group_by(ExamQuestion.ques_id, PaperDetail.ques_score_list)
            .all()
        )

        # 查询小小题信息
        small_ques_info = (
            new_session.query(
                SmallExamQuestion.ques_id,
                SmallExamQuestion.parent_ques_id,
                SmallExamQuestion.ques_code,
                QuesType.ques_type_name,
                SmallExamQuestion.ques_type_code,
                SmallExamQuestion.small_ques_num,
                SmallExamQuestion.ques_choices,
                SmallExamQuestion.ques_score,
                SmallExamQuestion.ques_mark_point,
                SmallExamQuestion.mark_rule,
                SmallExamQuestion.standard_answer,
                SmallExamQuestion.standard_answer_html,
                SmallExamQuestion.standard_choices_code,
                SmallExamQuestion.standard_parse,
            )
            .outerjoin(QuesType, QuesType.ques_type_code == SmallExamQuestion.ques_type_code)
            .filter(SmallExamQuestion.parent_ques_id.in_([row.ques_id for row in ques_info]))
            .order_by(SmallExamQuestion.small_ques_num)
            .all()
        )
        # 将小小题按父题ID分组
        small_ques_dict = {}
        for small_ques in small_ques_info:
            parent_id = small_ques.parent_ques_id
            small_ques_dict.setdefault(parent_id, []).append(
                {
                    "sub_ques_id": small_ques.ques_id,
                    "sub_ques_mark_rule": small_ques.mark_rule,
                    "sub_ques_mark_point": small_ques.ques_mark_point,
                    "sub_ques_std_score": str(small_ques.ques_score) if small_ques.ques_score else None,
                    "sub_ques_std_answer": small_ques.standard_answer,
                    "is_multiple": 1,
                    "standard_answer_html": small_ques.standard_answer_html,
                    "standard_choices_code": small_ques.standard_choices_code,
                    "standard_parse": small_ques.standard_parse,
                }
            )

        question_dict = {}
        if ques_info:
            subject_dict = {}
            subject_info = new_session.query(Subject.subject_id, Subject.subject_name, Subject.remark).all()
            if subject_info:
                for subject_id, subject_name, remark in subject_info:
                    subject_dict[subject_id] = {"subject_name": subject_name, "remark": remark}

            for ques in ques_info:
                ques_id = ques.ques_id
                ques_desc, ques_choices = splice_image_path(ques.ques_desc, ques.ques_choices)
                # material_desc, _ = splice_image_path(ques.material_desc, []) if ques.material_desc else None, None
                subject_id = ques.subject_id.split(",")[0] if ques.subject_id else ques.qeus_type_subject_id

                question_dict[ques_id] = {
                    "ques_code": ques.ques_code,
                    "ques_score": float(ques.ques_score) if ques.ques_score is not None else 0,
                    "parent_ques_id": ques.parent_ques_id,
                    "ques_type_code": ques.ques_type_code,
                    "ques_type_name": ques.ques_type_name,
                    "small_ques_num": ques.small_ques_num,
                    # "ques_desc": ques_desc.get("text"),
                    "ques_desc": ques.ques_desc,
                    "ques_choices": ques_choices,
                    "business_ques_type_id": ques.business_ques_type_id,
                    "ques_type_score": float(ques.ques_type_score) if ques.ques_type_score is not None else None,
                    "ques_score_list": ques.ques_score_list,
                    "ques_difficulty": ques.ques_difficulty,
                    "material_desc": ques.material_desc if ques.material_desc else {},
                    "standard_answer": ques.standard_answer,
                    "standard_answer_html": ques.standard_answer_html,
                    "standard_choices_code": ques.standard_choices_code,
                    "synonymous_answer": ques.synonymous_answer,
                    "exclude_answer": ques.exclude_answer,
                    "d_out_of_order_group": ques.d_out_of_order_group,
                    "weight": ques.weight,
                    "standard_parse": ques.standard_parse,
                    "e_mark_rule": ques.e_mark_rule,
                    "ques_mark_point": ques.ques_mark_point,
                    "subject_id": subject_id,
                    "subject_name": subject_dict.get(subject_id, {}).get("subject_name"),
                    "subject_info": subject_dict.get(subject_id, {}).get("remark"),
                    "ques_material": None,
                    "parent_material_desc": {},
                }
                if ques.parent_ques_id:
                    question_dict[ques_id]["sub_ques_list"] = small_ques_dict.get(ques_id, [])
            for ques_id, ques_item in question_dict.items():
                parent_ques_id = ques_item["parent_ques_id"]
                if parent_ques_id:
                    question_dict[ques_id]["ques_material"] = question_dict[parent_ques_id]["ques_desc"]
                    question_dict[ques_id]["parent_material_desc"] = question_dict[parent_ques_id]["material_desc"]
            # for ques_id, ques_info in question_dict.items():
            #     print(ques_id, ques_info["ques_desc"].get("text"), ques_info["material_desc"])

        redis.set(f"ques_info_by_id", json.dumps(question_dict, ensure_ascii=False))
    else:
        question_dict = json.loads(ques_info_json)
    return question_dict


def set_redis_ques_set_std_state(ques_id_list, state):
    """
    将试题定标状态加载到redis里
    """
    redis = next(redis_session())
    set_std_json = redis.get(f"ques_set_std_state_by_id")
    if set_std_json:
        set_std_dict = json.loads(set_std_json)
    else:
        set_std_dict = {}

    for ques_id in ques_id_list:
        set_std_dict[ques_id] = state
    redis.set(f"ques_set_std_state_by_id", json.dumps(set_std_dict))


def get_redis_ques_set_std_state(ques_id):
    """
    获取试题定标状态
    """
    redis = next(redis_session())
    set_std_json = redis.get(f"ques_set_std_state_by_id")
    if set_std_json:
        set_std_dict = json.loads(set_std_json)
        state = int(set_std_dict[ques_id])
    else:
        state = 0
    return state


def set_redis_error_type(new_session):
    """
    设置所有错误类型
    """
    error_type_info = new_session.query(ErrorType.type_id, ErrorType.type_name)
    if error_type_info:
        redis = next(redis_session())
        for type_id, type_name in error_type_info:
            redis.set(type_name, type_id)


def get_redis_error_type_id(error_type_name: str, new_session=None):
    """
    根据错误类型名称获取错误类型ID
    """
    redis = next(redis_session())
    type_id = redis.get(error_type_name)
    if type_id:
        return type_id
    # 若缓存中不存在，则向数据库添加并写入缓存
    if new_session is None:
        new_session = next(session_depend())
    type_id = configs.snow_worker.get_id()
    error_type = ErrorType(type_id=type_id, type_name=error_type_name)
    new_session.add(error_type)

    redis.set(error_type_name, type_id)
    return type_id


def get_redis_ques_small_info_dict(new_session, is_reload=False):
    """
    将试题（小小题）信息加载到redis里
    """
    redis = next(redis_session())
    ques_info_json = redis.get(f"ques_small_info_by_id")
    if is_reload or not ques_info_json:
        ques_info = new_session.query(
            SmallExamQuestion.ques_id,
            SmallExamQuestion.ques_score,
            SmallExamQuestion.parent_ques_id,
            SmallExamQuestion.ques_code,
            SmallExamQuestion.ques_type_code,
            SmallExamQuestion.business_ques_type_id,
            SmallExamQuestion.small_ques_num,
            SmallExamQuestion.ques_choices,
        ).all()
        ques_info = [dict(row._mapping) for row in ques_info]

        for q in ques_info:
            q["ques_score"] = float(q["ques_score"]) if q["ques_score"] is not None else None

        redis.set(f"ques_small_info_by_id", json.dumps(ques_info, ensure_ascii=False))
    else:
        ques_info_json = json.loads(ques_info_json)
    return ques_info_json


def set_unassigned_students_to_queue():
    """
    将HumanRepeatRoundDetail表中stu_state等于1（未分配）的记录添加到Redis队列
    """
    new_session = next(session_depend())
    redis = next(redis_session())

    # 查询所有未分配的考生记录
    unassigned_records = new_session.query(HumanRepeatRoundDetail.repeat_task_id, HumanRepeatRoundDetail.repeat_round_detail_id).filter(HumanRepeatRoundDetail.stu_state == 1).all()

    # 将未分配记录添加到对应的队列中
    repeat_task_ids = [p.repeat_task_id for p in unassigned_records]
    for repeat_task_id in repeat_task_ids:
        redis.delete(repeat_task_id)
    for repeat_task_id, repeat_round_detail_id in unassigned_records:
        redis.lpush(repeat_task_id, repeat_round_detail_id)
        # print(f"添加复评任务到队列：{repeat_task_id}  {repeat_round_detail_id}")
