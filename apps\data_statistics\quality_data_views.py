from fastapi import APIRouter, Depends
from settings import logger
from sqlalchemy import and_, func
from sqlalchemy.orm import Session
from typing import Any

from apps.data_statistics.services import get_curr_user_task_list
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse
from apps.models.models import ManualMark, ManualArbitrateQuality, UserInfo
from factory_apps.mysql_db.databases import session_depend
from utils.utils import round_half_up

quality_data_router = APIRouter()


@quality_data_router.post(path="/entirety_quality_finish_situation", response_model=BaseResponse, summary="质检主页：获取整体完成情况")
async def entirety_quality_finish_situation(user: Any = Depends(get_current_user),
                                            new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 质检主页：获取整体完成情况")
    curr_user_id = user.get("user_id")
    task_id_list = get_curr_user_task_list(new_session, curr_user_id, "5")
    # 所有质检数量
    all_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id.in_(task_id_list), ManualArbitrateQuality.aq_type == 2)).count()
    # 已质检数量
    yet_quality_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id.in_(task_id_list), ManualArbitrateQuality.aq_type == 2,
             ManualArbitrateQuality.aq_state == 4)).count()
    # 待质检数量
    wait_quality_count = all_quality_count - yet_quality_count
    quality_not_pass_rate = round_half_up(yet_quality_count / all_quality_count * 100, 2) if all_quality_count else 0

    data = {
        "all_quality_count": all_quality_count,
        "yet_quality_count": yet_quality_count,
        "wait_quality_count": wait_quality_count,
        "quality_finish_rate": quality_not_pass_rate
    }
    logger.info("获取质检主页：获取整体完成情况成功")
    return BaseResponse(data=data, msg="获取质检主页：获取整体完成情况成功")


@quality_data_router.post(path="/entirety_quality_situation", response_model=BaseResponse, summary="质检主页：获取整体质检情况饼图")
async def entirety_quality_situation(user: Any = Depends(get_current_user),
                                     new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 质检主页：获取整体质检情况饼图")
    curr_user_id = user.get("user_id")
    task_id_list = get_curr_user_task_list(new_session, curr_user_id, "5")

    quality_pass_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id.in_(task_id_list), ManualArbitrateQuality.aq_type == 2,
             ManualArbitrateQuality.aq_result == 1)).count()
    quality_not_pass_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
        and_(ManualArbitrateQuality.m_read_task_id.in_(task_id_list), ManualArbitrateQuality.aq_type == 2,
             ManualArbitrateQuality.aq_result != 1)).count()

    data = {
        "x_data": ["通过", "未通过"],
        "y_data": [
            {"value": quality_pass_count, "name": "通过"},
            {"value": quality_not_pass_count, "name": "未通过"}
        ]
    }
    logger.info("获取质检主页：获取整体质检情况饼图成功")
    return BaseResponse(data=data, msg="获取质检主页：获取整体质检情况饼图成功")


@quality_data_router.post(path="/task_quality_detail", response_model=BaseResponse, summary="质检主页：获取任务质检详情柱状图")
async def task_quality_detail(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 质检主页：获取任务质检详情柱状图")
    curr_user_id = user.get("user_id")
    task_list = get_curr_user_task_list(new_session, curr_user_id, "5", only_id=False)

    task_name_list, quality_pass_count_list, quality_not_pass_count_list = [], [], []
    for task in task_list:
        task_name_list.append(task["m_read_task_name"])
        task_id = task["m_read_task_id"]
        quality_pass_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
            and_(ManualArbitrateQuality.m_read_task_id == task_id, ManualArbitrateQuality.aq_type == 2,
                 ManualArbitrateQuality.aq_result == 1)).count()
        quality_pass_count_list.append(quality_pass_count)
        quality_not_pass_count = new_session.query(ManualArbitrateQuality.manual_aq_id).filter(
            and_(ManualArbitrateQuality.m_read_task_id == task_id, ManualArbitrateQuality.aq_type == 2,
                 ManualArbitrateQuality.aq_result != 1)).count()
        quality_not_pass_count_list.append(quality_not_pass_count)

    data = {
        "legend": ["通过", "未通过"],
        "x_data": task_name_list,
        "y1_data": quality_pass_count_list,
        "y2_data": quality_not_pass_count_list
    }
    logger.info("获取质检主页：获取任务质检详情柱状图成功")
    return BaseResponse(data=data, msg="获取质检主页：获取任务质检详情柱状图成功")


@quality_data_router.post(path="/expert_quality_situation", response_model=BaseResponse, summary="质检主页：获取专家质检情况柱状图")
async def expert_quality_situation(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 质检主页：获取专家质检情况柱状图")
    curr_user_id = user.get("user_id")
    task_id_list = get_curr_user_task_list(new_session, curr_user_id, "5")

    expert_name_list, quality_pass_count_list, quality_not_pass_count_list = [], [], []
    quality_info = new_session.query(func.group_concat(ManualArbitrateQuality.aq_result),
                                     UserInfo.name) \
        .outerjoin(ManualMark, ManualMark.distri_answer_id == ManualArbitrateQuality.distri_answer_id) \
        .join(UserInfo, UserInfo.user_id == ManualMark.mark_person_id) \
        .filter(and_(ManualArbitrateQuality.m_read_task_id.in_(task_id_list), ManualArbitrateQuality.aq_type == 2,
                ManualArbitrateQuality.aq_state == 4)) \
        .group_by(UserInfo.name).all()

    quality_pass_count_list, quality_not_pass_count_list = [], []
    for aq_result_str, expert_name in quality_info:
        expert_name_list.append(expert_name)
        aq_result_list = aq_result_str.split(",")
        quality_pass_count, quality_not_pass_count = 0, 0
        for aq_result in aq_result_list:
            if aq_result == "1":
                quality_pass_count += 1
            else:
                quality_not_pass_count += 1
        quality_pass_count_list.append(quality_pass_count)
        quality_not_pass_count_list.append(quality_not_pass_count)

    data = {
        "legend": ["通过", "未通过"],
        "x_data": expert_name_list,
        "y1_data": quality_pass_count_list,
        "y2_data": quality_not_pass_count_list
    }
    logger.info("获取质检主页：获取专家质检情况柱状图成功")
    return BaseResponse(data=data, msg="获取质检主页：获取专家质检情况柱状图成功")
