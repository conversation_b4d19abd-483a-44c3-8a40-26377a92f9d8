from sqlalchemy import and_, func, select, case, or_, Integer, desc
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime
import json
import traceback

from apps.models.models import UserRole, Project, Subject, UserInfo
from settings import configs, logger
from factory_apps import session_depend, redis_session
from apps.human_statistics.models import HumanStatisticsPerson, HumanStatisticsSmallGroup
from apps.human_task_manage.models import HumanPersonDistriAnswer, HumanReadTask, HumanReadTaskRound, HumanRoundDistriAnswer
from apps.human_mark_group.models import HumanMarkGroup
from apps.models.models import Project, Subject
"""
查询 HumanPersonDistriAnswer 表并写入到 HumanStatisticsPerson 和 HumanStatisticsSmallGroup 表中
"""


def query_and_insert_person_statistics() :
    new_session = next(session_depend())
    try:
        all_records = new_session.execute(select(
            HumanPersonDistriAnswer.ques_code,
            HumanPersonDistriAnswer.group_id,
            HumanPersonDistriAnswer.round_id,
            HumanPersonDistriAnswer.user_id,
            HumanPersonDistriAnswer.stu_secret_num,
            HumanPersonDistriAnswer.mark_score,
            HumanPersonDistriAnswer.mark_time,
            HumanPersonDistriAnswer.cost_time,
            HumanReadTaskRound.round_count,
            HumanReadTask.task_id,
            HumanReadTask.task_type,
            HumanReadTask.project_id,
            HumanReadTask.subject_id,
            HumanMarkGroup.parent_group_id
        ).join(HumanReadTaskRound, HumanPersonDistriAnswer.round_id == HumanReadTaskRound.round_id) \
        .join(HumanReadTask, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
        .join(HumanMarkGroup, HumanPersonDistriAnswer.group_id == HumanMarkGroup.group_id) \
        .filter(HumanPersonDistriAnswer.is_answer_marked == 1)).all()
        
        # 在Python中进行分组和聚合计算
        student_totals = {}
        grouped_data = {}
        cumulative_data = {} 
        for record in all_records:
            # 提取字段
            ques_code = record.ques_code  # 试题编号
            group_id = record.group_id  # 题组ID
            round_id = record.round_id  # 轮次ID
            user_id = record.user_id  # 用户ID
            stu_secret_num = record.stu_secret_num  # 考生密号
            mark_score = record.mark_score  # 评分分数
            mark_time = record.mark_time  # 评分时间
            cost_time = record.cost_time  # 评分耗时
            round_count = record.round_count  # 轮次
            task_id = record.task_id  # 任务ID
            task_type = record.task_type  # 任务类型
            project_id = record.project_id  # 项目ID
            subject_id = record.subject_id  # 科目ID
            ques_group_id = record.parent_group_id  # 题组ID
            
            # 类型转换：确保数值字段是数字类型
            try:
                mark_score = float(mark_score) if mark_score is not None else 0
            except (ValueError, TypeError):
                mark_score = 0
                logger.warning(f"mark_score 转换失败: {mark_score}, 类型: {type(mark_score)}")
                
            try:
                cost_time = float(cost_time) if cost_time is not None else 0
            except (ValueError, TypeError):
                cost_time = 0
                logger.warning(f"cost_time 转换失败: {cost_time}, 类型: {type(cost_time)}")
            
            # 计算每个考生在题组内的总分
            student_key = (user_id, ques_group_id, round_id, stu_secret_num)
            if student_key not in student_totals:
                student_totals[student_key] = 0
            student_totals[student_key] += mark_score

            # 构建分组键
            group_key = (ques_group_id, group_id, round_id, user_id, mark_time.date())
            # 构建分组键（不按时间分组，计算所有时间的平均分）
            group_key_avg = (ques_group_id, group_id, round_id, user_id)
            if group_key not in grouped_data:
                grouped_data[group_key] = {
                    'ques_group_id': ques_group_id,
                    'group_id': group_id,
                    'round_id': round_id,
                    'user_id': user_id,
                    'round_count': round_count,
                    'task_id': task_id,
                    'task_type': task_type,
                    'project_id': project_id,
                    'subject_id': subject_id,
                    'mark_time': mark_time,
                    'reviewed_count': set(),  # 使用set去重
                    'mark_scores': [],  # 存储所有评分
                    'cost_times': [],  # 存储所有耗时
                    'workload_count': set()  # 工作量计数
                }
            
            # 收集数据
            grouped_data[group_key]['reviewed_count'].add(stu_secret_num)
            grouped_data[group_key]['mark_scores'].append(mark_score)
            grouped_data[group_key]['cost_times'].append(cost_time)
            grouped_data[group_key]['workload_count'].add(stu_secret_num)
        

            # 收集累计平均分数据
            if group_key_avg not in cumulative_data:
                cumulative_data[group_key_avg] = {
                    'ques_group_id': ques_group_id,
                    'group_id': group_id,
                    'round_id': round_id,
                    'user_id': user_id,
                    'mark_scores': [],  # 存储所有评分
                    'reviewed_count': set(),  # 使用set去重
                }

            # 收集累计平均分数据
            cumulative_data[group_key_avg]['mark_scores'].append(mark_score)
            cumulative_data[group_key_avg]['reviewed_count'].add(stu_secret_num)

        
        # 计算最终的统计结果
        final_records = []
        for group_key, data in grouped_data.items():
            # 计算已阅量
            reviewed_count = len(data['reviewed_count'])
            
            # 计算平均分
            if data['mark_scores']:
                average_score = sum(data['mark_scores']) / len(data['reviewed_count'])
            else:
                average_score = 0
                
            # 计算最高分和最低分（从学生总分中获取）
            student_scores = []
            for (user_id, ques_group_id, round_id, stu_secret_num), score in student_totals.items():
                if user_id == data['user_id'] and ques_group_id == data['ques_group_id'] and round_id == data['round_id']:
                    student_scores.append(score)
            
            if student_scores:
                max_score = max(student_scores)
                min_score = min(student_scores)
            else:
                max_score = 0
                min_score = 0

            total_time = sum(data['cost_times'])
            # 计算工作量
            workload_count = len(data['workload_count'])

            # 计算最长速度和最短速度
            if data['cost_times']:
                max_speed = max(data['cost_times'])
                min_speed = min(data['cost_times'])
            else:
                max_speed = 0
                min_speed = 0

        # 计算累计平均分
            group_key_avg_for_cumulative = (data['ques_group_id'], data['group_id'], data['round_id'], data['user_id'])
            cumulative_average = 0
            if group_key_avg_for_cumulative in cumulative_data:
                cum_data = cumulative_data[group_key_avg_for_cumulative]
                if cum_data['mark_scores']:
                    cumulative_average = sum(cum_data['mark_scores']) / len(cum_data['reviewed_count'])

            # 构造最终记录
            final_record = (
                data['ques_group_id'],
                data['group_id'],
                data['round_id'],
                data['user_id'],
                list(data['reviewed_count'])[0] if data['reviewed_count'] else None,  # 取一个学生密号
                0,  # mark_score (不需要)
                data['mark_time'],  # mark_time
                0,  # cost_time (不需要)
                data['round_count'],
                data['task_id'],
                data['task_type'],
                data['project_id'],
                data['subject_id'],
                reviewed_count,
                average_score,
                max_score,
                min_score,
                total_time,
                workload_count,
                max_speed,
                min_speed,
                cumulative_average
            )
            final_records.append(final_record)
        
        # 收集需要创建和更新的记录
        new_records = []  # 新记录列表
        update_conditions = []  # 更新条件列表

        # 按照要求进行分组统计
        for record in final_records:
            # 获取记录信息
            ques_group_id = record[0]  # 试题编号
            group_id = record[1]  # 题组ID
            round_id = record[2]  # 轮次ID
            user_id = record[3]  # 用户ID
            stu_secret_num = record[4]  # 考生密号
            mark_score = record[5]  # 评分分数
            mark_time = record[6]  # 评分时间
            cost_time = record[7]  # 评分耗时
            round_count = record[8]  # 轮次
            task_id = record[9]  # 任务ID
            task_type = record[10]  # 任务类型
            project_id = record[11]  # 项目ID
            subject_id = record[12]  # 科目ID
            reviewed_count = record[13]  # 已阅量
            average_score = record[14]  # 平均分
            max_score = record[15]  # 最高分
            min_score = record[16]  # 最低分
            total_time = record[17]  # 工作时间
            workload_count = record[18]  # 工作量
            max_speed = record[19]  # 最长速度
            min_speed = record[20]  # 最短速度
            cumulative_average = record[21]

            # 按照项目、科目、题组、小组、任务、轮次、任务类型、日期进行分组统计
            # 为每种统计类型创建键值对
            for stat_type in [1, 2, 3, 4, 10, 11, 14, 15, 16]:  # 所有需要处理的统计类型
                # 根据统计类型处理
                if stat_type == 4:  # 已阅量
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsPerson).filter(
                        HumanStatisticsPerson.project_id == project_id,
                        HumanStatisticsPerson.subject_id == subject_id,
                        HumanStatisticsPerson.ques_group_id == ques_group_id,
                        HumanStatisticsPerson.group_id == group_id,
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.task_id == task_id,
                        HumanStatisticsPerson.round_count == round_count,
                        HumanStatisticsPerson.task_type == task_type,
                        HumanStatisticsPerson.statistics_type == stat_type,
                        HumanStatisticsPerson.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsPerson(
                            statistics_person_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            user_id=user_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 已阅量
                            statistics_result_1=reviewed_count,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsPerson.project_id == project_id,
                                HumanStatisticsPerson.subject_id == subject_id,
                                HumanStatisticsPerson.ques_group_id == ques_group_id,
                                HumanStatisticsPerson.group_id == group_id,
                                HumanStatisticsPerson.user_id == user_id,
                                HumanStatisticsPerson.task_id == task_id,
                                HumanStatisticsPerson.round_count == round_count,
                                HumanStatisticsPerson.task_type == task_type,
                                HumanStatisticsPerson.statistics_type == stat_type,
                                HumanStatisticsPerson.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsPerson.statistics_result_1: reviewed_count
                            }
                        })

                elif stat_type == 1:  # 平均分
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsPerson).filter(
                        HumanStatisticsPerson.project_id == project_id,
                        HumanStatisticsPerson.subject_id == subject_id,
                        HumanStatisticsPerson.ques_group_id == ques_group_id,
                        HumanStatisticsPerson.group_id == group_id,
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.task_id == task_id,
                        HumanStatisticsPerson.round_count == round_count,
                        HumanStatisticsPerson.task_type == task_type,
                        HumanStatisticsPerson.statistics_type == stat_type,
                        HumanStatisticsPerson.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsPerson(
                            statistics_person_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            user_id=user_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 平均分
                            statistics_result_1=average_score,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsPerson.project_id == project_id,
                                HumanStatisticsPerson.subject_id == subject_id,
                                HumanStatisticsPerson.ques_group_id == ques_group_id,
                                HumanStatisticsPerson.group_id == group_id,
                                HumanStatisticsPerson.user_id == user_id,
                                HumanStatisticsPerson.task_id == task_id,
                                HumanStatisticsPerson.round_count == round_count,
                                HumanStatisticsPerson.task_type == task_type,
                                HumanStatisticsPerson.statistics_type == stat_type,
                                HumanStatisticsPerson.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsPerson.statistics_result_1: average_score
                            }
                        })

                elif stat_type == 14:  # 平均分
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsPerson).filter(
                        HumanStatisticsPerson.project_id == project_id,
                        HumanStatisticsPerson.subject_id == subject_id,
                        HumanStatisticsPerson.ques_group_id == ques_group_id,
                        HumanStatisticsPerson.group_id == group_id,
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.task_id == task_id,
                        HumanStatisticsPerson.round_count == round_count,
                        HumanStatisticsPerson.task_type == task_type,
                        HumanStatisticsPerson.statistics_type == stat_type
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsPerson(
                            statistics_person_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            user_id=user_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 平均分
                            statistics_result_1=cumulative_average
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsPerson.project_id == project_id,
                                HumanStatisticsPerson.subject_id == subject_id,
                                HumanStatisticsPerson.ques_group_id == ques_group_id,
                                HumanStatisticsPerson.group_id == group_id,
                                HumanStatisticsPerson.user_id == user_id,
                                HumanStatisticsPerson.task_id == task_id,
                                HumanStatisticsPerson.round_count == round_count,
                                HumanStatisticsPerson.task_type == task_type,
                                HumanStatisticsPerson.statistics_type == stat_type
                            },
                            'update': {
                                HumanStatisticsPerson.statistics_result_1: cumulative_average
                            }
                        })

                elif stat_type == 2:  # 最高分
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsPerson).filter(
                        HumanStatisticsPerson.project_id == project_id,
                        HumanStatisticsPerson.subject_id == subject_id,
                        HumanStatisticsPerson.ques_group_id == ques_group_id,
                        HumanStatisticsPerson.group_id == group_id,
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.task_id == task_id,
                        HumanStatisticsPerson.round_count == round_count,
                        HumanStatisticsPerson.task_type == task_type,
                        HumanStatisticsPerson.statistics_type == stat_type,
                        HumanStatisticsPerson.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsPerson(
                            statistics_person_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            user_id=user_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 最高分
                            statistics_result_1=max_score,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsPerson.project_id == project_id,
                                HumanStatisticsPerson.subject_id == subject_id,
                                HumanStatisticsPerson.ques_group_id == ques_group_id,
                                HumanStatisticsPerson.group_id == group_id,
                                HumanStatisticsPerson.user_id == user_id,
                                HumanStatisticsPerson.task_id == task_id,
                                HumanStatisticsPerson.round_count == round_count,
                                HumanStatisticsPerson.task_type == task_type,
                                HumanStatisticsPerson.statistics_type == stat_type,
                                HumanStatisticsPerson.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsPerson.statistics_result_1: max_score
                            }
                        })

                elif stat_type == 3:  # 最低分
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsPerson).filter(
                        HumanStatisticsPerson.project_id == project_id,
                        HumanStatisticsPerson.subject_id == subject_id,
                        HumanStatisticsPerson.ques_group_id == ques_group_id,
                        HumanStatisticsPerson.group_id == group_id,
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.task_id == task_id,
                        HumanStatisticsPerson.round_count == round_count,
                        HumanStatisticsPerson.task_type == task_type,
                        HumanStatisticsPerson.statistics_type == stat_type,
                        HumanStatisticsPerson.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsPerson(
                            statistics_person_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            user_id=user_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 最低分
                            statistics_result_1=min_score,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsPerson.project_id == project_id,
                                HumanStatisticsPerson.subject_id == subject_id,
                                HumanStatisticsPerson.ques_group_id == ques_group_id,
                                HumanStatisticsPerson.group_id == group_id,
                                HumanStatisticsPerson.user_id == user_id,
                                HumanStatisticsPerson.task_id == task_id,
                                HumanStatisticsPerson.round_count == round_count,
                                HumanStatisticsPerson.task_type == task_type,
                                HumanStatisticsPerson.statistics_type == stat_type,
                                HumanStatisticsPerson.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsPerson.statistics_result_1: min_score
                            }
                        })

                elif stat_type == 10:  # 工作时间
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsPerson).filter(
                        HumanStatisticsPerson.project_id == project_id,
                        HumanStatisticsPerson.subject_id == subject_id,
                        HumanStatisticsPerson.ques_group_id == ques_group_id,
                        HumanStatisticsPerson.group_id == group_id,
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.task_id == task_id,
                        HumanStatisticsPerson.round_count == round_count,
                        HumanStatisticsPerson.task_type == task_type,
                        HumanStatisticsPerson.statistics_type == stat_type,
                        HumanStatisticsPerson.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsPerson(
                            statistics_person_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            user_id=user_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 工作时间
                            statistics_result_1=total_time,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsPerson.project_id == project_id,
                                HumanStatisticsPerson.subject_id == subject_id,
                                HumanStatisticsPerson.ques_group_id == ques_group_id,
                                HumanStatisticsPerson.group_id == group_id,
                                HumanStatisticsPerson.user_id == user_id,
                                HumanStatisticsPerson.task_id == task_id,
                                HumanStatisticsPerson.round_count == round_count,
                                HumanStatisticsPerson.task_type == task_type,
                                HumanStatisticsPerson.statistics_type == stat_type,
                                HumanStatisticsPerson.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsPerson.statistics_result_1: total_time
                            }
                        })

                elif stat_type == 11:  # 工作量
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsPerson).filter(
                        HumanStatisticsPerson.project_id == project_id,
                        HumanStatisticsPerson.subject_id == subject_id,
                        HumanStatisticsPerson.ques_group_id == ques_group_id,
                        HumanStatisticsPerson.group_id == group_id,
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.task_id == task_id,
                        HumanStatisticsPerson.round_count == round_count,
                        HumanStatisticsPerson.task_type == task_type,
                        HumanStatisticsPerson.statistics_type == stat_type,
                        HumanStatisticsPerson.date == mark_time.date()
                    ).first()

                    # 处理工作量统计 - 按小时分布
                    # 从 mark_time 中提取小时信息
                    try:
                        # 尝试解析 mark_time 为 datetime 对象
                        if isinstance(mark_time, str):
                            # 如果是字符串，尝试解析为 datetime 对象
                            parsed_time = datetime.strptime(mark_time.strip(), '%Y-%m-%d %H:%M:%S')
                        elif hasattr(mark_time, 'hour'):
                            # 如果已经是 datetime 对象，直接使用
                            parsed_time = mark_time
                        else:
                            # 如果无法解析，使用当前时间
                            parsed_time = datetime.now()
                        
                        hour = parsed_time.hour
                        hour_key = f"{hour}-{hour + 1}"
                    except (ValueError, TypeError):
                        # 如果解析失败，使用默认值
                        hour = 0
                        hour_key = "0-1"

                    if not existing_person_record:
                        # 创建新记录
                        # 构造工作量数据结构
                        workload_data = {
                            "time": mark_time.date().strftime('%Y-%m-%d'),
                            "hourly_distribution": {
                                hour_key: workload_count
                            }
                        }
                        new_record = HumanStatisticsPerson(
                            statistics_person_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            user_id=user_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 工作量
                            statistics_result_1=workload_count,
                            statistics_result_2=json.dumps(workload_data, ensure_ascii=False),
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        # 覆盖式更新
                        workload_data = {}
                        if existing_person_record.statistics_result_2:
                            try:
                                workload_data = json.loads(existing_person_record.statistics_result_2)
                            except json.JSONDecodeError:
                                workload_data = {}
                        # 更新小时分布
                        if "hourly_distribution" not in workload_data:
                            workload_data["hourly_distribution"] = {}
                        workload_data["hourly_distribution"][hour_key] = workload_count
                        workload_data["time"] = mark_time.date().strftime('%Y-%m-%d')
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsPerson.project_id == project_id,
                                HumanStatisticsPerson.subject_id == subject_id,
                                HumanStatisticsPerson.ques_group_id == ques_group_id,
                                HumanStatisticsPerson.group_id == group_id,
                                HumanStatisticsPerson.user_id == user_id,
                                HumanStatisticsPerson.task_id == task_id,
                                HumanStatisticsPerson.round_count == round_count,
                                HumanStatisticsPerson.task_type == task_type,
                                HumanStatisticsPerson.statistics_type == stat_type,
                                HumanStatisticsPerson.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsPerson.statistics_result_1: workload_count,
                                HumanStatisticsPerson.statistics_result_2: json.dumps(workload_data, ensure_ascii=False)
                            }
                        })

                elif stat_type == 15:  # 最长速度
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsPerson).filter(
                        HumanStatisticsPerson.project_id == project_id,
                        HumanStatisticsPerson.subject_id == subject_id,
                        HumanStatisticsPerson.ques_group_id == ques_group_id,
                        HumanStatisticsPerson.group_id == group_id,
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.task_id == task_id,
                        HumanStatisticsPerson.round_count == round_count,
                        HumanStatisticsPerson.task_type == task_type,
                        HumanStatisticsPerson.statistics_type == stat_type,
                        HumanStatisticsPerson.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsPerson(
                            statistics_person_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            user_id=user_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 最长速度
                            statistics_result_1=max_speed,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsPerson.project_id == project_id,
                                HumanStatisticsPerson.subject_id == subject_id,
                                HumanStatisticsPerson.ques_group_id == ques_group_id,
                                HumanStatisticsPerson.group_id == group_id,
                                HumanStatisticsPerson.user_id == user_id,
                                HumanStatisticsPerson.task_id == task_id,
                                HumanStatisticsPerson.round_count == round_count,
                                HumanStatisticsPerson.task_type == task_type,
                                HumanStatisticsPerson.statistics_type == stat_type,
                                HumanStatisticsPerson.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsPerson.statistics_result_1: max_speed
                            }
                        })

                elif stat_type == 16:  # 最短速度
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsPerson).filter(
                        HumanStatisticsPerson.project_id == project_id,
                        HumanStatisticsPerson.subject_id == subject_id,
                        HumanStatisticsPerson.ques_group_id == ques_group_id,
                        HumanStatisticsPerson.group_id == group_id,
                        HumanStatisticsPerson.user_id == user_id,
                        HumanStatisticsPerson.task_id == task_id,
                        HumanStatisticsPerson.round_count == round_count,
                        HumanStatisticsPerson.task_type == task_type,
                        HumanStatisticsPerson.statistics_type == stat_type,
                        HumanStatisticsPerson.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsPerson(
                            statistics_person_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            user_id=user_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 最短速度
                            statistics_result_1=min_speed,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsPerson.project_id == project_id,
                                HumanStatisticsPerson.subject_id == subject_id,
                                HumanStatisticsPerson.ques_group_id == ques_group_id,
                                HumanStatisticsPerson.group_id == group_id,
                                HumanStatisticsPerson.user_id == user_id,
                                HumanStatisticsPerson.task_id == task_id,
                                HumanStatisticsPerson.round_count == round_count,
                                HumanStatisticsPerson.task_type == task_type,
                                HumanStatisticsPerson.statistics_type == stat_type,
                                HumanStatisticsPerson.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsPerson.statistics_result_1: min_speed
                            }
                        })

        # 批量插入新记录
        if new_records:
            logger.info(f"准备批量插入 {len(new_records)} 条新记录")
            new_session.add_all(new_records)
            logger.info("批量插入完成")

        # 批量更新现有记录
        for condition in update_conditions:
            filter_conditions = condition['filter']
            update_values = condition['update']
            # 构建查询条件
            query = new_session.query(HumanStatisticsPerson)
            for filter_condition in filter_conditions:
                query = query.filter(filter_condition)
            query.update(update_values)

        new_session.commit()
        return True
    except Exception as e:
        logger.info(f"查询并插入统计数据失败：{e}")
        traceback.print_exc()
        new_session.rollback()
        return False





def query_and_insert_group_statistics():
    new_session = next(session_depend())
    try:
        all_records = new_session.execute(select(
            HumanPersonDistriAnswer.ques_code,
            HumanPersonDistriAnswer.group_id,
            HumanPersonDistriAnswer.round_id,
            HumanPersonDistriAnswer.stu_secret_num,
            HumanPersonDistriAnswer.mark_score,
            HumanPersonDistriAnswer.mark_time,
            HumanPersonDistriAnswer.cost_time,
            HumanReadTaskRound.round_count,
            HumanReadTask.task_id,
            HumanReadTask.task_type,
            HumanReadTask.project_id,
            HumanReadTask.subject_id,
            HumanMarkGroup.parent_group_id
        ).join(HumanReadTaskRound, HumanPersonDistriAnswer.round_id == HumanReadTaskRound.round_id) \
         .join(HumanReadTask, HumanReadTaskRound.task_id == HumanReadTask.task_id) \
         .join(HumanMarkGroup, HumanPersonDistriAnswer.group_id == HumanMarkGroup.group_id) \
         .filter(HumanPersonDistriAnswer.is_answer_marked == 1)).all()
        # 如果没有数据，返回成功
        student_totals = {}
        student_cost_times = {}

        grouped_data = {}
        cumulative_data = {}
        for record in all_records:
            # 提取字段
            ques_code = record.ques_code  # 试题编号
            group_id = record.group_id  # 题组ID
            round_id = record.round_id  # 轮次ID
            stu_secret_num = record.stu_secret_num  # 考生密号
            mark_score = record.mark_score  # 评分分数
            mark_time = record.mark_time  # 评分时间
            cost_time = record.cost_time  # 评分耗时
            round_count = record.round_count  # 轮次
            task_id = record.task_id  # 任务ID
            task_type = record.task_type  # 任务类型
            project_id = record.project_id  # 项目ID
            subject_id = record.subject_id  # 科目ID
            ques_group_id = record.parent_group_id  # 题组ID

            # 类型转换：确保数值字段是数字类型
            try:
                mark_score = float(mark_score) if mark_score is not None else 0
            except (ValueError, TypeError):
                mark_score = 0
                logger.warning(f"mark_score 转换失败: {mark_score}, 类型: {type(mark_score)}")

            try:
                cost_time = float(cost_time) if cost_time is not None else 0
            except (ValueError, TypeError):
                cost_time = 0
                logger.warning(f"cost_time 转换失败: {cost_time}, 类型: {type(cost_time)}")

            # 计算每个考生在题组内的总分
            student_key = (ques_group_id, round_id, stu_secret_num)
            if student_key not in student_totals:
                student_totals[student_key] = 0
            student_totals[student_key] += mark_score

            # 构建分组键
            group_key = (ques_group_id, group_id, round_id, mark_time.date())
            group_key_avg = (ques_group_id, group_id, round_id)
            if group_key not in grouped_data:
                grouped_data[group_key] = {
                    'ques_group_id': ques_group_id,
                    'group_id': group_id,
                    'round_id': round_id,
                    'round_count': round_count,
                    'task_id': task_id,
                    'task_type': task_type,
                    'project_id': project_id,
                    'subject_id': subject_id,
                    'mark_time': mark_time,
                    'reviewed_count': set(),  # 使用set去重
                    'mark_scores': [],  # 存储所有评分
                    'cost_times': [],  # 存储所有耗时
                    'workload_count': set()  # 工作量计数
                }

            # 收集数据
            grouped_data[group_key]['reviewed_count'].add(stu_secret_num)
            grouped_data[group_key]['mark_scores'].append(mark_score)
            grouped_data[group_key]['cost_times'].append(cost_time)
            grouped_data[group_key]['workload_count'].add(stu_secret_num)

            # 收集累计平均分数据
            if group_key_avg not in cumulative_data:
                cumulative_data[group_key_avg] = {
                    'ques_group_id': ques_group_id,
                    'group_id': group_id,
                    'round_id': round_id,
                    'mark_scores': [],  # 存储所有评分
                    'reviewed_count': set(),  # 使用set去重
                }

            # 收集累计平均分数据
            cumulative_data[group_key_avg]['mark_scores'].append(mark_score)
            cumulative_data[group_key_avg]['reviewed_count'].add(stu_secret_num)

        # 计算最终的统计结果
        final_records = []
        for group_key, data in grouped_data.items():
            # 计算已阅量
            reviewed_count = len(data['reviewed_count'])

            # 计算平均分
            if data['mark_scores']:
                average_score = sum(data['mark_scores']) / len(data['reviewed_count'])
            else:
                average_score = 0

            # 计算最高分和最低分（从学生总分中获取）
            student_scores = []
            for ( ques_group_id, round_id, stu_secret_num), score in student_totals.items():
                if ques_group_id == data['ques_group_id'] and round_id == data['round_id']:
                    student_scores.append(score)

            if student_scores:
                max_score = max(student_scores)
                min_score = min(student_scores)
            else:
                max_score = 0
                min_score = 0

            total_time = sum(data['cost_times'])
            # 计算工作量
            workload_count = len(data['workload_count'])

            # 计算最长速度和最短速度
            if data['cost_times']:
                max_speed = max(data['cost_times'])
                min_speed = min(data['cost_times'])
            else:
                max_speed = 0
                min_speed = 0

            # 计算累计平均分
            group_key_avg_for_cumulative = (data['ques_group_id'], data['group_id'], data['round_id'])
            cumulative_average = 0
            if group_key_avg_for_cumulative in cumulative_data:
                cum_data = cumulative_data[group_key_avg_for_cumulative]
                if cum_data['mark_scores']:
                    cumulative_average = sum(cum_data['mark_scores']) / len(cum_data['reviewed_count'])

            # 构造最终记录
            final_record = (
                data['ques_group_id'],
                data['group_id'],
                data['round_id'],
                list(data['reviewed_count'])[0] if data['reviewed_count'] else None,  # 取一个学生密号
                0,  # mark_score (不需要)
                data['mark_time'],  # mark_time
                0,  # cost_time (不需要)
                data['round_count'],
                data['task_id'],
                data['task_type'],
                data['project_id'],
                data['subject_id'],
                reviewed_count,
                average_score,
                max_score,
                min_score,
                total_time,
                workload_count,
                max_speed,
                min_speed,
                cumulative_average
            )
            final_records.append(final_record)

        # 收集需要创建和更新的记录
        new_records = []  # 新记录列表
        update_conditions = []  # 更新条件列表

        # 按照要求进行分组统计
        for record in final_records:
            # 获取记录信息
            ques_group_id = record[0]  # 试题编号
            group_id = record[1]  # 题组ID
            round_id = record[2]  # 轮次ID
            stu_secret_num = record[3]  # 考生密号
            mark_score = record[4]  # 评分分数
            mark_time = record[5]  # 评分时间
            cost_time = record[6]  # 评分耗时
            round_count = record[7]  # 轮次
            task_id = record[8]  # 任务ID
            task_type = record[9]  # 任务类型
            project_id = record[10]  # 项目ID
            subject_id = record[11]  # 科目ID
            reviewed_count = record[12]  # 已阅量
            average_score = record[13]  # 平均分
            max_score = record[14]  # 最高分
            min_score = record[15]  # 最低分
            total_time = record[16]  # 工作时间
            workload_count = record[17]  # 工作量
            max_speed = record[18]  # 最长速度
            min_speed = record[19]  # 最短速度
            cumulative_average = record[20]

            for stat_type in [1, 2, 3, 4, 10, 14, 15, 16]:  # 所有需要处理的统计类型
                # 根据统计类型处理
                if stat_type == 4:  # 已阅量
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsSmallGroup).filter(
                        HumanStatisticsSmallGroup.project_id == project_id,
                        HumanStatisticsSmallGroup.subject_id == subject_id,
                        HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                        HumanStatisticsSmallGroup.group_id == group_id,
                        HumanStatisticsSmallGroup.task_id == task_id,
                        HumanStatisticsSmallGroup.round_count == round_count,
                        HumanStatisticsSmallGroup.task_type == task_type,
                        HumanStatisticsSmallGroup.statistics_type == stat_type,
                        HumanStatisticsSmallGroup.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsSmallGroup(
                            statistics_group_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 已阅量
                            statistics_result_1=reviewed_count,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsSmallGroup.project_id == project_id,
                                HumanStatisticsSmallGroup.subject_id == subject_id,
                                HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                                HumanStatisticsSmallGroup.group_id == group_id,
                                HumanStatisticsSmallGroup.task_id == task_id,
                                HumanStatisticsSmallGroup.round_count == round_count,
                                HumanStatisticsSmallGroup.task_type == task_type,
                                HumanStatisticsSmallGroup.statistics_type == stat_type,
                                HumanStatisticsSmallGroup.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsSmallGroup.statistics_result_1: reviewed_count
                            }
                        })

                elif stat_type == 1:  # 平均分
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsSmallGroup).filter(
                        HumanStatisticsSmallGroup.project_id == project_id,
                        HumanStatisticsSmallGroup.subject_id == subject_id,
                        HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                        HumanStatisticsSmallGroup.group_id == group_id,
                        HumanStatisticsSmallGroup.task_id == task_id,
                        HumanStatisticsSmallGroup.round_count == round_count,
                        HumanStatisticsSmallGroup.task_type == task_type,
                        HumanStatisticsSmallGroup.statistics_type == stat_type,
                        HumanStatisticsSmallGroup.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsSmallGroup(
                            statistics_group_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 平均分
                            statistics_result_1=average_score,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsSmallGroup.project_id == project_id,
                                HumanStatisticsSmallGroup.subject_id == subject_id,
                                HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                                HumanStatisticsSmallGroup.group_id == group_id,
                                HumanStatisticsSmallGroup.task_id == task_id,
                                HumanStatisticsSmallGroup.round_count == round_count,
                                HumanStatisticsSmallGroup.task_type == task_type,
                                HumanStatisticsSmallGroup.statistics_type == stat_type,
                                HumanStatisticsSmallGroup.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsSmallGroup.statistics_result_1: average_score
                            }
                        })
                elif stat_type == 14:  # 平均分
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsSmallGroup).filter(
                        HumanStatisticsSmallGroup.project_id == project_id,
                        HumanStatisticsSmallGroup.subject_id == subject_id,
                        HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                        HumanStatisticsSmallGroup.group_id == group_id,
                        HumanStatisticsSmallGroup.task_id == task_id,
                        HumanStatisticsSmallGroup.round_count == round_count,
                        HumanStatisticsSmallGroup.task_type == task_type,
                        HumanStatisticsSmallGroup.statistics_type == stat_type
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsSmallGroup(
                            statistics_group_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 平均分
                            statistics_result_1=cumulative_average
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsSmallGroup.project_id == project_id,
                                HumanStatisticsSmallGroup.subject_id == subject_id,
                                HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                                HumanStatisticsSmallGroup.group_id == group_id,
                                HumanStatisticsSmallGroup.task_id == task_id,
                                HumanStatisticsSmallGroup.round_count == round_count,
                                HumanStatisticsSmallGroup.task_type == task_type,
                                HumanStatisticsSmallGroup.statistics_type == stat_type
                            },
                            'update': {
                                HumanStatisticsSmallGroup.statistics_result_1: cumulative_average
                            }
                        })

                elif stat_type == 2:  # 最高分
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsSmallGroup).filter(
                        HumanStatisticsSmallGroup.project_id == project_id,
                        HumanStatisticsSmallGroup.subject_id == subject_id,
                        HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                        HumanStatisticsSmallGroup.group_id == group_id,
                        HumanStatisticsSmallGroup.task_id == task_id,
                        HumanStatisticsSmallGroup.round_count == round_count,
                        HumanStatisticsSmallGroup.task_type == task_type,
                        HumanStatisticsSmallGroup.statistics_type == stat_type,
                        HumanStatisticsSmallGroup.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsSmallGroup(
                            statistics_group_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 最高分
                            statistics_result_1=max_score,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsSmallGroup.project_id == project_id,
                                HumanStatisticsSmallGroup.subject_id == subject_id,
                                HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                                HumanStatisticsSmallGroup.group_id == group_id,
                                HumanStatisticsSmallGroup.task_id == task_id,
                                HumanStatisticsSmallGroup.round_count == round_count,
                                HumanStatisticsSmallGroup.task_type == task_type,
                                HumanStatisticsSmallGroup.statistics_type == stat_type,
                                HumanStatisticsSmallGroup.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsSmallGroup.statistics_result_1: max_score
                            }
                        })

                elif stat_type == 3:  # 最低分
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsSmallGroup).filter(
                        HumanStatisticsSmallGroup.project_id == project_id,
                        HumanStatisticsSmallGroup.subject_id == subject_id,
                        HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                        HumanStatisticsSmallGroup.group_id == group_id,
                        HumanStatisticsSmallGroup.task_id == task_id,
                        HumanStatisticsSmallGroup.round_count == round_count,
                        HumanStatisticsSmallGroup.task_type == task_type,
                        HumanStatisticsSmallGroup.statistics_type == stat_type,
                        HumanStatisticsSmallGroup.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsSmallGroup(
                            statistics_group_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 最低分
                            statistics_result_1=min_score,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsSmallGroup.project_id == project_id,
                                HumanStatisticsSmallGroup.subject_id == subject_id,
                                HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                                HumanStatisticsSmallGroup.group_id == group_id,
                                HumanStatisticsSmallGroup.task_id == task_id,
                                HumanStatisticsSmallGroup.round_count == round_count,
                                HumanStatisticsSmallGroup.task_type == task_type,
                                HumanStatisticsSmallGroup.statistics_type == stat_type,
                                HumanStatisticsSmallGroup.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsSmallGroup.statistics_result_1: min_score
                            }
                        })

                elif stat_type == 10:  # 工作时间
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsSmallGroup).filter(
                        HumanStatisticsSmallGroup.project_id == project_id,
                        HumanStatisticsSmallGroup.subject_id == subject_id,
                        HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                        HumanStatisticsSmallGroup.group_id == group_id,
                        HumanStatisticsSmallGroup.task_id == task_id,
                        HumanStatisticsSmallGroup.round_count == round_count,
                        HumanStatisticsSmallGroup.task_type == task_type,
                        HumanStatisticsSmallGroup.statistics_type == stat_type,
                        HumanStatisticsSmallGroup.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsSmallGroup(
                            statistics_group_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 工作时间
                            statistics_result_1=total_time,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsSmallGroup.project_id == project_id,
                                HumanStatisticsSmallGroup.subject_id == subject_id,
                                HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                                HumanStatisticsSmallGroup.group_id == group_id,
                                HumanStatisticsSmallGroup.task_id == task_id,
                                HumanStatisticsSmallGroup.round_count == round_count,
                                HumanStatisticsSmallGroup.task_type == task_type,
                                HumanStatisticsSmallGroup.statistics_type == stat_type,
                                HumanStatisticsSmallGroup.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsSmallGroup.statistics_result_1: total_time
                            }
                        })

                elif stat_type == 15:  # 最长速度
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsSmallGroup).filter(
                        HumanStatisticsSmallGroup.project_id == project_id,
                        HumanStatisticsSmallGroup.subject_id == subject_id,
                        HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                        HumanStatisticsSmallGroup.group_id == group_id,
                        HumanStatisticsSmallGroup.task_id == task_id,
                        HumanStatisticsSmallGroup.round_count == round_count,
                        HumanStatisticsSmallGroup.task_type == task_type,
                        HumanStatisticsSmallGroup.statistics_type == stat_type,
                        HumanStatisticsSmallGroup.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsSmallGroup(
                            statistics_group_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 最长速度
                            statistics_result_1=max_speed,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsSmallGroup.project_id == project_id,
                                HumanStatisticsSmallGroup.subject_id == subject_id,
                                HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                                HumanStatisticsSmallGroup.group_id == group_id,
                                HumanStatisticsSmallGroup.task_id == task_id,
                                HumanStatisticsSmallGroup.round_count == round_count,
                                HumanStatisticsSmallGroup.task_type == task_type,
                                HumanStatisticsSmallGroup.statistics_type == stat_type,
                                HumanStatisticsSmallGroup.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsSmallGroup.statistics_result_1: max_speed
                            }
                        })

                elif stat_type == 16:  # 最短速度
                    # 查询是否存在记录
                    existing_person_record = new_session.query(HumanStatisticsSmallGroup).filter(
                        HumanStatisticsSmallGroup.project_id == project_id,
                        HumanStatisticsSmallGroup.subject_id == subject_id,
                        HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                        HumanStatisticsSmallGroup.group_id == group_id,
                        HumanStatisticsSmallGroup.task_id == task_id,
                        HumanStatisticsSmallGroup.round_count == round_count,
                        HumanStatisticsSmallGroup.task_type == task_type,
                        HumanStatisticsSmallGroup.statistics_type == stat_type,
                        HumanStatisticsSmallGroup.date == mark_time.date()
                    ).first()

                    if not existing_person_record:
                        # 创建新记录
                        new_record = HumanStatisticsSmallGroup(
                            statistics_group_id=configs.snow_worker.get_id(),
                            project_id=project_id,
                            subject_id=subject_id,
                            ques_group_id=ques_group_id,
                            group_id=group_id,
                            task_id=task_id,
                            round_count=round_count,
                            task_type=task_type,
                            statistics_type=stat_type,  # 最短速度
                            statistics_result_1=min_speed,
                            date=mark_time.date()
                        )
                        new_records.append(new_record)
                    else:
                        update_conditions.append({
                            'filter': {
                                HumanStatisticsSmallGroup.project_id == project_id,
                                HumanStatisticsSmallGroup.subject_id == subject_id,
                                HumanStatisticsSmallGroup.ques_group_id == ques_group_id,
                                HumanStatisticsSmallGroup.group_id == group_id,
                                HumanStatisticsSmallGroup.task_id == task_id,
                                HumanStatisticsSmallGroup.round_count == round_count,
                                HumanStatisticsSmallGroup.task_type == task_type,
                                HumanStatisticsSmallGroup.statistics_type == stat_type,
                                HumanStatisticsSmallGroup.date == mark_time.date()
                            },
                            'update': {
                                HumanStatisticsSmallGroup.statistics_result_1: min_speed
                            }
                        })

        # 批量插入新记录
        if new_records:
            logger.info(f"准备批量插入 {len(new_records)} 条新记录")
            new_session.add_all(new_records)
            logger.info("批量插入完成")

            # 批量更新现有记录
        for condition in update_conditions:
            filter_conditions = condition['filter']
            update_values = condition['update']
            # 构建查询条件
            query = new_session.query(HumanStatisticsSmallGroup)
            for filter_condition in filter_conditions:
                query = query.filter(filter_condition)
            query.update(update_values)

        new_session.commit()
        logger.info("小组统计数据处理完成")
        return True
    except Exception as e:
        logger.info(f"查询并插入统计数据失败：{e}")
        traceback.print_exc()
        new_session.rollback()
        return False
