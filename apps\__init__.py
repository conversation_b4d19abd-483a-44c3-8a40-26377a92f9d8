import os

from apscheduler.triggers.interval import IntervalTrigger
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware

from starlette.middleware.base import BaseHTTPMiddleware
from starlette.staticfiles import StaticFiles

from apps.base.routers import version_router
from apps.base.schedule_routers import schedule_router
from apps.load_global_data import auto_execute_task, load_global_data
from settings import configs

from fastapi import FastAPI



class KeepAliveMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        response.headers["Connection"] = "keep-alive"
        return response


class ClientIPMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 获取客户端IP地址
        client_ip = request.client.host if request.client else "unknown"

        # 将IP地址添加到请求状态中
        request.state.client_ip = client_ip

        response = await call_next(request)
        return response


def create_app() -> FastAPI:
    if configs.MODE.lower() == "pro" or "_MEI" in configs.ROOT_PATH:
        app = FastAPI()  # 生产环境关闭接口文档
    else:
        app = FastAPI(docs_url=None, redoc_url=None)  # 关闭自带api文档
    app.include_router(router=version_router, prefix=configs.VERSION)
    register_add_middleware(app)
    app.mount("/server_static", StaticFiles(directory=os.path.join(configs.PROJECT_PATH, "server_static")), name="server_static")
    # 挂载前端服务
    app.mount("/", StaticFiles(directory=os.path.join(configs.PROJECT_PATH, "web_root")), name="web_root")
    # 加载全局数据
    load_global_data()

    return app


def register_add_middleware(app: FastAPI):
    # app.add_middleware(AuthMiddleware)
    # app.add_middleware(RecordMiddleware)
    # app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])
    app.add_middleware(KeepAliveMiddleware)
    app.add_middleware(ClientIPMiddleware)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )


def create_schedule_app() -> FastAPI:
    if configs.MODE.lower() == "pro" or "_MEI" in configs.ROOT_PATH:
        app = FastAPI()  # 生产环境关闭接口文档
    else:
        app = FastAPI(docs_url=None, redoc_url=None)  # 关闭自带api文档
    app.include_router(router=schedule_router, prefix=configs.VERSION)
    register_add_middleware(app)
    app.mount("/server_static", StaticFiles(directory=os.path.join(configs.PROJECT_PATH, "server_static")), name="server_static")
    # 加载全局数据
    load_global_data()

    return app
