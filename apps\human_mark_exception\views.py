from fastapi import APIRouter, Depends
from apps.base.global_cache import get_redis_ques_info_dict
from apps.grade_manage.models import HumanStudentSubjectGradeDetail
from apps.human_official_mark.mark_redis_services import create_human_subject_grade
from apps.human_official_mark.services import get_stu_answer_info
from settings import logger
from sqlalchemy import select, exists, and_, func
from sqlalchemy.orm import Session
from typing import Any, List, Dict
from pydantic import BaseModel
import difflib

from apps.permission.services import get_data_permission_sub_id_list
from apps.projects_manage.services import subject_query_condition
from apps.read_paper.common_services import get_user_data_flag
from apps.users.services import get_current_user
from apps.models.models import TaskExecuteRecord, StuAnswer, ExamStudent, ExamQuestion, BusinessQuesType, Subject, UserInfo, ExamPaper, PaperDetail, Project
from apps.human_mark_exception.models import HumanAnswerSimilarity, HumanAnswerQuestSimilarity, HumanAnswerException
from apps.human_mark_exception.schemas import GetAnswerPlagiarismListReq, GetExceptionListReq, SimilarityResult, UpdateJudgementResult, UpdateExceptionResult, GetNoHandleExceptionListReq, \
    HandleExceptionAnswerReq
from helper import response_utils
from factory_apps import session_depend
from utils.time_func import format_now_time
from utils.utils import create_timestamp_id
from settings import configs
from apps.human_task_manage.models import HumanReadTask, HumanPersonDistriAnswer, HumanReadTaskRound
from apps.base.schemas import BaseResponse
from bs4 import BeautifulSoup
from collections import defaultdict
from helper.quest_retrieve import text_same_two
from apps.human_prepare.models import HumanPrepareProcess
from apps.human_prepare.schemas import DeleteAnswerReq, CreateExceptionReq

exception_router = APIRouter()


@exception_router.post(path="/get_answer_plagiarism_list", response_model=BaseResponse, summary="查询作答抄袭列表")
async def get_answer_plagiarism_list(req: GetAnswerPlagiarismListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    查询作答抄袭列表接口
    参数说明：
    - subject_id: 科目ID
    - paper_id: 试卷ID
    - ques_number: 题号（题序）
    - ques_code: 试题编号
    - task_id: 任务ID
    - stu_secret_num: 考生密号
    - judgement_status: 判定状态
    - similarity: 相似度阈值（可选过滤）
    - judgement_result: 判定结果（是否抄袭）
    """
    # 构建过滤条件
    filters = []
    if req.subject_id:
        filters.append(Subject.subject_id == req.subject_id)
    if req.paper_id:
        filters.append(ExamPaper.paper_id == req.paper_id)
    if req.ques_number:
        filters.append(ExamQuestion.ques_order == req.ques_number)
    if req.ques_code:
        filters.append(ExamQuestion.ques_code == req.ques_code)
    if req.stu_secret_num:
        filters.append(StuAnswer.stu_secret_num == req.stu_secret_num)

    # 查询作答相似度表并关联相关信息
    stmt = (
        select(
            HumanAnswerSimilarity.answer_similarity_id,
            HumanAnswerSimilarity.answer_id,
            HumanAnswerSimilarity.similarity_answer_list,
            Subject.subject_name,
            ExamPaper.paper_name,
            ExamQuestion.ques_order,
            ExamQuestion.ques_code,
            HumanReadTask.task_name,
            StuAnswer.stu_secret_num,
        )
        .join(StuAnswer, HumanAnswerSimilarity.answer_id == StuAnswer.answer_id)
        .join(ExamQuestion, StuAnswer.ques_id == ExamQuestion.ques_id)
        .join(ExamPaper, StuAnswer.paper_id == ExamPaper.paper_id)
        .join(Subject, ExamPaper.subject_id == Subject.subject_id)
        .join(HumanReadTask, HumanReadTask.task_id == req.task_id, isouter=True)
    )
    if filters:
        stmt = stmt.where(and_(*filters))

    # 分页参数
    page = req.page or 1
    page_size = req.page_size or 20
    offset = (page - 1) * page_size
    # 获取总记录数
    count_stmt = select(func.count(HumanAnswerSimilarity.answer_similarity_id)).where(and_(*filters)) if filters else select(func.count()).select_from(HumanAnswerSimilarity)
    total_records = new_session.execute(count_stmt).scalar()

    # 应用分页
    stmt = stmt.offset(offset).limit(page_size)

    result = new_session.execute(stmt).all()
    data = [dict(row._mapping) for row in result]

    # 构造分页响应
    pagination_data = {"items": data, "total": total_records, "page": page, "page_size": page_size}

    return BaseResponse(msg="查询作答抄袭列表成功", data=pagination_data)


@exception_router.post("/calculate_ques_answer_similarity", response_model=BaseResponse, summary="计算题干作答相似度")
async def calculate_ques_answer_similarity(user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    计算试题文本与作答文本的相似度接口
    """
    process = new_session.query(HumanPrepareProcess).filter(HumanPrepareProcess.process_type == 5).first()
    if not process:
        return BaseResponse(code=response_utils.no_field, msg="未找到 process_type 为 5 的 HumanPrepareProcess 记录")

    # 2 获取试题列表、查询有效试题信息
    # 收集所有主试题和子试题ID
    all_ques_ids = []
    question_stmt = select(ExamQuestion.ques_id).where(and_(ExamQuestion.parent_ques_id.is_(None), ExamQuestion.ques_type_code.in_(["E", "F", "G"])))
    main_questions = new_session.execute(question_stmt).all()

    # 收集所有子试题ID（类型F）
    for row in main_questions:
        ques_id = row.ques_id
        all_ques_ids.append(ques_id)
        small_question_stmt = select(ExamQuestion.ques_id).where(ExamQuestion.parent_ques_id == ques_id)
        small_questions = new_session.execute(small_question_stmt).all()
        all_ques_ids.extend([s.ques_id for s in small_questions])

    # 统计总作答数量
    total_answers = 0
    for ques_id in all_ques_ids:
        total_answers += new_session.query(StuAnswer).filter(StuAnswer.ques_id == ques_id).count()

    if total_answers == 0:
        process.progress = 100
        new_session.commit()
        return BaseResponse(msg="无作答数据")

    process.process_status = 1
    new_session.commit()
    total_processed = 0  # 初始化已处理计数器

    question_stmt = select(ExamQuestion.ques_id, ExamQuestion.ques_desc, ExamQuestion.ques_type_code).where(
        and_(ExamQuestion.parent_ques_id.is_(None), ExamQuestion.ques_type_code.in_(["E", "F", "G"]))
    )
    questions = new_session.execute(question_stmt).all()
    ##遍历试题，提取html格式ques_desc字段中的文本，赋值给ques_txt字段
    for row in questions:
        answers = []
        if row.ques_type_code == "F":
            row.ques_desc["s_qids"] = []
            small_question_stmt = select(ExamQuestion.ques_id, ExamQuestion.ques_desc, ExamQuestion.ques_type_code).where(ExamQuestion.parent_ques_id == row.ques_id)
            small_questions = new_session.execute(small_question_stmt).all()
            for s_q in small_questions:
                row.ques_desc["text"] += s_q.ques_desc["text"]
                row.ques_desc["s_qids"].append(s_q.ques_id)
            for s_qid in row.ques_desc["s_qids"]:
                # 查询试题和作答信息
                answer_stmt = select(StuAnswer.ques_id, StuAnswer.stu_secret_num, StuAnswer.stu_answer, StuAnswer.answer_image_path).where(StuAnswer.ques_id == s_qid)
                small_answers = new_session.execute(answer_stmt).all()
                answers += small_answers
            # 将answers中的元素按stu_secret_num分组，StuAnswer.stu_answer拼接在一起
            stu_answers = defaultdict(list)
            for a in answers:
                stu_answers[a.stu_secret_num].append((a.stu_answer, a.answer_image_path))
            answers = [
                {
                    "ques_id": row.ques_id,
                    "stu_secret_num": k,
                    "stu_answer": r"\n".join(an for an, _ in v),
                    "answer_image_paths": [path for _, path in v],
                }
                for k, v in stu_answers.items()
            ]
        else:
            # 查询试题和作答信息
            answer_stmt = select(StuAnswer.ques_id, StuAnswer.stu_secret_num, StuAnswer.stu_answer, StuAnswer.answer_image_path).where(StuAnswer.ques_id == row.ques_id)
            answers = new_session.execute(answer_stmt).all()

        # 比较试题与作答相似度
        answer_similaritys = []
        count = 0
        for a in answers:
            if row.ques_type_code == "F":
                answer_image_paths = a["answer_image_paths"]
            else:
                answer_image_paths = [a["answer_image_path"]]
            similarity = text_same_two(row.ques_desc["text"], a["stu_answer"])
            answer_similarity = HumanAnswerQuestSimilarity(
                answer_quest_similarity_id=configs.snow_worker.get_id(),
                ques_id=a["ques_id"],
                stu_secret_num=a["stu_secret_num"],
                similarity=similarity[0],
                same_words=similarity[1],
                answer_image_paths=answer_image_paths,
            )
            answer_similaritys.append(answer_similarity)
            count += 1
            if count == 1000:
                new_session.add_all(answer_similaritys)
                new_session.commit()
                # 计算并更新进度
                total_processed += count
                progress_percent = (total_processed / total_answers) * 100
                process.progress = min(int(progress_percent), 99)  # 最大值99，避免提前完成
                new_session.commit()
                answer_similaritys = []
                count = 0
        # 处理剩余不足1000条的作答
        if len(answer_similaritys) > 0:
            total_processed += len(answer_similaritys)
            new_session.add_all(answer_similaritys)
            new_session.commit()

    # 更新最终进度
    process.progress = 100
    process.process_status = 2
    new_session.commit()
    return BaseResponse(msg="计算相似度成功")


@exception_router.post("/get_ques_answer_similarity", response_model=BaseResponse, summary="获取题干作答相似度")
async def get_ques_answer_similarity(req: GetAnswerPlagiarismListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    计算试题文本与作答文本的相似度接口
    参数说明：
    - subject_id: 科目ID
    - paper_id: 试卷ID
    - ques_number: 题号（题序）
    - ques_code: 试题编号
    - stu_secret_num: 考生密号
    """
    # 1获取项目，科目列表
    # 获取用户有权限访问的科目ID列表
    # subject_ids = get_data_permission_sub_id_list(new_session, user["user_id"])
    # if not subject_ids:
    #     return BaseResponse(msg="暂无权限访问的科目", data=[])

    # 构建过滤条件
    filters = []
    if req.subject_id:
        filters.append(Subject.subject_id == req.subject_id)
    # if req.paper_id:
    #     filters.append(ExamPaper.paper_id == req.paper_id)
    # if req.ques_number:
    # filters.append(ExamQuestion.ques_order == req.ques_number)
    if req.ques_code:
        filters.append(ExamQuestion.ques_code == req.ques_code)
    if req.stu_secret_num:
        filters.append(HumanAnswerQuestSimilarity.stu_secret_num == req.stu_secret_num)

    # 查询相似列表
    stmt = (
        select(
            HumanAnswerQuestSimilarity.answer_quest_similarity_id,
            HumanAnswerQuestSimilarity.ques_id,
            HumanAnswerQuestSimilarity.stu_secret_num,
            HumanAnswerQuestSimilarity.similarity,
            HumanAnswerQuestSimilarity.same_words,
            HumanAnswerQuestSimilarity.judgement_result,
            ExamQuestion.ques_code,
            HumanAnswerQuestSimilarity.answer_image_paths,
            Subject.subject_name,
        )
        .join(ExamQuestion, HumanAnswerQuestSimilarity.ques_id == ExamQuestion.ques_id)
        .join(BusinessQuesType, BusinessQuesType.business_ques_type_id == ExamQuestion.business_ques_type_id)
        .join(Subject, Subject.subject_id == BusinessQuesType.subject_id)
        .where(and_(*filters))
    )

    # 应用分页
    stmt = stmt.offset((req.page_size - 1) * req.page_size).limit(req.page_size)
    results = new_session.execute(stmt).all()
    results = [
        {**dict(row._mapping), "judgement_result_text": "未判定" if dict(row._mapping)["judgement_result"] == 0 else "抄袭" if dict(row._mapping)["judgement_result"] == 1 else "未抄袭"}
        for row in results
    ]

    # 获取总记录数
    count_stmt = select(func.count(HumanAnswerQuestSimilarity.answer_quest_similarity_id)).join(ExamQuestion, HumanAnswerQuestSimilarity.ques_id == ExamQuestion.ques_id).where(and_(*filters))
    total_records = new_session.execute(count_stmt).scalar()
    # 构造分页响应
    pagination_data = {
        "items": results,
        "total": total_records,
    }

    return BaseResponse(msg="计算相似度成功", data=pagination_data)


# 接口：判定答案抄袭
##入参：answer_quest_similarity_id，judgement_result
##逻辑：根据answer_quest_similarity_id查找HumanAnswerQuestSimilarity，将judgement_result根据参数修改


@exception_router.post("/update_judgement_result", response_model=BaseResponse, summary="更新判定结果")
async def update_judgement_result(req: UpdateJudgementResult, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    更新判定结果接口
    参数说明：
    - answer_quest_similarity_id: 作答相似度记录ID
    - judgement_result: 判定结果（0-未判定，1-抄袭，2-未抄袭）
    """
    # 查询相似度记录
    similarity_record = new_session.query(HumanAnswerQuestSimilarity).filter(HumanAnswerQuestSimilarity.answer_quest_similarity_id == req.answer_quest_similarity_id).first()

    if not similarity_record:
        return BaseResponse(code=response_utils.no_field, msg="未找到对应的相似度记录")

    # 更新判定结果
    similarity_record.judgement_result = req.judgement_result
    new_session.commit()

    return BaseResponse(msg="判定结果更新成功")


@exception_router.post(path="/create_answer_exception", response_model=BaseResponse, summary="新增问题卷")
async def create_answer_exception(query: CreateExceptionReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 新增问题卷")
    task_id, round_id, exception_data = query.task_id, query.round_id, query.exception_data
    try:
        for item in exception_data:
            sample = HumanAnswerException(
                answer_exception_id=configs.snow_worker.get_id(),
                answer_id=item.answer_id,
                task_id=task_id,
                round_id=round_id,
                reviewer_id=user["user_id"],
                exception_type=item.exception_type,
                handler_state=0
            )
            new_session.add(sample)
        new_session.commit()
        return BaseResponse(msg="新增问题卷成功")
    except Exception as e:
        logger.error(f"新增问题卷失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="新增问题卷失败")


@exception_router.post(path="/delete_answer_exception", response_model=BaseResponse, summary="删除问题卷")
async def delete_answer_exception(query: DeleteAnswerReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    先把原来的删掉，再新增
    """
    logger.info(f"{user['username']} 删除问题卷")
    task_id, round_id, exception_data, is_delete_all = query.task_id, query.round_id, query.exception_data, query.is_delete_all
    try:
        answer_id_list = [i["answer_id"] for i in exception_data]
        condition = and_(HumanAnswerException.task_id == task_id, HumanAnswerException.round_id == round_id, HumanAnswerException.answer_id.in_(answer_id_list))

        handler_state = new_session.query(func.max(HumanAnswerException.handler_state)).filter(condition).scalar()
        if handler_state == 1:
            return BaseResponse(code=response_utils.permission_deny, msg="该问题卷已被处理，不允许删除")

        new_session.query(HumanAnswerException).filter(condition).delete()
        if not is_delete_all:
            for item in exception_data:
                sample = HumanAnswerException(
                    answer_exception_id=configs.snow_worker.get_id(),
                    answer_id=item.answer_id,
                    task_id=task_id,
                    round_id=round_id,
                    reviewer_id=user["user_id"],
                    exception_type=item.exception_type,
                    handler_state=0
                )
                new_session.add(sample)
        new_session.commit()
        return BaseResponse(msg="删除问题卷成功")
    except Exception as e:
        logger.error(f"删除问题卷失败，{e}")
        new_session.rollback()
        return BaseResponse(code=response_utils.server_error, msg="删除问题卷失败")


@exception_router.post(path="/get_answer_exception_list", response_model=BaseResponse, summary="获取作答异常卷列表")
async def get_answer_exception_list(req: GetExceptionListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    获取作答异常卷列表接口
    参数说明：
    - subject_id: 科目ID
    - ques_code: 试题编号
    """
    # 构建过滤条件
    from apps.models.models import ExamQuestion
    from sqlalchemy import select, and_
    logger.info(f"{user['username']} 获取作答异常卷列表")
    filters = []
    if req.project_id:
        filters.append(Project.project_id == req.project_id)
    if req.subject_id:
        filters.append(Subject.subject_id == req.subject_id)
    # if req.paper_id:
    #     filters.append(ExamPaper.paper_id == req.paper_id)
    # if req.ques_number:
    #     filters.append(ExamQuestion.ques_order == req.ques_number)
    if req.ques_code:
        filters.append(ExamQuestion.ques_code == req.ques_code)
    if req.handler_state is not None:
        filters.append(HumanAnswerException.handler_state == req.handler_state)
    if req.is_show_self == 1:
        filters.append(HumanAnswerException.handler_id == user["user_id"])

        # 查询作答异常表并关联相关信息
    stmt = (
        select(
            HumanAnswerException.answer_exception_id,
            HumanAnswerException.answer_id,
            HumanAnswerException.exception_reason,
            HumanAnswerException.handler_state,
            HumanAnswerException.exception_type,
            HumanAnswerException.created_time,
            HumanAnswerException.task_id,
            HumanAnswerException.mark_score,
            HumanAnswerException.round_id,
            Subject.subject_name,
            Subject.subject_id,
            Project.project_name,
            Project.project_id,
            # ExamPaper.paper_name,
            ExamQuestion.ques_code,
            HumanReadTask.task_name,
            HumanReadTask.task_type,
            StuAnswer.stu_secret_num,
            StuAnswer.answer_image_path,
        )
        .join(StuAnswer, HumanAnswerException.answer_id == StuAnswer.answer_id)
        .join(ExamQuestion, StuAnswer.ques_id == ExamQuestion.ques_id)
        # .join(ExamPaper, StuAnswer.paper_id == ExamPaper.paper_id)
        .join(Subject, Subject.subject_id == StuAnswer.subject_id)
        .join(Project, Project.project_id == StuAnswer.project_id)
        .join(HumanReadTask, HumanReadTask.task_id == HumanAnswerException.task_id, isouter=True)
    )
    if filters:
        stmt = stmt.where(and_(*filters))

    # 分页参数
    page = req.current_page
    page_size = req.page_size
    offset = (page - 1) * page_size
    # 获取总记录数
    # count_stmt = select(func.count(HumanAnswerException.answer_exception_id)).where(and_(*filters)) if filters else select(func.count()).select_from(HumanAnswerException)
    # total_records = new_session.execute(count_stmt).scalar()

    # 应用分页
    stmt = stmt.offset(offset).limit(page_size)

    page = req.current_page
    page_size = req.page_size
    offset = (page - 1) * page_size
    # 获取总记录数 - 这里需要修改为每个考生每道题的条数
    # 先获取所有异常记录的考生和试题组合

    # 获取所有异常记录中的唯一考生-试题组合
    total_records = (new_session.query(
        StuAnswer.stu_secret_num,
        ExamQuestion.ques_code
    ).join(ExamQuestion, StuAnswer.ques_id == ExamQuestion.ques_id)
     .join(HumanAnswerException, HumanAnswerException.answer_id == StuAnswer.answer_id)
                     .where(and_(*filters,))
                     .distinct().count()
                     )

    # 计算总条数：每个考生在每道题下的作答条数

    # for stu_secret_num, ques_code in unique_combinations:
    #     # 获取该考生在该试题下的所有作答数量
    #     ques_ids = new_session.query(ExamQuestion.ques_id).filter(ExamQuestion.ques_code == ques_code).all()
    #     ques_id_list = [q.ques_id for q in ques_ids] if ques_ids else []
    #     if ques_id_list:
    #         count = new_session.query(StuAnswer.answer_id).filter(
    #             and_(
    #                 StuAnswer.stu_secret_num == stu_secret_num,
    #                 StuAnswer.ques_id.in_(ques_id_list)
    #             )
    #         ).count()
    #         total_records += count

    # 应用分页 - 但这里需要重新设计逻辑，因为原来的分页是基于异常记录的
    # 为了保持一致性，我们还是按照原来的逻辑查询，但返回的数据结构需要调整
    # 为每个考生-试题组合返回一条记录，包含该考生的所有作答数据
    result = new_session.execute(stmt).all()
    answer_exception_dict = {}
    for row in result:
        answer_exception_dict[row.answer_id] = {
            "answer_exception_id": row.answer_exception_id,
            "exception_type": row.exception_type,
            "mark_score": row.mark_score
        }
    data = []
    processed_combinations = set()  # 用于避免重复处理相同的考生-试题组合

    for row in result:
        # 检查是否已经处理过这个考生-试题组合
        combination_key = (row.stu_secret_num, row.ques_code)
        if combination_key in processed_combinations:
            continue

        # 获取该考生在该试题下的所有作答信息
        # 需要获取完整的作答数据，包括分数和所有相关作答图片
        from apps.human_official_mark.services import get_stu_answer_info
        from apps.human_task_manage.models import HumanPersonDistriAnswer
        from apps.models.models import ExamQuestion
        from sqlalchemy import select, and_

        # 获取该考生在该试题下的所有作答信息
        # 首先获取试题ID列表（因为一个ques_code可能对应多个ques_id）
        ques_ids = new_session.query(ExamQuestion.ques_id).filter(ExamQuestion.ques_code == row.ques_code).all()
        ques_id_list = [q.ques_id for q in ques_ids] if ques_ids else []

        # 获取该考生在该任务下的所有作答信息
        answer_ids = []
        if ques_id_list:
            # 获取该考生在该试题下的所有作答
            answer_info = new_session.execute(
                select(StuAnswer.answer_id).where(
                    and_(
                        StuAnswer.stu_secret_num == row.stu_secret_num,
                        StuAnswer.ques_id.in_(ques_id_list)
                    )
                )
            ).all()
            answer_ids = [a.answer_id for a in answer_info]

        # 获取完整的作答信息
        stu_answer_dict = get_stu_answer_info(new_session, row.ques_code)

        # 获取该考生在该任务下的所有作答分数信息
        score_info = []
        if answer_ids:
            # 获取分数信息
            score_info = new_session.execute(
                select(
                    HumanPersonDistriAnswer.answer_id,
                    HumanPersonDistriAnswer.mark_score,
                    HumanPersonDistriAnswer.mark_point_score_list
                ).where(
                    and_(
                        HumanPersonDistriAnswer.answer_id.in_(answer_ids),
                        HumanPersonDistriAnswer.is_answer_marked == 1
                    )
                )
            ).all()

        # 构建完整的作答数据
        answer_data = []
        total_score = 0
        if answer_ids and stu_answer_dict:
            for answer_id in answer_ids:
                if answer_id in stu_answer_dict:
                    mark_score = answer_exception_dict[answer_id]["mark_score"]
                    answer_item = {
                        "answer_exception_id": answer_exception_dict[answer_id]["answer_exception_id"],
                        "answer_id": answer_id,
                        "answer_image_path": stu_answer_dict[answer_id]["answer_image_path"],
                        "word_count": stu_answer_dict[answer_id]["word_count"],
                        "is_do": stu_answer_dict[answer_id]["is_do"],
                        "mark_score": mark_score,  # 默认分数为None
                        "mark_point_score_list": [],  # 默认分数列表为空
                        "exception_type": answer_exception_dict[answer_id]["exception_type"],
                    }
                    # 查找对应的分数信息
                    # for score_row in score_info:
                    #     if score_row.answer_id == answer_id:
                    #         # answer_item["mark_score"] = float(score_row.mark_score) if score_row.mark_score else None
                    #         answer_item["mark_point_score_list"] = score_row.mark_point_score_list or []
                    #         break
                    answer_data.append(answer_item)
                    total_score += mark_score if mark_score else 0

        # 构建返回数据 - 每个考生-试题组合返回一条记录
        item = {
            "exception_reason": row.exception_reason,
            "handler_state": row.handler_state,
            "created_time": row.created_time and str(row.created_time).replace("T", " "),
            "task_id": row.task_id,
            "subject_name": row.subject_name,
            "subject_id": row.subject_id,
            "project_name": row.project_name,
            "project_id": row.project_id,
            "ques_code": row.ques_code,
            "task_name": row.task_name,
            "task_type": row.task_type,
            "stu_secret_num": row.stu_secret_num,
            "round_id": row.round_id,
            "mark_score": total_score,
            "answer_data": answer_data  # 添加完整的作答数据
        }
        data.append(item)
        processed_combinations.add(combination_key)

    # 构造分页响应

    # 构造分页响应
    pagination_data = {"items": data, "total": total_records, "page": page, "page_size": page_size}

    return BaseResponse(msg="查询作答异常卷列表成功", data=pagination_data)


@exception_router.post("/update_exception_result", response_model=BaseResponse, summary="处理作答异常")
async def update_exception_result(req: UpdateExceptionResult, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    """
    处理作答异常
    """
    # 查询相似度记录
    similarity_record = new_session.query(HumanAnswerException).filter(HumanAnswerException.answer_exception_id == req.answer_exception_id).first()

    if not similarity_record:
        return BaseResponse(code=response_utils.no_field, msg="未找到对应的作答异常")

    # 更新判定结果
    similarity_record.handler_state = req.handler_state
    new_session.commit()

    return BaseResponse(msg="处理作答异常成功")


@exception_router.post(path="/get_no_handle_exception_list", response_model=BaseResponse, summary="获取未处理问题卷列表")
async def get_no_handle_exception_list(query: GetNoHandleExceptionListReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取未处理问题卷列表")
    selected_stu_secret_num = query.stu_secret_num

    select_fields = [
        HumanAnswerException.answer_exception_id, HumanAnswerException.task_id, HumanAnswerException.round_id, HumanAnswerException.reviewer_id,
        HumanAnswerException.answer_id, HumanAnswerException.exception_type, HumanAnswerException.created_time, HumanReadTask.ques_code
    ]

    stmt = select(*select_fields) \
        .join(HumanReadTask, HumanReadTask.task_id == HumanAnswerException.task_id) \
        .filter(HumanAnswerException.handler_state == 0)

    exception_info = new_session.execute(stmt).all()

    exception_list = [dict(row._mapping) for row in exception_info]

    for item in exception_list:
        ques_code, answer_id = item["ques_code"], item["answer_id"]
        stu_answer_dict = get_stu_answer_info(new_session, ques_code)
        stu_answer_id_dict = stu_answer_dict[answer_id]
        item["stu_secret_num"] = stu_answer_id_dict["stu_secret_num"]
        item["answer_image_path"] = stu_answer_id_dict["answer_image_path"]
        item["word_count"] = stu_answer_id_dict["word_count"]
        item["is_do"] = stu_answer_id_dict["is_do"]

    exception_dict = {}
    for item in exception_list:
        stu_secret_num = item["stu_secret_num"]
        if stu_secret_num in exception_dict:
            exception_dict[stu_secret_num].append(item)
        else:
            exception_dict[stu_secret_num] = [item]

    exception_list = []
    first_one_list = []
    for stu_secret_num, answer_data in exception_dict.items():
        item = {
            "stu_secret_num": stu_secret_num,
            "answer_data": answer_data,
            "ques_code": answer_data[0]["ques_code"],
            "round_id": answer_data[0]["round_id"]
        }

        if stu_secret_num == selected_stu_secret_num:
            first_one_list.append(item)
            continue

        exception_list.append(item)

    fina_exception = first_one_list + exception_list
    data = {
        "data": fina_exception
    }

    return BaseResponse(data=data)


@exception_router.post(path="/handle_exception_answer", response_model=BaseResponse, summary="处理问题卷")
async def handle_exception_answer(query: HandleExceptionAnswerReq, user: Any = Depends(get_current_user), new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 处理问题卷")
    stu_secret_num, task_id, round_id, ques_code, mark_info = query.stu_secret_num, query.task_id, query.round_id, query.ques_code, query.mark_info
    curr_user_id = user["user_id"]

    mark_time = format_now_time()

    task_info = new_session.query(HumanReadTask.task_type, HumanReadTask.project_id, HumanReadTask.subject_id).filter(HumanReadTask.task_id == task_id).first()

    task_type, project_id, subject_id = task_info

    round_count = new_session.query(HumanReadTaskRound.round_count).filter(HumanReadTaskRound.round_id == round_id).scalar()

    stu_answer_dict = get_stu_answer_info(new_session, ques_code)

    for mark_item in mark_info:
        answer_exception_id, answer_id, mark_point_score_list, mark_score = mark_item.model_dump().values()
        # 保存分数
        new_session.query(HumanAnswerException).filter(HumanAnswerException.answer_exception_id == answer_exception_id).update({
            HumanAnswerException.handler_state: 1,
            HumanAnswerException.handler_id: curr_user_id,
            HumanAnswerException.mark_point_score_list: mark_point_score_list,
            HumanAnswerException.mark_score: mark_score,
            HumanAnswerException.handle_time: mark_time
        })
        # 保存科目成绩
        ques_id = stu_answer_dict[answer_id]["ques_id"]
        create_human_subject_grade(new_session, round_id, round_count, project_id, subject_id, task_type, stu_secret_num, mark_score, curr_user_id, answer_id, ques_id, mark_point_score_list)

    new_session.commit()
    return BaseResponse(msg="处理问题卷成功")
