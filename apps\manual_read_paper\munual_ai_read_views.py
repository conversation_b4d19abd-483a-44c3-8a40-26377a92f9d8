import threading

from fastapi import APIRouter, Depends
from settings import logger
from sqlalchemy import and_
from typing import Any

from sqlalchemy.orm import Session

from apps.manual_read_paper.manual_ai_read_services import ai_arbitrate_task, ai_quality_task, manual_ai_mark_task, \
    get_ai_manual_data, get_ai_mark_again_data
from apps.manual_read_paper.schemas import StartManualAiTaskReq, GetAiMarkTaskProcessReq
from apps.manual_read_paper.services import check_task_can_or_not_mark
from apps.users.services import get_current_user
from apps.base.schemas import BaseResponse

from apps.models.models import ManualDistributeAnswer, ManualMark
from helper import response_utils
from factory_apps import session_depend
from utils.utils import round_half_up

manual_ai_read_router = APIRouter()


@manual_ai_read_router.post(path="/start_ai_read_task", response_model=BaseResponse,
                            summary="AI 开始执行阅卷/仲裁/质检任务")
async def start_ai_read_task(query: StartManualAiTaskReq, user: Any = Depends(get_current_user),
                             new_session: Session = Depends(session_depend)):
    (m_read_task_id, paper_code, ques_id, ques_code, ques_type_code, group_id, project_id, subject_id, batch_num,
     ai_type, execute_user_id, execute_role_id, is_mark_again) = query.model_dump().values()

    msg = check_task_can_or_not_mark(new_session, m_read_task_id)
    if msg:
        return BaseResponse(code=response_utils.permission_deny, msg=msg)

    curr_user_id = user['user_id']
    if execute_role_id == "3":
        logger.info(f"{user['username']} 开始 AI 评分")
        result, distri_answer_data = get_ai_manual_data(m_read_task_id, paper_code, ques_code, ques_id, ques_type_code,
                                                        group_id, project_id, subject_id, user)
        logger.info(f"获取需要评分的数据 {distri_answer_data}")
        args = (distri_answer_data, m_read_task_id, group_id, user)
        if not result:
            return BaseResponse(code=response_utils.server_error, msg="服务器异常")
        if not is_mark_again:
            if not distri_answer_data:
                return BaseResponse(code=response_utils.no_field, msg="评分数据已评分，是否进行重评？")
        else:
            result, all_stu_answer_id_info = get_ai_mark_again_data(new_session, m_read_task_id, curr_user_id)
            logger.info(f"获取需要重新评分的数据 {all_stu_answer_id_info}")
            args = (all_stu_answer_id_info, m_read_task_id, group_id, user)

        logger.info("AI 评分任务执行")
        threading.Thread(target=manual_ai_mark_task, args=args).start()

    elif execute_role_id == "4":
        logger.info(f"{user['username']} 开始 AI 仲裁评分")
        # 获取需要仲裁的数据
        result, distri_answer_data = get_ai_manual_data(m_read_task_id, paper_code, ques_code, ques_id, ques_type_code,
                                                        group_id, project_id, subject_id, user)
        logger.info(f"获取需要仲裁的数据 {distri_answer_data}")
        if not result:
            return BaseResponse(code=response_utils.server_error, msg="服务器异常")
        if not distri_answer_data:
            return BaseResponse(code=response_utils.no_field, msg="暂无需仲裁数据")

        args = (distri_answer_data, m_read_task_id, ques_id, group_id, user)
        threading.Thread(target=ai_arbitrate_task, args=args).start()

    elif execute_role_id == "5":
        logger.info(f"{user['username']} 开始 AI 质检评分")
        # 获取需要质检的数据
        result, distri_answer_data = get_ai_manual_data(m_read_task_id, paper_code, ques_code, ques_id, ques_type_code,
                                                        group_id, project_id, subject_id, user)
        logger.info(f"获取需要质检的数据 {distri_answer_data}")
        if not result:
            return BaseResponse(code=response_utils.server_error, msg="服务器异常")
        if not distri_answer_data:
            return BaseResponse(code=response_utils.no_field, msg="暂无需质检数据")

        args = (distri_answer_data, m_read_task_id, ques_id, group_id, user)
        threading.Thread(target=ai_quality_task, args=args).start()
    else:
        return BaseResponse(code=response_utils.params_error, msg="该角色无可执行任务")
    return BaseResponse(msg="任务开始执行")


@manual_ai_read_router.post(path="/get_ai_mark_task_process", response_model=BaseResponse,
                            summary="获取 AI 执行评分任务的进度")
async def get_ai_read_task_process(query: GetAiMarkTaskProcessReq, user: Any = Depends(get_current_user),
                                   new_session: Session = Depends(session_depend)):
    logger.info(f"{user['username']} 获取 AI 执行评分任务的进度")
    m_read_task_id_list = query.m_read_task_id_list
    curr_user_id = user["user_id"]
    data = []
    for m_read_task_id in m_read_task_id_list:
        # 所有 AI 需要评分的考生作答数量
        distri_count = new_session.query(ManualDistributeAnswer.distri_answer_id).filter(
            ManualDistributeAnswer.m_read_task_id == m_read_task_id).count()
        # AI 已评分的考生作答数量
        marked_count = new_session.query(ManualMark.manual_mark_id).filter(and_(
            ManualMark.m_read_task_id == m_read_task_id, ManualMark.mark_person_id == curr_user_id)).count()
        if distri_count != 0:
            percentage = round_half_up(marked_count / distri_count * 100, 2)
        else:
            percentage = 0
        item = {
            "m_read_task_id": m_read_task_id,
            "marked_count": marked_count,
            "total": distri_count,
            "percentage": percentage
        }
        data.append(item)
    return BaseResponse(data=data)
