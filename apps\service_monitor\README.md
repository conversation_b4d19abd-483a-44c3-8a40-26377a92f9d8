# 服务监控系统

这是一个用于监控和控制exe服务的API系统。系统可以检查服务状态并在服务异常时重启服务。

## 功能特性

1. **服务状态监控** - 检查服务进程是否存在以及通过HTTP接口检查健康状态
2. **服务自动重启** - 当检测到服务异常时，自动尝试重启服务
3. **RESTful API接口** - 提供标准的HTTP接口供前端调用

## API接口

### 1. 检查服务状态
```
POST /monitor/status
```
请求体:
```json
{
  "service_name": "data_statistics_service"
}
```

响应:
```json
{
  "service_name": "data_statistics_service",
  "status": "running",
  "last_check_time": "2025-08-25T17:45:30.123456",
  "error_message": null,
  "health_check_url": "http://localhost:8080/health"
}
```

### 2. 轮询检查服务状态
```
POST /monitor/poll
```
请求体:
```json
{
  "service_name": "data_statistics_service"
}
```

响应:
```json
{
  "service_name": "data_statistics_service",
  "status": "running",
  "last_check_time": "2025-08-25T17:45:30.123456",
  "error_message": null,
  "health_check_url": "http://localhost:8080/health"
}
```

### 3. 获取最新服务状态
```
GET /monitor/latest/{service_name}
```

### 4. 获取监控历史
```
GET /monitor/history/{service_name}
```

## 安装和运行

1. 安装依赖:
```
pip install -r requirements.txt
```

2. 运行服务:
```
uvicorn views:app --host 0.0.0.0 --port 8000
```

## 配置说明

在 `services.py` 文件中需要根据实际情况修改以下配置：

```python
self.services_config = {
    "data_statistics_service": {
        "exe_path": "C:/path/to/your/data_statistics_service.exe",  # exe文件路径
        "health_check_url": "http://localhost:8080/health",  # 健康检查URL
        "process_name": "data_statistics_service.exe"  # 进程名称
    }
}
```

## 项目结构

- `schemas.py` - 数据模型定义
- `services.py` - 业务逻辑和服务监控核心代码
- `views.py` - API接口定义
- `requirements.txt` - 项目依赖包

## 注意事项

1. 确保 `data_statistics_service.exe` 文件路径正确
2. 确保健康检查URL可以访问
3. 确保运行环境有足够权限启动和停止进程
4. 实际部署时需要替换模拟的数据库存储为真实的数据库
