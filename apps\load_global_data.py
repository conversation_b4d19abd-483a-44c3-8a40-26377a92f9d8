import threading
import time

import requests
from sqlalchemy import select

from apps.ai_set_std.set_std_services import get_has_answer_ques_id_list, set_answer_group_count_to_redis
from apps.base.global_cache import set_all_small_ques_count, set_stu_num_by_task_id, set_manual_mark_info, get_redis_ques_info_dict, get_redis_ques_small_info_dict,set_unassigned_students_to_queue
from apps.base.services import request_api, delete_keys_with_prefix
from apps.human_official_mark.services import get_stu_answer_info
from apps.human_task_manage.models import HumanReadTask
from apps.models.models import SameStuAnswerGroup, QuesSetStdState
from factory_apps import session_depend, redis_session
from settings import configs, logger


def load_task_answer(ques_code_list=None):
    new_session = next(session_depend())
    if ques_code_list is None:
        ques_code_list = list(new_session.execute(select(HumanReadTask.ques_code)).scalars().all())

    for ques_code in ques_code_list:
        get_stu_answer_info(new_session, ques_code, is_reload=True)


def load_global_data():
    """
    加载全局数据
    """
    new_session = next(session_depend())
    # 将作答按照 ques_code 加载到redis
    threading.Thread(target=load_task_answer).start()
    # 加载所有试题的小题数
    set_all_small_ques_count(new_session)
    # 将数据库的试题信息加载到redis
    get_redis_ques_info_dict(new_session, is_reload=True)
    get_redis_ques_small_info_dict(new_session, is_reload=True)
    ques_id_list = get_has_answer_ques_id_list(new_session, is_reload=True)
    set_answer_group_count_to_redis(new_session, ques_id_list, {})
    # 获取并设置所有或者指定任务的学生数量
    set_stu_num_by_task_id()
    # 设置阅卷任务每个小组需要评分的考生，校验考生是否属于该专家评分
    set_manual_mark_info()
    set_unassigned_students_to_queue()
    new_session.close()
    # 清空缓存 redis 里的科目成绩字段
    redis = next(redis_session())
    delete_keys_with_prefix(redis, "grade_id_")


def check_sys_alive():
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/base/sys_alive"
    try:
        res = requests.get(url)
        if res.status_code == 200:
            return True
    except requests.exceptions.ConnectionError:
        return False


def auto_request_again_set_std(ques_id, select_group_id_list):
    req_data = {"ques_id": ques_id, "select_group_id_list": select_group_id_list}
    token = configs.GLOBAL_TOKEN
    url = f"http://127.0.0.1:{configs.PORT}{configs.VERSION}/set_std/again_ai_set_std"
    res, msg = request_api(url, "POST", req_data, token, "重新定标的数据")
    if res != 0:
        logger.info(f"{ques_id} 重新定标任务运行成功")
    else:
        logger.info(f"{ques_id} 重新定标任务运行失败")


def auto_execute_task():
    """
    程序运行起来后自动执行的任务
    """
    new_session = next(session_depend())
    # 程序重启检测是否有正在定标的任务
    ques_info = new_session.query(QuesSetStdState.ques_id).filter(QuesSetStdState.set_std_state == 2).all()
    if ques_info:
        ques_id_list = [i[0] for i in ques_info]
        # 自动启动
        # result, _ = auto_continue_ai_set_std(ques_id_list, token, is_auto_launch=True)
        # if result:
        #     logger.info("正在运行中的任务自启成功")
        # else:
        #     logger.error("正在运行中的任务自启失败")

        # 将正在运行中的任务状态改为已暂停
        new_session.query(QuesSetStdState).filter(QuesSetStdState.ques_id.in_(ques_id_list)).update({QuesSetStdState.running_state: 4, QuesSetStdState.set_std_state: 4, QuesSetStdState.pause_state: 3})
        new_session.commit()

    # 程序重启检测是否有重新定标的数据，有的话继续定标
    group_info = new_session.query(SameStuAnswerGroup.ques_id, SameStuAnswerGroup.same_answer_group_id).filter(SameStuAnswerGroup.mark_priority == 1).all()
    if group_info:
        # 因为是调用系统自身的接口，所以要等待系统启动后才可进行调用 API 操作
        while True:
            time.sleep(1)
            if check_sys_alive():
                break

        ques_group_id_dict = {}
        for ques_id, same_answer_group_id in group_info:
            if ques_id in ques_group_id_dict:
                ques_group_id_dict[ques_id].append(same_answer_group_id)
            else:
                ques_group_id_dict = {ques_id: [same_answer_group_id]}

        for ques_id in ques_group_id_dict:
            select_group_id_list = ques_group_id_dict[ques_id]
            threading.Thread(target=auto_request_again_set_std, args=(ques_id, select_group_id_list)).start()
    new_session.close()
