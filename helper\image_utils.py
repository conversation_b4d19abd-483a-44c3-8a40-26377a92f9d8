import os
from PIL import Image
from settings import configs


def concat_images_vertically(image_paths, output_path):
    # 打开所有图片
    images = [Image.open(os.path.join(configs.PROJECT_PATH, "server_static", p)) for p in image_paths]

    # 确认宽度相同
    widths = [img.width for img in images]
    if len(set(widths)) != 1:
        raise ValueError("图片宽度不一致，无法合成！")

    # 计算合成后的宽高
    total_height = sum(img.height for img in images)
    width = images[0].width

    # 创建一张空白图
    result = Image.new("RGB", (width, total_height))

    # 逐张拼接
    y_offset = 0
    for img in images:
        result.paste(img, (0, y_offset))
        y_offset += img.height

    # 保存结果
    result.save(output_path)
