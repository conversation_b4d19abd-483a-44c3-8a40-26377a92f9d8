import traceback
from urllib.parse import quote_plus

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from settings import configs

password = configs.MYSQL_CONFIG['PASSWORD']
encode_pwd = quote_plus(password)

# 数据库连接配置
sqlalchemy_db_uri = rf"mysql+pymysql://{configs.MYSQL_CONFIG['USER']}:{encode_pwd}@{configs.MYSQL_CONFIG['HOST']}:{configs.MYSQL_CONFIG['PORT']}/{configs.MYSQL_CONFIG['DATABASE']}"

# 创建数据库引擎
engine = create_engine(sqlalchemy_db_uri,
                       pool_size=configs.MYSQL_ENGINE_CONFIG["POOL_SIZE"],  # 最小连接数
                       max_overflow=configs.MYSQL_ENGINE_CONFIG["MAX_OVERFLOW"],  # 允许连接数超过 pool_size 多少，可用于设置最大连接数
                       echo=configs.MYSQL_ENGINE_CONFIG["ECHO"],  # 打印执行的 sql 语句
                       pool_pre_ping=configs.MYSQL_ENGINE_CONFIG["POOL_PRE_PING"],  # 开启连接断开检测
                       pool_recycle=configs.MYSQL_ENGINE_CONFIG["POOL_RECYCLE"],  # 连接过期时间，单位为秒
                       connect_args={
                           "connect_timeout": configs.MYSQL_CONFIG["CONNECT_TIMEOUT"]  # 服务器等待连接握手超时时间，单位为秒
                       }
                       )


# 设置 MySQL 全局变量 max_connections
def set_max_connections(value: int):
    try:
        with engine.connect() as connection:
            connection.execute(text(f"SET GLOBAL max_connections = {value}"))
            # group_concat 默认最长长度为 1024，将其调大
            connection.execute(text("SET SESSION group_concat_max_len = 900000;"))
    except Exception as e:
        print(f"数据库连接失败：{e}，请检查 AppSetting.json 的数据库配置并重启服务")
        traceback.print_exc()


# 调用函数设置 max_connections 数量
set_max_connections(configs.MYSQL_CONFIG["MAX_CONNECTIONS"])

# 检查 group_concat_max_len 是否设置成功
# with engine.connect() as connection:
#     result = connection.execute(text("select @@session.group_concat_max_len;"))
#     print("result", result.scalar())

db_session = sessionmaker(bind=engine, autoflush=True, autocommit=False)


def session_depend():
    new_session = db_session()
    # 调整排序操作的内存缓冲区大小为 1G
    new_session.execute(text("SET sort_buffer_size = 1073741824;"))
    try:
        yield new_session
    except Exception as e:
        # 确保在异常情况下也能正确关闭会话
        new_session.rollback()
        raise e
    finally:
        # 确保会话被正确关闭
        if new_session:
            new_session.close()


ddl_engine = create_engine(sqlalchemy_db_uri, isolation_level="AUTOCOMMIT")


def ddl_session():
    new_session = sessionmaker(bind=ddl_engine)()
    try:
        yield new_session
    except Exception as e:
        # 确保在异常情况下也能正确关闭会话
        new_session.rollback()
        raise e
    finally:
        # 确保会话被正确关闭
        if new_session:
            new_session.close()
