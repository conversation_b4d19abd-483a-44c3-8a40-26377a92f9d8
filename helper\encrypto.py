from base64 import b64decode

from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

from settings import configs


def decrypt(ciphertext: str) -> str:
    KEY = configs.PWD_KEY.encode("utf-8")
    cipher = AES.new(KEY, AES.MODE_ECB)
    encrypted_data = b64decode(ciphertext)  # 因为crypto-js默认输出是Base64编码的
    decrypted_data = unpad(cipher.decrypt(encrypted_data), AES.block_size)
    return decrypted_data.decode("utf-8")
